<template>
  <div class="welcome-container">
    <!-- 背景网格和渐变 -->
    <div class="background-layer">
      <div class="gradient-base"></div>
      <div class="gradient-overlay"></div>
      <div class="tech-grid"></div>
      <div class="data-streams">
        <div class="stream stream-1"></div>
        <div class="stream stream-2"></div>
        <div class="stream stream-3"></div>
      </div>
      <div class="floating-particles">
        <div class="particle particle-1"></div>
        <div class="particle particle-2"></div>
        <div class="particle particle-3"></div>
        <div class="particle particle-4"></div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 欢迎区域 -->
      <section class="welcome-section">
        <div class="welcome-content">
          <h1 class="welcome-title">欢迎回来，{{ username }}</h1>
          <p class="welcome-subtitle">{{ todayMessage }}</p>
        </div>
        <div class="hero-illustration">
          <div class="bus-3d-container">
            <div class="bus-model"></div>
            <div class="floating-elements">
              <div class="element element-1"></div>
              <div class="element element-2"></div>
              <div class="element element-3"></div>
            </div>
          </div>
        </div>
      </section>

      <!-- 功能卡片和指标区域 -->
      <section class="content-grid">
        <!-- 功能卡片区域 -->
        <div class="features-area">
          <div class="features-grid">
            <div v-for="feature in features" :key="feature.id" class="feature-card" @click="navigateTo(feature)">
              <div class="feature-icon">
                <svg v-if="feature.id === 'bigscreen'" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <rect x="2" y="6" width="20" height="12" rx="2" stroke="currentColor" stroke-width="1.5" />
                  <path d="M12 18v2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <path d="M8 22h8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <circle cx="6" cy="10" r="1" fill="currentColor" />
                  <circle cx="18" cy="14" r="1" fill="currentColor" />
                  <path d="M8 12l3 2 5-4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                </svg>

                <svg v-else-if="feature.id === 'monitor'" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <rect x="3" y="8" width="18" height="10" rx="2" stroke="currentColor" stroke-width="1.5" />
                  <path d="M7 16h10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <circle cx="6" cy="12" r="1" fill="currentColor" />
                  <circle cx="18" cy="12" r="1" fill="currentColor" />
                  <path d="M12 6v2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <path d="M8 4l1 2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <path d="M16 4l-1 2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                </svg>

                <svg v-else-if="feature.id === 'schedule'" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <rect x="3" y="4" width="18" height="18" rx="2" stroke="currentColor" stroke-width="1.5" />
                  <path d="M16 2v4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <path d="M8 2v4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <path d="M3 10h18" stroke="currentColor" stroke-width="1.5" />
                  <path d="M8 14h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                  <path d="M12 14h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                  <path d="M16 14h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                  <path d="M8 18h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                  <path d="M12 18h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                </svg>

                <svg v-else-if="feature.id === 'passenger'" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="1.5" />
                  <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="1.5" />
                  <path d="M22 21v-2a4 4 0 0 0-3-3.87" stroke="currentColor" stroke-width="1.5" />
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" stroke="currentColor" stroke-width="1.5" />
                  <path d="M12 16l2 2 4-4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                </svg>

                <svg v-else-if="feature.id === 'alerts'" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <rect x="3" y="8" width="18" height="10" rx="2" stroke="currentColor" stroke-width="1.5" />
                  <path d="M7 16h10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <circle cx="6" cy="12" r="1" fill="currentColor" />
                  <circle cx="18" cy="12" r="1" fill="currentColor" />
                  <path
                    d="M12 3l1.09 3.26L17 7l-2.91.74L13 11l-1.09-3.26L8 7l2.91-.74L12 3z"
                    stroke="currentColor"
                    stroke-width="1.5"
                    stroke-linejoin="round"
                  />
                  <circle cx="19" cy="5" r="3" fill="none" stroke="currentColor" stroke-width="1.5" />
                  <path d="M19 4v2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <path d="M19 8h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                </svg>

                <svg v-else-if="feature.id === 'reports'" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M3 3v18h18" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <path d="M18 17l-5-5-4 4-4-4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                  <rect x="7" y="12" width="2" height="6" fill="currentColor" opacity="0.3" />
                  <rect x="11" y="8" width="2" height="10" fill="currentColor" opacity="0.5" />
                  <rect x="15" y="6" width="2" height="12" fill="currentColor" opacity="0.7" />
                </svg>

                <svg v-else-if="feature.id === 'settings'" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="1.5" />
                  <path d="M12 1v6m0 6v6" stroke="currentColor" stroke-width="1.5" opacity="0.5" />
                  <path d="M21 12h-6m-6 0H3" stroke="currentColor" stroke-width="1.5" opacity="0.5" />
                  <path d="M4.22 4.22l4.24 4.24m7.07 7.07l4.24 4.24" stroke="currentColor" stroke-width="1.5" opacity="0.3" />
                  <path d="M19.78 4.22l-4.24 4.24M8.46 15.54l-4.24 4.24" stroke="currentColor" stroke-width="1.5" opacity="0.3" />
                  <circle cx="12" cy="12" r="8" stroke="currentColor" stroke-width="1.5" opacity="0.2" />
                </svg>

                <svg v-else-if="feature.id === 'announcement'" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M3 11v3a1 1 0 0 0 1 1h3l3 3v-10l-3 3H4a1 1 0 0 0-1 1z" stroke="currentColor" stroke-width="1.5" fill="currentColor" opacity="0.1" />
                  <path d="M13.5 5.5c1.5 1.5 1.5 3.5 0 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <path d="M16 3c3 3 3 7 0 10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <path d="M19.5 1.5c4.5 4.5 4.5 11.5 0 16" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" opacity="0.5" />
                  <circle cx="18" cy="6" r="1.5" fill="currentColor" opacity="0.7" />
                  <path d="M17 7l1 1" stroke="currentColor" stroke-width="1" stroke-linecap="round" opacity="0.7" />
                </svg>
              </div>
              <h3 class="feature-title">{{ feature.title }}</h3>
              <p class="feature-description">{{ feature.description }}</p>
              <div class="feature-arrow">→</div>
            </div>
          </div>
        </div>

        <!-- 关键指标区域 -->
        <div class="metrics-area">
          <div class="metrics-grid">
            <div v-for="metric in metrics" :key="metric.id" class="metric-card" :class="`metric-${metric.id}`">
              <div class="metric-icon">
                <svg v-if="metric.id === 'buses'" width="32" height="32" viewBox="0 0 24 24" fill="none">
                  <rect x="3" y="8" width="18" height="10" rx="2" stroke="currentColor" stroke-width="1.5" />
                  <path d="M7 16h10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <circle cx="6" cy="12" r="1" fill="currentColor" />
                  <circle cx="18" cy="12" r="1" fill="currentColor" />
                </svg>
                <svg v-else-if="metric.id === 'trips'" width="32" height="32" viewBox="0 0 24 24" fill="none">
                  <path d="M8 2v3m8-3v3" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <rect x="3" y="4" width="18" height="18" rx="2" stroke="currentColor" stroke-width="1.5" />
                  <path d="M3 9h18" stroke="currentColor" stroke-width="1.5" />
                  <path d="M7 13h2m6 0h2m-10 4h2m6 0h2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                </svg>
                <svg v-else-if="metric.id === 'passengers'" width="32" height="32" viewBox="0 0 24 24" fill="none">
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="1.5" />
                  <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="1.5" />
                  <path d="M20 8v6m3-3h-6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                </svg>
                <svg v-else-if="metric.id === 'delays'" width="32" height="32" viewBox="0 0 24 24" fill="none">
                  <path d="M12 9v3l2 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5" />
                  <path d="M12 6v.01M12 18v.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                </svg>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ metric.value }}</div>
                <div class="metric-label">{{ metric.label }}</div>
                <div class="metric-trend" :class="metric.trendClass">
                  {{ metric.trend }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup name="Index" lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 用户信息
const username = ref('管理员');
const userInitial = ref('管');

// 时间显示
const currentTime = ref('');
const currentDate = ref('');
const todayMessage = ref('');

// 功能模块
const features = ref([
  {
    id: 'bigscreen',
    title: '大屏监控',
    description: '全屏可视化大屏监控展示',
    path: '/bigScreen'
  },
  {
    id: 'monitor',
    title: '实时车辆监控',
    description: '实时追踪车辆位置和运行状态',
    path: '/transit-monitor/vehicle'
  },
  {
    id: 'schedule',
    title: '线路调度管理',
    description: '智能排班和路线优化调度',
    path: '/schedule/template'
  },
  {
    id: 'passenger',
    title: '站点客流分析',
    description: '客流量统计和乘客行为分析',
    path: '/transit-monitor/display/passenger'
  },
  {
    id: 'alerts',
    title: '车辆状态告警',
    description: '异常情况监控和及时告警',
    path: '/transit-monitor/vehicle/alert'
  },
  {
    id: 'reports',
    title: '数据报表',
    description: '运营数据统计和分析报告',
    path: '/transit-monitor/display'
  },
  {
    id: 'announcement',
    title: '报站系统测试',
    description: '解析和测试F31格式报站配置文件',
    path: '/gateway/f31-parser'
  },
  {
    id: 'settings',
    title: '系统设置',
    description: '系统配置和参数管理',
    path: '/basic/operation'
  }
]);

// 关键指标
const metrics = ref([
  {
    id: 'buses',
    value: '156',
    label: '运营中车辆',
    trend: '+5.2%',
    trendClass: 'trend-up'
  },
  {
    id: 'trips',
    value: '2,847',
    label: '今日班次',
    trend: '+12.3%',
    trendClass: 'trend-up'
  },
  {
    id: 'passengers',
    value: '18,942',
    label: '当前载客量',
    trend: '+8.7%',
    trendClass: 'trend-up'
  },
  {
    id: 'delays',
    value: '3',
    label: '延误告警',
    trend: '-2.1%',
    trendClass: 'trend-down'
  }
]);

// 更新时间和消息
const updateDateTime = () => {
  const now = new Date();

  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  });

  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  });

  const weekday = now.toLocaleDateString('zh-CN', { weekday: 'long' });
  todayMessage.value = `今天是${weekday}，公交线路运营状况良好`;
};

// 导航函数
const navigateTo = (feature: any) => {
  if (feature.id === 'bigscreen') {
    // 大屏页面在新窗口打开
    window.open(feature.path, '_blank');
  } else {
    // 其他页面在当前窗口打开
    router.push(feature.path);
  }
};

// 导航到主要功能模块（去掉原有的enterDashboard函数）

let timeInterval: NodeJS.Timeout;

onMounted(() => {
  updateDateTime();
  timeInterval = setInterval(updateDateTime, 1000);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});
</script>

<style scoped>
/* 基础容器 */
.welcome-container {
  position: relative;
  min-height: 100vh;
  height: 100%;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 40%, #334155 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
}

/* 确保背景覆盖整个视窗 */
.welcome-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 40%, #334155 100%);
  z-index: -2;
}

/* 背景层 - 优化多层背景动画 */
.background-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.gradient-base {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 40%, #334155 100%);
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(ellipse at top right, rgba(64, 158, 255, 0.15) 0%, transparent 60%),
    radial-gradient(ellipse at bottom left, rgba(103, 194, 58, 0.1) 0%, transparent 60%);
  opacity: 0.8;
  animation: gradientPulse 30s ease-in-out infinite;
  will-change: opacity, transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

@keyframes gradientPulse {
  0%,
  100% {
    opacity: 0;
    transform: scale(1) translateZ(0);
  }
  25% {
    opacity: 0.3;
    transform: scale(1.01) translateZ(0);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.02) translateZ(0);
  }
  75% {
    opacity: 0.3;
    transform: scale(1.01) translateZ(0);
  }
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(rgba(64, 158, 255, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(64, 158, 255, 0.1) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: gridMove 40s linear infinite;
  will-change: transform;
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(60px, 60px);
  }
}

/* 主内容区 - 调整布局，去掉顶部信息栏 */
.main-content {
  position: relative;
  min-height: calc(100vh - 80px);
  padding: 40px 28px 40px 28px;
  display: flex;
  flex-direction: column;
  z-index: 1;
}

/* 欢迎区域 - 调整位置 */
.welcome-section {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 40px;
  align-items: center;
  height: 200px;
  margin-bottom: 30px;
  flex-shrink: 0;
}

.welcome-content {
  padding: 10px 0;
}

.welcome-title {
  font-size: 42px;
  font-weight: 300;
  color: #f8fafc;
  margin: 0 0 16px 0;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.welcome-subtitle {
  font-size: 16px;
  color: #94a3b8;
  margin: 0;
  line-height: 1.6;
}

.hero-illustration {
  position: relative;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bus-3d-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.bus-model {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 160px;
  height: 64px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  border-radius: 32px 32px 16px 16px;
  box-shadow: 0 16px 32px rgba(64, 158, 255, 0.4);
  animation: busFloat 4s ease-in-out infinite;
}

.bus-model::before {
  content: '';
  position: absolute;
  top: 12px;
  left: 16px;
  right: 16px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
}

.bus-model::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 24px;
  right: 24px;
  height: 16px;
  background: rgba(64, 158, 255, 0.4);
  border-radius: 50%;
  filter: blur(8px);
}

@keyframes busFloat {
  0%,
  100% {
    transform: translate(-50%, -50%) translateY(0px);
  }
  50% {
    transform: translate(-50%, -50%) translateY(-8px);
  }
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.element {
  position: absolute;
  background: rgba(64, 158, 255, 0.2);
  border-radius: 50%;
  animation: elementFloat 6s ease-in-out infinite;
  border: 1px solid rgba(64, 158, 255, 0.3);
}

.element-1 {
  width: 40px;
  height: 40px;
  top: 15%;
  right: 10%;
  animation-delay: 0s;
}

.element-2 {
  width: 30px;
  height: 30px;
  bottom: 25%;
  left: 15%;
  animation-delay: 2s;
}

.element-3 {
  width: 24px;
  height: 24px;
  top: 55%;
  right: 25%;
  animation-delay: 4s;
}

@keyframes elementFloat {
  0%,
  100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-16px) scale(1.1);
  }
}

/* 内容网格区域 - 剩余空间 */
.content-grid {
  flex: 1;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  min-height: 0;
}

/* 功能卡片区域 */
.features-area {
  display: flex;
  flex-direction: column;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  height: 100%;
}

.feature-card {
  background: rgba(15, 23, 42, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  padding: 24px 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(64, 158, 255, 0.3);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 140px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(103, 194, 58, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(64, 158, 255, 0.3);
  border-color: rgba(64, 158, 255, 0.6);
  background: rgba(15, 23, 42, 0.95);
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-icon {
  width: 24px;
  height: 24px;
  color: #409eff;
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}

.feature-title {
  font-size: 16px;
  font-weight: 600;
  color: #f8fafc;
  margin: 0 0 12px 0;
  position: relative;
  z-index: 1;
}

.feature-description {
  font-size: 13px;
  color: #94a3b8;
  margin: 0 0 16px 0;
  line-height: 1.5;
  position: relative;
  z-index: 1;
  flex: 1;
}

.feature-arrow {
  font-size: 16px;
  color: #64748b;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  align-self: flex-end;
}

.feature-card:hover .feature-arrow {
  color: #409eff;
  transform: translateX(4px);
}

.feature-card:hover .feature-icon {
  color: #67c23a;
  transform: scale(1.05);
}

/* 关键指标区域 - 重新设计 */
.metrics-area {
  display: flex;
  flex-direction: column;
}

.metrics-grid {
  display: grid;
  grid-template-rows: repeat(4, 1fr);
  gap: 16px;
  height: 100%;
}

.metric-card {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 20px;
  text-align: left;
  border: 1px solid rgba(64, 158, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #409eff, #67c23a);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.metric-card:hover {
  transform: translateX(8px) scale(1.02);
  box-shadow: 0 16px 48px rgba(64, 158, 255, 0.3);
  background: linear-gradient(135deg, rgba(15, 23, 42, 1) 0%, rgba(30, 41, 59, 1) 100%);
  border-color: rgba(64, 158, 255, 0.6);
}

.metric-card:hover::before {
  opacity: 1;
}

.metric-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.metric-icon svg {
  width: 32px;
  height: 32px;
  transition: all 0.3s ease;
}

.metric-buses .metric-icon {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.2), rgba(64, 158, 255, 0.1));
  color: #409eff;
  border: 1px solid rgba(64, 158, 255, 0.3);
}

.metric-trips .metric-icon {
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.2), rgba(103, 194, 58, 0.1));
  color: #67c23a;
  border: 1px solid rgba(103, 194, 58, 0.3);
}

.metric-passengers .metric-icon {
  background: linear-gradient(135deg, rgba(230, 162, 60, 0.2), rgba(230, 162, 60, 0.1));
  color: #e6a23c;
  border: 1px solid rgba(230, 162, 60, 0.3);
}

.metric-delays .metric-icon {
  background: linear-gradient(135deg, rgba(245, 108, 108, 0.2), rgba(245, 108, 108, 0.1));
  color: #f56c6c;
  border: 1px solid rgba(245, 108, 108, 0.3);
}

.metric-card:hover .metric-icon {
  transform: rotate(-5deg) scale(1.1);
}

.metric-card:hover .metric-icon svg {
  transform: scale(1.1);
}

.metric-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-value {
  font-size: 36px;
  font-weight: 800;
  color: #f8fafc;
  margin-bottom: 4px;
  line-height: 1;
  letter-spacing: -1.5px;
  transition: all 0.3s ease;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.metric-card:hover .metric-value {
  transform: scale(1.05);
  color: #ffffff;
}

.metric-label {
  font-size: 13px;
  color: #94a3b8;
  margin-bottom: 8px;
  font-weight: 500;
  letter-spacing: 0.3px;
  opacity: 0.9;
}

.metric-trend {
  font-size: 13px;
  font-weight: 600;
  padding: 5px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.metric-trend::before {
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
}

.trend-up {
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.2), rgba(103, 194, 58, 0.3));
  color: #67c23a;
  border: 1px solid rgba(103, 194, 58, 0.4);
}

.trend-up::before {
  border-width: 0 4px 6px 4px;
  border-color: transparent transparent #67c23a transparent;
}

.trend-down {
  background: linear-gradient(135deg, rgba(245, 108, 108, 0.2), rgba(245, 108, 108, 0.3));
  color: #f56c6c;
  border: 1px solid rgba(245, 108, 108, 0.4);
}

.trend-down::before {
  border-width: 6px 4px 0 4px;
  border-color: #f56c6c transparent transparent transparent;
}

.metric-card:hover .metric-trend {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .welcome-section {
    grid-template-columns: 1fr;
    height: 140px;
    text-align: center;
  }

  .hero-illustration {
    display: none;
  }

  .content-grid {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
  }

  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .metrics-grid {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: 1fr;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 20px 20px;
  }

  .welcome-title {
    font-size: 28px;
  }

  .welcome-subtitle {
    font-size: 14px;
  }

  .content-grid {
    gap: 15px;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 12px;
  }

  .metric-card {
    padding: 16px;
  }

  .metric-icon {
    width: 48px;
    height: 48px;
  }

  .metric-icon svg {
    width: 28px;
    height: 28px;
  }

  .metric-value {
    font-size: 32px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 10px 15px;
  }

  .welcome-section {
    height: 160px;
  }

  .welcome-title {
    font-size: 24px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .feature-card {
    padding: 16px 12px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    gap: 10px;
  }

  .metric-card {
    padding: 14px;
  }

  .metric-icon {
    width: 44px;
    height: 44px;
  }

  .metric-icon svg {
    width: 24px;
    height: 24px;
  }

  .metric-value {
    font-size: 28px;
  }

  .metric-label {
    font-size: 12px;
  }
}

/* 科技感装饰元素 */
.data-streams {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.stream {
  position: absolute;
  width: 2px;
  height: 100px;
  background: linear-gradient(180deg, transparent 0%, rgba(64, 158, 255, 0.6) 50%, transparent 100%);
  animation: streamFlow 8s linear infinite;
}

.stream-1 {
  left: 20%;
  animation-delay: 0s;
}

.stream-2 {
  left: 60%;
  animation-delay: 3s;
}

.stream-3 {
  right: 25%;
  animation-delay: 6s;
}

@keyframes streamFlow {
  0% {
    transform: translateY(-100px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(calc(100vh + 100px));
    opacity: 0;
  }
}

.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(64, 158, 255, 0.4);
  border-radius: 50%;
  animation: particleFloat 12s ease-in-out infinite;
}

.particle-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.particle-2 {
  top: 40%;
  right: 15%;
  animation-delay: 3s;
}

.particle-3 {
  bottom: 30%;
  left: 25%;
  animation-delay: 6s;
}

.particle-4 {
  top: 60%;
  right: 30%;
  animation-delay: 9s;
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.4;
  }
  25% {
    transform: translateY(-20px) scale(1.2);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) scale(0.8);
    opacity: 0.6;
  }
  75% {
    transform: translateY(-30px) scale(1.1);
    opacity: 0.7;
  }
}
</style>
