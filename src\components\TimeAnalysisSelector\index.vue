<template>
  <div class="time-analysis-selector">
    <el-form :model="formParams" size="default" :inline="true">
      <el-form-item>
        <slot name="front"></slot>
      </el-form-item>
      <el-form-item label="分析方式" prop="analysisType">
        <el-select
          v-model="formParams.analysisType"
          placeholder="分析方式"
          clearable
          style="width: 120px"
          @change="handleAnalysisTypeChange"
        >
          <el-option
            v-for="option in analysisTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围" prop="dateRange" v-if="formParams.analysisType">
        <!-- 年度分析时间选择器(两个单独的年份选择器) -->
        <div v-if="formParams.analysisType === '0'" class="year-range-container">
          <el-date-picker
            v-model="yearRange.startYear"
            type="year"
            placeholder="开始年份"
            format="YYYY"
            value-format="YYYY"
            style="width: 140px"
            @change="handleYearRangeChange"
          />
          <span class="year-range-separator">至</span>
          <el-date-picker
            v-model="yearRange.endYear"
            type="year"
            placeholder="结束年份"
            format="YYYY"
            value-format="YYYY"
            style="width: 140px"
            :disabled-date="endYearDisabledDate"
            @change="handleYearRangeChange"
          />
        </div>

        <!-- 月度分析时间选择器(范围，最多12个月) -->
        <el-date-picker
          v-else-if="formParams.analysisType === '2'"
          v-model="dateRange"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          format="YYYY-MM"
          value-format="YYYY-MM"
          :disabled-date="monthDisabledDate"
          @change="handleDateRangeChange"
          style="width: 280px"
        />

        <!-- 日分析时间选择器(范围，最多30天) -->
        <el-date-picker
          v-else-if="formParams.analysisType === '3'"
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :disabled-date="dayDisabledDate"
          @change="handleDateRangeChange"
          style="width: 280px"
        />

        <!-- 小时分析时间选择器(范围，最多24小时) -->
        <el-date-picker
          v-else-if="formParams.analysisType === '4'"
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :disabled-date="hourDisabledDate"
          @change="handleDateRangeChange"
          style="width: 380px"
        />
      </el-form-item>
      <slot name="actions"></slot>
    </el-form>
  </div>
</template>

<script setup name="TimeAnalysisSelector">
import { ref, reactive, onMounted, defineEmits, defineExpose, defineProps } from 'vue'

// Props
const props = defineProps({
  // 初始分析类型
  initialAnalysisType: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['analysis-type-change', 'params-change'])

// 响应式数据
const analysisTypeOptions = ref([
  { label: '年度', value: '0' },
  { label: '月度', value: '2' },
  { label: '日', value: '3' },
  { label: '小时', value: '4' }
])

const formParams = reactive({
  analysisType: props.initialAnalysisType,
  startTime: undefined,
  endTime: undefined,
})

// 起始和结束时间范围
const dateRange = ref([])

// 年范围选择
const yearRange = reactive({
  startYear: '',
  endYear: '',
})

// 选择器状态
const pickerMinDate = ref('')

// 禁用日期函数
const endYearDisabledDate = (time) => {
  // 如果设置了开始年份，则只能选择大于等于开始年份且不超过开始年份+2年的年份（最多3年跨度）
  if (yearRange.startYear) {
    const startYear = parseInt(yearRange.startYear)
    const currentYear = time.getFullYear()
    return currentYear < startYear || currentYear > startYear + 2
  }
  return false
}

const monthDisabledDate = (time) => {
  if (pickerMinDate.value) {
    const startDate = new Date(pickerMinDate.value)
    const endDate = new Date(time)
    // 计算月份差
    const months = (endDate.getFullYear() - startDate.getFullYear()) * 12 +
                  endDate.getMonth() - startDate.getMonth()
    const isAfter = time.getTime() > pickerMinDate.value
    const maxMonths = 12 // 最多选择12个月
    const tooFar = months > maxMonths
    if (isAfter && tooFar) {
      return true
    }
  }
  return false
}

const dayDisabledDate = (time) => {
  if (pickerMinDate.value) {
    // 计算天数差
    const dayDiff = Math.ceil((time.getTime() - pickerMinDate.value) / (1000 * 60 * 60 * 24))
    const isAfter = time.getTime() > pickerMinDate.value
    const maxDays = 30 // 最多选择30天
    const tooFar = dayDiff > maxDays
    if (isAfter && tooFar) {
      return true
    }
  }
  return false
}

const hourDisabledDate = (time) => {
  if (pickerMinDate.value) {
    // 计算小时差
    const hourDiff = Math.ceil((time.getTime() - pickerMinDate.value) / (1000 * 60 * 60))
    const isAfter = time.getTime() > pickerMinDate.value
    const maxHours = 24 // 最多选择24小时
    const tooFar = hourDiff > maxHours
    if (isAfter && tooFar) {
      return true
    }
  }
  return false
}

// 方法定义
// 处理分析类型变更
function handleAnalysisTypeChange(val) {
  // 切换分析类型时重置时间范围
  dateRange.value = []
  yearRange.startYear = ''
  yearRange.endYear = ''
  // 重置日期选择器状态
  pickerMinDate.value = ''

  // 设置默认的时间范围
  if (val) {
    const now = new Date()
    switch (val) {
      case '0': // 年度
        // 默认选择当前年份和前一年
        const lastYear = now.getFullYear() - 1
        const currentYear = now.getFullYear()
        yearRange.startYear = lastYear.toString()
        yearRange.endYear = currentYear.toString()
        dateRange.value = [lastYear.toString(), currentYear.toString()]
        break
      case '2': // 月度
        // 默认选择当前月份和前一个月
        const currentMonth = formatDate(now, 'YYYY-MM')
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        dateRange.value = [formatDate(lastMonth, 'YYYY-MM'), currentMonth]
        break
      case '3': // 日
        // 设置为最近7天
        const weekAgoDay = new Date(now)
        weekAgoDay.setDate(now.getDate() - 6) // 当前日期算一天，所以减6
        dateRange.value = [
          formatDate(weekAgoDay, 'YYYY-MM-DD'),
          formatDate(now, 'YYYY-MM-DD')
        ]
        break
      case '4': // 小时
        // 设置为当天的24小时
        const dayStart = new Date(now)
        dayStart.setHours(0, 0, 0, 0)
        const dayEnd = new Date(now)
        dayEnd.setHours(23, 59, 59, 999)
        dateRange.value = [
          formatDate(dayStart, 'YYYY-MM-DD HH:mm:ss'),
          formatDate(dayEnd, 'YYYY-MM-DD HH:mm:ss')
        ]
        break
    }
  }

  // 更新查询参数
  updateTimeParams()

  // 触发事件通知父组件
  emit('analysis-type-change', val)
  emit('params-change', {
    analysisType: formParams.analysisType,
    startTime: formParams.startTime,
    endTime: formParams.endTime
  })
}

// 处理年份范围变更
function handleYearRangeChange() {
  if (yearRange.startYear && yearRange.endYear) {
    // 确保结束年份不小于开始年份
    const startYear = parseInt(yearRange.startYear)
    const endYear = parseInt(yearRange.endYear)

    if (endYear < startYear) {
      yearRange.endYear = yearRange.startYear
    }

    // 确保年份范围不超过3年
    if (endYear - startYear > 2) {
      yearRange.endYear = (startYear + 2).toString()
    }

    // 更新dateRange，用于后续处理
    dateRange.value = [yearRange.startYear, yearRange.endYear]

    // 更新查询参数
    updateTimeParams()
  }
}

// 处理日期范围变更
function handleDateRangeChange(val) {
  if (val && val.length === 2) {
    pickerMinDate.value = new Date(val[0]).getTime()
  }
  // 更新查询参数
  updateTimeParams()
}

// 更新时间查询参数
function updateTimeParams() {
  // 根据不同分析类型和选择的日期范围，设置startTime和endTime
  const analysisType = formParams.analysisType
  if (!analysisType || !dateRange.value) {
    // 触发事件通知父组件
    emit('params-change', {
      analysisType: formParams.analysisType,
      startTime: undefined,
      endTime: undefined
    })
    return
  }

  switch (analysisType) {
    case '0': // 年度
      if (Array.isArray(dateRange.value) && dateRange.value.length === 2) {
        formParams.startTime = `${dateRange.value[0]}-01-01 00:00:00`
        formParams.endTime = `${dateRange.value[1]}-12-31 23:59:59`
      }
      break
    case '2': // 月度
      if (Array.isArray(dateRange.value) && dateRange.value.length === 2) {
        // 获取开始月份的第一天
        formParams.startTime = `${dateRange.value[0]}-01 00:00:00`

        // 获取结束月份的最后一天
        const [endYear, endMonth] = dateRange.value[1].split('-')
        const lastDay = new Date(endYear, endMonth, 0).getDate() // 获取月份的最后一天
        formParams.endTime = `${dateRange.value[1]}-${lastDay.toString().padStart(2, '0')} 23:59:59`
      }
      break
    case '3': // 日
      if (Array.isArray(dateRange.value) && dateRange.value.length === 2) {
        formParams.startTime = `${dateRange.value[0]} 00:00:00`
        formParams.endTime = `${dateRange.value[1]} 23:59:59`
      }
      break
    case '4': // 小时
      if (Array.isArray(dateRange.value) && dateRange.value.length === 2) {
        formParams.startTime = dateRange.value[0]
        formParams.endTime = dateRange.value[1]
      }
      break
  }

  // 触发事件通知父组件
  emit('params-change', {
    analysisType: formParams.analysisType,
    startTime: formParams.startTime,
    endTime: formParams.endTime
  })
}

// 日期格式化工具
function formatDate(date, format) {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

// 重置方法，供父组件调用
function reset() {
  formParams.analysisType = ''
  dateRange.value = []
  yearRange.startYear = ''
  yearRange.endYear = ''
  formParams.startTime = undefined
  formParams.endTime = undefined
  pickerMinDate.value = ''

  // 触发事件通知父组件
  emit('params-change', {
    analysisType: formParams.analysisType,
    startTime: formParams.startTime,
    endTime: formParams.endTime
  })
}

// 获取当前参数方法，供父组件调用
function getParams() {
  return {
    analysisType: formParams.analysisType,
    startTime: formParams.startTime,
    endTime: formParams.endTime
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 如果有初始类型，自动设置默认值
  if (formParams.analysisType) {
    handleAnalysisTypeChange(formParams.analysisType)
  }
})

// 暴露给父组件的方法
defineExpose({
  reset,
  getParams
})
</script>

<style scoped>
.time-analysis-selector {
  margin-bottom: 0;
}

:deep(.el-form) {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.year-range-container {
  display: flex;
  align-items: center;
}

.year-range-separator {
  margin: 0 10px;
  color: #606266;
  font-size: 14px;
}

/* Element Plus 组件样式适配 */
:deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 16px;
}

:deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}

:deep(.el-select) {
  min-width: 120px;
}

:deep(.el-date-editor) {
  --el-date-editor-width: auto;
}

:deep(.el-date-editor .el-input__inner) {
  padding: 0 12px;
}

/* 暗色主题适配 */
.time-analysis-selector.dark {
  :deep(.el-form-item__label) {
    color: #e5e7eb;
  }

  .year-range-separator {
    color: #9ca3af;
  }
}
</style>
