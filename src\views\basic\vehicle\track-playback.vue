<template>
  <div class="track-playback-fullscreen">
    <!-- 顶部内容头部 -->
    <div class="content-header">
      <div class="header-left">
        <div class="title-section">
          <el-icon class="title-icon"><Van /></el-icon>
          <div class="title-text">
            <h2>{{ vehicleInfo.plateNumber }} - 轨迹回放</h2>
            <span>{{ vehicleInfo.vehicleNumber }} - {{ getVehicleTypeText(vehicleInfo.vehicleType) }}</span>
          </div>
        </div>
      </div>

      <div class="header-center">
        <div class="control-section">
          <el-date-picker
            v-model="selectedDate"
            type="date"
            placeholder="选择日期"
            size="default"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
          <el-time-picker
            v-model="selectedTimeRange"
            is-range
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="HH:mm"
            value-format="HH:mm"
            size="default"
            @change="handleTimeRangeChange"
          />
          <el-button type="primary" icon="Search" @click="loadTrackData">加载轨迹</el-button>
        </div>
      </div>

      <div class="header-right">
        <el-button icon="Back" @click="goBack">返回</el-button>
      </div>
    </div>

    <!-- 主内容区域 -->
    <el-row :gutter="20">
      <!-- 左侧组织机构树 -->
      <el-col :span="5">
        <div class="left-panel">
          <div class="panel-header">
            <div class="header-left">
              <el-icon><OfficeBuilding /></el-icon>
              <span>组织机构管理</span>
            </div>
          </div>
          <div class="panel-content">
            <el-tree
              :data="deptTreeData"
              :props="{ children: 'children', label: 'label' }"
              :expand-on-click-node="false"
              :default-expand-all="true"
              @node-click="handleDeptSelect"
              node-key="id"
              class="dept-tree"
            >
              <template #default="{ node, data }">
                <div class="dept-node">
                  <el-icon class="dept-icon">
                    <OfficeBuilding v-if="data.type === 'company'" />
                    <Grid v-else-if="data.type === 'dept'" />
                    <User v-else-if="data.type === 'route'" />
                    <Van v-else />
                  </el-icon>
                  <span class="dept-label">{{ node.label }}</span>
                  <span class="vehicle-count" v-if="data.vehicleCount">({{ data.vehicleCount }})</span>
                  <span class="vehicle-status" v-if="data.type === 'vehicle'" :class="`status-${data.status}`">
                    {{ getStatusText(data.status) }}
                  </span>
                </div>
              </template>
            </el-tree>
          </div>
        </div>
      </el-col>

      <!-- 右侧轨迹回放区域 -->
      <el-col :span="19">
        <div class="track-content">
          <VehicleTrackPlayback
            v-if="vehicleInfo.vehicleId"
            :vehicle="vehicleInfo"
            ref="trackPlaybackRef"
          />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="TrackPlayback">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getCurrentInstance } from 'vue';
import { Van, OfficeBuilding, Grid, User } from '@element-plus/icons-vue';
import VehicleTrackPlayback from './VehicleTrackPlayback.vue';

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

// 车辆信息
const vehicleInfo = ref({
  vehicleId: '',
  plateNumber: '',
  vehicleNumber: '',
  vehicleType: ''
});

// 时间选择
const selectedDate = ref(new Date().toISOString().split('T')[0]);
const selectedTimeRange = ref(['08:00', '18:00']);

// 组件引用
const trackPlaybackRef = ref(null);

// 组织机构树数据
const deptTreeData = ref([]);
const selectedDeptName = ref('当前组织机构');

// 获取车辆类型文本
function getVehicleTypeText(type) {
  const typeMap = {
    '1': '公交车',
    '2': '出租车',
    '3': '货车',
    '4': '私家车',
    'bus': '公交车',
    'electric_bus': '电动公交',
    'hybrid_bus': '混合动力'
  };
  return typeMap[type] || '未知';
}

// 获取状态文本
function getStatusText(status) {
  const statusMap = {
    'online': '在线',
    'offline': '离线',
    'running': '行驶中',
    'stopped': '停车',
    'maintenance': '维修中'
  };
  return statusMap[status] || '未知';
}

// 生成模拟车辆数据
function generateVehicleData(deptId, count) {
  const vehicles = [];
  const statuses = ['online', 'offline', 'running', 'stopped'];
  const types = ['bus', 'electric_bus'];

  for (let i = 1; i <= count; i++) {
    vehicles.push({
      id: `${deptId}_${i}`,
      label: `赣A${String(Math.floor(Math.random() * 90000) + 10000)}`,
      type: 'vehicle',
      vehicleId: `${deptId}_${i}`,
      plateNumber: `赣A${String(Math.floor(Math.random() * 90000) + 10000)}`,
      vehicleNumber: `${deptId}-${String(i).padStart(3, '0')}`,
      vehicleType: types[Math.floor(Math.random() * types.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      deptId: deptId
    });
  }
  return vehicles;
}

// 初始化组织机构树数据，包含车辆节点
function initializeDeptTreeWithVehicles() {
  deptTreeData.value = [
    {
      id: 1,
      label: '南昌公交集团',
      type: 'company',
      vehicleCount: 156,
      children: [
        {
          id: 11,
          label: '第一运营部',
          type: 'dept',
          vehicleCount: 45,
          children: [
            {
              id: 111,
              label: '1路线',
              type: 'route',
              vehicleCount: 12,
              children: generateVehicleData(111, 8)
            },
            {
              id: 112,
              label: '2路线',
              type: 'route',
              vehicleCount: 15,
              children: generateVehicleData(112, 8)
            },
            {
              id: 113,
              label: '3路线',
              type: 'route',
              vehicleCount: 18,
              children: generateVehicleData(113, 8)
            }
          ]
        },
        {
          id: 12,
          label: '第二运营部',
          type: 'dept',
          vehicleCount: 52,
          children: [
            {
              id: 121,
              label: '5路线',
              type: 'route',
              vehicleCount: 20,
              children: generateVehicleData(121, 8)
            },
            {
              id: 122,
              label: '6路线',
              type: 'route',
              vehicleCount: 16,
              children: generateVehicleData(122, 8)
            },
            {
              id: 123,
              label: '8路线',
              type: 'route',
              vehicleCount: 16,
              children: generateVehicleData(123, 8)
            }
          ]
        },
        {
          id: 13,
          label: '第三运营部',
          type: 'dept',
          vehicleCount: 59,
          children: [
            {
              id: 131,
              label: '10路线',
              type: 'route',
              vehicleCount: 22,
              children: generateVehicleData(131, 8)
            },
            {
              id: 132,
              label: '12路线',
              type: 'route',
              vehicleCount: 19,
              children: generateVehicleData(132, 8)
            },
            {
              id: 133,
              label: '15路线',
              type: 'route',
              vehicleCount: 18,
              children: generateVehicleData(133, 8)
            }
          ]
        }
      ]
    }
  ];
}

// 处理组织机构选择
function handleDeptSelect(data) {
  console.log('选择节点:', data);
  if (data.type === 'vehicle') {
    // 如果是车辆节点，直接查看轨迹
    viewVehicleTrack(data);
  }
}

// 处理车辆选择
function handleVehicleSelect(vehicle) {
  console.log('选择车辆:', vehicle);
}

// 查看车辆轨迹
function viewVehicleTrack(vehicle) {
  // 更新当前车辆信息
  vehicleInfo.value = {
    vehicleId: vehicle.vehicleId,
    plateNumber: vehicle.plateNumber,
    vehicleNumber: vehicle.vehicleNumber,
    vehicleType: vehicle.vehicleType
  };

  // 重新加载轨迹数据
  if (trackPlaybackRef.value && trackPlaybackRef.value.loadTrackData) {
    trackPlaybackRef.value.loadTrackData();
  }
}

// 处理日期变化
function handleDateChange() {
  console.log('日期变化:', selectedDate.value);
}

// 处理时间范围变化
function handleTimeRangeChange() {
  console.log('时间范围变化:', selectedTimeRange.value);
}

// 加载轨迹数据
function loadTrackData() {
  if (trackPlaybackRef.value && trackPlaybackRef.value.loadTrackData) {
    trackPlaybackRef.value.loadTrackData();
  }
}

// 返回车辆管理页面
function goBack() {
  router.push('/basic/vehicle');
}

// 页面初始化
onMounted(() => {
  // 初始化组织机构树数据
  initializeDeptTreeWithVehicles();

  // 从路由参数获取车辆信息
  vehicleInfo.value = {
    vehicleId: route.query.vehicleId || '',
    plateNumber: route.query.plateNumber || '',
    vehicleNumber: route.query.vehicleNumber || '',
    vehicleType: route.query.vehicleType || ''
  };

  console.log('轨迹回放页面初始化，车辆信息:', vehicleInfo.value);
});
</script>

<style scoped>
/* 全屏布局基础样式 - 科技感深色主题 */
.track-playback-fullscreen {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100%;
}

/* 内容头部样式 */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.header-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 24px;
  color: #60a5fa;
}

.title-text h2 {
  margin: 0;
  color: #f8fafc;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
  text-shadow: 0 0 10px rgba(96, 165, 250, 0.3);
}

.title-text span {
  color: #94a3b8;
  font-size: 14px;
  font-weight: 500;
}

.control-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

/* 左侧面板样式 */
.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  height: calc(100vh - 160px);
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
  flex-shrink: 0;
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 12px;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

/* 组织机构树样式 */
.dept-tree {
  background: transparent;
}

:deep(.el-tree) {
  background: transparent;
  color: #e5e7eb;
}

:deep(.el-tree-node__content) {
  background: transparent;
  border-radius: 8px;
  margin-bottom: 4px;
  padding: 8px 12px;
  transition: all 0.3s ease;
}

:deep(.el-tree-node__content:hover) {
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
  transform: translateX(4px);
}

:deep(.el-tree-node__expand-icon) {
  color: #94a3b8;
}

:deep(.el-tree-node__expand-icon.expanded) {
  color: #60a5fa;
}

.dept-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.dept-icon {
  font-size: 16px;
  color: #60a5fa;
  transition: all 0.3s ease;
}

.dept-label {
  flex: 1;
  color: #e5e7eb;
  font-size: 14px;
  font-weight: 500;
}

.vehicle-count {
  font-size: 12px;
  color: #94a3b8;
  background: rgba(30, 41, 59, 0.6);
  padding: 2px 8px;
  border-radius: 10px;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

/* 车辆状态样式 */
.vehicle-status {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  margin-left: 8px;
}

.status-online {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.status-offline {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.status-running {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.status-stopped {
  background: rgba(168, 85, 247, 0.2);
  color: #a855f7;
}

.status-maintenance {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

/* 轨迹内容区域 */
.track-content {
  background: transparent;
  min-height: calc(100vh - 160px);
}

/* 按钮样式自定义 */
:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

:deep(.el-button--primary:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

:deep(.el-button--default) {
  background: rgba(71, 85, 105, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.3);
  color: #f1f5f9;
}

:deep(.el-button--default:hover) {
  background: rgba(71, 85, 105, 1);
  border-color: rgba(148, 163, 184, 0.5);
  transform: translateY(-1px);
}

/* 日期时间选择器样式 */
:deep(.el-date-editor),
:deep(.el-input) {
  background: rgba(30, 41, 59, 0.8) !important;
  border: 1px solid rgba(147, 197, 253, 0.3) !important;
  border-radius: 8px !important;
}

:deep(.el-date-editor:hover),
:deep(.el-input:hover) {
  border-color: rgba(59, 130, 246, 0.6) !important;
}

:deep(.el-input__wrapper) {
  background: rgba(30, 41, 59, 0.8) !important;
  border: none !important;
  box-shadow: none !important;
}

:deep(.el-input__inner) {
  color: #f8fafc !important;
  background: transparent !important;
}

:deep(.el-date-editor .el-input__inner) {
  color: #f8fafc !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-left {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .control-section {
    flex-wrap: wrap;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .track-playback-fullscreen {
    padding: 10px;
  }

  .content-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .control-section {
    width: 100%;
    flex-direction: column;
  }

  .control-section .el-date-picker,
  .control-section .el-time-picker {
    width: 100%;
  }
}
</style>
