<!-- 节假日差异化模式参数配置组件 -->
<template>
  <div class="holiday-config">
    <el-form ref="formRef" :model="config" label-width="120px" size="large">
      <div class="config-section">
        <h4 class="section-title">
          <el-icon><Clock /></el-icon>
          基本设置
        </h4>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="模板名称" required>
              <el-input v-model="config.templateName" placeholder="请输入模板名称" maxlength="50" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="config-section">
        <h4 class="section-title">
          <el-icon><Calendar /></el-icon>
          日期类型配置
        </h4>

        <el-tabs v-model="activeTab" type="card">
          <!-- 工作日配置 -->
          <el-tab-pane label="工作日" name="workday">
            <div class="day-type-config">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="首班车时间">
                    <el-time-picker v-model="config.workday.firstBusTime" placeholder="选择首班车时间" value-format="HH:mm" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="末班车时间">
                    <el-time-picker v-model="config.workday.lastBusTime" placeholder="选择末班车时间" value-format="HH:mm" style="width: 100%" />
                  </el-form-item>
                </el-col>
              </el-row>

              <div class="schedule-pattern">
                <h5>时段设置：</h5>
                <div v-for="(period, index) in config.workday.periods" :key="index" class="period-row">
                  <el-row :gutter="16">
                    <el-col :span="5">
                      <el-time-picker v-model="period.startTime" placeholder="开始时间" value-format="HH:mm" style="width: 100%" />
                    </el-col>
                    <el-col :span="5">
                      <el-time-picker v-model="period.endTime" placeholder="结束时间" value-format="HH:mm" style="width: 100%" />
                    </el-col>
                    <el-col :span="4">
                      <el-input-number v-model="period.interval" :min="3" :max="60" placeholder="间隔" style="width: 100%" />
                    </el-col>
                    <el-col :span="6">
                      <el-input v-model="period.description" placeholder="时段描述" style="width: 100%" />
                    </el-col>
                    <el-col :span="4">
                      <el-button type="danger" size="small" @click="removePeriod('workday', index)" v-if="config.workday.periods.length > 1">
                        删除
                      </el-button>
                    </el-col>
                  </el-row>
                </div>
                <el-button type="primary" size="small" @click="addPeriod('workday')">
                  <el-icon><CirclePlus /></el-icon>
                  添加时段
                </el-button>
              </div>
            </div>
          </el-tab-pane>

          <!-- 周末配置 -->
          <el-tab-pane label="周末" name="weekend">
            <div class="day-type-config">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="首班车时间">
                    <el-time-picker v-model="config.weekend.firstBusTime" placeholder="选择首班车时间" value-format="HH:mm" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="末班车时间">
                    <el-time-picker v-model="config.weekend.lastBusTime" placeholder="选择末班车时间" value-format="HH:mm" style="width: 100%" />
                  </el-form-item>
                </el-col>
              </el-row>

              <div class="schedule-pattern">
                <h5>时段设置：</h5>
                <div v-for="(period, index) in config.weekend.periods" :key="index" class="period-row">
                  <el-row :gutter="16">
                    <el-col :span="5">
                      <el-time-picker v-model="period.startTime" placeholder="开始时间" value-format="HH:mm" style="width: 100%" />
                    </el-col>
                    <el-col :span="5">
                      <el-time-picker v-model="period.endTime" placeholder="结束时间" value-format="HH:mm" style="width: 100%" />
                    </el-col>
                    <el-col :span="4">
                      <el-input-number v-model="period.interval" :min="3" :max="60" placeholder="间隔" style="width: 100%" />
                    </el-col>
                    <el-col :span="6">
                      <el-input v-model="period.description" placeholder="时段描述" style="width: 100%" />
                    </el-col>
                    <el-col :span="4">
                      <el-button type="danger" size="small" @click="removePeriod('weekend', index)" v-if="config.weekend.periods.length > 1">
                        删除
                      </el-button>
                    </el-col>
                  </el-row>
                </div>
                <el-button type="primary" size="small" @click="addPeriod('weekend')">
                  <el-icon><CirclePlus /></el-icon>
                  添加时段
                </el-button>
              </div>
            </div>
          </el-tab-pane>

          <!-- 节假日配置 -->
          <el-tab-pane label="节假日" name="holiday">
            <div class="day-type-config">
              <el-alert
                title="节假日模式说明"
                description="节假日通常客流较少，可以适当减少班次或延长发车间隔"
                type="info"
                show-icon
                :closable="false"
                style="margin-bottom: 20px"
              />

              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="运营模式">
                    <el-radio-group v-model="config.holiday.mode">
                      <el-radio value="normal">正常运营</el-radio>
                      <el-radio value="reduced">减班运营</el-radio>
                      <el-radio value="stopped">停运</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="8" v-if="config.holiday.mode === 'normal' || config.holiday.mode === 'reduced'">
                  <el-form-item label="首班车时间">
                    <el-time-picker v-model="config.holiday.firstBusTime" placeholder="选择首班车时间" value-format="HH:mm" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="8" v-if="config.holiday.mode === 'normal' || config.holiday.mode === 'reduced'">
                  <el-form-item label="末班车时间">
                    <el-time-picker v-model="config.holiday.lastBusTime" placeholder="选择末班车时间" value-format="HH:mm" style="width: 100%" />
                  </el-form-item>
                </el-col>
              </el-row>

              <div v-if="config.holiday.mode === 'reduced'" class="reduced-config">
                <el-form-item label="减班幅度">
                  <el-slider
                    v-model="config.holiday.reductionRate"
                    :min="10"
                    :max="70"
                    :step="10"
                    show-stops
                    show-input
                    :format-tooltip="formatReductionTooltip"
                  />
                  <div class="form-tip">相比工作日减少的班次百分比</div>
                </el-form-item>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <div class="preview-section">
        <h4 class="section-title">
          <el-icon><DataAnalysis /></el-icon>
          差异化对比
        </h4>
        <div class="comparison-stats">
          <div class="comparison-card">
            <h5>工作日</h5>
            <div class="stat-grid">
              <div class="stat-item">
                <span class="stat-value">{{ getTripsCount('workday') }}</span>
                <span class="stat-label">班次</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ getOperatingTime('workday') }}</span>
                <span class="stat-label">运营时长</span>
              </div>
            </div>
          </div>

          <div class="comparison-card">
            <h5>周末</h5>
            <div class="stat-grid">
              <div class="stat-item">
                <span class="stat-value">{{ getTripsCount('weekend') }}</span>
                <span class="stat-label">班次</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ getOperatingTime('weekend') }}</span>
                <span class="stat-label">运营时长</span>
              </div>
            </div>
          </div>

          <div class="comparison-card">
            <h5>节假日</h5>
            <div class="stat-grid">
              <div class="stat-item">
                <span class="stat-value">{{ getTripsCount('holiday') }}</span>
                <span class="stat-label">班次</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ getHolidayMode() }}</span>
                <span class="stat-label">运营模式</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="config-section">
        <h4 class="section-title">
          <el-icon><EditPen /></el-icon>
          其他设置
        </h4>
        <el-form-item label="模板描述">
          <el-input v-model="config.description" type="textarea" :rows="3" placeholder="请输入模板描述（可选）" maxlength="200" show-word-limit />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Clock, Calendar, CirclePlus, DataAnalysis, EditPen } from '@element-plus/icons-vue';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue']);

// Form reference
const formRef = ref();

const activeTab = ref('workday');

const config = ref({
  templateName: '',
  routeId: null,
  workday: {
    firstBusTime: '06:00',
    lastBusTime: '22:00',
    periods: [
      { startTime: '06:00', endTime: '09:00', interval: 8, description: '早高峰' },
      { startTime: '09:00', endTime: '17:00', interval: 15, description: '平峰' },
      { startTime: '17:00', endTime: '20:00', interval: 8, description: '晚高峰' },
      { startTime: '20:00', endTime: '22:00', interval: 20, description: '夜间' }
    ]
  },
  weekend: {
    firstBusTime: '07:00',
    lastBusTime: '21:00',
    periods: [
      { startTime: '07:00', endTime: '11:00', interval: 15, description: '上午' },
      { startTime: '11:00', endTime: '18:00', interval: 20, description: '下午' },
      { startTime: '18:00', endTime: '21:00', interval: 15, description: '晚间' }
    ]
  },
  holiday: {
    mode: 'reduced',
    firstBusTime: '08:00',
    lastBusTime: '20:00',
    reductionRate: 30
  },
  description: '',
  ...props.modelValue
});

const routeOptions = ref([
  { routeId: 1, routeName: '1路' },
  { routeId: 2, routeName: '2路' },
  { routeId: 3, routeName: '3路' },
  { routeId: 4, routeName: '4路' }
]);

// 添加时段
function addPeriod(dayType) {
  config.value[dayType].periods.push({
    startTime: '22:00',
    endTime: '23:00',
    interval: 30,
    description: '新时段'
  });
}

// 删除时段
function removePeriod(dayType, index) {
  config.value[dayType].periods.splice(index, 1);
}

// 计算班次数量
function getTripsCount(dayType) {
  const dayConfig = config.value[dayType];
  if (dayType === 'holiday' && dayConfig.mode === 'stopped') return 0;

  if (!dayConfig.periods || !dayConfig.periods.length) return '--';

  let total = 0;
  dayConfig.periods.forEach((period) => {
    if (period.startTime && period.endTime && period.interval) {
      const [startHour, startMin] = period.startTime.split(':').map(Number);
      const [endHour, endMin] = period.endTime.split(':').map(Number);
      const duration = endHour * 60 + endMin - (startHour * 60 + startMin);
      if (duration > 0) {
        total += Math.floor(duration / period.interval);
      }
    }
  });

  if (dayType === 'holiday' && dayConfig.mode === 'reduced') {
    total = Math.floor(total * (1 - dayConfig.reductionRate / 100));
  }

  return total || '--';
}

// 计算运营时长
function getOperatingTime(dayType) {
  const dayConfig = config.value[dayType];
  if (dayType === 'holiday' && dayConfig.mode === 'stopped') return '停运';

  if (!dayConfig.firstBusTime || !dayConfig.lastBusTime) return '--';

  const [firstHour, firstMin] = dayConfig.firstBusTime.split(':').map(Number);
  const [lastHour, lastMin] = dayConfig.lastBusTime.split(':').map(Number);
  const totalMinutes = lastHour * 60 + lastMin - (firstHour * 60 + firstMin);

  if (totalMinutes <= 0) return '--';

  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  return `${hours}小时${minutes}分钟`;
}

// 获取节假日模式
function getHolidayMode() {
  const modeMap = {
    'normal': '正常运营',
    'reduced': '减班运营',
    'stopped': '停运'
  };
  return modeMap[config.value.holiday.mode] || '未知';
}

// 格式化减班幅度提示
function formatReductionTooltip(val) {
  return `减少${val}%`;
}

// 监听配置变化
watch(
  config,
  (newValue) => {
    emit('update:modelValue', newValue);
  },
  { deep: true }
);
</script>

<style scoped>
.holiday-config {
  padding: 20px 0;
}

.config-section {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.6) 0%, rgba(30, 41, 59, 0.4) 100%);
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
}

.day-type-config {
  padding: 0;
}

.schedule-pattern {
  margin-top: 20px;
}

.schedule-pattern h5 {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 14px;
}

.period-row {
  margin-bottom: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.reduced-config {
  margin-top: 20px;
  padding: 16px;
  background: #fef3c7;
  border-radius: 6px;
  border: 1px solid #f59e0b;
}

.form-tip {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.preview-section {
  background: transparent;
  color: #e2e8f0;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.preview-section .section-title {
  color: #e2e8f0;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
}

.comparison-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.comparison-card {
  background: rgba(15, 23, 42, 0.3);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.comparison-card:hover {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(15, 23, 42, 0.5);
}

.comparison-card h5 {
  margin: 0 0 12px 0;
  color: #e2e8f0;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
}

.stat-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
}

/* Element Plus 深色主题样式覆盖 */
:deep(.el-input .el-input__count .el-input__count-inner) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #94a3b8 !important;
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-form-item__label) {
  color: #e2e8f0 !important;
}

:deep(.el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  box-shadow: none !important;
}

:deep(.el-input__inner) {
  color: #e2e8f0 !important;
}

:deep(.el-input__inner::placeholder) {
  color: #94a3b8 !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

:deep(.el-time-picker .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

:deep(.el-input-number .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  background: rgba(30, 41, 59, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e2e8f0 !important;
}

:deep(.el-textarea__inner) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e2e8f0 !important;
}
</style>
