<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>司机分配管理</span>
          <el-button @click="$router.go(-1)">返回</el-button>
        </div>
      </template>

      <div class="allocation-content">
        <el-alert
          title="司机分配管理页面"
          description="此页面用于管理线路司机分配，支持司机的添加、移除和排班管理。"
          type="info"
          :closable="false"
          style="margin-bottom: 20px"
        />

        <el-empty description="司机分配管理功能开发中" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="DriverAllocation"></script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.allocation-content {
  padding: 20px;
}
</style>
