@use './variables.module.scss' as *;

/* ===================
   侧边栏布局样式 (从 sidebar.scss)
=================== */

#app {
  .main-container {
    height: 100%;
    transition: margin-left 0.28s;
    margin-left: $base-sidebar-width;
    position: relative;
  }

  .sidebarHide {
    margin-left: 0 !important;
  }

  .sidebar-container {
    -webkit-transition: width 0.28s;
    transition: width 0.28s;
    width: $base-sidebar-width !important;
    background-color: $base-menu-background;
    height: 100%;
    position: fixed;
    font-size: 0;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    -webkit-box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.1);

    // reset element-ui css
    .horizontal-collapse-transition {
      transition:
        0s width ease-in-out,
        0s padding-left ease-in-out,
        0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 20px;
    }

    // 优化二级菜单图标和文字间距
    .el-sub-menu .el-menu-item {
      .svg-icon {
        margin-right: 24px;
        font-size: 16px;
      }

      span {
        font-size: 14px;
        line-height: 1.4;
      }
    }

    // 优化一级菜单图标和文字间距
    .el-sub-menu__title {
      .svg-icon {
        margin-right: 20px;
        font-size: 18px;
      }

      span {
        font-size: 15px;
        font-weight: 500;
      }
    }

    // menu hover
    .theme-dark .sub-menu-title-noDropdown,
    .theme-dark .el-sub-menu__title {
      &:hover {
        background-color: $base-sub-menu-title-hover !important;
      }
    }
    .sub-menu-title-noDropdown,
    .el-sub-menu__title {
      &:hover {
        background-color: rgba(0, 0, 0, 0.05) !important;
      }
    }

    & .theme-dark .is-active > .el-sub-menu__title {
      color: $base-menu-color-active !important;
    }

    & .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      min-width: $base-sidebar-width !important;
      &:hover {
        background-color: rgba(0, 0, 0, 0.1) !important;
      }
    }

    & .theme-dark .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .theme-dark .el-sub-menu .el-menu-item {
      background-color: $base-sub-menu-background !important;

      &:hover {
        background-color: $base-sub-menu-hover !important;
      }
    }

    & .theme-dark .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .theme-dark .el-menu-item {
      &:hover {
        // you can use $sub-menuHover
        background-color: $base-menu-hover !important;
      }
    }
    & .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .el-menu-item {
      &:hover {
        // you can use $sub-menuHover
        background-color: rgba(0, 0, 0, 0.04) !important;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .main-container {
      margin-left: 54px;
    }

    .sub-menu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    .el-sub-menu {
      overflow: hidden;

      & > .el-sub-menu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    .el-menu--collapse {
      .el-sub-menu {
        & > .el-sub-menu__title {
          & > span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
          & > i {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: $base-sidebar-width !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform 0.28s;
      width: $base-sidebar-width !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$base-sidebar-width, 0, 0);
      }
    }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

/* ===================
   滚动条样式 (从 scrollbar.scss)
=================== */

// 导入 OverlayScrollbars 基础样式
@import 'overlayscrollbars/overlayscrollbars.css';

// 浅色主题滚动条样式
.os-theme-light {
  --os-size: 8px;
  --os-padding-perpendicular: 2px;
  --os-padding-axis: 2px;
  --os-track-border-radius: 4px;
  --os-track-bg: transparent;
  --os-track-bg-hover: rgba(0, 0, 0, 0.05);
  --os-track-bg-active: rgba(0, 0, 0, 0.08);
  --os-track-border: transparent;
  --os-handle-border-radius: 4px;
  --os-handle-bg: rgba(0, 0, 0, 0.2);
  --os-handle-bg-hover: rgba(0, 0, 0, 0.35);
  --os-handle-bg-active: rgba(0, 0, 0, 0.5);
  --os-handle-border: transparent;
  --os-handle-min-size: 30px;
  --os-handle-max-size: none;
  --os-handle-perpendicular-size: 100%;
  --os-handle-perpendicular-size-hover: 100%;
  --os-handle-interactive-area-offset: 0px;
}

// 深色主题滚动条样式
.os-theme-dark {
  --os-size: 8px;
  --os-padding-perpendicular: 2px;
  --os-padding-axis: 2px;
  --os-track-border-radius: 4px;
  --os-track-bg: transparent;
  --os-track-bg-hover: rgba(255, 255, 255, 0.08);
  --os-track-bg-active: rgba(255, 255, 255, 0.12);
  --os-track-border: transparent;
  --os-handle-border-radius: 4px;
  --os-handle-bg: rgba(255, 255, 255, 0.3);
  --os-handle-bg-hover: rgba(255, 255, 255, 0.45);
  --os-handle-bg-active: rgba(255, 255, 255, 0.6);
  --os-handle-border: transparent;
  --os-handle-min-size: 30px;
  --os-handle-max-size: none;
  --os-handle-perpendicular-size: 100%;
  --os-handle-perpendicular-size-hover: 100%;
  --os-handle-interactive-area-offset: 0px;
}

// 针对 .content-area 的特殊样式
.content-area {
  // 确保 OverlayScrollbars 正确应用
  &.os-host {
    .os-viewport {
      // 保持内容区域的原有样式
      padding: inherit;
    }

    // 滚动条轨道样式优化
    .os-scrollbar-vertical {
      right: 2px;
      width: 8px;

      .os-scrollbar-track {
        background: transparent;
        border-radius: 4px;

        &:hover {
          background: var(--os-track-bg-hover);
        }
      }

      .os-scrollbar-handle {
        border-radius: 4px;
        min-height: 30px;
        background: var(--os-handle-bg);
        transition: all 0.2s ease;

        &:hover {
          background: var(--os-handle-bg-hover);
        }

        &:active {
          background: var(--os-handle-bg-active);
        }
      }
    }

    // 水平滚动条（如果需要）
    .os-scrollbar-horizontal {
      bottom: 2px;
      height: 8px;

      .os-scrollbar-track {
        background: transparent;
        border-radius: 4px;

        &:hover {
          background: var(--os-track-bg-hover);
        }
      }

      .os-scrollbar-handle {
        border-radius: 4px;
        min-width: 30px;
        background: var(--os-handle-bg);
        transition: all 0.2s ease;

        &:hover {
          background: var(--os-handle-bg-hover);
        }

        &:active {
          background: var(--os-handle-bg-active);
        }
      }
    }
  }
}

// 全局滚动条样式覆盖（作为备用方案）
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: background 0.2s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.35);
  }

  &:active {
    background: rgba(0, 0, 0, 0.5);
  }
}

// 深色模式下的 webkit 滚动条
html.dark {
  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);

    &:hover {
      background: rgba(255, 255, 255, 0.45);
    }

    &:active {
      background: rgba(255, 255, 255, 0.6);
    }
  }
}

// Firefox 滚动条样式
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

html.dark * {
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}
