<template>
  <el-config-provider :locale="appStore.locale" :size="appStore.size">
    <router-view />
  </el-config-provider>
</template>

<script setup lang="ts">
import { useSettingsStore } from '@/store/modules/settings';
import { handleThemeStyle } from '@/utils/theme';
import { useAppStore } from '@/store/modules/app';
import { updateScrollbarTheme } from '@/utils/scrollbar';

const appStore = useAppStore();
const settingsStore = useSettingsStore();

// 监听主题变化，同步更新滚动条样式
watch(
  () => settingsStore.theme,
  (newTheme) => {
    handleThemeStyle(newTheme);
    // 延迟更新滚动条主题，确保DOM已更新
    nextTick(() => {
      updateScrollbarTheme();
    });
  }
);

// 监听深色模式变化
watch(
  () => document.documentElement.classList.contains('dark'),
  () => {
    nextTick(() => {
      updateScrollbarTheme();
    });
  }
);

onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(settingsStore.theme);
    // 初始化滚动条主题
    updateScrollbarTheme();
  });
});
</script>
