/* ===================
   功能模块样式合并
=================== */

/* ===================
   全局弹窗样式 (从 global-dialog.scss)
=================== */

/* 全局弹窗样式 - 科技感设计 */
.tech-dialog-overlay {
  background: rgba(0, 0, 0, 0.85) !important;
  backdrop-filter: blur(8px) !important;
}

/* 弹窗标题样式 */
.dialog-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 20px;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid rgba(59, 130, 246, 0.3);

  .title-icon {
    margin-right: 12px;
    font-size: 22px;
    color: #3b82f6;
  }

  .title-text {
    flex: 1;
    letter-spacing: 0.5px;
  }

  .title-extra {
    font-size: 14px;
    color: #94a3b8;
    font-weight: 400;
  }
}

/* 弹窗内容区域 */
.dialog-content {
  padding: 20px 0;

  .dialog-content-section {
    margin-bottom: 24px;
    padding: 16px;
    background: rgba(51, 65, 85, 0.3);
    border-radius: 8px;
    border: 1px solid rgba(59, 130, 246, 0.2);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(51, 65, 85, 0.4);
      border-color: rgba(59, 130, 246, 0.3);
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(59, 130, 246, 0.1);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #e2e8f0;
    margin-bottom: 12px;
    display: flex;
    align-items: center;

    .section-icon {
      margin-right: 8px;
      font-size: 18px;
      color: #60a5fa;
    }
  }
}

/* 弹窗详情项 */
.dialog-detail-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: rgba(30, 41, 59, 0.4);
  border-radius: 6px;
  border: 1px solid rgba(71, 85, 105, 0.3);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(30, 41, 59, 0.6);
    border-color: rgba(59, 130, 246, 0.3);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  }

  &:last-child {
    margin-bottom: 0;
  }

  .detail-label {
    min-width: 100px;
    color: #94a3b8;
    font-weight: 500;
    font-size: 14px;
    margin-right: 16px;
    flex-shrink: 0;
  }

  .detail-value {
    color: #e2e8f0;
    font-size: 14px;
    flex: 1;

    &.highlight {
      color: #60a5fa;
      font-weight: 600;
    }

    &.success {
      color: #34d399;
    }

    &.warning {
      color: #fbbf24;
    }

    &.danger {
      color: #f87171;
    }
  }

  .detail-icon {
    margin-left: 8px;
    font-size: 16px;
    color: #60a5fa;
  }
}

/* 弹窗操作按钮区域 */
.dialog-actions {
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid rgba(71, 85, 105, 0.3);
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .action-button {
    padding: 10px 24px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid transparent;

    &.primary {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: #ffffff;
      border-color: #3b82f6;

      &:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }
    }

    &.secondary {
      background: rgba(71, 85, 105, 0.4);
      color: #e2e8f0;
      border-color: rgba(71, 85, 105, 0.6);

      &:hover {
        background: rgba(71, 85, 105, 0.6);
        border-color: rgba(96, 165, 250, 0.4);
      }
    }
  }
}

/* ===================
   车辆监控性能优化 (从 vehicle-monitor-performance.scss)
=================== */

/* 高性能模式 - 禁用所有动画和视觉效果 */
.vehicle-monitor-fullscreen.performance-mode {
  /* 强制GPU加速 */
  will-change: auto;
  transform: translateZ(0);

  /* 简化背景系统 */
  .background-system {
    /* 只保留基础渐变，移除所有动画 */
    .bg-gradient-base {
      animation: none !important;
      background: linear-gradient(135deg, #0a0e1a 0%, #0f172a 25%, #1e293b 75%, #0f172a 100%) !important;
    }

    /* 完全隐藏性能消耗大的层级 */
    .bg-glow-layer,
    .bg-grid-layer,
    .bg-particles,
    .scan-line {
      display: none !important;
    }
  }

  /* 简化统计卡片动画 */
  .stat-card {
    transition: none !important;

    &::before {
      display: none !important;
    }

    &:hover {
      transform: none !important;
      box-shadow: 0 2px 8px rgba(96, 165, 250, 0.1) !important;
    }
  }

  /* 简化面板动画 */
  .left-route-panel {
    transition: width 0.2s ease !important;

    &::before {
      display: none !important;
    }
  }

  /* 性能控制器样式 */
  .performance-control {
    margin-right: 12px;
  }

  /* 使用CSS containment提高渲染性能 */
  .route-detail-block {
    contain: layout style paint !important;
  }

  .track-wrapper {
    contain: layout style !important;
  }

  .stations-layer,
  .stations-info-layer,
  .vehicles-layer {
    contain: layout style paint !important;
  }

  /* 减少重绘次数 */
  .vehicle-marker,
  .station-dot,
  .station-name {
    will-change: auto !important;
  }
}
