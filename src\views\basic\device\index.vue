<template>
  <div class='device-management-fullscreen'>
    <el-row :gutter='20'>
      <!-- 组织机构树 -->
      <el-col :lg='4' :xs='24'>
        <div class='left-panel'>
          <div class='panel-header'>
            <div class='header-left'>
              <el-icon>
                <Monitor />
              </el-icon>
              <span>组织机构筛选</span>
            </div>
          </div>
          <div class='panel-content'>
            <div class='search-box'>
              <el-input v-model='deptName' placeholder='请输入组织机构名称' prefix-icon='Search' clearable />
            </div>
            <el-tree
              ref='deptTreeRef'
              class='dept-tree'
              node-key='id'
              :data='deptOptions'
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node='false'
              :filter-node-method='filterNode'
              highlight-current
              default-expand-all
              @node-click='handleNodeClick'
            />
          </div>
        </div>
      </el-col>
      <el-col :lg='20' :xs='24'>
        <div class='right-content'>
          <!-- 顶部工具栏 -->
          <header class='content-header'>
            <div class='header-left'>
              <div class='title-section'>
                <el-icon class='title-icon'>
                  <Monitor />
                </el-icon>
                <h1 class='page-title'>设备管理</h1>
              </div>
              <div class='stats-cards'>
                <div class='stat-card'>
                  <div class='stat-value'>{{ deviceList.length }}</div>
                  <div class='stat-label'>总设备</div>
                </div>
                <div class='stat-card online'>
                  <div class='stat-value'>{{ deviceList.filter(d => d.status === '1').length }}</div>
                  <div class='stat-label'>在线</div>
                </div>
                <div class='stat-card warning'>
                  <div class='stat-value'>{{ deviceList.filter(d => d.status === '2').length }}</div>
                  <div class='stat-label'>离线</div>
                </div>
                <div class='stat-card danger'>
                  <div class='stat-value'>{{ deviceList.filter(d => d.status === '0').length }}</div>
                  <div class='stat-label'>故障</div>
                </div>
              </div>
            </div>
          </header>

          <!-- 搜索表单 -->
          <div class='search-section' v-show='showSearch'>
            <el-form :model='queryParams' ref='queryFormRef' :inline='true' label-width='68px'>
              <el-form-item label='设备编号' prop='deviceNumber'>
                <el-input
                  v-model='queryParams.deviceNumber'
                  placeholder='请输入设备编号'
                  clearable
                  @keyup.enter='handleQuery'
                />
              </el-form-item>
              <el-form-item label='设备名称' prop='deviceName'>
                <el-input
                  v-model='queryParams.deviceName'
                  placeholder='请输入设备名称'
                  clearable
                  @keyup.enter='handleQuery'
                />
              </el-form-item>
              <el-form-item label='设备类型' prop='deviceType'>
                <el-select v-model='queryParams.deviceType' placeholder='请选择设备类型' clearable>
                  <el-option label='视频设备' value='video' />
                  <el-option label='GPS设备' value='gps' />
                </el-select>
              </el-form-item>
              <el-form-item label='状态' prop='status'>
                <el-select v-model='queryParams.status' placeholder='请选择状态' clearable>
                  <el-option label='在线' value='1' />
                  <el-option label='离线' value='2' />
                  <el-option label='故障' value='0' />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type='primary' icon='Search' @click='handleQuery'>搜索</el-button>
                <el-button icon='Refresh' @click='resetQuery'>重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 操作按钮区域 -->
          <div class='action-section'>
            <div class='action-buttons'>
              <el-button type='primary' plain icon='Plus' @click='handleAdd'>新增</el-button>
              <el-button type='success' plain icon='Edit' :disabled='single' @click='handleUpdate'>修改</el-button>
              <el-button type='danger' plain icon='Delete' :disabled='multiple' @click='handleDelete'>删除</el-button>
              <el-button type='success' plain icon='Refresh' @click='handleStatusSync'>状态同步</el-button>
            </div>
            <right-toolbar v-model:showSearch='showSearch' @queryTable='getList'></right-toolbar>
          </div>

          <!-- 设备卡片列表 -->
          <div class='device-cards-container' v-loading='loading'>
            <div class='cards-grid'>
              <div
                v-for='device in deviceList'
                :key='device.deviceId'
                class='device-card'
                :class="{ 'selected': selectedDevices.includes(device.deviceId) }"
                @click='toggleSelection(device)'
              >
                <!-- 卡片头部 -->
                <div class='card-header'>
                  <div class='device-info'>
                    <div class='device-name'>{{ device.deviceName }}</div>
                    <div class='device-number'>{{ device.deviceNumber }}</div>
                  </div>
                  <div class='status-badge'>
                    <el-tag
                      v-if="device.status === '1'"
                      type='success'
                      size='small'
                      effect='dark'
                    >在线
                    </el-tag>
                    <el-tag
                      v-else-if="device.status === '2'"
                      type='warning'
                      size='small'
                      effect='dark'
                    >离线
                    </el-tag>
                    <el-tag
                      v-else
                      type='danger'
                      size='small'
                      effect='dark'
                    >故障
                    </el-tag>
                  </div>
                </div>

                <!-- 卡片内容 -->
                <div class='card-content'>
                  <div class='device-image'>
                    <el-icon size='36' :color='getDeviceIconColor(device.deviceType)'>
                      <component :is='getDeviceIcon(device.deviceType)' />
                    </el-icon>
                  </div>

                  <div class='device-details'>
                    <div class='detail-item'>
                      <span class='label'>设备类型:</span>
                      <span class='value'>{{ getDeviceTypeText(device.deviceType) }}</span>
                    </div>
                    <div class='detail-item'>
                      <span class='label'>所属组织机构:</span>
                      <span class='value'>{{ device.deptName }}</span>
                    </div>
                    <div class='detail-item'>
                      <span class='label'>安装位置:</span>
                      <span class='value'>{{ device.installPosition }}</span>
                    </div>
                    <div class='detail-item'>
                      <span class='label'>最后在线:</span>
                      <span class='value'>{{ device.lastOnlineTime }}</span>
                    </div>
                  </div>
                </div>

                <!-- 卡片操作按钮 -->
                <div class='card-actions'>
                  <el-button
                    type='primary'
                    size='small'
                    icon='Edit'
                    @click.stop='handleUpdate(device)'
                  >修改
                  </el-button>
                  <el-button
                    type='info'
                    size='small'
                    icon='View'
                    @click.stop='handleDetail(device)'
                  >详情
                  </el-button>
                  <el-button
                    type='danger'
                    size='small'
                    icon='Delete'
                    @click.stop='handleDelete(device)'
                  >删除
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if='!loading && deviceList.length === 0' class='empty-state'>
              <el-empty description='暂无设备数据' />
            </div>
          </div>

          <pagination
            v-show='total>0'
            :total='total'
            v-model:page='queryParams.pageNum'
            v-model:limit='queryParams.pageSize'
            @pagination='getList'
          />
        </div>

        <!-- 添加或修改设备对话框 -->
        <el-dialog
          :title='title'
          v-model='open'
          width='1000px'
          append-to-body
        >
          <el-form ref='deviceFormRef' :model='form' :rules='rules' label-width='100px'>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='设备名称' prop='deviceName'>
                  <el-input v-model='form.deviceName' placeholder='请输入设备名称' />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='设备编号' prop='deviceNumber'>
                  <el-input v-model='form.deviceNumber' placeholder='请输入设备编号' />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='设备类型' prop='deviceType'>
                  <el-radio-group v-model='form.deviceType' @change='handleDeviceTypeChange'>
                    <el-radio value='video'>视频设备</el-radio>
                    <el-radio value='gps'>GPS设备</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='设备型号' prop='deviceModel'>
                  <el-input v-model='form.deviceModel' placeholder='请输入设备型号' />
                </el-form-item>
              </el-col>
            </el-row>
            <!-- 视频设备通道配置 -->
            <el-row v-if="form.deviceType === 'video'">
              <el-col :span='24'>
                <el-form-item label='通道配置' prop='channelConfig'>
                  <div class='channel-config-container'>
                    <div class='channel-header'>
                      <span>通道列表</span>
                      <el-button type='primary' size='small' icon='Plus' @click='addChannel'>添加通道</el-button>
                    </div>
                    <div class='channel-table' v-if='form.channelConfig && form.channelConfig.length > 0'>
                      <el-table
                        :data='form.channelConfig'
                        style='width: 100%'
                        size='small'
                        class='channel-config-table'
                      >
                        <el-table-column label='通道名称' >
                          <template #default='{ row, $index }'>
                            <el-input
                              v-model='row.name'
                              placeholder='请输入通道名称'
                              size='small'
                            />
                          </template>
                        </el-table-column>
                        <el-table-column label='通道编号' >
                          <template #default='{ row, $index }'>
                            <el-input
                              v-model='row.number'
                              placeholder='如：CH01'
                              size='small'
                            />
                          </template>
                        </el-table-column>
                        <el-table-column label='分辨率' >
                          <template #default='{ row, $index }'>
                            <el-select
                              v-model='row.resolution'
                              placeholder='选择分辨率'
                              size='small'
                              style='width: 100%'
                            >
                              <el-option label='1920x1080' value='1920x1080' />
                              <el-option label='1280x720' value='1280x720' />
                              <el-option label='640x480' value='640x480' />
                            </el-select>
                          </template>
                        </el-table-column>
                        <el-table-column label='状态' width='100' align='center'>
                          <template #default='{ row, $index }'>
                            <el-switch
                              v-model='row.status'
                              size='small'
                              active-color='#13ce66'
                              inactive-color='#ff4949'
                            />
                          </template>
                        </el-table-column>
                        <el-table-column label='操作' width='80' align='center'>
                          <template #default='{ row, $index }'>
                            <el-button
                              type='danger'
                              size='small'
                              icon='Delete'
                              @click='removeChannel($index)'
                              circle
                            />
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                    <div v-else class='no-channels'>
                      <el-empty description='暂无通道配置' :image-size='60' />
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='制造商' prop='manufacturer'>
                  <el-input v-model='form.manufacturer' placeholder='请输入制造商' />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='终端号' prop='serialNumber'>
                  <el-input v-model='form.serialNumber' placeholder='请输入终端号' />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='安装位置' prop='installPosition'>
                  <el-input v-model='form.installPosition' placeholder='请输入安装位置' />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='安装日期' prop='installDate'>
                  <el-date-picker
                    v-model='form.installDate'
                    type='date'
                    placeholder='选择安装日期'
                    value-format='YYYY-MM-DD'
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='所属组织机构' prop='deptId'>
                  <el-tree-select
                    v-model='form.deptId'
                    :data='deptOptions'
                    :props="{ label: 'label', children: 'children', value: 'id' }"
                    placeholder='请选择所属组织机构'
                    check-strictly
                    :render-after-expand='false'
                    style='width: 100%'
                  />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='IP地址' prop='ipAddress'>
                  <el-input v-model='form.ipAddress' placeholder='请输入IP地址' />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span='12'>
                <el-form-item label='端口号' prop='port'>
                  <el-input-number v-model='form.port' :min='1' :max='65535' placeholder='请输入端口号' />
                </el-form-item>
              </el-col>
              <el-col :span='12'>
                <el-form-item label='状态' prop='status'>
                  <el-select v-model='form.status' placeholder='请选择状态'>
                    <el-option label='在线' value='1' />
                    <el-option label='离线' value='2' />
                    <el-option label='故障' value='0' />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label='备注' prop='remark'>
              <el-input v-model='form.remark' type='textarea' placeholder='请输入内容' />
            </el-form-item>
          </el-form>
          <template #footer>
            <div class='dialog-footer'>
              <el-button type='primary' @click='submitForm'>确 定</el-button>
              <el-button @click='cancel'>取 消</el-button>
            </div>
          </template>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name='Device'>
//import { listDevice, getDevice, delDevice, addDevice, updateDevice } from '@/api/basic/device';
import { deptTreeSelect } from '@/api/system/user';
import { Monitor, VideoCameraFilled, Position, WarnTriangleFilled } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

const deviceList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

// 卡片选择相关
const selectedDevices = ref([]);

// 组织机构树相关数据
const deptName = ref('');
const deptOptions = ref([]);
const deptTreeRef = ref();

// 表单引用
const queryFormRef = ref();
const deviceFormRef = ref();

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 25,
    deviceNumber: null,
    deviceName: null,
    deviceType: null,
    status: null,
    deptId: null
  },
  rules: {
    deviceName: [
      { required: true, message: '设备名称不能为空', trigger: 'blur' }
    ],
    deviceNumber: [
      { required: true, message: '设备编号不能为空', trigger: 'blur' }
    ],
    deviceType: [
      { required: true, message: '设备类型不能为空', trigger: 'change' }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 组织机构树筛选 */
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选组织机构树 */
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

/** 查询组织机构下拉树结构 */
async function getTreeSelect() {
  try {
    const res = await deptTreeSelect();
    deptOptions.value = res.data;
  } catch (error) {
    console.error('获取组织机构树失败:', error);
    // 模拟组织机构数据
    deptOptions.value = [
      {
        id: 1,
        label: '总公司',
        children: [
          {
            id: 2,
            label: '运营部',
            children: [
              { id: 3, label: '第一车队' },
              { id: 4, label: '第二车队' }
            ]
          },
          {
            id: 5,
            label: '技术部',
            children: [
              { id: 6, label: '信息中心' },
              { id: 7, label: '监控中心' }
            ]
          }
        ]
      }
    ];
  }
}

/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = data.id;
  handleQuery();
}

/** 获取设备图标 */
function getDeviceIcon(deviceType) {
  const iconMap = {
    'gps': 'Position',
    'video': 'VideoCameraFilled'
  };
  return iconMap[deviceType] || 'Monitor';
}

/** 获取设备图标颜色 */
function getDeviceIconColor(deviceType) {
  const colorMap = {
    'gps': '#67C23A',
    'video': '#F56C6C'
  };
  return colorMap[deviceType] || '#409EFF';
}

/** 获取设备类型文本 */
function getDeviceTypeText(type) {
  const typeMap = {
    'gps': 'GPS设备',
    'video': '视频设备'
  };
  return typeMap[type] || type;
}

/** 查询设备列表 */
function getList() {
  loading.value = true;
  // 模拟数据
  const mockData = [
    {
      deviceId: 1,
      deviceName: 'GPS定位终端-001',
      deviceNumber: 'GPS001',
      deviceType: 'gps',
      deviceModel: 'GT06N',
      manufacturer: '华大科技',
      serialNumber: '**********',
      installPosition: '前挡风玻璃',
      installDate: '2023-01-15',
      deptId: 3,
      deptName: '第一车队',
      ipAddress: '*************',
      port: 8001,
      status: '1',
      lastOnlineTime: '2024-08-16 10:30',
      remark: '主GPS定位设备',
      channelConfig: []
    },
    {
      deviceId: 2,
      deviceName: '车载视频监控-002',
      deviceNumber: 'VIDEO002',
      deviceType: 'video',
      deviceModel: 'HD1080P',
      manufacturer: '海康威视',
      serialNumber: 'SN20230002',
      installPosition: '驾驶室内',
      installDate: '2023-02-20',
      deptId: 3,
      deptName: '第一车队',
      ipAddress: '*************',
      port: 8002,
      status: '1',
      lastOnlineTime: '2024-08-16 10:28',
      remark: '前后双摄像头',
      channelConfig: [
        { name: '前置摄像头', number: 'CH01', resolution: '1920x1080', status: true },
        { name: '后置摄像头', number: 'CH02', resolution: '1920x1080', status: true }
      ]
    },
    {
      deviceId: 3,
      deviceName: 'GPS定位终端-003',
      deviceNumber: 'GPS003',
      deviceType: 'gps',
      deviceModel: 'ST-300',
      manufacturer: '中移物联',
      serialNumber: 'SN20230003',
      installPosition: '仪表台',
      installDate: '2023-03-10',
      deptId: 4,
      deptName: '第二车队',
      ipAddress: '*************',
      port: 8003,
      status: '2',
      lastOnlineTime: '2024-08-16 08:15',
      remark: '综合定位终端',
      channelConfig: []
    },
    {
      deviceId: 4,
      deviceName: '多路视频监控-004',
      deviceNumber: 'VIDEO004',
      deviceType: 'video',
      deviceModel: 'IP-CAM200',
      manufacturer: '大华技术',
      serialNumber: 'SN20230004',
      installPosition: '车厢内',
      installDate: '2023-04-05',
      deptId: 3,
      deptName: '第一车队',
      ipAddress: '*************',
      port: 8004,
      status: '1',
      lastOnlineTime: '2024-08-16 10:25',
      remark: '乘客区域监控',
      channelConfig: [
        { name: '车头监控', number: 'CH01', resolution: '1920x1080', status: true },
        { name: '车尾监控', number: 'CH02', resolution: '1280x720', status: true },
        { name: '车门监控', number: 'CH03', resolution: '1280x720', status: true },
        { name: '车厢中部监控', number: 'CH04', resolution: '640x480', status: false }
      ]
    },
    {
      deviceId: 5,
      deviceName: 'GPS定位终端-005',
      deviceNumber: 'GPS005',
      deviceType: 'gps',
      deviceModel: 'GT08',
      manufacturer: '华大科技',
      serialNumber: 'SN20230005',
      installPosition: '车顶',
      installDate: '2023-05-12',
      deptId: 4,
      deptName: '第二车队',
      ipAddress: '*************',
      port: 8005,
      status: '1',
      lastOnlineTime: '2024-08-16 10:32',
      remark: '备用GPS设备',
      channelConfig: []
    },
    {
      deviceId: 6,
      deviceName: 'GPS定位终端-006',
      deviceNumber: 'GPS006',
      deviceType: 'gps',
      deviceModel: 'ST-400',
      manufacturer: '中移物联',
      serialNumber: 'SN20230006',
      installPosition: '驾驶室',
      installDate: '2023-06-18',
      deptId: 6,
      deptName: '信息中心',
      ipAddress: '*************',
      port: 8006,
      status: '0',
      lastOnlineTime: '2024-08-15 15:20',
      remark: '故障待修',
      channelConfig: []
    },
    {
      deviceId: 7,
      deviceName: '高清视频监控-007',
      deviceNumber: 'VIDEO007',
      deviceType: 'video',
      deviceModel: '4K-DVR',
      manufacturer: '海康威视',
      serialNumber: 'SN20230007',
      installPosition: '前挡风玻璃',
      installDate: '2023-07-22',
      deptId: 4,
      deptName: '第二车队',
      ipAddress: '*************',
      port: 8007,
      status: '1',
      lastOnlineTime: '2024-08-16 10:20',
      remark: '4K高清录制',
      channelConfig: [
        { name: '主摄像头', number: 'CH01', resolution: '1920x1080', status: true }
      ]
    },
    {
      deviceId: 8,
      deviceName: '智能视频监控-008',
      deviceNumber: 'VIDEO008',
      deviceType: 'video',
      deviceModel: 'PTZ-CAM',
      manufacturer: '大华技术',
      serialNumber: 'SN20230008',
      installPosition: '车门附近',
      installDate: '2023-08-30',
      deptId: 7,
      deptName: '监控中心',
      ipAddress: '*************',
      port: 8008,
      status: '1',
      lastOnlineTime: '2024-08-16 10:27',
      remark: '可调节监控',
      channelConfig: [
        { name: '左侧监控', number: 'CH01', resolution: '1920x1080', status: true },
        { name: '右侧监控', number: 'CH02', resolution: '1920x1080', status: true }
      ]
    },
    {
      deviceId: 9,
      deviceName: 'GPS定位终端-009',
      deviceNumber: 'GPS009',
      deviceType: 'gps',
      deviceModel: 'GT10',
      manufacturer: '中科创达',
      serialNumber: 'SN20230009',
      installPosition: '后备箱',
      installDate: '2023-09-15',
      deptId: 3,
      deptName: '第一车队',
      ipAddress: '*************',
      port: 8009,
      status: '2',
      lastOnlineTime: '2024-08-16 07:45',
      remark: '隐蔽安装',
      channelConfig: []
    },
    {
      deviceId: 10,
      deviceName: 'GPS定位终端-010',
      deviceNumber: 'GPS010',
      deviceType: 'gps',
      deviceModel: 'ST-500',
      manufacturer: '华为',
      serialNumber: 'SN20230010',
      installPosition: '中控台',
      installDate: '2023-10-08',
      deptId: 4,
      deptName: '第二车队',
      ipAddress: '*************',
      port: 8010,
      status: '1',
      lastOnlineTime: '2024-08-16 10:35',
      remark: '5G智能终端',
      channelConfig: []
    }
  ];

  setTimeout(() => {
    let filteredData = mockData;

    // 根据组织机构筛选
    if (queryParams.value.deptId) {
      filteredData = filteredData.filter(item => item.deptId === queryParams.value.deptId);
    }

    // 根据设备编号筛选
    if (queryParams.value.deviceNumber) {
      filteredData = filteredData.filter(item =>
        item.deviceNumber.includes(queryParams.value.deviceNumber)
      );
    }

    // 根据设备名称筛选
    if (queryParams.value.deviceName) {
      filteredData = filteredData.filter(item =>
        item.deviceName.includes(queryParams.value.deviceName)
      );
    }

    // 根据设备类型筛选
    if (queryParams.value.deviceType) {
      filteredData = filteredData.filter(item => item.deviceType === queryParams.value.deviceType);
    }

    // 根据状态筛选
    if (queryParams.value.status) {
      filteredData = filteredData.filter(item => item.status === queryParams.value.status);
    }

    deviceList.value = filteredData;
    total.value = filteredData.length;
    loading.value = false;
  }, 500);
}

// 其他方法实现...
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  queryFormRef.value?.resetFields();
  queryParams.value.pageNum = 1;
  queryParams.value.deptId = null;
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.deviceId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 卡片选择切换 */
function toggleSelection(device) {
  const index = selectedDevices.value.indexOf(device.deviceId);
  if (index > -1) {
    selectedDevices.value.splice(index, 1);
  } else {
    selectedDevices.value.push(device.deviceId);
  }

  // 更新选择状态
  ids.value = selectedDevices.value;
  single.value = selectedDevices.value.length !== 1;
  multiple.value = selectedDevices.value.length === 0;
}

function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加设备';
}

function handleUpdate(row) {
  reset();
  form.value = { ...row };
  open.value = true;
  title.value = '修改设备';
}

function handleDetail(row) {
  proxy.$router.push({
    path: '/basic/device/detail',
    query: { deviceId: row.deviceId }
  });
}

function handleDelete(row) {
  const deviceIds = row.deviceId || ids.value;
  proxy.$modal.confirm('是否确认删除设备编号为"' + deviceIds + '"的数据项？').then(function() {
    proxy.$modal.msgSuccess('删除成功');
    getList();
  }).catch(() => {
  });
}

function handleStatusSync() {
  proxy.$modal.msgSuccess('状态同步完成');
  getList();
}

function submitForm() {
  deviceFormRef.value?.validate(valid => {
    if (valid) {
      if (form.value.deviceId != null) {
        proxy.$modal.msgSuccess('修改成功');
      } else {
        proxy.$modal.msgSuccess('新增成功');
      }
      open.value = false;
      getList();
    }
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    deviceId: null,
    deviceName: null,
    deviceNumber: null,
    deviceType: null,
    deviceModel: null,
    manufacturer: null,
    serialNumber: null,
    installPosition: null,
    installDate: null,
    deptId: null,
    ipAddress: null,
    port: null,
    status: '1',
    remark: null,
    channelConfig: []
  };
  deviceFormRef.value?.resetFields();
  deviceFormRef.value?.clearValidate();
}

/** 设备类型变化处理 */
function handleDeviceTypeChange(value) {
  if (value === 'video') {
    // 如果切换到视频设备，初始化通道配置
    if (!form.value.channelConfig || form.value.channelConfig.length === 0) {
      form.value.channelConfig = [];
    }
  } else {
    // 如果切换到GPS设备，清除通道配置
    form.value.channelConfig = [];
  }
}

/** 添加通道 */
function addChannel() {
  if (!form.value.channelConfig) {
    form.value.channelConfig = [];
  }
  form.value.channelConfig.push({
    name: '',
    number: '',
    resolution: '1920x1080',
    status: true
  });
}

/** 移除通道 */
function removeChannel(index) {
  if (form.value.channelConfig && index >= 0 && index < form.value.channelConfig.length) {
    form.value.channelConfig.splice(index, 1);
  }
}

getList();
getTreeSelect();
</script>

<style scoped>
/* 全屏布局基础样式 - 科技感深色主题 */
.device-management-fullscreen {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 左侧面板样式 */
.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 24px;
}

.search-box {
  margin-bottom: 16px;
}

.dept-tree {
  background: transparent;
  color: #cbd5e1;
}

/* 右侧内容区域 */
.right-content {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  backdrop-filter: blur(10px);
}

/* 顶部工具栏 */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 24px;
  color: #60a5fa;
}

.page-title {
  font-size: 20px;
  font-weight: 700;
  color: #f8fafc;
  margin: 0;
}

.stats-cards {
  display: flex;
  gap: 12px;
}

.stat-card {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  padding: 12px 16px;
  text-align: center;
  min-width: 80px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
}

.stat-card.online {
  border-color: rgba(34, 197, 94, 0.4);
  background: rgba(34, 197, 94, 0.1);
}

.stat-card.warning {
  border-color: rgba(245, 158, 11, 0.4);
  background: rgba(245, 158, 11, 0.1);
}

.stat-card.danger {
  border-color: rgba(239, 68, 68, 0.4);
  background: rgba(239, 68, 68, 0.1);
}

.stat-value {
  font-size: 22px;
  font-weight: 700;
  color: #f8fafc;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #94a3b8;
  margin-top: 4px;
}

/* 搜索区域 */
.search-section {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 设备卡片容器 */
.device-cards-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  min-height: 400px;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

/* 设备卡片样式 - 科技感设计 */
.device-card {
  background: linear-gradient(145deg,
  rgba(30, 41, 59, 0.9) 0%,
  rgba(51, 65, 85, 0.8) 100%);
  border: 2px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3),
  inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.device-card:hover {
  transform: translateY(-4px);
  border-color: rgba(96, 165, 250, 0.4);
  box-shadow: 0 12px 40px rgba(96, 165, 250, 0.2),
  0 0 30px rgba(96, 165, 250, 0.1),
  inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.device-card.selected {
  border-color: #22c55e;
  background: linear-gradient(145deg,
  rgba(34, 197, 94, 0.15) 0%,
  rgba(30, 41, 59, 0.9) 50%,
  rgba(51, 65, 85, 0.8) 100%);
  box-shadow: 0 0 30px rgba(34, 197, 94, 0.3),
  0 8px 25px rgba(0, 0, 0, 0.3),
  inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 科技感装饰线 */
.device-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg,
  transparent 0%,
  rgba(96, 165, 250, 0.6) 50%,
  transparent 100%);
}

.device-card.selected::before {
  background: linear-gradient(90deg,
  transparent 0%,
  rgba(34, 197, 94, 0.8) 50%,
  transparent 100%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 16px;
  font-weight: bold;
  color: #f8fafc;
  margin-bottom: 4px;
  text-shadow: 0 0 10px rgba(96, 165, 250, 0.3);
}

.device-number {
  font-size: 12px;
  color: #94a3b8;
  font-weight: 500;
}

.status-badge {
  margin-left: 8px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.device-image {
  text-align: center;
  padding: 16px 0;
  background: linear-gradient(135deg,
  rgba(96, 165, 250, 0.1) 0%,
  rgba(59, 130, 246, 0.05) 100%);
  border-radius: 12px;
  border: 1px solid rgba(96, 165, 250, 0.2);
}

.device-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: linear-gradient(135deg,
  rgba(15, 23, 42, 0.8) 0%,
  rgba(30, 41, 59, 0.6) 100%);
  border-radius: 8px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  font-size: 13px;
}

.detail-item .label {
  color: #94a3b8;
  font-weight: 500;
  min-width: 80px;
}

.detail-item .value {
  color: #f8fafc;
  font-weight: 600;
  text-align: right;
  flex: 1;
  margin-left: 8px;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid rgba(147, 197, 253, 0.2);
}

.card-actions .el-button {
  flex: 1;
  font-size: 12px;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 600;
  border-width: 1px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 0;
  color: #94a3b8;
}

/* 通道配置样式 */
.channel-config-container {
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  padding: 16px;
  background: rgba(15, 23, 42, 0.5);
  width: 100%;
}

.channel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  width: 100%;
}

.channel-header span {
  font-weight: 600;
  color: #f8fafc;
  font-size: 14px;
}

.channel-table {
  width: 100%;
}

.channel-config-table {
  background: rgba(30, 41, 59, 0.6);
  border-radius: 6px;
  overflow: hidden;
}

.channel-config-table .el-table__header {
  background: rgba(51, 65, 85, 0.8);
}

.channel-config-table .el-table__header th {
  background: rgba(51, 65, 85, 0.8);
  color: #f8fafc;
  font-weight: 600;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
}

.channel-config-table .el-table__body tr {
  background: rgba(30, 41, 59, 0.6);
}

.channel-config-table .el-table__body tr:hover {
  background: rgba(51, 65, 85, 0.6);
}

.channel-config-table .el-table__body td {
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
  padding: 8px;
}

.channel-config-table .el-input__inner,
.channel-config-table .el-select .el-input__inner {
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(147, 197, 253, 0.2);
  color: #f8fafc;
}

.channel-config-table .el-input__inner:focus,
.channel-config-table .el-select .el-input__inner:focus {
  border-color: rgba(96, 165, 250, 0.4);
}

.no-channels {
  text-align: center;
  padding: 20px;
  color: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .device-management-fullscreen {
    padding: 10px;
  }

  .header-left {
    flex-direction: column;
    gap: 16px;
    width: 100%;
  }

  .stats-cards {
    width: 100%;
    justify-content: space-around;
  }

  .cards-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .channel-config-table {
    font-size: 12px;
  }

  .channel-config-table .el-table__header th {
    padding: 8px 4px;
    font-size: 12px;
  }

  .channel-config-table .el-table__body td {
    padding: 4px;
  }

  .channel-config-table .el-input,
  .channel-config-table .el-select {
    font-size: 12px;
  }
}
</style>
