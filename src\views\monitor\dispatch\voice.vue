<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>语音功能维护</span>
          <el-button @click="$router.go(-1)">返回</el-button>
        </div>
      </template>

      <div class="voice-content">
        <el-alert title="语音功能维护" description="维护常用的语音指令模板。" type="info" :closable="false" style="margin-bottom: 20px" />

        <el-empty description="语音功能维护开发中" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="DispatchVoice"></script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.voice-content {
  padding: 20px;
}
</style>
