<template>
  <div class='arrival-ontime-analysis'>
    <el-row :gutter='20'>
      <!-- 左侧线路和站点树 -->
      <el-col :lg='4' :xs='24'>
        <div class='left-panel'>
          <div class='panel-header'>
            <div class='header-left'>
              <el-icon>
                <Location />
              </el-icon>
              <span>线路站点</span>
            </div>
          </div>
          <div class='panel-content'>
            <div class='search-box'>
              <el-input v-model='deptName' placeholder='请输入线路或站点' prefix-icon='Search' clearable />
            </div>

            <el-tree
              ref='deptTreeRef'
              class='dept-tree'
              node-key='id'
              :data='deptOptions'
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node='false'
              :filter-node-method='filterNode'
              highlight-current
              default-expand-all
              @node-click='handleNodeClick'
            >
              <template #default="{ node, data }">
                <span class="tree-node">
                  <el-icon v-if="data.type === 'route'" class="route-icon">
                    <Connection />
                  </el-icon>
                  <el-icon v-else-if="data.type === 'station'" class="station-icon">
                    <Location />
                  </el-icon>
                  <span class="node-label" :class="data.type">{{ data.label }}</span>
                  <el-tag
                    v-if="data.type === 'station' && data.onTimeRate"
                    :type="getOnTimeRateType(data.onTimeRate)"
                    size="small"
                    style="margin-left: 8px;"
                  >
                    {{ data.onTimeRate }}%
                  </el-tag>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
      </el-col>

      <el-col :lg='20' :xs='24'>
        <div class='right-content'>
          <!-- 筛选条件区域 -->
          <div class='filter-section'>
            <el-card class='filter-card'>
              <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
                <TimeAnalysisSelector
                  ref="timeAnalysisSelectorRef"
                  :initialAnalysisType="queryParams.analysisType"
                  @analysis-type-change="handleAnalysisTypeChange"
                  @params-change="handleTimeAnalysisChange"
                />
                <el-form-item label="线路编号" prop="routeNumber">
                  <el-input
                    v-model="queryParams.routeNumber"
                    placeholder="请输入线路编号"
                    clearable
                    style="width: 120px"
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="站点名称" prop="stationName">
                  <el-input
                    v-model="queryParams.stationName"
                    placeholder="请输入站点名称"
                    clearable
                    style="width: 160px"
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="车牌号" prop="plateNumber">
                  <el-input
                    v-model="queryParams.plateNumber"
                    placeholder="请输入车牌号"
                    clearable
                    style="width: 160px"
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="准点状态" prop="onTimeStatus">
                  <el-select v-model="queryParams.onTimeStatus" placeholder="请选择状态" clearable style="width: 120px">
                    <el-option label="全部" value="" />
                    <el-option label="准点" value="ontime" />
                    <el-option label="早到" value="early" />
                    <el-option label="晚点" value="late" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
                  <el-button @click="resetQuery" icon="Refresh">重置</el-button>
                  <el-button type="success" plain @click="handleExport" icon="Download">导出</el-button>
                  <el-button type="primary" plain @click="handlePrint" icon="Printer">打印</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>

          <!-- Tab切换区域 -->
          <div class='tabs-section'>
            <!-- 时间范围显示 - 放在所有Tab外面 -->
            <div class="time-range-info" v-if="queryParams.startTime && queryParams.endTime">
              <el-alert
                :title="`统计时间范围：${getTimeRangeText()} (${getAnalysisTypeText(queryParams.analysisType)})`"
                type="info"
                :closable="false"
                show-icon
                style="margin-bottom: 16px;"
              />
            </div>

            <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
              <!-- 线路站点稽查统计 -->
              <el-tab-pane label="线路站点稽查统计" name="routeStationSummary">
                <div class="tab-content">
                  <!-- 统计卡片区域 -->
                  <div class='stats-section'>
                    <el-row :gutter='16'>
                      <el-col :span='6'>
                        <div class='stat-card total'>
                          <div class='stat-icon'>
                            <el-icon><Connection /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.totalRoutes }}</div>
                            <div class='stat-label'>总线路数</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card stations'>
                          <div class='stat-icon'>
                            <el-icon><Location /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.totalStations }}</div>
                            <div class='stat-label'>总站点数</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card average'>
                          <div class='stat-icon'>
                            <el-icon><TrendCharts /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.avgOnTimeRate }}%</div>
                            <div class='stat-label'>平均准点率</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card arrivals'>
                          <div class='stat-icon'>
                            <el-icon><Operation /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.totalArrivals }}</div>
                            <div class='stat-label'>总到站次数</div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 图表区域 -->
                  <div class='charts-section'>
                    <el-row :gutter='16'>
                      <el-col :span='24'>
                        <div class='chart-card'>
                          <div class='chart-header'>
                            <h3>线路站点准点率对比图 - {{ getAnalysisTypeText(queryParams.analysisType) }}</h3>
                            <span v-if="queryParams.startTime && queryParams.endTime" class="chart-subtitle">
                              {{ getTimeRangeText() }}
                            </span>
                          </div>
                          <div ref="routeStationChartRef" class='chart-container'></div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 汇总表格 -->
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>线路站点稽查统计表</h4>
                      </div>
                    </div>
                    <el-table :data="routeStationList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="线路编号" align="center" prop="routeNumber" width="100" />
                      <el-table-column label="线路名称" align="center" prop="routeName" min-width="150" />
                      <el-table-column label="站点名称" align="center" prop="stationName" min-width="150" />
                      <el-table-column label="站点序号" align="center" prop="stationOrder" width="100" />
                      <el-table-column label="计划到站" align="center" prop="plannedArrivals" width="100" />
                      <el-table-column label="实际到站" align="center" prop="actualArrivals" width="100" />
                      <el-table-column label="准点到站" align="center" prop="onTimeArrivals" width="100">
                        <template #default="scope">
                          <span class="ontime-count">{{ scope.row.onTimeArrivals }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="准点率" align="center" prop="onTimeRate" width="100">
                        <template #default="scope">
                          <el-tag :type="getOnTimeRateType(scope.row.onTimeRate)" size="small">
                            {{ scope.row.onTimeRate }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="平均延误" align="center" prop="avgDelay" width="100">
                        <template #default="scope">
                          <span class="delay-time">{{ scope.row.avgDelay }}分钟</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="routeStationTotal > 0"
                      :total="routeStationTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getRouteStationList"
                    />
                  </div>
                </div>
              </el-tab-pane>

              <!-- 线路站点稽查日统计 -->
              <el-tab-pane label="线路站点稽查日统计" name="routeStationDaily">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>线路站点稽查日统计表</h4>
                      </div>
                    </div>
                    <el-table :data="routeStationDailyList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="统计日期" align="center" prop="statisticDate" width="120" />
                      <el-table-column label="线路编号" align="center" prop="routeNumber" width="100" />
                      <el-table-column label="线路名称" align="center" prop="routeName" min-width="120" />
                      <el-table-column label="站点名称" align="center" prop="stationName" min-width="150" />
                      <el-table-column label="计划到站" align="center" prop="plannedArrivals" width="100" />
                      <el-table-column label="实际到站" align="center" prop="actualArrivals" width="100" />
                      <el-table-column label="准点到站" align="center" prop="onTimeArrivals" width="100">
                        <template #default="scope">
                          <span class="ontime-count">{{ scope.row.onTimeArrivals }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="早到次数" align="center" prop="earlyArrivals" width="100">
                        <template #default="scope">
                          <span class="early-count">{{ scope.row.earlyArrivals }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="晚点次数" align="center" prop="lateArrivals" width="100">
                        <template #default="scope">
                          <span class="late-count">{{ scope.row.lateArrivals }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="准点率" align="center" prop="onTimeRate" width="100">
                        <template #default="scope">
                          <el-tag :type="getOnTimeRateType(scope.row.onTimeRate)" size="small">
                            {{ scope.row.onTimeRate }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="routeStationDailyTotal > 0"
                      :total="routeStationDailyTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getRouteStationDailyList"
                    />
                  </div>
                </div>
              </el-tab-pane>

              <!-- 车辆站点稽查统计 -->
              <el-tab-pane label="车辆站点稽查统计" name="vehicleStationSummary">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>车辆站点稽查统计表</h4>
                      </div>
                    </div>
                    <el-table :data="vehicleStationList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="车牌号" align="center" prop="plateNumber" width="120" />
                      <el-table-column label="车辆编号" align="center" prop="vehicleNumber" width="100" />
                      <el-table-column label="所属线路" align="center" prop="routeName" min-width="120" />
                      <el-table-column label="站点名称" align="center" prop="stationName" min-width="150" />
                      <el-table-column label="驾驶员" align="center" prop="driverName" min-width="100" />
                      <el-table-column label="计划到站" align="center" prop="plannedArrivals" width="100" />
                      <el-table-column label="实际到站" align="center" prop="actualArrivals" width="100" />
                      <el-table-column label="准点到站" align="center" prop="onTimeArrivals" width="100">
                        <template #default="scope">
                          <span class="ontime-count">{{ scope.row.onTimeArrivals }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="准点率" align="center" prop="onTimeRate" width="100">
                        <template #default="scope">
                          <el-tag :type="getOnTimeRateType(scope.row.onTimeRate)" size="small">
                            {{ scope.row.onTimeRate }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="平均延误" align="center" prop="avgDelay" width="100">
                        <template #default="scope">
                          <span class="delay-time">{{ scope.row.avgDelay }}分钟</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="vehicleStationTotal > 0"
                      :total="vehicleStationTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getVehicleStationList"
                    />
                  </div>
                </div>
              </el-tab-pane>

              <!-- 车辆站点稽查日统计 -->
              <el-tab-pane label="车辆站点稽查日统计" name="vehicleStationDaily">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>车辆站点稽查日统计表</h4>
                      </div>
                    </div>
                    <el-table :data="vehicleStationDailyList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="统计日期" align="center" prop="statisticDate" width="120" />
                      <el-table-column label="车牌号" align="center" prop="plateNumber" width="120" />
                      <el-table-column label="车辆编号" align="center" prop="vehicleNumber" width="100" />
                      <el-table-column label="所属线路" align="center" prop="routeName" min-width="120" />
                      <el-table-column label="站点名称" align="center" prop="stationName" min-width="150" />
                      <el-table-column label="驾驶员" align="center" prop="driverName" min-width="100" />
                      <el-table-column label="计划到站" align="center" prop="plannedArrivals" width="100" />
                      <el-table-column label="实际到站" align="center" prop="actualArrivals" width="100" />
                      <el-table-column label="准点到站" align="center" prop="onTimeArrivals" width="100">
                        <template #default="scope">
                          <span class="ontime-count">{{ scope.row.onTimeArrivals }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="准点率" align="center" prop="onTimeRate" width="100">
                        <template #default="scope">
                          <el-tag :type="getOnTimeRateType(scope.row.onTimeRate)" size="small">
                            {{ scope.row.onTimeRate }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="vehicleStationDailyTotal > 0"
                      :total="vehicleStationDailyTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getVehicleStationDailyList"
                    />
                  </div>
                </div>
              </el-tab-pane>

              <!-- 站点稽查统计 -->
              <el-tab-pane label="站点稽查统计" name="stationSummary">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>站点稽查统计表</h4>
                      </div>
                    </div>
                    <el-table :data="stationSummaryList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="站点名称" align="center" prop="stationName" min-width="150" />
                      <el-table-column label="站点编号" align="center" prop="stationCode" width="120" />
                      <el-table-column label="所属区域" align="center" prop="area" min-width="120" />
                      <el-table-column label="途径线路数" align="center" prop="routeCount" width="100" />
                      <el-table-column label="计划到站" align="center" prop="plannedArrivals" width="100" />
                      <el-table-column label="实际到站" align="center" prop="actualArrivals" width="100" />
                      <el-table-column label="准点到站" align="center" prop="onTimeArrivals" width="100">
                        <template #default="scope">
                          <span class="ontime-count">{{ scope.row.onTimeArrivals }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="准点率" align="center" prop="onTimeRate" width="100">
                        <template #default="scope">
                          <el-tag :type="getOnTimeRateType(scope.row.onTimeRate)" size="small">
                            {{ scope.row.onTimeRate }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="平均延误" align="center" prop="avgDelay" width="100">
                        <template #default="scope">
                          <span class="delay-time">{{ scope.row.avgDelay }}分钟</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="stationSummaryTotal > 0"
                      :total="stationSummaryTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getStationSummaryList"
                    />
                  </div>
                </div>
              </el-tab-pane>

              <!-- 站点稽查明细 -->
              <el-tab-pane label="站点稽查明细" name="stationDetails">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>站点稽查明细表</h4>
                      </div>
                    </div>
                    <el-table :data="stationDetailsList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="到站日期" align="center" prop="arrivalDate" width="120" />
                      <el-table-column label="线路" align="center" prop="routeName" min-width="100" />
                      <el-table-column label="站点名称" align="center" prop="stationName" min-width="150" />
                      <el-table-column label="车牌号" align="center" prop="plateNumber" width="120" />
                      <el-table-column label="班次编号" align="center" prop="tripNumber" width="100" />
                      <el-table-column label="计划到站" align="center" prop="scheduledTime" width="120" />
                      <el-table-column label="实际到站" align="center" prop="actualTime" width="120" />
                      <el-table-column label="时差" align="center" prop="timeDifference" width="100">
                        <template #default="scope">
                          <span :class="getTimeDifferenceClass(scope.row.timeDifference)">
                            {{ scope.row.timeDifference }}分钟
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column label="状态" align="center" prop="status" width="100">
                        <template #default="scope">
                          <el-tag :type="getArrivalStatusType(scope.row.status)" size="small">
                            {{ getArrivalStatusText(scope.row.status) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="驾驶员" align="center" prop="driverName" min-width="100" />
                      <el-table-column label="备注" align="center" prop="remark" min-width="120" show-overflow-tooltip />
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleArrivalDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="stationDetailsTotal > 0"
                      :total="stationDetailsTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getStationDetailsList"
                    />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 详情弹窗 -->
    <el-dialog :title="dialogTitle" v-model="showDetailDialog" width="800px" append-to-body>
      <div v-if="detailData" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item v-if="detailData.routeName" label="线路名称">{{ detailData.routeName }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.routeNumber" label="线路编号">{{ detailData.routeNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.stationName" label="站点名称">{{ detailData.stationName }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.stationCode" label="站点编号">{{ detailData.stationCode }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.plateNumber" label="车牌号">{{ detailData.plateNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.vehicleNumber" label="车辆编号">{{ detailData.vehicleNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.driverName" label="驾驶员">{{ detailData.driverName }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.statisticDate" label="统计日期">{{ detailData.statisticDate }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.plannedArrivals" label="计划到站">{{ detailData.plannedArrivals }}次</el-descriptions-item>
          <el-descriptions-item v-if="detailData.actualArrivals" label="实际到站">{{ detailData.actualArrivals }}次</el-descriptions-item>
          <el-descriptions-item v-if="detailData.onTimeArrivals" label="准点到站">{{ detailData.onTimeArrivals }}次</el-descriptions-item>
          <el-descriptions-item v-if="detailData.onTimeRate" label="准点率">
            <el-tag :type="getOnTimeRateType(detailData.onTimeRate)" size="small">
              {{ detailData.onTimeRate }}%
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item v-if="detailData.avgDelay" label="平均延误">{{ detailData.avgDelay }}分钟</el-descriptions-item>
          <el-descriptions-item v-if="detailData.area" label="所属区域">{{ detailData.area }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.routeCount" label="途径线路数">{{ detailData.routeCount }}条</el-descriptions-item>
          <el-descriptions-item v-if="detailData.earlyArrivals" label="早到次数">{{ detailData.earlyArrivals }}次</el-descriptions-item>
          <el-descriptions-item v-if="detailData.lateArrivals" label="晚点次数">{{ detailData.lateArrivals }}次</el-descriptions-item>
          <el-descriptions-item label="备注" span="2">{{ detailData.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ArrivalOnTimeAnalysis">
import { ref, reactive, onMounted, nextTick, watchEffect, computed } from 'vue';
import { Location, Connection, TrendCharts, Operation } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import TimeAnalysisSelector from '@/components/TimeAnalysisSelector/index.vue';
import Pagination from '@/components/Pagination/index.vue';

// 定义组件
defineOptions({
  components: {
    TimeAnalysisSelector,
    Pagination
  }
});

const loading = ref(true);
const showDetailDialog = ref(false);
const detailData = ref(null);
const activeTab = ref('routeStationSummary');

// 树形相关
const deptName = ref('');
const deptTreeRef = ref();

// 图表引用
const routeStationChartRef = ref(null);
let routeStationChart = null;

// 表单引用
const queryRef = ref();
const timeAnalysisSelectorRef = ref();

// 分页数据
const routeStationTotal = ref(0);
const routeStationDailyTotal = ref(0);
const vehicleStationTotal = ref(0);
const vehicleStationDailyTotal = ref(0);
const stationSummaryTotal = ref(0);
const stationDetailsTotal = ref(0);

// 各个Tab的数据列表
const routeStationList = ref([]);
const routeStationDailyList = ref([]);
const vehicleStationList = ref([]);
const vehicleStationDailyList = ref([]);
const stationSummaryList = ref([]);
const stationDetailsList = ref([]);

// 统计数据
const summaryStats = ref({
  totalRoutes: '32',
  totalStations: '486',
  avgOnTimeRate: '89.3',
  totalArrivals: '24,680'
});

// 线路和站点树数据
const deptOptions = ref([]);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  analysisType: '3',
  startTime: null,
  endTime: null,
  dateRange: null,
  routeNumber: null,
  stationName: null,
  plateNumber: null,
  onTimeStatus: null,
  routeId: null,
  stationId: null
});

// 计算弹窗标题
const dialogTitle = computed(() => {
  if (!detailData.value) return '详情';
  if (detailData.value.stationName && !detailData.value.plateNumber) {
    return `${detailData.value.stationName} - 站点到站准点率详情`;
  }
  if (detailData.value.plateNumber && detailData.value.stationName) {
    return `${detailData.value.plateNumber} - ${detailData.value.stationName} 到站详情`;
  }
  return '到站准点率详情';
});

onMounted(() => {
  getTreeSelect();
  handleTabChange(activeTab.value);
  nextTick(() => {
    initCharts();
  });
});

/** 组织机构树筛选 */
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选组织机构树 */
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

/** 查询线路站点下拉树结构 */
async function getTreeSelect() {
  try {
    deptOptions.value = [
      {
        id: 'route_115',
        label: '115路',
        type: 'route',
        routeNumber: '115',
        children: [
          { id: 'station_1', label: '火车站', type: 'station', stationCode: 'S001', routeId: 'route_115', onTimeRate: 92.3 },
          { id: 'station_2', label: '市政府', type: 'station', stationCode: 'S002', routeId: 'route_115', onTimeRate: 88.7 },
          { id: 'station_3', label: '中心广场', type: 'station', stationCode: 'S003', routeId: 'route_115', onTimeRate: 94.1 },
          { id: 'station_4', label: '高新区', type: 'station', stationCode: 'S004', routeId: 'route_115', onTimeRate: 89.5 }
        ]
      },
      {
        id: 'route_135',
        label: '135路',
        type: 'route',
        routeNumber: '135',
        children: [
          { id: 'station_5', label: '汽车站', type: 'station', stationCode: 'S005', routeId: 'route_135', onTimeRate: 91.2 },
          { id: 'station_6', label: '商业街', type: 'station', stationCode: 'S006', routeId: 'route_135', onTimeRate: 87.8 },
          { id: 'station_7', label: '体育馆', type: 'station', stationCode: 'S007', routeId: 'route_135', onTimeRate: 93.6 },
          { id: 'station_8', label: '科技园', type: 'station', stationCode: 'S008', routeId: 'route_135', onTimeRate: 90.4 }
        ]
      },
      {
        id: 'route_201',
        label: '201路',
        type: 'route',
        routeNumber: '201',
        children: [
          { id: 'station_9', label: '机场', type: 'station', stationCode: 'S009', routeId: 'route_201', onTimeRate: 95.7 },
          { id: 'station_10', label: '大学城', type: 'station', stationCode: 'S010', routeId: 'route_201', onTimeRate: 92.1 },
          { id: 'station_11', label: '工业园', type: 'station', stationCode: 'S011', routeId: 'route_201', onTimeRate: 88.3 },
          { id: 'station_12', label: '医院', type: 'station', stationCode: 'S012', routeId: 'route_201', onTimeRate: 91.9 }
        ]
      },
      {
        id: 'route_301',
        label: '301路',
        type: 'route',
        routeNumber: '301',
        children: [
          { id: 'station_13', label: '东站', type: 'station', stationCode: 'S013', routeId: 'route_301', onTimeRate: 86.4 },
          { id: 'station_14', label: '购物中心', type: 'station', stationCode: 'S014', routeId: 'route_301', onTimeRate: 89.7 },
          { id: 'station_15', label: '住宅区', type: 'station', stationCode: 'S015', routeId: 'route_301', onTimeRate: 92.8 },
          { id: 'station_16', label: '北站', type: 'station', stationCode: 'S016', routeId: 'route_301', onTimeRate: 90.2 }
        ]
      }
    ];
  } catch (error) {
    console.error('获取线路站点树失败:', error);
  }
}

/** 节点单击事件 */
function handleNodeClick(data) {
  if (data.type === 'route') {
    queryParams.routeId = data.id;
    queryParams.routeNumber = data.routeNumber;
    queryParams.stationId = null;
    queryParams.stationName = null;
  } else if (data.type === 'station') {
    queryParams.stationId = data.id;
    queryParams.stationName = data.label;
    queryParams.routeId = null;
    queryParams.routeNumber = null;
  }
  handleQuery();
}

/** 处理时间分析类型变化 */
function handleAnalysisTypeChange(analysisType) {
  queryParams.analysisType = analysisType;
}

/** 处理时间分析参数变化 */
function handleTimeAnalysisChange(params) {
  queryParams.analysisType = params.analysisType;
  queryParams.startTime = params.startTime;
  queryParams.endTime = params.endTime;
  // 更新统计数据和图表
  updateStatsData();
}

/** 更新统计数据 */
function updateStatsData() {
  // 这里可以根据时间范围更新统计数据
  // 实际项目中应该调用API获取对应时间范围的数据
  handleTabChange(activeTab.value);
}

/** 获取时间范围文本 */
function getTimeRangeText() {
  if (!queryParams.startTime || !queryParams.endTime) {
    return '未设置时间范围';
  }

  const startDate = queryParams.startTime.split(' ')[0];
  const endDate = queryParams.endTime.split(' ')[0];

  switch(queryParams.analysisType) {
    case '0': // 年度
      const startYear = new Date(queryParams.startTime).getFullYear();
      const endYear = new Date(queryParams.endTime).getFullYear();
      return `${startYear}年 - ${endYear}年`;
    case '2': // 月度
      const startMonth = queryParams.startTime.split('-').slice(0, 2).join('-');
      const endMonth = queryParams.endTime.split('-').slice(0, 2).join('-');
      return `${startMonth} 至 ${endMonth}`;
    case '3': // 日
      return `${startDate} 至 ${endDate}`;
    case '4': // 小时
      return `${queryParams.startTime} 至 ${queryParams.endTime}`;
    default:
      return `${startDate} 至 ${endDate}`;
  }
}

/** 获取分析类型文本 */
function getAnalysisTypeText(analysisType) {
  const typeMap = {
    '0': '年度分析',
    '1': '周度分析',
    '2': '月度分析',
    '3': '日度分析',
    '4': '小时分析'
  };
  return typeMap[analysisType] || '未知分析';
}

/** Tab切换事件 */
function handleTabChange(tabName) {
  queryParams.pageNum = 1;
  activeTab.value = tabName;
  loading.value = true;

  switch (tabName) {
    case 'routeStationSummary':
      getRouteStationList();
      break;
    case 'routeStationDaily':
      getRouteStationDailyList();
      break;
    case 'vehicleStationSummary':
      getVehicleStationList();
      break;
    case 'vehicleStationDaily':
      getVehicleStationDailyList();
      break;
    case 'stationSummary':
      getStationSummaryList();
      break;
    case 'stationDetails':
      getStationDetailsList();
      break;
  }
}

// 获取线路站点稽查统计数据
function getRouteStationList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateRouteStationData();
    routeStationList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    routeStationTotal.value = mockData.length;
    loading.value = false;

    // 更新图表
    nextTick(() => {
      updateRouteStationChart();
    });
  }, 500);
}

// 获取线路站点稽查日统计数据
function getRouteStationDailyList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateRouteStationDailyData();
    routeStationDailyList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    routeStationDailyTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 获取车辆站点稽查统计数据
function getVehicleStationList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateVehicleStationData();
    vehicleStationList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    vehicleStationTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 获取车辆站点稽查日统计数据
function getVehicleStationDailyList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateVehicleStationDailyData();
    vehicleStationDailyList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    vehicleStationDailyTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 获取站点稽查统计数据
function getStationSummaryList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateStationSummaryData();
    stationSummaryList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    stationSummaryTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 获取站点稽查明细数据
function getStationDetailsList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateStationDetailsData();
    stationDetailsList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    stationDetailsTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 生成线路站点稽查统计模拟数据
function generateRouteStationData() {
  const routes = [
    { routeNumber: '115', routeName: '115路' },
    { routeNumber: '135', routeName: '135路' },
    { routeNumber: '201', routeName: '201路' },
    { routeNumber: '301', routeName: '301路' },
    { routeNumber: '202', routeName: '202路' }
  ];

  const stations = [
    '火车站', '市政府', '中心广场', '高新区', '汽车站', '商业街', '体育馆', '科技园',
    '机场', '大学城', '工业园', '医院', '东站', '购物中心', '住宅区', '北站'
  ];

  const data = [];
  let id = 1;

  routes.forEach(route => {
    const routeStations = stations.slice(0, Math.floor(Math.random() * 8) + 4);
    routeStations.forEach((stationName, index) => {
      const plannedArrivals = Math.floor(Math.random() * 100 + 200);
      const actualArrivals = Math.floor(plannedArrivals * (0.92 + Math.random() * 0.08));
      const onTimeArrivals = Math.floor(actualArrivals * (0.80 + Math.random() * 0.20));

      data.push({
        id: id++,
        ...route,
        stationName,
        stationOrder: index + 1,
        plannedArrivals,
        actualArrivals,
        onTimeArrivals,
        onTimeRate: ((onTimeArrivals / actualArrivals) * 100).toFixed(1),
        avgDelay: (Math.random() * 12 + 3).toFixed(1),
        remark: Math.random() > 0.7 ? '运行正常' : null
      });
    });
  });

  return data;
}

// 生成线路站点稽查日统计模拟数据
function generateRouteStationDailyData() {
  const data = [];
  const routes = ['115路', '135路', '201路', '301路'];
  const stations = ['火车站', '市政府', '中心广场', '高新区', '汽车站', '商业街'];

  for (let i = 0; i < 30; i++) {
    routes.forEach((routeName, routeIndex) => {
      stations.slice(0, 3).forEach((stationName, stationIndex) => {
        const baseDate = new Date();
        baseDate.setDate(baseDate.getDate() - i);

        const plannedArrivals = Math.floor(Math.random() * 50 + 150);
        const actualArrivals = Math.floor(plannedArrivals * (0.92 + Math.random() * 0.08));
        const onTimeArrivals = Math.floor(actualArrivals * (0.80 + Math.random() * 0.20));
        const earlyArrivals = Math.floor(actualArrivals * (0.05 + Math.random() * 0.05));
        const lateArrivals = actualArrivals - onTimeArrivals - earlyArrivals;

        data.push({
          id: i * routes.length * 3 + routeIndex * 3 + stationIndex + 1,
          statisticDate: baseDate.toISOString().split('T')[0],
          routeNumber: routeName.replace('路', ''),
          routeName,
          stationName,
          plannedArrivals,
          actualArrivals,
          onTimeArrivals,
          earlyArrivals: Math.max(0, earlyArrivals),
          lateArrivals: Math.max(0, lateArrivals),
          onTimeRate: ((onTimeArrivals / actualArrivals) * 100).toFixed(1),
          remark: Math.random() > 0.8 ? '交通拥堵影响' : null
        });
      });
    });
  }

  return data.sort((a, b) => new Date(b.statisticDate) - new Date(a.statisticDate));
}

// 生成车辆站点稽查统计模拟数据
function generateVehicleStationData() {
  const vehicles = [
    { plateNumber: '京A12345', vehicleNumber: 'V001', routeName: '115路', driverName: '张志明' },
    { plateNumber: '京A12346', vehicleNumber: 'V002', routeName: '135路', driverName: '李华强' },
    { plateNumber: '京A12347', vehicleNumber: 'V003', routeName: '201路', driverName: '王建国' },
    { plateNumber: '京A12348', vehicleNumber: 'V004', routeName: '301路', driverName: '陈美丽' },
    { plateNumber: '京A12349', vehicleNumber: 'V005', routeName: '115路', driverName: '刘德华' }
  ];

  const stations = ['火车站', '市政府', '中心广场', '高新区', '汽车站', '商业街'];

  const data = [];
  let id = 1;

  vehicles.forEach(vehicle => {
    stations.forEach(stationName => {
      const plannedArrivals = Math.floor(Math.random() * 30 + 50);
      const actualArrivals = Math.floor(plannedArrivals * (0.92 + Math.random() * 0.08));
      const onTimeArrivals = Math.floor(actualArrivals * (0.75 + Math.random() * 0.25));

      data.push({
        id: id++,
        ...vehicle,
        stationName,
        plannedArrivals,
        actualArrivals,
        onTimeArrivals,
        onTimeRate: ((onTimeArrivals / actualArrivals) * 100).toFixed(1),
        avgDelay: (Math.random() * 15 + 2).toFixed(1),
        remark: Math.random() > 0.7 ? '表现良好' : null
      });
    });
  });

  return data;
}

// 生成车辆站点稽查日统计模拟数据
function generateVehicleStationDailyData() {
  const data = [];
  const vehicles = [
    { plateNumber: '京A12345', vehicleNumber: 'V001', routeName: '115路', driverName: '张志明' },
    { plateNumber: '京A12346', vehicleNumber: 'V002', routeName: '135路', driverName: '李华强' },
    { plateNumber: '京A12347', vehicleNumber: 'V003', routeName: '201路', driverName: '王建国' }
  ];
  const stations = ['火车站', '市政府', '中心广场'];

  for (let i = 0; i < 15; i++) {
    vehicles.forEach((vehicle, vehicleIndex) => {
      stations.forEach((stationName, stationIndex) => {
        const baseDate = new Date();
        baseDate.setDate(baseDate.getDate() - i);

        const plannedArrivals = Math.floor(Math.random() * 15 + 25);
        const actualArrivals = Math.floor(plannedArrivals * (0.92 + Math.random() * 0.08));
        const onTimeArrivals = Math.floor(actualArrivals * (0.75 + Math.random() * 0.25));

        data.push({
          id: i * vehicles.length * stations.length + vehicleIndex * stations.length + stationIndex + 1,
          statisticDate: baseDate.toISOString().split('T')[0],
          ...vehicle,
          stationName,
          plannedArrivals,
          actualArrivals,
          onTimeArrivals,
          onTimeRate: ((onTimeArrivals / actualArrivals) * 100).toFixed(1),
          remark: Math.random() > 0.8 ? '正常运营' : null
        });
      });
    });
  }

  return data.sort((a, b) => new Date(b.statisticDate) - new Date(a.statisticDate));
}

// 生成站点稽查统计模拟数据
function generateStationSummaryData() {
  const stations = [
    { stationName: '火车站', stationCode: 'S001', area: '中心城区' },
    { stationName: '市政府', stationCode: 'S002', area: '政务区' },
    { stationName: '中心广场', stationCode: 'S003', area: '商业区' },
    { stationName: '高新区', stationCode: 'S004', area: '高新区' },
    { stationName: '汽车站', stationCode: 'S005', area: '交通枢纽' },
    { stationName: '商业街', stationCode: 'S006', area: '商业区' },
    { stationName: '体育馆', stationCode: 'S007', area: '体育区' },
    { stationName: '科技园', stationCode: 'S008', area: '高新区' }
  ];

  return stations.map((station, index) => {
    const routeCount = Math.floor(Math.random() * 8) + 2;
    const plannedArrivals = Math.floor(Math.random() * 500 + 800);
    const actualArrivals = Math.floor(plannedArrivals * (0.92 + Math.random() * 0.08));
    const onTimeArrivals = Math.floor(actualArrivals * (0.80 + Math.random() * 0.20));

    return {
      id: index + 1,
      ...station,
      routeCount,
      plannedArrivals,
      actualArrivals,
      onTimeArrivals,
      onTimeRate: ((onTimeArrivals / actualArrivals) * 100).toFixed(1),
      avgDelay: (Math.random() * 10 + 2).toFixed(1),
      remark: Math.random() > 0.7 ? '客流较大' : null
    };
  });
}

// 生成站点稽查明细模拟数据
function generateStationDetailsData() {
  const data = [];
  const routes = [
    { routeName: '115路' },
    { routeName: '135路' },
    { routeName: '201路' },
    { routeName: '301路' }
  ];
  const stations = ['火车站', '市政府', '中心广场', '高新区'];
  const vehicles = ['京A12345', '京A12346', '京A12347', '京A12348'];
  const drivers = ['张志明', '李华强', '王建国', '陈美丽'];

  for (let i = 0; i < 50; i++) {
    const routeIndex = Math.floor(Math.random() * routes.length);
    const stationIndex = Math.floor(Math.random() * stations.length);
    const vehicleIndex = Math.floor(Math.random() * vehicles.length);

    const baseDate = new Date();
    baseDate.setDate(baseDate.getDate() - Math.floor(i / 10));

    const scheduledHour = 6 + Math.floor(Math.random() * 14);
    const scheduledMinute = Math.floor(Math.random() * 60);

    const scheduledTime = `${scheduledHour.toString().padStart(2, '0')}:${scheduledMinute.toString().padStart(2, '0')}`;

    const timeDifference = Math.round((Math.random() - 0.6) * 20); // -12 to 8 minutes
    const actualMinute = scheduledMinute + timeDifference;
    const actualHour = scheduledHour + Math.floor(actualMinute / 60);
    const normalizedMinute = ((actualMinute % 60) + 60) % 60;

    const actualTime = `${actualHour.toString().padStart(2, '0')}:${normalizedMinute.toString().padStart(2, '0')}`;

    let status;
    if (timeDifference <= -3) status = 'early';
    else if (timeDifference >= 5) status = 'late';
    else status = 'ontime';

    data.push({
      id: i + 1,
      arrivalDate: baseDate.toISOString().split('T')[0],
      routeName: routes[routeIndex].routeName,
      stationName: stations[stationIndex],
      plateNumber: vehicles[vehicleIndex],
      tripNumber: `T${(2000 + i).toString()}`,
      scheduledTime,
      actualTime,
      timeDifference,
      status,
      driverName: drivers[vehicleIndex],
      remark: Math.random() > 0.8 ? (status === 'late' ? '交通拥堵' : '正常') : null
    });
  }

  return data.sort((a, b) => new Date(b.arrivalDate) - new Date(a.arrivalDate));
}

// 初始化图表
function initCharts() {
  initRouteStationChart();
}

// 初始化线路站点准点率对比图表
function initRouteStationChart() {
  if (!routeStationChartRef.value) return;

  routeStationChart = echarts.init(routeStationChartRef.value);
  updateRouteStationChart();
}

// 更新线路站点准点率对比图表
function updateRouteStationChart() {
  if (!routeStationChart) return;

  const stations = routeStationList.value.slice(0, 10); // 取前10条数据
  const stationNames = stations.map(s => s.stationName);
  const onTimeRates = stations.map(s => parseFloat(s.onTimeRate));
  const avgDelays = stations.map(s => parseFloat(s.avgDelay));

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' },
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' }
    },
    legend: {
      data: ['准点率', '平均延误'],
      textStyle: { color: '#e5e7eb' },
      top: '5%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: stationNames,
      axisLabel: { color: '#94a3b8', rotate: 30 },
      axisLine: { lineStyle: { color: '#374151' } }
    },
    yAxis: [
      {
        type: 'value',
        name: '准点率(%)',
        position: 'left',
        axisLabel: { color: '#94a3b8' },
        axisLine: { lineStyle: { color: '#374151' } },
        splitLine: { lineStyle: { color: '#374151' } }
      },
      {
        type: 'value',
        name: '平均延误(分钟)',
        position: 'right',
        axisLabel: { color: '#94a3b8' },
        axisLine: { lineStyle: { color: '#374151' } },
        splitLine: { show: false }
      }
    ],
    series: [
      {
        name: '准点率',
        type: 'bar',
        data: onTimeRates,
        itemStyle: { color: '#67C23A' },
        yAxisIndex: 0
      },
      {
        name: '平均延误',
        type: 'line',
        data: avgDelays,
        itemStyle: { color: '#E6A23C' },
        lineStyle: { color: '#E6A23C' },
        yAxisIndex: 1
      }
    ]
  };

  routeStationChart.setOption(option);
}

// 查询
function handleQuery() {
  queryParams.pageNum = 1;
  handleTabChange(activeTab.value);
}

// 重置
function resetQuery() {
  queryRef.value?.resetFields();
  queryParams.routeId = null;
  queryParams.stationId = null;
  queryParams.analysisType = '3';
  queryParams.startTime = null;
  queryParams.endTime = null;
  timeAnalysisSelectorRef.value?.reset();
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

// 表格选择
function handleSelectionChange(selection) {
  // 处理表格选择
}

// 查看详情
function handleDetail(row) {
  detailData.value = row;
  showDetailDialog.value = true;
}

// 查看到站详情
function handleArrivalDetail(row) {
  detailData.value = {
    ...row,
    routeName: row.routeName,
    stationName: row.stationName
  };
  showDetailDialog.value = true;
}

// 导出数据
function handleExport() {
  ElMessage.success('导出成功');
}

// 打印报表
function handlePrint() {
  window.print();
}

// 获取准点率类型
function getOnTimeRateType(rate) {
  const numRate = parseFloat(rate);
  if (numRate >= 95) return 'success';
  if (numRate >= 90) return '';
  if (numRate >= 80) return 'warning';
  return 'danger';
}

// 获取时差样式类
function getTimeDifferenceClass(timeDiff) {
  if (timeDiff <= -3) return 'early-time';
  if (timeDiff >= 5) return 'late-time';
  return 'ontime-time';
}

// 获取到站状态类型
function getArrivalStatusType(status) {
  const typeMap = {
    'ontime': 'success',
    'early': 'warning',
    'late': 'danger'
  };
  return typeMap[status] || 'info';
}

// 获取到站状态文本
function getArrivalStatusText(status) {
  const textMap = {
    'ontime': '准点',
    'early': '早到',
    'late': '晚点'
  };
  return textMap[status] || '未知';
}
</script>

<style scoped>
.arrival-ontime-analysis {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 左侧面板样式 */
.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 24px;
}

.search-box {
  margin-bottom: 16px;
}

/* 右侧内容区域 */
.right-content {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  backdrop-filter: blur(10px);
}

/* 筛选条件区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  background: rgba(15, 23, 42, 0.98) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

/* Tab区域 */
.tabs-section {
  margin-bottom: 20px;
}

.tab-content {
  padding-top: 20px;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.time-range-info {
  margin-bottom: 16px;
}

.stat-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(41, 52, 70, 0.6) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
  border-color: rgba(96, 165, 250, 0.4);
}

.stat-card.total {
  border-left: 4px solid #409EFF;
}

.stat-card.stations {
  border-left: 4px solid #E6A23C;
}

.stat-card.average {
  border-left: 4px solid #67C23A;
}

.stat-card.arrivals {
  border-left: 4px solid #F56C6C;
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #60a5fa;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #f8fafc;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #94a3b8;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  height: 400px;
}

.chart-card .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.chart-card .chart-header h3 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.chart-subtitle {
  font-size: 12px;
  color: #60a5fa;
  opacity: 0.8;
}

.chart-container {
  height: 320px;
  width: 100%;
}

/* 表格样式 */
.table-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  min-height: 620px;
  height: calc(100vh - 480px);
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.table-header h4 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.table-subtitle {
  font-size: 12px;
  color: #60a5fa;
  opacity: 0.8;
}

/* 数据样式 */
.ontime-count {
  color: #67C23A;
  font-weight: 600;
}

.early-count {
  color: #E6A23C;
  font-weight: 600;
}

.late-count {
  color: #F56C6C;
  font-weight: 600;
}

.delay-time {
  color: #F56C6C;
  font-weight: 600;
}

.early-time {
  color: #E6A23C;
  font-weight: 600;
}

.ontime-time {
  color: #67C23A;
  font-weight: 600;
}

.late-time {
  color: #F56C6C;
  font-weight: 600;
}

/* 详情弹窗 */
.detail-content {
  max-height: 500px;
  overflow-y: auto;
}

/* 树形组件样式 */
.dept-tree {
  background: transparent;
  color: #e5e7eb;
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
}

.tree-node {
  display: flex;
  align-items: center;
  font-size: 14px;
  flex: 1;
}

.route-icon {
  margin-right: 8px;
  color: #60a5fa;
  font-size: 16px;
  flex-shrink: 0;
}

.station-icon {
  margin-right: 8px;
  color: #E6A23C;
  font-size: 16px;
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  min-width: 0;
  white-space: nowrap;
}

.node-label.route {
  font-weight: 500;
  color: #f1f5f9;
}

.node-label.station {
  color: #94a3b8;
}

/* Element UI组件覆盖 */
:deep(.el-form-item__label) {
  color: #e5e7eb !important;
}

:deep(.el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e5e7eb !important;
}

:deep(.el-input__inner) {
  color: #e5e7eb !important;
}

:deep(.el-input__inner::placeholder) {
  color: #64748b !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
}

:deep(.el-table) {
  background: transparent !important;
  color: #e5e7eb !important;
  height: 100% !important;
  flex: 1 !important;
}

:deep(.el-table tr) {
  background: rgba(15, 23, 42, 0.6) !important;
}

:deep(.el-table tr:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
}

:deep(.el-table th.el-table__cell) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #f8fafc !important;
  border-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-table td.el-table__cell) {
  border-color: rgba(147, 197, 253, 0.1) !important;
  color: #e5e7eb !important;
}

:deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.8);
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  background: transparent;
  border: none;
  color: #94a3b8;
  transition: all 0.3s ease;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item:hover) {
  background: rgba(59, 130, 246, 0.1);
  color: #93c5fd;
}

/* Element Tree组件覆盖样式 */
:deep(.el-tree-node) {
  background: transparent;
}

:deep(.el-tree-node:hover > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 6px;
}

:deep(.el-tree-node__content) {
  background: transparent !important;
  border: none !important;
  color: #e5e7eb !important;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

:deep(.el-tree-node__content:hover) {
  background-color: rgba(59, 130, 246, 0.15) !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.2) !important;
  color: #93c5fd !important;
}

:deep(.el-tree-node__expand-icon) {
  color: #64748b !important;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-tree .el-tree-node__children) {
  overflow: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .arrival-ontime-analysis {
    padding: 10px;
  }

  .stats-section .el-col {
    margin-bottom: 16px;
  }

  .chart-card {
    height: 300px;
  }

  .chart-container {
    height: 220px;
  }
}
</style>
