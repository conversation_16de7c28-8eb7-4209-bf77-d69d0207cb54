export interface ScheduleTemplateVO {
  /** 模板ID */
  templateId: string | number;
  /** 模板名称 */
  templateName: string;
  /** 模板类型 */
  templateType: 'fixed' | 'period' | 'density' | 'holiday' | 'copy' | 'custom';
  /** 模板描述 */
  templateDescription?: string;
  /** 首班车时间 */
  firstBusTime: string;
  /** 末班车时间 */
  lastBusTime: string;
  /** 发车间隔（分钟），固定间隔模式下使用 */
  departureInterval?: number;
  /** 日发车次数 */
  dailyTrips?: number;
  /** 模板状态 */
  status: '0' | '1';
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 创建人 */
  createBy?: string;
  /** 更新人 */
  updateBy?: string;
  /** 模板配置详情 */
  templateConfig?: TemplateConfig;
  /** 使用统计 */
  usageStats?: TemplateUsageStats;
}

export interface ScheduleTemplateForm {
  /** 模板ID */
  templateId?: string | number;
  /** 模板名称 */
  templateName: string;
  /** 模板类型 */
  templateType: 'fixed' | 'period' | 'density' | 'holiday' | 'copy' | 'custom';
  /** 模板描述 */
  templateDescription?: string;
  /** 首班车时间 */
  firstBusTime: string;
  /** 末班车时间 */
  lastBusTime: string;
  /** 发车间隔（分钟），固定间隔模式下使用 */
  departureInterval?: number;
  /** 模板状态 */
  status: '0' | '1';
  /** 模板配置详情 */
  templateConfig?: TemplateConfig;
}

export interface ScheduleTemplateQuery {
  /** 页码 */
  pageNum?: number;
  /** 页面大小 */
  pageSize?: number;
  /** 模板名称 */
  templateName?: string;
  /** 模板类型 */
  templateType?: string;
  /** 模板状态 */
  status?: string;
  /** 创建时间开始 */
  createTimeStart?: string;
  /** 创建时间结束 */
  createTimeEnd?: string;
}

export interface TemplateConfig {
  /** 固定间隔模式配置 */
  fixedConfig?: FixedIntervalConfig;
  /** 分时段模式配置 */
  periodConfig?: PeriodConfig;
  /** 智能密度模式配置 */
  densityConfig?: DensityConfig;
  /** 节假日模式配置 */
  holidayConfig?: HolidayConfig;
  /** 复制模式配置 */
  copyConfig?: CopyConfig;
  /** 自定义模式配置 */
  customConfig?: CustomConfig;
}

/** 固定间隔模式配置 */
export interface FixedIntervalConfig {
  /** 发车间隔（分钟） */
  interval: number;
  /** 是否允许末班车延迟 */
  allowLastBusDelay?: boolean;
  /** 最大延迟时间（分钟） */
  maxDelayMinutes?: number;
}

/** 分时段模式配置 */
export interface PeriodConfig {
  /** 时段配置列表 */
  periods: Array<{
    /** 时段名称 */
    name: string;
    /** 开始时间 */
    startTime: string;
    /** 结束时间 */
    endTime: string;
    /** 发车间隔（分钟） */
    interval: number;
  }>;
}

/** 智能密度模式配置 */
export interface DensityConfig {
  /** 基础间隔（分钟） */
  baseInterval: number;
  /** 密度调整策略 */
  densityStrategy: 'passenger_flow' | 'historical_data' | 'manual';
  /** 调整参数 */
  adjustmentParams?: {
    /** 高密度时间段 */
    highDensityPeriods?: Array<{
      startTime: string;
      endTime: string;
      intervalMultiplier: number;
    }>;
    /** 低密度时间段 */
    lowDensityPeriods?: Array<{
      startTime: string;
      endTime: string;
      intervalMultiplier: number;
    }>;
  };
}

/** 节假日模式配置 */
export interface HolidayConfig {
  /** 工作日配置 */
  workdayConfig: {
    firstBusTime: string;
    lastBusTime: string;
    interval: number;
  };
  /** 节假日配置 */
  holidayConfig: {
    firstBusTime: string;
    lastBusTime: string;
    interval: number;
  };
  /** 特殊日期配置 */
  specialDateConfigs?: Array<{
    date: string;
    config: {
      firstBusTime: string;
      lastBusTime: string;
      interval: number;
    };
  }>;
}

/** 复制模式配置 */
export interface CopyConfig {
  /** 源模板ID */
  sourceTemplateId: string | number;
  /** 调整参数 */
  adjustments?: {
    /** 时间偏移（分钟） */
    timeOffset?: number;
    /** 间隔调整倍数 */
    intervalMultiplier?: number;
  };
}

/** 自定义模式配置 */
export interface CustomConfig {
  /** 自定义发车时刻表 */
  customSchedule: Array<{
    /** 发车时间 */
    departureTime: string;
    /** 备注 */
    note?: string;
  }>;
}

/** 模板使用统计 */
export interface TemplateUsageStats {
  /** 应用天数 */
  appliedDays: number;
  /** 准点率 */
  punctualityRate: number;
  /** 平均载客率 */
  averageLoadFactor?: number;
  /** 最后应用日期 */
  lastAppliedDate?: string;
}

/** 模板导入表单 */
export interface ScheduleTemplateImportForm {
  /** 文件 */
  file: File;
  /** 是否覆盖已存在的模板 */
  overwrite?: boolean;
  /** 导入选项 */
  importOptions?: {
    /** 是否验证时间格式 */
    validateTimeFormat?: boolean;
    /** 是否自动修正错误 */
    autoCorrectErrors?: boolean;
  };
}

/** 模板预览结果 */
export interface TemplatePreviewResult {
  /** 发车时刻表 */
  schedule: Array<{
    /** 发车时间 */
    departureTime: string;
    /** 班次号 */
    tripNumber?: string;
    /** 备注 */
    note?: string;
  }>;
  /** 统计信息 */
  statistics: {
    /** 总班次数 */
    totalTrips: number;
    /** 运营时长（分钟） */
    operationDuration: number;
    /** 平均间隔（分钟） */
    averageInterval: number;
  };
}
