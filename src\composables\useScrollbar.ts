/**
 * 滚动条组合式函数
 * 提供统一的滚动条管理功能
 */
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { OverlayScrollbars } from 'overlayscrollbars';
import { initScrollbar, updateScrollbarTheme, destroyScrollbar } from '@/utils/scrollbar';
import { useSettingsStore } from '@/store/modules/settings';

export function useScrollbar(elementRef?: Ref<HTMLElement | undefined>) {
  const scrollbarInstance = ref<OverlayScrollbars | null>(null);
  const settingsStore = useSettingsStore();

  /**
   * 初始化滚动条
   */
  const initializeScrollbar = (element?: HTMLElement) => {
    const targetElement = element || elementRef?.value;
    if (!targetElement) return;

    // 销毁已存在的实例
    if (scrollbarInstance.value) {
      scrollbarInstance.value.destroy();
    }

    // 创建新实例
    scrollbarInstance.value = initScrollbar(targetElement);
  };

  /**
   * 更新滚动条主题
   */
  const updateTheme = () => {
    updateScrollbarTheme();
  };

  /**
   * 销毁滚动条
   */
  const destroyScrollbarInstance = () => {
    if (scrollbarInstance.value) {
      scrollbarInstance.value.destroy();
      scrollbarInstance.value = null;
    }
  };

  /**
   * 滚动到指定位置
   */
  const scrollTo = (options: { top?: number; left?: number; behavior?: 'auto' | 'smooth' }) => {
    if (scrollbarInstance.value) {
      const viewport = scrollbarInstance.value.elements().viewport;
      viewport.scrollTo({
        top: options.top || 0,
        left: options.left || 0,
        behavior: options.behavior || 'smooth'
      });
    }
  };

  /**
   * 滚动到顶部
   */
  const scrollToTop = (smooth = true) => {
    scrollTo({ top: 0, behavior: smooth ? 'smooth' : 'auto' });
  };

  /**
   * 滚动到底部
   */
  const scrollToBottom = (smooth = true) => {
    if (scrollbarInstance.value) {
      const viewport = scrollbarInstance.value.elements().viewport;
      scrollTo({
        top: viewport.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto'
      });
    }
  };

  /**
   * 获取滚动位置
   */
  const getScrollPosition = () => {
    if (scrollbarInstance.value) {
      const viewport = scrollbarInstance.value.elements().viewport;
      return {
        top: viewport.scrollTop,
        left: viewport.scrollLeft
      };
    }
    return { top: 0, left: 0 };
  };

  // 监听主题变化
  watch(
    () => settingsStore.theme,
    () => {
      updateTheme();
    }
  );

  // 生命周期管理
  onMounted(() => {
    if (elementRef?.value) {
      initializeScrollbar();
    }
  });

  onUnmounted(() => {
    destroyScrollbarInstance();
  });

  return {
    scrollbarInstance: readonly(scrollbarInstance),
    initializeScrollbar,
    updateTheme,
    destroyScrollbarInstance,
    scrollTo,
    scrollToTop,
    scrollToBottom,
    getScrollPosition
  };
}
