<template>
  <div class="app-container">
    <!-- 顶部操作区域 -->
    <div class="template-header">
      <div class="header-title">
        <el-icon><DocumentCopy /></el-icon>
        <span>发车计划模板管理</span>
      </div>
      <div class="header-actions">
        <el-button type="primary" size="large" @click="handleQuickCreate">
          <el-icon><Star /></el-icon>
          快速生成模板
        </el-button>
        <el-button type="success" plain size="large" @click="handleImportTemplate">
          <el-icon><Upload /></el-icon>
          导入模板
        </el-button>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <div class="search-section">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
        <el-form-item label="模板名称" prop="templateName">
          <el-input v-model="queryParams.templateName" placeholder="请输入模板名称" clearable style="width: 200px" @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="模板类型" prop="templateType">
          <el-select v-model="queryParams.templateType" placeholder="请选择类型" clearable style="width: 150px">
            <el-option label="固定间隔" value="fixed" />
            <el-option label="分时段" value="period" />
            <el-option label="智能密度" value="density" />
            <el-option label="复制模式" value="copy" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="启用" value="1" />
            <el-option label="停用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 批量操作工具栏 -->
    <div class="toolbar-section" v-show="selectedTemplates.length > 0">
      <div class="selected-info">
        <el-icon><Select /></el-icon>
        <span>已选择 {{ selectedTemplates.length }} 个模板</span>
      </div>
      <div class="batch-actions">
        <el-button type="success" plain @click="handleBatchEnable">
          <el-icon><CircleCheck /></el-icon>
          批量启用
        </el-button>
        <el-button type="warning" plain @click="handleBatchDisable">
          <el-icon><CircleClose /></el-icon>
          批量停用
        </el-button>
        <el-button type="danger" plain @click="handleBatchDelete">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 模板卡片列表 -->
    <div class="template-grid" v-loading="loading">
      <div
        v-for="template in templateList"
        :key="template.templateId"
        class="template-card"
        :class="{ 'selected': selectedTemplates.includes(template.templateId) }"
        @click="toggleSelection(template)"
      >
        <!-- 卡片头部 -->
        <div class="card-header">
          <div class="header-left">
            <el-checkbox :model-value="selectedTemplates.includes(template.templateId)" @change="toggleSelection(template)" @click.stop />
            <div class="template-icon">
              <el-icon v-if="template.templateType === 'fixed'"><Clock /></el-icon>
              <el-icon v-else-if="template.templateType === 'period'"><Timer /></el-icon>
              <el-icon v-else-if="template.templateType === 'density'"><TrendCharts /></el-icon>
              <el-icon v-else-if="template.templateType === 'copy'"><CopyDocument /></el-icon>
              <el-icon v-else><Setting /></el-icon>
            </div>
            <div class="template-title">
              <h3>{{ template.templateName }}</h3>
              <p>{{ template.templateDescription || '发车时间模板' }}</p>
            </div>
          </div>
          <div class="header-right">
            <el-tag :type="template.status === '1' ? 'success' : 'danger'" size="small">
              {{ template.status === '1' ? '启用' : '停用' }}
            </el-tag>
            <el-dropdown trigger="click" @click.stop>
              <el-button text circle>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handlePreview(template)">
                    <el-icon><View /></el-icon>
                    预览时刻表
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleEdit(template)">
                    <el-icon><Edit /></el-icon>
                    编辑模板
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleCopy(template)">
                    <el-icon><CopyDocument /></el-icon>
                    复制模板
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="handleToggleStatus(template)">
                    <el-icon><Switch /></el-icon>
                    {{ template.status === '1' ? '停用' : '启用' }}
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleDelete(template)" class="danger">
                    <el-icon><Delete /></el-icon>
                    删除模板
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <!-- 卡片内容 -->
        <div class="card-content">
          <div class="template-type">
            <el-tag :type="getTemplateTypeTag(template.templateType).type" size="small">
              {{ getTemplateTypeTag(template.templateType).label }}
            </el-tag>
          </div>

          <div class="template-info">
            <div class="info-item">
              <span class="label">运营时间：</span>
              <span class="value">{{ template.firstBusTime }} - {{ template.lastBusTime }}</span>
            </div>
            <div class="info-item" v-if="template.departureInterval">
              <span class="label">发车间隔：</span>
              <span class="value">{{ template.departureInterval }}分钟</span>
            </div>
            <div class="info-item" v-if="template.dailyTrips">
              <span class="label">日发车次：</span>
              <span class="value">{{ template.dailyTrips }}班</span>
            </div>
          </div>

          <div class="template-stats" v-if="template.usageStats">
            <div class="stat-item">
              <span class="stat-label">应用天数</span>
              <span class="stat-value">{{ template.usageStats.appliedDays || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">准点率</span>
              <span class="stat-value">{{ template.usageStats.punctualityRate || '--' }}%</span>
            </div>
          </div>
        </div>

        <!-- 卡片底部 -->
        <div class="card-footer">
          <div class="update-info">
            <span>更新时间：{{ formatDate(template.updateTime) }}</span>
          </div>
          <div class="quick-actions">
            <el-button size="small" text type="primary" @click="handleApplyTemplate(template)" @click.stop> 立即应用 </el-button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="templateList.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无模板数据" :image-size="120">
          <el-button type="primary" @click="handleQuickCreate"> 立即创建模板 </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-wrapper" v-show="total > 0">
      <pagination :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>

    <!-- 快速生成模板对话框 -->
    <el-dialog title="快速生成模板" v-model="quickCreateVisible" width="1000px" append-to-body :close-on-click-modal="false">
      <el-scrollbar height="600px" class="dialog-scrollbar">
        <div class="quick-create-content">
          <!-- 步骤指示器 -->
          <el-steps :active="currentStep" align-center class="create-steps">
            <el-step title="选择生成模式">
              <template #icon>
                <el-icon><Setting /></el-icon>
              </template>
            </el-step>
            <el-step title="配置参数">
              <template #icon>
                <el-icon><Edit /></el-icon>
              </template>
            </el-step>
            <el-step title="预览确认">
              <template #icon>
                <el-icon><View /></el-icon>
              </template>
            </el-step>
          </el-steps>

          <!-- 步骤1: 选择模式 -->
          <div v-show="currentStep === 0" class="step-content">
            <div class="mode-selection">
              <h3>选择生成模式</h3>
              <div class="mode-grid">
                <div
                  v-for="mode in generationModes"
                  :key="mode.value"
                  class="mode-card"
                  :class="{ 'selected': selectedMode === mode.value }"
                  @click="selectedMode = mode.value"
                >
                  <div class="mode-icon">
                    <el-icon :size="24"><component :is="mode.icon" /></el-icon>
                  </div>
                  <div class="mode-info">
                    <h4>{{ mode.label }}</h4>
                    <p>{{ mode.description }}</p>
                    <div class="mode-features">
                      <span v-for="feature in mode.features" :key="feature" class="feature-tag">
                        {{ feature }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 步骤2: 配置参数 -->
          <div v-show="currentStep === 1" class="step-content">
            <div class="parameter-config">
              <h3>配置模板参数</h3>
              <!-- 参数配置内容将根据选择的模式动态显示 -->
              <component :is="getParameterComponent()" v-model="templateConfig" />
            </div>
          </div>

          <!-- 步骤3: 预览确认 -->
          <div v-show="currentStep === 2" class="step-content">
            <div class="preview-section">
              <TemplatePreview :config="templateConfig" :selected-mode="selectedMode" />
            </div>
          </div>
        </div>
      </el-scrollbar>

      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
          <el-button v-if="currentStep < 2" type="primary" @click="nextStep">下一步</el-button>
          <el-button v-if="currentStep === 2" type="primary" @click="handleCreateTemplate">创建模板</el-button>
          <el-button @click="quickCreateVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 模板编辑对话框 -->
    <el-dialog title="编辑模板" v-model="editVisible" width="800px" append-to-body :close-on-click-modal="false">
      <el-scrollbar height="500px" class="dialog-scrollbar">
        <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="模板名称" prop="templateName">
                <el-input v-model="editForm.templateName" placeholder="请输入模板名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="模板类型">
                <el-tag :type="getTemplateTypeTag(editForm.templateType).type">
                  {{ getTemplateTypeTag(editForm.templateType).label }}
                </el-tag>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="模板状态" prop="status">
                <el-radio-group v-model="editForm.status">
                  <el-radio value="1">启用</el-radio>
                  <el-radio value="0">停用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发车时间段" prop="operationPeriod">
                <el-input v-model="editForm.operationPeriod" placeholder="如：05:30-22:30" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="模板描述" prop="templateDescription">
            <el-input v-model="editForm.templateDescription" type="textarea" :rows="3" placeholder="请输入模板描述" maxlength="200" show-word-limit />
          </el-form-item>
        </el-form>
      </el-scrollbar>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveEdit">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 模板详情预览对话框 -->
    <el-dialog title="模板详情" v-model="detailVisible" width="1000px" append-to-body>
      <el-scrollbar height="700px" class="dialog-scrollbar">
        <div class="template-detail-content">
          <TemplatePreview v-if="detailTemplate" :config="detailTemplate" :selected-mode="detailTemplate.templateType" />
        </div>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>

<style scoped>
/* 模板页面样式 - 深色主题 - 性能优化版本 */
.app-container {
  padding: 20px;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  position: relative;
  overflow: hidden;
}

/* 移除性能消耗大的动态背景效果 */
.app-container::before,
.app-container::after {
  display: none;
}

/* 确保内容在背景效果之上 */
.app-container > * {
  position: relative;
  z-index: 2;
}

/* 顶部区域 - 简化版本 */
.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  color: white;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 搜索区域 - 简化版本 */
.search-section {
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

/* 工具栏 - 简化版本 */
.toolbar-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 12px 20px;
  margin-bottom: 20px;
}

.selected-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #93c5fd;
  font-weight: 500;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

/* 模板卡片网格 */
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

/* 模板卡片 - 性能优化版本 */
.template-card {
  background: rgba(30, 41, 59, 0.7);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  overflow: hidden;
  transition: border-color 0.2s ease;
  cursor: pointer;
}

.template-card:hover {
  border-color: rgba(59, 130, 246, 0.6);
}

.template-card.selected {
  border-color: rgba(59, 130, 246, 0.8);
  background: rgba(59, 130, 246, 0.1);
}

/* 卡片头部 - 简化版本 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
  background: rgba(15, 23, 42, 0.4);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.template-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(99, 102, 241, 0.8) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.template-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #e2e8f0;
}

.template-title p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #94a3b8;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 卡片内容 */
.card-content {
  padding: 16px 20px;
  color: #e2e8f0;
}

.template-type {
  margin-bottom: 12px;
}

.template-info {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-item .label {
  color: #94a3b8;
}

.info-item .value {
  color: #e2e8f0;
  font-weight: 500;
}

/* 统计信息 - 简化版本 */
.template-stats {
  display: flex;
  gap: 20px;
  padding: 12px;
  background: rgba(15, 23, 42, 0.4);
  border-radius: 6px;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-label {
  font-size: 12px;
  color: #94a3b8;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #e2e8f0;
}

/* 卡片底部 - 简化版本 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: rgba(15, 23, 42, 0.6);
  border-top: 1px solid rgba(147, 197, 253, 0.1);
}

.update-info {
  font-size: 12px;
  color: #94a3b8;
}

/* 空状态 */
.empty-state {
  grid-column: 1 / -1;
  padding: 60px 0;
  text-align: center;
}

/* 分页 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 步骤指示器样式 */
.create-steps {
  margin-bottom: 30px;
}

:deep(.el-steps) {
  background: transparent;
}

:deep(.el-step__head) {
  color: #94a3b8 !important;
}

:deep(.el-step__head.is-process) {
  color: #3b82f6 !important;
  border-color: #3b82f6 !important;
}

:deep(.el-step__head.is-finish) {
  color: #10b981 !important;
  border-color: #10b981 !important;
}

:deep(.el-step__title) {
  color: #94a3b8 !important;
}

:deep(.el-step__title.is-process) {
  color: #e2e8f0 !important;
}

:deep(.el-step__title.is-finish) {
  color: #10b981 !important;
}

:deep(.el-step__line) {
  background-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-step__line.is-finish) {
  background-color: #10b981 !important;
}

:deep(.el-step__icon) {
  background-color: rgba(30, 41, 59, 0.8) !important;
  border: 2px solid rgba(147, 197, 253, 0.3) !important;
  color: #94a3b8 !important;
}

:deep(.el-step__icon.is-process) {
  background-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: #ffffff !important;
}

:deep(.el-step__icon.is-finish) {
  background-color: #10b981 !important;
  border-color: #10b981 !important;
  color: #ffffff !important;
}

/* 快速创建对话框 */
.quick-create-content {
  padding: 20px 0;
}

/* 弹窗滚动条容器 */
.dialog-scrollbar {
  max-height: 100%;
}

.dialog-scrollbar .el-scrollbar__view {
  padding: 0 !important;
}

.step-content {
  min-height: 200px;
}

/* 模式选择 */
.mode-selection h3 {
  margin-bottom: 20px;
  text-align: center;
  color: #e2e8f0;
}

.mode-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.mode-card {
  border: 2px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.6) 0%, rgba(51, 65, 85, 0.4) 100%);
}

.mode-card:hover {
  border-color: rgba(59, 130, 246, 0.6);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.mode-card.selected {
  border-color: rgba(59, 130, 246, 0.8);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(99, 102, 241, 0.1) 100%);
}

.mode-icon {
  text-align: center;
  margin-bottom: 12px;
  color: #60a5fa;
}

.mode-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #e2e8f0;
}

.mode-info p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #94a3b8;
}

.mode-features {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.feature-tag {
  background: rgba(59, 130, 246, 0.2);
  color: #93c5fd;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* 参数配置区域 */
.parameter-config h3 {
  margin-bottom: 20px;
  color: #e2e8f0;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

.parameter-config {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #e2e8f0;
}

/* 预览区域 */
.preview-section h3 {
  margin-bottom: 20px;
  color: #e2e8f0;
}

.preview-content {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.6) 0%, rgba(30, 41, 59, 0.4) 100%);
  border-radius: 8px;
  padding: 20px;
  min-height: 300px;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

/* 下拉菜单危险操作 */
:deep(.el-dropdown-menu__item.danger) {
  color: #f87171;
}

:deep(.el-dropdown-menu__item.danger:hover) {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Element Plus 组件深色主题覆盖 */
:deep(.el-form-item__label) {
  color: #e2e8f0 !important;
}

:deep(.el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  box-shadow: none !important;
}

:deep(.el-input__inner) {
  color: #e2e8f0 !important;
}

:deep(.el-input__inner::placeholder) {
  color: #94a3b8 !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

:deep(.el-select__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  box-shadow: none !important;
}

:deep(.el-select__selection) {
  color: #e2e8f0 !important;
}

:deep(.el-select__placeholder) {
  color: #94a3b8 !important;
}

:deep(.el-textarea__inner) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e2e8f0 !important;
}

/* Element Plus 滚动条深色主题样式 */
:deep(.dialog-scrollbar .el-scrollbar__bar) {
  opacity: 0.8;
}

:deep(.dialog-scrollbar .el-scrollbar__thumb) {
  background-color: rgba(147, 197, 253, 0.4) !important;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

:deep(.dialog-scrollbar .el-scrollbar__thumb:hover) {
  background-color: rgba(59, 130, 246, 0.6) !important;
}

:deep(.dialog-scrollbar .el-scrollbar__track) {
  background-color: rgba(15, 23, 42, 0.2) !important;
  border-radius: 6px;
}

:deep(.dialog-scrollbar .el-scrollbar__view) {
  padding-right: 8px;
}

/* Element Plus 弹窗性能优化 */
:deep(.el-dialog) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
}

:deep(.el-dialog__header) {
  background: rgba(59, 130, 246, 0.1) !important;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2) !important;
  padding: 16px 20px !important;
}

:deep(.el-dialog__title) {
  color: #e2e8f0 !important;
  font-weight: 600 !important;
}

:deep(.el-dialog__close) {
  color: #94a3b8 !important;
}

:deep(.el-dialog__close:hover) {
  color: #e2e8f0 !important;
}

:deep(.el-dialog__body) {
  padding: 20px 20px 10px 20px !important;
  background: transparent !important;
}

:deep(.el-dialog__footer) {
  background: rgba(15, 23, 42, 0.5) !important;
  border-top: 1px solid rgba(59, 130, 246, 0.2) !important;
  padding: 16px 20px !important;
}

/* 移除弹窗的复杂动画效果 */
:deep(.el-overlay) {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

:deep(.el-overlay-dialog) {
  transition: none !important;
}

:deep(.dialog-fade-enter-active) {
  transition: none !important;
}

:deep(.dialog-fade-leave-active) {
  transition: none !important;
}

:deep(.el-textarea__inner::placeholder) {
  color: #94a3b8 !important;
}

/* 配置组件内的标题和文本颜色统一设置 */
:deep(.config-section) {
  margin-bottom: 20px !important;
}

:deep(.section-title) {
  color: #e2e8f0 !important;
}

:deep(.period-title) {
  color: #e2e8f0 !important;
}

:deep(.config-section h4) {
  color: #e2e8f0 !important;
}

:deep(.config-section h5) {
  color: #e2e8f0 !important;
}

:deep(.config-section .el-text) {
  color: #e2e8f0 !important;
}

:deep(.stat-value) {
  color: #e2e8f0 !important;
}

:deep(.stat-label) {
  color: #94a3b8 !important;
}

:deep(.timeline-item span) {
  color: #e2e8f0 !important;
}

:deep(.form-tip) {
  color: #94a3b8 !important;
}

:deep(.el-alert) {
  background: rgba(30, 41, 59, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
}

:deep(.el-alert__content) {
  color: #e2e8f0 !important;
}

:deep(.el-alert__title) {
  color: #e2e8f0 !important;
}

:deep(.el-card) {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.6) 0%, rgba(30, 41, 59, 0.4) 100%) !important;
  border-color: rgba(147, 197, 253, 0.2) !important;
  color: #e2e8f0 !important;
}

:deep(.el-card__header) {
  background: rgba(15, 23, 42, 0.4) !important;
  border-color: rgba(147, 197, 253, 0.1) !important;
  color: #e2e8f0 !important;
}

:deep(.el-card__body) {
  color: #e2e8f0 !important;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
}

:deep(.el-checkbox__label) {
  color: #e2e8f0 !important;
}

:deep(.el-radio__label) {
  color: #e2e8f0 !important;
}

:deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
}

:deep(.el-tag) {
  background: rgba(59, 130, 246, 0.2) !important;
  border-color: rgba(59, 130, 246, 0.4) !important;
  color: #93c5fd !important;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  border-color: #3b82f6 !important;
}

:deep(.el-empty__description p) {
  color: #94a3b8 !important;
}

/* 下拉选择器弹出层样式 - 移除backdrop-filter */
:deep(.el-select-dropdown) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(147, 197, 253, 0.3) !important;
}

:deep(.el-select-dropdown__item) {
  color: #e2e8f0 !important;
  background: transparent !important;
}

:deep(.el-select-dropdown__item:hover) {
  background: rgba(59, 130, 246, 0.2) !important;
}

:deep(.el-select-dropdown__item.selected) {
  background: rgba(59, 130, 246, 0.3) !important;
  color: #ffffff !important;
}

/* 时间选择器样式 */
:deep(.el-time-picker) {
  width: 100%;
}

:deep(.el-time-panel) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(147, 197, 253, 0.3) !important;
}

:deep(.el-time-panel__content) {
  background: transparent !important;
}

:deep(.el-time-spinner__item) {
  color: #e2e8f0 !important;
}

:deep(.el-time-spinner__item:hover) {
  background: rgba(59, 130, 246, 0.2) !important;
}

:deep(.el-time-spinner__item.active) {
  background: rgba(59, 130, 246, 0.3) !important;
  color: #ffffff !important;
}

/* 数字输入框样式 */
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
}

:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  background: rgba(30, 41, 59, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e2e8f0 !important;
}

:deep(.el-input-number__decrease:hover),
:deep(.el-input-number__increase:hover) {
  background: rgba(59, 130, 246, 0.3) !important;
  color: #ffffff !important;
}
</style>

<script setup name="ScheduleTemplate">
import { ref, reactive, toRefs, onMounted, getCurrentInstance } from 'vue';
import {
  DocumentCopy,
  Star,
  Upload,
  Search,
  Refresh,
  Select,
  CircleCheck,
  CircleClose,
  Delete,
  Clock,
  Timer,
  TrendCharts,
  CopyDocument,
  Setting,
  MoreFilled,
  View,
  Edit,
  Switch,
  Calendar
} from '@element-plus/icons-vue';
// import {
//   listScheduleTemplate,
//   getScheduleTemplate,
//   addScheduleTemplate,
//   updateScheduleTemplate,
//   delScheduleTemplate,
//   toggleScheduleTemplateStatus,
//   copyScheduleTemplate,
//   applyTemplateToSchedule
// } from '@/api/schedule/template'
// import type { ScheduleTemplateVO, ScheduleTemplateQuery, ScheduleTemplateForm } from '@/api/schedule/template/types'
import FixedIntervalConfig from '@/components/schedule/FixedIntervalConfig.vue';
import PeriodConfig from '@/components/schedule/PeriodConfig.vue';
import DensityConfig from '@/components/schedule/DensityConfig.vue';
import HolidayConfig from '@/components/schedule/HolidayConfig.vue';
import CopyConfig from '@/components/schedule/CopyConfig.vue';
import TemplatePreview from '@/components/schedule/TemplatePreview.vue';

const { proxy } = getCurrentInstance();

// 响应式数据
const templateList = ref([]);
const selectedTemplates = ref([]);
const loading = ref(true);
const total = ref(0);

// 对话框控制
const quickCreateVisible = ref(false);
const previewVisible = ref(false);
const editVisible = ref(false);
const detailVisible = ref(false);

// 编辑相关
const editForm = ref({});
const detailTemplate = ref(null);
const editRules = {
  templateName: [
    { required: true, message: '模板名称不能为空', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
};

// 快速创建相关
const currentStep = ref(0);
const selectedMode = ref('');
const templateConfig = ref({});

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 12,
    templateName: undefined,
    templateType: undefined,
    status: undefined
  }
});

const { queryParams } = toRefs(data);

// 生成模式配置
const generationModes = ref([
  {
    value: 'fixed',
    label: '固定间隔模式',
    description: '全天统一发车间隔，适合客流相对稳定的线路',
    icon: 'Clock',
    features: ['简单直观', '轻松配置', '适合新手']
  },
  {
    value: 'period',
    label: '分时段模式',
    description: '不同时段不同发车频率，灵活适应客流变化',
    icon: 'Timer',
    features: ['灵活高效', '适应客流', '节省成本']
  },
  {
    value: 'density',
    label: '智能密度模式',
    description: '基于经验或数据的客流预测，智能分配发车密度',
    icon: 'TrendCharts',
    features: ['智能推荐', '快速生成', '适合新线']
  },
  {
    value: 'holiday',
    label: '节假日差异化',
    description: '区分工作日和节假日的不同需求，精准调度',
    icon: 'Calendar',
    features: ['区分日期', '精准调度', '节约成本']
  },
  {
    value: 'copy',
    label: '快速复制模式',
    description: '基于现有成功模板快速创建新模板',
    icon: 'CopyDocument',
    features: ['快速复制', '保持经验', '适配相似']
  }
]);

/** 获取模板类型标签 */
function getTemplateTypeTag(type) {
  const typeMap = {
    'fixed': { label: '固定间隔', type: 'info' },
    'period': { label: '分时段', type: 'warning' },
    'density': { label: '智能密度', type: 'success' },
    'holiday': { label: '节假日', type: 'primary' },
    'copy': { label: '复制模式', type: '' },
    'custom': { label: '自定义', type: 'success' }
  };
  return typeMap[type] || { label: '未知', type: 'info' };
}

/** 时间格式化 */
function formatDate(dateStr) {
  if (!dateStr) return '--';
  return new Date(dateStr).toLocaleDateString('zh-CN');
}

/** 查询模板列表 */
function getList() {
  loading.value = true;
  // 模拟数据
  const mockData = [
    {
      templateId: 1,
      templateName: '固定间隔发车模板',
      templateType: 'fixed',
      departureInterval: 8,
      firstBusTime: '05:30',
      lastBusTime: '22:30',
      dailyTrips: 128,
      templateDescription: '8分钟固定间隔发车模板，适用于平峰时段',
      status: '1',
      updateTime: '2024-01-15T10:30:00',
      usageStats: {
        appliedDays: 45,
        punctualityRate: 92.5
      }
    },
    {
      templateId: 2,
      templateName: '分时段发车模板',
      templateType: 'period',
      departureInterval: null,
      firstBusTime: '06:00',
      lastBusTime: '21:30',
      dailyTrips: 156,
      templateDescription: '高峰6分钟，平峰10分钟，低峰15分钟',
      status: '1',
      updateTime: '2024-01-14T15:20:00',
      usageStats: {
        appliedDays: 32,
        punctualityRate: 88.3
      }
    },
    {
      templateId: 3,
      templateName: '智能密度发车模板',
      templateType: 'density',
      departureInterval: 12,
      firstBusTime: '06:30',
      lastBusTime: '20:00',
      dailyTrips: 68,
      templateDescription: '基于客流预测的智能发车模板',
      status: '0',
      updateTime: '2024-01-13T09:45:00',
      usageStats: {
        appliedDays: 12,
        punctualityRate: 95.1
      }
    },
    {
      templateId: 4,
      templateName: '节假日发车模板',
      templateType: 'holiday',
      departureInterval: 15,
      firstBusTime: '07:00',
      lastBusTime: '19:00',
      dailyTrips: 48,
      templateDescription: '节假日专用发车模板，运营时间调整',
      status: '1',
      updateTime: '2024-01-12T14:10:00',
      usageStats: {
        appliedDays: 8,
        punctualityRate: 90.2
      }
    }
  ];

  setTimeout(() => {
    templateList.value = mockData;
    total.value = mockData.length;
    loading.value = false;
  }, 500);
}

/** 搜索查询 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置搜索 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

/** 切换模板选择 */
function toggleSelection(template) {
  const index = selectedTemplates.value.indexOf(template.templateId);
  if (index > -1) {
    selectedTemplates.value.splice(index, 1);
  } else {
    selectedTemplates.value.push(template.templateId);
  }
}

/** 快速创建模板 */
function handleQuickCreate() {
  currentStep.value = 0;
  selectedMode.value = '';
  templateConfig.value = {};
  quickCreateVisible.value = true;
}

/** 导入模板 */
function handleImportTemplate() {
  proxy.$modal.msgInfo('导入模板功能开发中...');
}

/** 批量启用 */
function handleBatchEnable() {
  proxy.$modal.confirm(`确认启用所选择的 ${selectedTemplates.value.length} 个模板吗？`).then(() => {
    proxy.$modal.msgSuccess('批量启用成功');
    selectedTemplates.value = [];
    getList();
  });
}

/** 批量停用 */
function handleBatchDisable() {
  proxy.$modal.confirm(`确认停用所选择的 ${selectedTemplates.value.length} 个模板吗？`).then(() => {
    proxy.$modal.msgSuccess('批量停用成功');
    selectedTemplates.value = [];
    getList();
  });
}

/** 批量删除 */
function handleBatchDelete() {
  proxy.$modal
    .confirm(`确认删除所选择的 ${selectedTemplates.value.length} 个模板吗？此操作不可恢复！`, '警告', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      proxy.$modal.msgSuccess('批量删除成功');
      selectedTemplates.value = [];
      getList();
    });
}

/** 预览模板 */
function handlePreview(template) {
  detailTemplate.value = {
    ...template,
    // 根据模板类型构造配置对象
    templateName: template.templateName,
    firstBusTime: template.firstBusTime,
    lastBusTime: template.lastBusTime,
    departureInterval: template.departureInterval,
    description: template.templateDescription
  };
  detailVisible.value = true;
}

/** 编辑模板 */
function handleEdit(template) {
  editForm.value = {
    templateId: template.templateId,
    templateName: template.templateName,
    templateType: template.templateType,
    status: template.status,
    templateDescription: template.templateDescription,
    operationPeriod: `${template.firstBusTime}-${template.lastBusTime}`
  };
  editVisible.value = true;
}

/** 保存编辑 */
function handleSaveEdit() {
  proxy.$refs.editFormRef.validate((valid) => {
    if (valid) {
      proxy.$modal.msgSuccess('模板编辑成功');
      editVisible.value = false;
      getList();
    }
  });
}

/** 复制模板 */
function handleCopy(template) {
  proxy.$modal.confirm(`确认复制模板"${template.templateName}"吗？`).then(() => {
    proxy.$modal.msgSuccess('复制成功');
    getList();
  });
}

/** 切换模板状态 */
function handleToggleStatus(template) {
  const action = template.status === '1' ? '停用' : '启用';
  proxy.$modal.confirm(`确认${action}模板"${template.templateName}"吗？`).then(() => {
    proxy.$modal.msgSuccess(`${action}成功`);
    getList();
  });
}

/** 应用模板 */
function handleApplyTemplate(template) {
  proxy.$modal.confirm(`确认应用模板"${template.templateName}"到排班计划吗？`).then(() => {
    proxy.$modal.msgSuccess('应用成功');
  });
}

/** 删除模板 */
function handleDelete(template) {
  proxy.$modal
    .confirm(`确认删除模板"${template.templateName}"吗？此操作不可恢复！`, '警告', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      proxy.$modal.msgSuccess('删除成功');
      getList();
    });
}

// 快速创建步骤控制
function nextStep() {
  if (currentStep.value === 0 && !selectedMode.value) {
    proxy.$modal.msgWarning('请选择生成模式');
    return;
  }
  currentStep.value++;
}

function prevStep() {
  currentStep.value--;
}

/** 获取参数配置组件 */
function getParameterComponent() {
  const componentMap = {
    'fixed': FixedIntervalConfig,
    'period': PeriodConfig,
    'density': DensityConfig,
    'holiday': HolidayConfig,
    'copy': CopyConfig
  };
  return componentMap[selectedMode.value] || 'div';
}

/** 获取预览组件 */
function getPreviewComponent() {
  return TemplatePreview;
}

/** 创建模板 */
function handleCreateTemplate() {
  proxy.$modal.msgSuccess('模板创建成功');
  quickCreateVisible.value = false;
  getList();
}

// 组件挂载
onMounted(() => {
  getList();
});
</script>
