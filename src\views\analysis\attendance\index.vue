<template>
  <div class='attendance-analysis'>
    <el-row :gutter='20'>
      <!-- 左侧组织机构驾驶员树 -->
      <el-col :lg='4' :xs='24'>
        <div class='left-panel'>
          <div class='panel-header'>
            <div class='header-left'>
              <el-icon>
                <User />
              </el-icon>
              <span>组织机构筛选</span>
            </div>
          </div>
          <div class='panel-content'>
            <div class='search-box'>
              <el-input v-model='deptName' placeholder='请输入组织机构' prefix-icon='Search' clearable />
            </div>

            <el-tree
              ref='deptTreeRef'
              class='dept-tree'
              node-key='id'
              :data='deptOptions'
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node='false'
              :filter-node-method='filterNode'
              highlight-current
              default-expand-all
              @node-click='handleNodeClick'
            >
              <template #default="{ node, data }">
                <span class="tree-node">
                  <el-icon class="dept-icon">
                    <OfficeBuilding />
                  </el-icon>
                  <span class="node-label dept">{{ data.label }}</span>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
      </el-col>

      <el-col :lg='20' :xs='24'>
        <div class='right-content'>
          <!-- 筛选条件区域 -->
          <div class='filter-section'>
            <el-card class='filter-card'>
              <!-- 时间分析选择器 -->
              <TimeAnalysisSelector
                :initialAnalysisType="queryParams.analysisType"
                :availableTypes="['0', '2', '3']"
                @params-change="handleTimeAnalysisChange"
                ref="timeAnalysisSelectorRef"
              >
                <template #actions>
                  <el-form-item label="司机姓名" prop="driverName">
                    <el-input
                      v-model="queryParams.driverName"
                      placeholder="请输入司机姓名"
                      clearable
                      style="width: 160px"
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="工号" prop="workNumber">
                    <el-input
                      v-model="queryParams.workNumber"
                      placeholder="请输入工号"
                      clearable
                      style="width: 120px"
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>

                  <el-form-item>
                    <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
                    <el-button @click="resetQuery" icon="Refresh">重置</el-button>
                  </el-form-item>
                </template>
              </TimeAnalysisSelector>
            </el-card>
          </div>

          <!-- Tab切换区域 -->
          <div class='tabs-section'>
            <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
              <!-- 图表分析 -->
              <el-tab-pane label="图表分析" name="charts">
                <div class="tab-content">
                  <!-- 统计卡片区域 -->
                  <div class='stats-section'>
                    <el-row :gutter='16'>
                      <el-col :span='6'>
                        <div class='stat-card attendance'>
                          <div class='stat-icon'>
                            <el-icon><TrendCharts /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ statsData.attendanceRate }}%</div>
                            <div class='stat-label'>总出勤率</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card normal'>
                          <div class='stat-icon'>
                            <el-icon><CircleCheck /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ statsData.normalCount }}</div>
                            <div class='stat-label'>正常出勤</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card late'>
                          <div class='stat-icon'>
                            <el-icon><Clock /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ statsData.lateCount }}</div>
                            <div class='stat-label'>迟到次数</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card absent'>
                          <div class='stat-icon'>
                            <el-icon><Warning /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ statsData.absentCount }}</div>
                            <div class='stat-label'>缺勤人次</div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 图表区域 -->
                  <div class='charts-section'>
                    <el-row :gutter='16'>
                      <el-col :span='24'>
                        <div class='chart-card'>
                          <div class='chart-header'>
                            <h3>考勤趋势图 - {{ getAnalysisTypeText(queryParams.analysisType) }}</h3>
                          </div>
                          <div ref="attendanceChartRef" class='chart-container'></div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 明细表格 -->
              <el-tab-pane label="明细表格" name="table">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <h4>驾驶员考勤明细表</h4>
                      <div class="table-legend">
                        <div class="legend-item">
                          <span class="legend-color normal"></span>
                          <span class="legend-text">正常出勤</span>
                        </div>
                        <div class="legend-item">
                          <span class="legend-color late"></span>
                          <span class="legend-text">迟到</span>
                        </div>
                        <div class="legend-item">
                          <span class="legend-color absent"></span>
                          <span class="legend-text">缺勤</span>
                        </div>
                        <div class="legend-item">
                          <span class="legend-color rate"></span>
                          <span class="legend-text">出勤率</span>
                        </div>
                      </div>
                      <div class="table-toolbar">
                        <el-button type="success" size="small" icon="Download" @click="handleExportTable">导出表格</el-button>
                      </div>
                    </div>
                    <el-table :data="detailTableData" @selection-change="handleSelectionChange" style="width: 100%" height="580" border>
                      <el-table-column type="selection" width="55" align="center" fixed="left" />
                      <el-table-column label="司机姓名" align="center" prop="driverName" width="120" fixed="left" />
                      <el-table-column label="工号" align="center" prop="workNumber" width="100" fixed="left" />
                      <el-table-column label="组织机构" align="center" prop="deptName" width="120" fixed="left" />
                      <el-table-column label="总出勤率" align="center" prop="totalAttendanceRate" width="120" fixed="left">
                        <template #default="scope">
                          <span class="attendance-rate">{{ scope.row.totalAttendanceRate }}%</span>
                        </template>
                      </el-table-column>
                      <el-table-column :label="workHoursLabel" align="center" prop="totalWorkHours" width="120" fixed="left" />
                      <el-table-column label="车辆数" align="center" prop="vehicleCount" width="100" fixed="left">
                        <template #default="scope">
                          <el-button link type="primary" @click="handleVehicleDetail(scope.row)" size="small">
                            {{ scope.row.vehicleCount }}
                          </el-button>
                        </template>
                      </el-table-column>

                      <!-- 动态生成的日期列 -->
                      <el-table-column
                        v-for="dateCol in dynamicDateColumns"
                        :key="dateCol.prop"
                        :label="dateCol.label"
                        align="center"
                        :prop="dateCol.prop"
                      >
                        <template #default="scope">
                          <div class="attendance-cell" @click="handleAttendanceDetail(scope.row, dateCol)">
                            <div class="normal-attendance">{{ scope.row[dateCol.normalProp] || '-' }}</div>
                            <div class="late-count">{{ scope.row[dateCol.lateProp] || '-' }}</div>
                            <div class="absent-count">{{ scope.row[dateCol.absentProp] || '-' }}</div>
                            <div class="attendance-rate-cell">{{ scope.row[dateCol.rateProp] || '-' }}%</div>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="total > 0"
                      :total="total"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getList"
                    />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
    </el-row>



    <!-- 车辆详情弹窗 -->
    <el-dialog :title="`${vehicleDetailData?.driverName} - 车辆列表`" v-model="showVehicleDetailDialog" width="600px" append-to-body>
      <div v-if="vehicleDetailData" class="vehicle-detail-content">
        <el-table :data="vehicleDetailData.vehicles" border>
          <el-table-column label="车牌号" prop="plateNumber" align="center" />
          <el-table-column label="车辆编号" prop="vehicleNumber" align="center" />
          <el-table-column label="状态" prop="status" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.status === '在用' ? 'success' : 'warning'">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showVehicleDetailDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 考勤详情弹窗 -->
    <el-dialog
      v-model="showAttendanceDetailDialog"
      :title="attendanceDetailTitle"
      :width="attendanceDetailDialogWidth"
      :destroy-on-close="true"
      top="5vh"
      class="attendance-detail-dialog"
    >
      <!-- 单日考勤详情 -->
      <div v-if="attendanceDetailData && !attendanceDetailData.isDailyList" class="attendance-detail-content">
        <div class="detail-grid">
          <div class="detail-item">
            <label>签到时间:</label>
            <span :class="{'late-time': attendanceDetailData.isLate}">
              {{ attendanceDetailData.checkInTime || '未签到' }}
              <el-tag v-if="attendanceDetailData.isLate" type="warning" size="small">迟到</el-tag>
            </span>
          </div>
          <div class="detail-item">
            <label>签退时间:</label>
            <span :class="{'early-leave': attendanceDetailData.isEarlyLeave}">
              {{ attendanceDetailData.checkOutTime || '未签退' }}
              <el-tag v-if="attendanceDetailData.isEarlyLeave" type="warning" size="small">早退</el-tag>
            </span>
          </div>
          <div class="detail-item">
            <label>工作时长:</label>
            <span class="work-hours">{{ attendanceDetailData.workHours || '0.0' }}小时</span>
          </div>
          <div class="detail-item">
            <label>加班时长:</label>
            <span class="overtime-hours">{{ attendanceDetailData.overtimeHours || '0.0' }}小时</span>
          </div>
          <div class="detail-item">
            <label>考勤状态:</label>
            <el-tag :type="getAttendanceStatusType(attendanceDetailData.status)">
              {{ attendanceDetailData.status || '正常' }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>GPS位置:</label>
            <span class="gps-location">{{ attendanceDetailData.location || '未知位置' }}</span>
          </div>
        </div>

        <div class="work-summary" v-if="attendanceDetailData.workSummary">
          <h4>工作摘要</h4>
          <p>{{ attendanceDetailData.workSummary }}</p>
        </div>
      </div>

      <!-- 每日明细表格 -->
      <div v-if="attendanceDetailData && attendanceDetailData.isDailyList" class="daily-detail-content">
        <div class="daily-detail-header">
          <div class="period-info">
            <span class="period-label">统计周期：</span>
            <span class="period-value">{{ attendanceDetailData.periodText }}</span>
          </div>
          <div class="summary-stats">
            <div class="stat-item">
              <span class="stat-label">总出勤天数：</span>
              <span class="stat-value normal">{{ attendanceDetailData.totalNormalDays }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">迟到次数：</span>
              <span class="stat-value late">{{ attendanceDetailData.totalLateDays }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">缺勤天数：</span>
              <span class="stat-value absent">{{ attendanceDetailData.totalAbsentDays }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">平均出勤率：</span>
              <span class="stat-value rate">{{ attendanceDetailData.avgAttendanceRate }}%</span>
            </div>
          </div>
        </div>

        <el-table
          :data="attendanceDetailData.dailyList"
          border
          max-height="300"
          class="daily-detail-table"
        >
          <el-table-column label="日期" prop="date" width="120" align="center" />
          <el-table-column label="星期" prop="weekday" width="80" align="center" />
          <el-table-column label="签到时间" prop="checkInTime" width="100" align="center">
            <template #default="scope">
              <span :class="{'late-time': scope.row.isLate}">
                {{ scope.row.checkInTime || '未签到' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="签退时间" prop="checkOutTime" width="100" align="center">
            <template #default="scope">
              <span :class="{'early-leave': scope.row.isEarlyLeave}">
                {{ scope.row.checkOutTime || '未签退' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="工作时长" prop="workHours" width="100" align="center">
            <template #default="scope">
              <span class="work-hours">{{ scope.row.workHours }}h</span>
            </template>
          </el-table-column>
          <el-table-column label="考勤状态" prop="status" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getAttendanceStatusType(scope.row.status)" size="small">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remark" align="center" show-overflow-tooltip />
        </el-table>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAttendanceDetailDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="AttendanceAnalysis">
import { ref, reactive, computed, onMounted, nextTick, watchEffect } from 'vue';
import { TrendCharts, Clock, Warning, CircleCheck, User, OfficeBuilding } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import TimeAnalysisSelector from '@/components/TimeAnalysisSelector/index.vue';

const loading = ref(true);
const activeTab = ref('charts');
const showVehicleDetailDialog = ref(false);
const vehicleDetailData = ref(null);
const showAttendanceDetailDialog = ref(false);
const attendanceDetailData = ref(null);

// 表单引用
const queryRef = ref();
const timeAnalysisSelectorRef = ref();
const attendanceChartRef = ref();
let attendanceChart = null;

// 分页数据
const total = ref(0);

// 表格数据
const detailTableData = ref([]);
const dynamicDateColumns = ref([]);

// 工时标签（响应式数据，只在查询时更新）
const workHoursLabel = ref('月度工时');

// 统计数据
const statsData = ref({
  attendanceRate: '94.5',
  lateCount: '26',
  absentCount: '8',
  normalCount: '342'
});

// 树形相关
const deptName = ref('');
const deptTreeRef = ref();

// 组织机构树数据
const deptOptions = ref([]);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  analysisType: '2', // 默认月度分析
  startTime: undefined,
  endTime: undefined,
  dateRange: null,
  driverName: null,
  workNumber: null,
  deptId: null
});

onMounted(() => {
  getTreeSelect();
  // 初始化工时标签
  updateWorkHoursLabel();
  handleTabChange(activeTab.value);
  nextTick(() => {
    initCharts();
  });
});

/** 组织机构树筛选 */
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选组织机构树 */
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

/** 查询组织机构下拉树结构 */
async function getTreeSelect() {
  try {
    deptOptions.value = [
      {
        id: 1,
        label: '总公司',
        children: [
          {
            id: 2,
            label: '运营部',
            children: [
              { id: 3, label: '第一车队' },
              { id: 4, label: '第二车队' },
              { id: 5, label: '第三车队' }
            ]
          },
          {
            id: 6,
            label: '维修部',
            children: []
          }
        ]
      }
    ];
  } catch (error) {
    console.error('获取组织机构树失败:', error);
  }
}

/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.deptId = data.id;
  queryParams.driverName = null;
  queryParams.workNumber = null;
  handleQuery();
}

/** Tab切换事件 */
function handleTabChange(tabName) {
  queryParams.pageNum = 1;
  activeTab.value = tabName;
  loading.value = true;

  switch (tabName) {
    case 'charts':
      // 图表分析不需要加载表格数据，只更新图表
      loading.value = false;
      break;
    case 'table':
      getList();
      break;
  }
}

/** 时间分析参数变化处理 */
function handleTimeAnalysisChange(params) {
  queryParams.analysisType = params.analysisType;
  queryParams.startTime = params.startTime;
  queryParams.endTime = params.endTime;

  // 更新趋势图
  if (activeTab.value === 'charts') {
    updateAttendanceChart();
  }
}

/** 初始化考勤趋势图表 */
function initAttendanceChart() {
  if (!attendanceChartRef.value) return;

  attendanceChart = echarts.init(attendanceChartRef.value);
  updateAttendanceChart();
}

/** 更新考勤趋势图表 */
function updateAttendanceChart() {
  if (!attendanceChart) return;

  const xAxisData = generateXAxisData();
  const attendanceData = generateSeriesData(xAxisData.length, 85, 98);
  const lateData = generateSeriesData(xAxisData.length, 2, 8);

  function generateXAxisData() {
    const analysisType = queryParams.analysisType;
    const startTime = queryParams.startTime;
    const endTime = queryParams.endTime;

    if (!startTime || !endTime) {
      // 如果没有时间参数，使用默认数据
      return ['1月', '2月', '3月', '4月', '5月', '6月'];
    }

    const xData = [];
    const start = new Date(startTime);
    const end = new Date(endTime);

    switch(analysisType) {
      case '0': // 年度
        for (let year = start.getFullYear(); year <= end.getFullYear(); year++) {
          xData.push(`${year}年`);
        }
        break;
      case '2': // 月度
        for (let d = new Date(start.getFullYear(), start.getMonth(), 1); d <= end; d.setMonth(d.getMonth() + 1)) {
          xData.push(`${d.getMonth() + 1}月`);
        }
        break;
      case '3': // 日
        for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
          xData.push(`${d.getMonth() + 1}/${d.getDate()}`);
        }
        break;
      default:
        return ['1月', '2月', '3月', '4月', '5月', '6月'];
    }

    return xData.length > 0 ? xData : ['1月', '2月', '3月', '4月', '5月', '6月'];
  }

  function generateSeriesData(length, min, max) {
    const data = [];
    for (let i = 0; i < length; i++) {
      data.push((Math.random() * (max - min) + min).toFixed(1));
    }
    return data;
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' }
    },
    legend: {
      data: ['出勤率', '迟到率'],
      textStyle: { color: '#e5e7eb' },
      top: '5%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: { color: '#94a3b8' },
      axisLine: { lineStyle: { color: '#374151' } }
    },
    yAxis: {
      type: 'value',
      name: '百分比(%)',
      nameTextStyle: { color: '#94a3b8' },
      axisLabel: { color: '#94a3b8' },
      axisLine: { lineStyle: { color: '#374151' } },
      splitLine: { lineStyle: { color: '#374151' } }
    },
    series: [
      {
        name: '出勤率',
        type: 'line',
        data: attendanceData,
        smooth: true,
        itemStyle: { color: '#67C23A' },
        lineStyle: { color: '#67C23A' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
            { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
          ])
        }
      },
      {
        name: '迟到率',
        type: 'line',
        data: lateData,
        smooth: true,
        itemStyle: { color: '#E6A23C' },
        lineStyle: { color: '#E6A23C' }
      }
    ]
  };

  attendanceChart.setOption(option);
}

/** 获取分析类型文本 */
function getAnalysisTypeText(type) {
  const typeMap = {
    '0': '年度统计',
    '2': '月度统计',
    '3': '日统计'
  };
  return typeMap[type] || '月度统计';
}

// 初始化图表
function initCharts() {
  initAttendanceChart();
}

// 获取表格数据
function getList() {
  loading.value = true;

  setTimeout(() => {
    // 生成动态日期列
    generateDynamicColumns();

    // 生成表格数据
    const mockData = generateTableData();
    detailTableData.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    total.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 生成动态日期列
function generateDynamicColumns() {
  const analysisType = queryParams.analysisType;
  const startTime = queryParams.startTime;
  const endTime = queryParams.endTime;

  dynamicDateColumns.value = [];

  if (!startTime || !endTime) {
    // 使用默认日期列
    for (let i = 1; i <= 7; i++) {
      const date = new Date();
      date.setDate(date.getDate() - (7 - i));
      const dateStr = `${date.getMonth() + 1}/${date.getDate()}`;

      dynamicDateColumns.value.push({
        prop: `date${i}`,
        label: dateStr,
        normalProp: `normal_${i}`,
        lateProp: `late_${i}`,
        absentProp: `absent_${i}`,
        rateProp: `rate_${i}`
      });
    }
    return;
  }

  const start = new Date(startTime);
  const end = new Date(endTime);
  let index = 1;

  switch(analysisType) {
    case '0': // 年度
      for (let year = start.getFullYear(); year <= end.getFullYear(); year++) {
        dynamicDateColumns.value.push({
          prop: `date${index}`,
          label: `${year}年`,
          normalProp: `normal_${index}`,
          lateProp: `late_${index}`,
          absentProp: `absent_${index}`,
          rateProp: `rate_${index}`
        });
        index++;
      }
      break;
    case '2': // 月度
      for (let d = new Date(start.getFullYear(), start.getMonth(), 1); d <= end; d.setMonth(d.getMonth() + 1)) {
        dynamicDateColumns.value.push({
          prop: `date${index}`,
          label: `${d.getMonth() + 1}月`,
          normalProp: `normal_${index}`,
          lateProp: `late_${index}`,
          absentProp: `absent_${index}`,
          rateProp: `rate_${index}`
        });
        index++;
      }
      break;
    case '3': // 日
      for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
        dynamicDateColumns.value.push({
          prop: `date${index}`,
          label: `${d.getMonth() + 1}/${d.getDate()}`,
          normalProp: `normal_${index}`,
          lateProp: `late_${index}`,
          absentProp: `absent_${index}`,
          rateProp: `rate_${index}`
        });
        index++;
      }
      break;
  }
}

// 生成表格模拟数据
function generateTableData() {
  const drivers = [
    { driverName: '张志明', workNumber: 'D001', deptName: '第一车队' },
    { driverName: '李华强', workNumber: 'D002', deptName: '第二车队' },
    { driverName: '王建国', workNumber: 'D003', deptName: '第三车队' },
    { driverName: '陈美丽', workNumber: 'D004', deptName: '第一车队' },
    { driverName: '刘德华', workNumber: 'D005', deptName: '维修部' },
    { driverName: '赵雅芝', workNumber: 'D006', deptName: '第二车队' },
    { driverName: '孙悟空', workNumber: 'D007', deptName: '第三车队' },
    { driverName: '朱小明', workNumber: 'D008', deptName: '第一车队' }
  ];

  return drivers.map((driver, index) => {
    const rowData = {
      id: index + 1,
      ...driver,
      totalAttendanceRate: (Math.random() * 15 + 85).toFixed(1),
      totalWorkHours: Math.floor(Math.random() * 50 + 150) + 'h',
      vehicleCount: Math.floor(Math.random() * 3 + 1),
      phoneNumber: '138****' + (1000 + Math.floor(Math.random() * 9000)),
      remark: Math.random() > 0.7 ? '表现优秀' : null
    };

    // 为每个动态列生成数据
    dynamicDateColumns.value.forEach((col, colIndex) => {
      const normalDays = Math.floor(Math.random() * 25 + 20);
      const lateDays = Math.floor(Math.random() * 3);
      const absentDays = Math.floor(Math.random() * 2);
      const totalDays = normalDays + lateDays + absentDays;
      const rate = totalDays > 0 ? ((normalDays / totalDays) * 100).toFixed(1) : '0.0';

      rowData[col.normalProp] = normalDays;
      rowData[col.lateProp] = lateDays;
      rowData[col.absentProp] = absentDays;
      rowData[col.rateProp] = rate;
    });

    return rowData;
  });
}

// 更新工时标签
function updateWorkHoursLabel() {
  const analysisType = queryParams.analysisType;
  switch(analysisType) {
    case '0':
      workHoursLabel.value = '年度工时';
      break;
    case '2':
      workHoursLabel.value = '月度工时';
      break;
    case '3':
      workHoursLabel.value = '日工时';
      break;
    default:
      workHoursLabel.value = '月度工时';
  }
}

// 处理车辆详情
function handleVehicleDetail(row) {
  vehicleDetailData.value = {
    driverName: row.driverName,
    vehicles: [
      { plateNumber: '京A12345', vehicleNumber: 'V001', status: '在用' },
      { plateNumber: '京A12346', vehicleNumber: 'V002', status: '维修中' }
    ]
  };
  showVehicleDetailDialog.value = true;
}

// 导出表格
function handleExportTable() {
  ElMessage.success('导出成功');
}

// 更新查询和重置函数
function handleQuery() {
  queryParams.pageNum = 1;

  // 更新工时标签
  updateWorkHoursLabel();

  // 如果在图表分析Tab，更新图表
  if (activeTab.value === 'charts') {
    updateAttendanceChart();
  } else {
    handleTabChange(activeTab.value);
  }
}

function resetQuery() {
  queryRef.value?.resetFields();
  queryParams.deptId = null;
  timeAnalysisSelectorRef.value?.reset();
  deptTreeRef.value?.setCurrentKey(null);

  // 重置工时标签为默认值
  workHoursLabel.value = '月度工时';

  handleQuery();
}

function handleSelectionChange(selection) {
  // 处理表格选择
}

function getAttendanceStatusType(status) {
  const typeMap = {
    'normal': 'success',
    'late': 'warning',
    'early': 'info',
    'absent': 'danger'
  };
  return typeMap[status] || 'info';
}

function getAttendanceStatusText(status) {
  const textMap = {
    'normal': '正常',
    'late': '迟到',
    'early': '早退',
    'absent': '缺勤'
  };
  return textMap[status] || '未知';
}

function getShiftType(shift) {
  const typeMap = {
    '早班': 'success',
    '晚班': 'warning',
    '全天班': ''
  };
  return typeMap[shift] || 'info';
}

// 考勤详情弹窗标题
const attendanceDetailTitle = computed(() => {
  if (!attendanceDetailData.value) return '考勤详情';

  if (attendanceDetailData.value.isDailyList) {
    return `${attendanceDetailData.value.driverName} - ${attendanceDetailData.value.periodText} 每日明细`;
  }

  return `${attendanceDetailData.value.driverName} - ${attendanceDetailData.value.date} 考勤详情`;
});

// 考勤详情弹窗宽度
const attendanceDetailDialogWidth = computed(() => {
  if (!attendanceDetailData.value) return '600px';
  return attendanceDetailData.value.isDailyList ? '1000px' : '600px';
});

// 处理考勤详情点击
function handleAttendanceDetail(row, dateCol) {
  // 判断当前统计方式是否为日统计
  const isDaily = queryParams.analysisType === '3';

  if (isDaily) {
    // 日统计：显示单日考勤详情
    const detailData = generateAttendanceDetailData(row, dateCol);
    attendanceDetailData.value = detailData;
  } else {
    // 非日统计：显示该时间段内每天的明细表格
    const dailyDetailData = generateDailyDetailData(row, dateCol);
    attendanceDetailData.value = dailyDetailData;
  }

  showAttendanceDetailDialog.value = true;
}

// 生成考勤详情模拟数据
function generateAttendanceDetailData(row, dateCol) {
  const checkInHour = Math.floor(Math.random() * 2) + 7; // 7-8点之间
  const checkInMinute = Math.floor(Math.random() * 60);
  const checkOutHour = Math.floor(Math.random() * 2) + 17; // 17-18点之间
  const checkOutMinute = Math.floor(Math.random() * 60);

  const isLate = checkInHour > 8 || (checkInHour === 8 && checkInMinute > 30);
  const isEarlyLeave = checkOutHour < 17 || (checkOutHour === 17 && checkOutMinute < 30);

  const workStartTime = new Date();
  workStartTime.setHours(checkInHour, checkInMinute, 0, 0);

  const workEndTime = new Date();
  workEndTime.setHours(checkOutHour, checkOutMinute, 0, 0);

  const workHours = ((workEndTime.getTime() - workStartTime.getTime()) / (1000 * 60 * 60)).toFixed(1);
  const overtimeHours = Math.max(0, workHours - 8).toFixed(1);

  const status = isLate ? '迟到' : (isEarlyLeave ? '早退' : '正常');

  const locations = [
    '总公司调度中心 (116.397°, 39.916°)',
    '第一车队办公室 (116.405°, 39.922°)',
    '第二车队办公室 (116.389°, 39.908°)',
    '维修车间 (116.412°, 39.935°)'
  ];

  const workSummaries = [
    '完成早班运营任务，车辆状态良好，无异常情况。',
    '参与车辆例行检查，发现并处理2项小问题。',
    '执行晚班运营，客流量较大，安全行驶无事故。',
    '协助新员工培训，分享安全驾驶经验。'
  ];

  return {
    isDailyList: false,
    driverName: row.driverName,
    workNumber: row.workNumber,
    date: dateCol.label,
    checkInTime: `${checkInHour.toString().padStart(2, '0')}:${checkInMinute.toString().padStart(2, '0')}`,
    checkOutTime: `${checkOutHour.toString().padStart(2, '0')}:${checkOutMinute.toString().padStart(2, '0')}`,
    workHours: workHours,
    overtimeHours: overtimeHours,
    status: status,
    isLate: isLate,
    isEarlyLeave: isEarlyLeave,
    location: locations[Math.floor(Math.random() * locations.length)],
    workSummary: Math.random() > 0.3 ? workSummaries[Math.floor(Math.random() * workSummaries.length)] : null
  };
}

// 生成每日明细数据
function generateDailyDetailData(row, dateCol) {
  const analysisType = queryParams.analysisType;
  const periodText = getPeriodText(dateCol.label, analysisType);

  // 根据统计类型生成日期范围
  const dateRange = generateDateRange(dateCol.label, analysisType);

  // 生成每日考勤数据
  const dailyList = dateRange.map((date, index) => {
    const checkInHour = Math.floor(Math.random() * 2) + 7; // 7-8点之间
    const checkInMinute = Math.floor(Math.random() * 60);
    const checkOutHour = Math.floor(Math.random() * 2) + 17; // 17-18点之间
    const checkOutMinute = Math.floor(Math.random() * 60);

    const isLate = checkInHour > 8 || (checkInHour === 8 && checkInMinute > 30);
    const isEarlyLeave = checkOutHour < 17 || (checkOutHour === 17 && checkOutMinute < 30);

    const workStartTime = new Date();
    workStartTime.setHours(checkInHour, checkInMinute, 0, 0);

    const workEndTime = new Date();
    workEndTime.setHours(checkOutHour, checkOutMinute, 0, 0);

    const workHours = ((workEndTime.getTime() - workStartTime.getTime()) / (1000 * 60 * 60)).toFixed(1);

    // 随机生成一些缺勤和休息日
    const isAbsent = Math.random() < 0.05; // 5%概率缺勤
    const isRest = date.getDay() === 0 || date.getDay() === 6; // 周末

    let status = '正常';
    let checkInTime = `${checkInHour.toString().padStart(2, '0')}:${checkInMinute.toString().padStart(2, '0')}`;
    let checkOutTime = `${checkOutHour.toString().padStart(2, '0')}:${checkOutMinute.toString().padStart(2, '0')}`;
    let remark = '';

    if (isAbsent) {
      status = '缺勤';
      checkInTime = '';
      checkOutTime = '';
      remark = '未到岗';
    } else if (isRest) {
      status = '休息';
      checkInTime = '';
      checkOutTime = '';
      remark = '周末休息';
    } else if (isLate) {
      status = '迟到';
      remark = '迟到' + Math.floor(Math.random() * 30 + 5) + '分钟';
    } else if (isEarlyLeave) {
      status = '早退';
      remark = '早退' + Math.floor(Math.random() * 30 + 5) + '分钟';
    }

    return {
      date: `${date.getMonth() + 1}/${date.getDate()}`,
      weekday: ['日', '一', '二', '三', '四', '五', '六'][date.getDay()],
      checkInTime: checkInTime,
      checkOutTime: checkOutTime,
      workHours: isAbsent || isRest ? '0.0' : workHours,
      status: status,
      isLate: isLate && !isAbsent && !isRest,
      isEarlyLeave: isEarlyLeave && !isAbsent && !isRest,
      remark: remark
    };
  });

  // 计算统计数据
  const totalNormalDays = dailyList.filter(item => item.status === '正常').length;
  const totalLateDays = dailyList.filter(item => item.status === '迟到').length;
  const totalAbsentDays = dailyList.filter(item => item.status === '缺勤').length;
  const totalWorkDays = dailyList.filter(item => item.status !== '休息').length;
  const avgAttendanceRate = totalWorkDays > 0 ? ((totalNormalDays + totalLateDays) / totalWorkDays * 100).toFixed(1) : '0.0';

  return {
    isDailyList: true,
    driverName: row.driverName,
    workNumber: row.workNumber,
    periodText: periodText,
    dailyList: dailyList,
    totalNormalDays: totalNormalDays,
    totalLateDays: totalLateDays,
    totalAbsentDays: totalAbsentDays,
    avgAttendanceRate: avgAttendanceRate
  };
}

// 获取周期文本
function getPeriodText(label, analysisType) {
  const currentYear = new Date().getFullYear();

  switch(analysisType) {
    case '0': // 年度
      return label;
    case '2': // 月度
      return `${currentYear}年${label}`;
    case '3': // 日
      return `${currentYear}年${label}`;
    default:
      return label;
  }
}

// 生成日期范围
function generateDateRange(label, analysisType) {
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth();
  const dates = [];

  switch(analysisType) {
    case '0': // 年度 - 生成该年每个月的代表日期
      const year = parseInt(label.replace('年', ''));
      for (let month = 0; month < 12; month++) {
        // 每个月取15号作为代表
        dates.push(new Date(year, month, 15));
      }
      break;

    case '2': // 月度 - 生成该月每一天
      const month = parseInt(label.replace('月', '')) - 1;
      const daysInMonth = new Date(currentYear, month + 1, 0).getDate();
      for (let day = 1; day <= daysInMonth; day++) {
        dates.push(new Date(currentYear, month, day));
      }
      break;

    case '3': // 日 - 生成当天（简化处理）
      const today = new Date();
      dates.push(today);
      break;

    default:
      // 默认生成最近7天
      for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        dates.push(date);
      }
  }

  return dates;
}
</script>

<style scoped>
.attendance-analysis {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* Tab区域 */
.tabs-section {
  margin-bottom: 20px;
}

.tab-content {
  padding-top: 20px;
}

/* 筛选条件区域 */
.filter-section {
  margin-bottom: 24px;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  height: 400px;
}

.chart-card .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.chart-card .chart-header h3 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.chart-container {
  height: 320px;
  width: 100%;
}

/* 表格样式 */
.table-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  min-height: 400px;
  margin-bottom: 20px;
}

.filter-card {
  background: rgba(15, 23, 42, 0.98) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 24px;
}

.right-content {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  backdrop-filter: blur(10px);
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(41, 52, 70, 0.6) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
  border-color: rgba(96, 165, 250, 0.4);
}

.stat-card.attendance {
  border-left: 4px solid #409EFF;
}

.stat-card.late {
  border-left: 4px solid #E6A23C;
}

.stat-card.absent {
  border-left: 4px solid #F56C6C;
}

.stat-card.normal {
  border-left: 4px solid #67C23A;
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #60a5fa;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #f8fafc;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #94a3b8;
}

/* 表格图例样式 */
.table-legend {
  display: flex;
  gap: 16px;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #94a3b8;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.normal {
  background-color: #67C23A;
}

.legend-color.late {
  background-color: #E6A23C;
}

.legend-color.absent {
  background-color: #F56C6C;
}

.legend-color.rate {
  background-color: #409EFF;
}

.table-toolbar {
  display: flex;
  gap: 8px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.table-header h4 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

/* 考勤单元格样式 */
.attendance-cell {
  text-align: center;
  font-size: 12px;
  line-height: 1.2;
}

.normal-attendance {
  color: #67C23A;
  font-weight: 600;
}

.late-count {
  color: #E6A23C;
  font-weight: 600;
}

.absent-count {
  color: #F56C6C;
  font-weight: 600;
}

.attendance-rate-cell {
  color: #409EFF;
  font-weight: 600;
}

/* 表格样式 */
.attendance-rate {
  color: #67C23A;
  font-weight: 600;
}

.late-high {
  color: #E6A23C;
  font-weight: 600;
}

.absent-high {
  color: #F56C6C;
  font-weight: 600;
}

/* 搜索框 */
.search-box {
  margin-bottom: 16px;
}

/* 树形组件样式 */
.dept-tree {
  background: transparent;
  color: #e5e7eb;
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
}

.tree-node {
  display: flex;
  align-items: center;
  font-size: 14px;
  flex: 1;
}

.dept-icon {
  margin-right: 8px;
  color: #60a5fa;
  font-size: 16px;
  flex-shrink: 0;
}

.driver-icon {
  margin-right: 8px;
  color: #34d399;
  font-size: 16px;
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  min-width: 0;
  white-space: nowrap;
}

.node-label.dept {
  font-weight: 500;
  color: #f1f5f9;
}

.node-label.driver {
  color: #94a3b8;
}

.work-number {
  color: #64748b;
  font-size: 12px;
  margin-left: 4px;
  flex-shrink: 0;
}

/* 详情弹窗 */
.detail-content {
  max-height: 500px;
  overflow-y: auto;
}

/* Element UI组件覆盖 */
:deep(.el-form-item__label) {
  color: #e5e7eb !important;
}

:deep(.el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e5e7eb !important;
}

:deep(.el-input__inner) {
  color: #e5e7eb !important;
}

:deep(.el-input__inner::placeholder) {
  color: #64748b !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
}

:deep(.el-table) {
  background: transparent !important;
  color: #e5e7eb !important;
  height: 100% !important;
  flex: 1 !important;
}

:deep(.el-table tr) {
  background: rgba(15, 23, 42, 0.6) !important;
}

:deep(.el-table tr:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
}

:deep(.el-table th.el-table__cell) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #f8fafc !important;
  border-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-table td.el-table__cell) {
  border-color: rgba(147, 197, 253, 0.1) !important;
  color: #e5e7eb !important;
}

:deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.8);
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  background: transparent;
  border: none;
  color: #94a3b8;
  transition: all 0.3s ease;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item:hover) {
  background: rgba(59, 130, 246, 0.1);
  color: #93c5fd;
}

/* Element Tree组件覆盖样式 */
:deep(.el-tree-node) {
  background: transparent;
}

:deep(.el-tree-node:hover > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 6px;
}

:deep(.el-tree-node__content) {
  background: transparent !important;
  border: none !important;
  color: #e5e7eb !important;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

:deep(.el-tree-node__content:hover) {
  background-color: rgba(59, 130, 246, 0.15) !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.2) !important;
  color: #93c5fd !important;
}

:deep(.el-tree-node__expand-icon) {
  color: #64748b !important;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-tree .el-tree-node__children) {
  overflow: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .attendance-analysis {
    padding: 10px;
  }

  .stats-section .el-col {
    margin-bottom: 16px;
  }
}

/* 考勤详情弹窗样式 */
.attendance-detail-content {
  padding: 20px 0;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px 24px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.detail-item label {
  font-weight: 600;
  color: #94a3b8;
  min-width: 80px;
  margin-right: 12px;
  font-size: 14px;
}

.detail-item span {
  color: #e5e7eb;
  font-size: 14px;
  flex: 1;
}

.late-time {
  color: #E6A23C !important;
  font-weight: 600;
}

.early-leave {
  color: #F56C6C !important;
  font-weight: 600;
}

.work-hours {
  color: #67C23A !important;
  font-weight: 600;
}

.overtime-hours {
  color: #409EFF !important;
  font-weight: 600;
}

.gps-location {
  font-family: monospace;
  color: #60a5fa !important;
}

.work-summary {
  margin-top: 20px;
  padding: 16px;
  background: rgba(30, 41, 59, 0.4);
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.work-summary h4 {
  margin: 0 0 12px 0;
  color: #f8fafc;
  font-size: 14px;
  font-weight: 600;
}

.work-summary p {
  margin: 0;
  color: #cbd5e1;
  line-height: 1.6;
  font-size: 13px;
}

/* 让考勤单元格具有点击效果 */
.attendance-cell {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 4px 8px;
  margin: -4px -8px;
}

.attendance-cell:hover {
  background-color: rgba(59, 130, 246, 0.1);
  transform: scale(1.02);
}

/* 每日明细表格样式 */
.daily-detail-content {
  padding: 0;
}

.daily-detail-header {
  background: rgba(30, 41, 59, 0.4);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

.period-info {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.period-label {
  color: #94a3b8;
  font-weight: 600;
  font-size: 14px;
}

.period-value {
  color: #f8fafc;
  font-weight: 600;
  font-size: 16px;
  margin-left: 8px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: rgba(15, 23, 42, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

.stat-label {
  color: #94a3b8;
  font-size: 12px;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
}

.stat-value.normal {
  color: #67C23A;
}

.stat-value.late {
  color: #E6A23C;
}

.stat-value.absent {
  color: #F56C6C;
}

.stat-value.rate {
  color: #409EFF;
}

.daily-detail-table {
  background: transparent !important;
}

:deep(.daily-detail-table .el-table) {
  background: rgba(15, 23, 42, 0.6) !important;
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 8px !important;
}

:deep(.daily-detail-table .el-table tr) {
  background: rgba(15, 23, 42, 0.4) !important;
}

:deep(.daily-detail-table .el-table tr:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
}

:deep(.daily-detail-table .el-table th.el-table__cell) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #f8fafc !important;
  border-color: rgba(147, 197, 253, 0.2) !important;
  font-weight: 600;
}

:deep(.daily-detail-table .el-table td.el-table__cell) {
  border-color: rgba(147, 197, 253, 0.1) !important;
  color: #e5e7eb !important;
}

/* 考勤详情弹窗样式优化 */
:deep(.attendance-detail-dialog) {
  max-height: 90vh;
}

:deep(.attendance-detail-dialog .el-dialog__body) {
  max-height: 75vh;
  overflow-y: auto;
  padding: 20px;
}

/* 每日明细内容区域 */
.daily-detail-content {
  max-height: 65vh;
  overflow-y: auto;
}

/* 单日考勤详情内容区域 */
.attendance-detail-content {
  max-height: 60vh;
  overflow-y: auto;
}
</style>
