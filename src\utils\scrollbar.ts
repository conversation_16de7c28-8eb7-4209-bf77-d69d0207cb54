/**
 * 全局滚动条配置
 * 使用 OverlayScrollbars 提供跨浏览器一致的滚动条体验
 */
import { OverlayScrollbars } from 'overlayscrollbars';
import type { PartialOptions } from 'overlayscrollbars';

// 浅色主题滚动条配置
const lightThemeOptions: PartialOptions = {
  scrollbars: {
    theme: 'os-theme-light',
    visibility: 'auto',
    autoHide: 'move',
    autoHideDelay: 1300,
    dragScroll: true,
    clickScroll: true,
    pointers: ['mouse', 'touch', 'pen']
  },
  overflow: {
    x: 'hidden',
    y: 'scroll'
  }
};

// 深色主题滚动条配置
const darkThemeOptions: PartialOptions = {
  scrollbars: {
    theme: 'os-theme-dark',
    visibility: 'auto',
    autoHide: 'move',
    autoHideDelay: 1300,
    dragScroll: true,
    clickScroll: true,
    pointers: ['mouse', 'touch', 'pen']
  },
  overflow: {
    x: 'hidden',
    y: 'scroll'
  }
};

/**
 * 获取当前主题的滚动条配置
 */
export function getScrollbarOptions(): PartialOptions {
  const isDark = document.documentElement.classList.contains('dark');
  return isDark ? darkThemeOptions : lightThemeOptions;
}

/**
 * 初始化元素的滚动条
 */
export function initScrollbar(element: HTMLElement): OverlayScrollbars | null {
  if (!element) return null;

  const options = getScrollbarOptions();
  return OverlayScrollbars(element, options);
}

/**
 * 更新所有滚动条主题
 */
export function updateScrollbarTheme() {
  try {
    // 检查 OverlayScrollbars 是否有 instances 方法
    if (typeof OverlayScrollbars.instances === 'function') {
      const instances = OverlayScrollbars.instances();
      const options = getScrollbarOptions();

      instances.forEach((instance) => {
        if (instance && typeof instance.options === 'function') {
          instance.options(options);
        }
      });
    } else {
      // 如果没有 instances 方法，则重新初始化所有滚动条元素
      const scrollElements = document.querySelectorAll('[data-overlayscrollbars-initialize]');
      const options = getScrollbarOptions();

      scrollElements.forEach((element) => {
        const instance = OverlayScrollbars.getInstance(element as HTMLElement);
        if (instance && typeof instance.options === 'function') {
          instance.options(options);
        }
      });
    }
  } catch (error) {
    console.warn('滚动条主题更新失败:', error);
  }
}

/**
 * 销毁滚动条实例
 */
export function destroyScrollbar(element: HTMLElement) {
  const instance = OverlayScrollbars.getInstance(element);
  if (instance) {
    instance.destroy();
  }
}
