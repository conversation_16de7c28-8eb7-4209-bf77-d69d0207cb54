import request from '@/utils/request'

export interface F31ParseResult {
  fileName?: string
  fileSize?: number
  success?: boolean
  errorMessage?: string
  fileHeader?: {
    flag?: string
    fileType?: string
    version?: number
    fileLength?: number
    actualLength?: number
  }
  libraryHeader?: {
    indexCodeType?: number
    infoSizeCountMax?: number
    hdrLength?: number
    infoSizeEach?: number
    infoVersionCount?: number
    updateVersionThis?: number
    updateVersionFrom?: number
  }
  versionList?: Array<{
    infoType?: number
    infoVersion?: number
  }>
  stations?: Array<{
    indexCode?: number
    infoType?: number
    infoTypeName?: string
    stationName?: string
    memo?: string
  }>
  totalRecords?: number
}

export interface SupportedFormats {
  fileExtensions: string[]
  description: string
  supportedTypes: string[]
  maxFileSize: number
  protocol: string
}

/**
 * 解析F31文件
 * @param file F31格式文件
 */
export function parseF31File(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/gateway/f31-parser/parse',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取文件十六进制dump
 * @param file F31格式文件
 * @param maxBytes 最大字节数，默认1024
 */
export function getHexDump(file: File, maxBytes: number = 1024) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('maxBytes', maxBytes.toString())
  
  return request({
    url: '/gateway/f31-parser/hex-dump',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取支持的文件格式
 */
export function getSupportedFormats() {
  return request({
    url: '/gateway/f31-parser/supported-formats',
    method: 'get'
  })
}