  /* 大屏容器全局样式重置 */
.big-screen-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  color: #f8fafc;
  font-family: 'Microsoft YaHei', sans-serif;
  overflow: hidden;
  position: relative;
}

/* 重置所有子元素的边框样式 */
.big-screen-container * {
  box-sizing: border-box;
}

/* 重置所有Element UI组件在大屏中的默认样式 */
.big-screen-container .el-card {
  background: rgba(30, 41, 59, 0.95) !important;
  border: 1px solid #409eff !important;
  border-radius: 12px !important;
}

.big-screen-container .el-card .el-card__body {
  background: transparent !important;
  border: none !important;
}

/* 顶部标题栏美化 */
.screen-header {
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 100%);
  border-bottom: 2px solid transparent;
  border-image: linear-gradient(90deg, #409eff 0%, #722ed1 40%, #9f7aea 100%) 1;
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
}

.screen-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    rgba(64, 158, 255, 0.08) 0%,
    rgba(114, 46, 209, 0.12) 50%,
    rgba(159, 122, 234, 0.08) 100%);
  opacity: 0.4;
  z-index: -1;
}

.screen-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, rgba(114, 46, 209, 0.8) 50%, transparent 100%);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.header-left {
  display: flex;
  align-items: center;
  height: 100%;
}

.header-left .logo-section {
  display: flex;
  align-items: center;
  margin-right: 48px;
  transition: all 0.3s ease;
}

.header-left .logo-section:hover {
  transform: translateY(-1px);
}

.logo-section .logo-img {
  height: 36px;
  margin-right: 12px;
  filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.1));
}

.logo-section .logo-text {
  font-size: 20px;
  font-weight: 700;
  color: transparent !important;
  letter-spacing: 0.5px;
  white-space: nowrap;
  display: inline-block;
  background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 30%, #c7d2fe 60%, #a5b4fc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}


.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
}

.map-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.8) 100%);
  border: 1px solid rgba(64, 158, 255, 0.4);
  border-radius: 18px;
  padding: 10px 20px;
  backdrop-filter: blur(15px);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(64, 158, 255, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  height: auto;
  max-height: 45px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-group .control-label {
  font-size: 12px;
  font-weight: 600;
  color: #e0e7ff;
  white-space: nowrap;
  opacity: 0.9;
  line-height: 1;
  margin: 0;
  padding: 0;
}

.control-group .el-checkbox-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.control-group .el-checkbox {
  margin-right: 0;
  padding: 4px 10px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.08) 0%, rgba(103, 194, 58, 0.05) 100%);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 15px;
  transition: all 0.3s ease;
  font-size: 11px;
  height: 26px;
  display: flex;
  align-items: center;
  backdrop-filter: blur(5px);
}

.control-group .el-checkbox:hover {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.15) 0%, rgba(103, 194, 58, 0.1) 100%);
  border-color: rgba(64, 158, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.control-group .el-checkbox.is-checked {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.2) 0%, rgba(103, 194, 58, 0.15) 100%);
  border-color: rgba(103, 194, 58, 0.6);
  box-shadow: 0 0 16px rgba(103, 194, 58, 0.3);
}

.control-group .el-button-group {
  display: flex;
  gap: 4px;
  align-items: center;
}

.control-group .el-button {
  border-radius: 14px !important;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.08) 0%, rgba(103, 194, 58, 0.05) 100%) !important;
  border: 1px solid rgba(64, 158, 255, 0.2) !important;
  color: #e0e7ff !important;
  font-size: 11px !important;
  padding: 4px 12px !important;
  height: 26px !important;
  display: flex;
  align-items: center;
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-group .el-button:hover {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.15) 0%, rgba(103, 194, 58, 0.1) 100%) !important;
  border-color: rgba(64, 158, 255, 0.4) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.3);
}

/* Element UI 深度样式覆盖 - 顶部控制区域 */
:deep(.map-controls .el-checkbox) {
  color: #e0e7ff !important;
  height: 26px !important;
  align-items: center !important;
}

:deep(.map-controls .el-checkbox__label) {
  color: #e0e7ff !important;
  font-size: 11px !important;
  line-height: 1 !important;
  padding-left: 8px !important;
}

:deep(.map-controls .el-checkbox__input) {
  line-height: 1 !important;
}

:deep(.map-controls .el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #9f7aea !important;
  border-color: #9f7aea !important;
}

:deep(.map-controls .el-checkbox__input.is-checked + .el-checkbox__label) {
  color: #ffffff !important;
}

:deep(.map-controls .el-button-group .el-button) {
  height: 26px !important;
  line-height: 1 !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.map-controls .el-button-group .el-button span) {
  color: #e0e7ff !important;
  line-height: 1 !important;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-right .current-time {
  text-align: right;
  position: relative;
  z-index: 1;
}

.header-actions {
  display: flex;
  align-items: center;
}

.fullscreen-btn-header {
  width: 40px !important;
  height: 40px !important;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(103, 194, 58, 0.05) 100%) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 20px !important;
  transition: all 0.3s ease !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2) !important;
}

.fullscreen-btn-header:hover {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.2) 0%, rgba(103, 194, 58, 0.1) 100%) !important;
  border-color: rgba(64, 158, 255, 0.6) !important;
  transform: scale(1.08) translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.4) !important;
}

.time-display {
  font-size: 28px;
  font-weight: 800;
  color: transparent;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 50%, #9f7aea 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 15px rgba(64, 158, 255, 0.4),
  0 0 20px rgba(159, 122, 234, 0.3),
  0 2px 4px rgba(0, 0, 0, 0.3);
  font-family: 'Arial', 'Microsoft YaHei', monospace;
  position: relative;
  transition: all 0.3s ease;
}

.time-display::before {
  content: '';
  position: absolute;
  inset: -4px;
  background: radial-gradient(circle, rgba(64, 158, 255, 0.08) 0%, rgba(159, 122, 234, 0.06) 50%, transparent 70%);
  border-radius: 6px;
  opacity: 0;
  transition: all 0.3s ease;
}

.current-time:hover .time-display {
  transform: scale(1.05);
  text-shadow: 0 0 25px rgba(64, 158, 255, 0.6),
  0 0 30px rgba(159, 122, 234, 0.5),
  0 4px 8px rgba(0, 0, 0, 0.4);
}

.current-time:hover .time-display::before {
  opacity: 1;
}

.date-display {
  font-size: 12px;
  color: #94a3b8;
  margin-top: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.current-time:hover .date-display {
  color: #cbd5e1;
}


/* 主体内容 - 地图作为底图 */
.screen-main {
  height: calc(100vh - 70px);
  position: relative; /* 为悬浮面板提供定位基准 */
  overflow: hidden;
}

/* 地图区域 - 全屏底图 */
.map-section.fullscreen-map {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  z-index: 1; /* 底层 */
}


.map-section.fullscreen-map .map-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* 侧边面板布局 - 左右两个完整面板 */
.side-panel {
  /* 所有位置和尺寸由内联样式控制 */
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.side-panel .panel-card {
  background: transparent !important;
  border: none !important;
  border-radius: 12px !important;
  backdrop-filter: blur(15px) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
  overflow: hidden !important;
  position: relative;
}

/* 确保卡片内容在流光效果之上 */
.side-panel .panel-card > * {
  position: relative;
  z-index: 1;
}

/* 线条流光效果 - 紫色调 */
.side-panel .panel-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(114, 46, 209, 0.8) 30%,
    rgba(159, 122, 234, 0.9) 50%,
    rgba(114, 46, 209, 0.8) 70%,
    transparent 100%);
  animation: borderShimmerTop 3s infinite;
  z-index: -1;
}

/* 右边框流光 */
.side-panel .panel-card::after {
  content: '';
  position: absolute;
  top: -100%;
  right: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(180deg,
    transparent 0%,
    rgba(114, 46, 209, 0.8) 30%,
    rgba(159, 122, 234, 0.9) 50%,
    rgba(114, 46, 209, 0.8) 70%,
    transparent 100%);
  animation: borderShimmerRight 3s infinite 0.75s;
  z-index: -1;
}

/* 为每个面板添加底边和左边的流光 */
.side-panel .panel-card {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.8) !important;
}

/* 简化方案：只使用顶边和右边的流光线条 */

/* 边框流光动画 */
@keyframes borderShimmerTop {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes borderShimmerRight {
  0% { top: -100%; }
  100% { top: 100%; }
}

/* 为每个面板添加不同的动画延迟，创建异步效果 */
.left-panel .panel-card:nth-child(1)::before {
  animation-delay: 0s;
}

.left-panel .panel-card:nth-child(2)::before {
  animation-delay: 1s;
}

.right-panel .panel-card:nth-child(1)::before {
  animation-delay: 2s;
}

.right-panel .panel-card:nth-child(2)::before {
  animation-delay: 3s;
}

/* 确保侧边面板的卡片头部样式正确 */
.side-panel .card-header {
  padding: 12px 15px;
  background: rgba(64, 158, 255, 0.1);
  border-bottom: 1px solid rgba(64, 158, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 14px;
  color: #f8fafc;
  position: relative;
  overflow: hidden;
}

.side-panel .card-header .el-icon {
  color: #409eff;
  font-size: 16px;
}

/* 控制面板样式 */
.control-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.control-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-label {
  font-size: 13px;
  font-weight: 600;
  color: #67c23a;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.control-label::before {
  content: '';
  width: 3px;
  height: 12px;
  background: linear-gradient(135deg, #67c23a 0%, #409eff 100%);
  border-radius: 2px;
}

.control-section .el-checkbox-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  padding: 8px 0;
}

.control-section .el-checkbox {
  margin-right: 0;
  margin-bottom: 0;
  padding: 6px 8px;
  background: rgba(64, 158, 255, 0.1);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 6px;
  transition: all 0.3s ease;
  text-align: center;
  font-size: 12px;
}

.control-section .el-checkbox:hover {
  background: rgba(64, 158, 255, 0.2);
  border-color: #409eff;
  transform: translateX(2px);
}

.control-section .el-checkbox.is-checked {
  background: rgba(103, 194, 58, 0.2);
  border-color: #67c23a;
}

.control-section .el-button-group {
  display: flex;
  gap: 6px;
}

.control-section .el-button {
  flex: 1;
  border-radius: 6px !important;
  transition: all 0.3s ease;
  background: rgba(64, 158, 255, 0.1) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  color: #f8fafc !important;
  font-size: 12px !important;
  padding: 6px 8px !important;
}

.control-section .el-button:hover {
  background: rgba(64, 158, 255, 0.3) !important;
  border-color: #409eff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 下拉框样式美化 */
:deep(.control-section .el-select) {
  width: 100%;
}

:deep(.control-section .el-select .el-input__inner) {
  background: rgba(15, 23, 42, 0.8) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 6px !important;
  color: #f8fafc !important;
  transition: all 0.3s ease !important;
}

:deep(.control-section .el-select .el-input__inner:hover) {
  border-color: #409eff !important;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3) !important;
}

:deep(.control-section .el-select .el-input__inner:focus) {
  border-color: #67c23a !important;
  box-shadow: 0 0 8px rgba(103, 194, 58, 0.3) !important;
}

:deep(.el-select-dropdown) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item) {
  color: #f8fafc !important;
  background: transparent !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background: rgba(64, 158, 255, 0.2) !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item.selected) {
  background: rgba(103, 194, 58, 0.2) !important;
  color: #67c23a !important;
}


/* 状态面板内容 */
/* 状态面板内容美化 */
.status-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

/* 运营统计面板样式 */
.stats-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.stats-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 统计数据网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
  min-height: 60px;
}

.stat-item:hover {
  border-color: rgba(64, 158, 255, 0.5);
  background: rgba(30, 41, 59, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #67c23a;
  margin-bottom: 4px;
  line-height: 1;
  text-shadow: 0 0 8px rgba(103, 194, 58, 0.6);
  animation: dataValuePulse 2s ease-in-out infinite;
}

.stat-label {
  font-size: 11px;
  font-weight: 500;
  color: #94a3b8;
  text-align: center;
  opacity: 0.9;
  line-height: 1;
}

/* 运营效率样式 */
.efficiency-stats {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.efficiency-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.efficiency-item:hover {
  background: rgba(15, 23, 42, 0.8);
  border-color: rgba(64, 158, 255, 0.4);
  transform: translateX(2px);
}

.efficiency-label {
  font-size: 11px;
  color: #94a3b8;
  min-width: 50px;
  font-weight: 500;
}

.efficiency-bar {
  flex: 1;
  height: 8px;
  background: rgba(30, 41, 59, 0.8);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.efficiency-progress {
  height: 100%;
  background: linear-gradient(90deg, #67c23a 0%, #409eff 100%);
  border-radius: 4px;
  transition: width 0.8s ease;
  position: relative;
}

.efficiency-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: progressShine 2s infinite;
}

@keyframes progressShine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.efficiency-value {
  font-size: 11px;
  color: #67c23a;
  font-weight: 600;
  min-width: 40px;
  text-align: right;
}

/* 热门线路样式 */
.popular-routes {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.route-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.route-item:hover {
  background: rgba(15, 23, 42, 0.8);
  border-color: rgba(64, 158, 255, 0.4);
  transform: translateX(2px);
}

.route-name {
  font-size: 12px;
  color: #f8fafc;
  font-weight: 600;
}

.route-passengers {
  font-size: 11px;
  color: #67c23a;
  font-weight: 500;
}

/* 告警列表样式优化 */
.alert-list {
  padding: 0 20px 20px;
}

/* 车辆监控面板美化 */
.monitoring-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.monitoring-content .vehicle-items-mini {
  /* 移除固定高度，使用flex自适应 */
  overflow-y: auto;
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.vehicle-item-mini {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(64, 158, 255, 0.1);
  border-radius: 6px;
  background: rgba(15, 23, 42, 0.8);
  position: relative;
  overflow: hidden;
  min-height: 60px; /* 固定最小高度 */
  flex-shrink: 0; /* 防止被压缩 */
}

.vehicle-item-mini::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(180deg, #67c23a 0%, #409eff 100%);
  opacity: 0;
  transition: all 0.3s ease;
}

.vehicle-item-mini:last-child {
  margin-bottom: 0;
}

.vehicle-item-mini:hover {
  background: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.3);
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.vehicle-item-mini:hover::before {
  opacity: 1;
}

.vehicle-status-indicator-mini {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
}

.vehicle-status-indicator-mini::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: inherit;
}

.vehicle-status-indicator-mini.running {
  background: #67c23a;
  box-shadow: 0 0 8px rgba(103, 194, 58, 0.6);
  animation: pulse-green 2s infinite;
}

.vehicle-status-indicator-mini.stopped {
  background: #e6a23c;
  box-shadow: 0 0 6px rgba(230, 162, 60, 0.5);
}

.vehicle-status-indicator-mini.maintenance {
  background: #f56c6c;
  box-shadow: 0 0 6px rgba(245, 108, 108, 0.5);
}

.vehicle-status-indicator-mini.fault {
  background: #909399;
  box-shadow: 0 0 4px rgba(144, 147, 153, 0.4);
}

@keyframes pulse-green {
  0%, 100% {
    box-shadow: 0 0 8px rgba(103, 194, 58, 0.6);
  }
  50% {
    box-shadow: 0 0 16px rgba(103, 194, 58, 0.8), 0 0 24px rgba(103, 194, 58, 0.4);
  }
}

.vehicle-item-mini:hover .vehicle-status-indicator-mini {
  transform: scale(1.2);
}

.vehicle-info-mini {
  flex: 1;
  margin-right: 12px;
  min-width: 0;
}

.vehicle-number-mini {
  font-weight: 700;
  color: #f8fafc;
  font-size: 12px;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.vehicle-number-mini::after {
  content: '🚌';
  font-size: 10px;
  opacity: 0.7;
}

.vehicle-route-mini {
  font-size: 10px;
  color: #94a3b8;
  font-weight: 500;
}

.vehicle-data-mini {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: flex-end;
  min-width: 60px;
}

.data-value-mini {
  font-size: 10px;
  font-weight: 600;
  color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid rgba(103, 194, 58, 0.2);
  text-align: center;
  min-width: 50px;
  transition: all 0.3s ease;
}

.vehicle-item-mini:hover .data-value-mini {
  background: rgba(103, 194, 58, 0.2);
  border-color: rgba(103, 194, 58, 0.4);
  transform: scale(1.05);
}

/* 刷新按钮美化 */
.refresh-btn-mini {
  width: 28px !important;
  height: 28px !important;
  background: rgba(64, 158, 255, 0.1) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  color: #409eff !important;
  transition: all 0.3s ease !important;
}

.refresh-btn-mini:hover {
  background: rgba(64, 158, 255, 0.2) !important;
  border-color: #409eff !important;
  transform: rotate(180deg) scale(1.1) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
}


/* 迷你图表容器美化 */
.chart-container-mini {
  height: 180px !important;
  width: 100% !important;
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 8px;
  padding: 10px;
  position: relative;
}

.chart-container-mini::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #67c23a 0%, #409eff 50%, #e6a23c 100%);
  border-radius: 8px 8px 0 0;
}

/* 状态图例 - 2x2网格布局 */
.status-legend-mini {
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 8px;
  padding: 12px;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 8px;
}

.status-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 6px;
  transition: all 0.3s ease;
  min-height: 50px;
}

.status-card:hover {
  border-color: rgba(64, 158, 255, 0.5);
  background: rgba(30, 41, 59, 0.7);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.status-value {
  font-size: 18px;
  font-weight: bold;
  color: #f8fafc;
  margin-bottom: 4px;
  line-height: 1;
}

.status-name {
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  opacity: 0.9;
  line-height: 1;
}

/* 车辆监控 - 迷你版滚动条样式 */
.vehicle-items-mini::-webkit-scrollbar {
  width: 4px;
}

.vehicle-items-mini::-webkit-scrollbar-thumb {
  background: rgba(64, 158, 255, 0.3);
  border-radius: 2px;
}


.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  border: 1px solid #409eff;
  border-radius: 8px;
  overflow: hidden;
}

.map-canvas {
  width: 100%;
  height: 100%;
}

/* 顶部工具栏图例样式 */
.legend-group {
  max-width: 400px;
}

.header-legend-items {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.header-legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  color: #e0e7ff;
  white-space: nowrap;
  padding: 2px 6px;
  background: rgba(64, 158, 255, 0.05);
  border: 1px solid rgba(64, 158, 255, 0.15);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.header-legend-item:hover {
  background: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.3);
  transform: translateY(-1px);
}

.header-legend-marker {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.header-legend-marker.running {
  background: #67c23a;
  box-shadow: 0 0 4px rgba(103, 194, 58, 0.6);
}

.header-legend-marker.stopped {
  background: #e6a23c;
  box-shadow: 0 0 4px rgba(230, 162, 60, 0.6);
}

.header-legend-marker.maintenance {
  background: #f56c6c;
  box-shadow: 0 0 4px rgba(245, 108, 108, 0.6);
}

.header-legend-marker.fault {
  background: #909399;
  box-shadow: 0 0 4px rgba(144, 147, 153, 0.6);
}

.header-legend-marker.station {
  background: #409eff;
  box-shadow: 0 0 4px rgba(64, 158, 255, 0.6);
}

/* 地图筛选器面板 - 简化版 */
.map-filters-panel {
  position: absolute;
  top: 20px;
  right: 360px;
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 12px;
  padding: 16px;
  min-width: 280px;
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease;
}

/* 筛选面板头部 */
.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.filter-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #f8fafc;
}

.filter-title .el-icon {
  color: #409eff;
  font-size: 16px;
}

/* 隐藏按钮样式 */
.filter-hide-btn {
  width: 26px !important;
  height: 26px !important;
  background: rgba(64, 158, 255, 0.1) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  color: #409eff !important;
  transition: all 0.3s ease !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.filter-hide-btn:hover {
  background: rgba(64, 158, 255, 0.2) !important;
  border-color: #409eff !important;
  transform: scale(1.1) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
}

/* 关闭图标样式 */
.close-icon {
  position: relative;
  width: 12px;
  height: 12px;
  display: inline-block;
}

.close-icon::before,
.close-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 2px;
  background-color: #409eff;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.close-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.close-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.filter-hide-btn:hover .close-icon::before,
.filter-hide-btn:hover .close-icon::after {
  background-color: #ffffff;
}

/* 路线列表样式 */
.route-list-content {
  padding: 12px;
}

.route-list-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.route-list-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.route-list-item:hover {
  background: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.route-status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 12px;
  box-shadow: 0 0 8px currentColor;
}

.route-info {
  flex: 1;
  min-width: 0;
}

.route-name {
  font-size: 14px;
  font-weight: 600;
  color: #f8fafc;
  margin-bottom: 4px;
}

.route-description {
  font-size: 12px;
  color: #94a3b8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.route-data {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.data-value {
  font-size: 12px;
  color: #409eff;
  font-weight: 500;
}

/* 路线表格颜色指示器 */
.route-color-indicator {
  width: 20px;
  height: 12px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 路线列表弹窗样式 */
.route-list-dialog {
  .el-dialog__body {
    padding: 0 20px 20px 20px;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(20px);
  }

  .el-dialog__header {
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 2px solid rgba(64, 158, 255, 0.3);
  }

  .el-dialog__title {
    color: #f8fafc;
    font-weight: 600;
  }

  .el-dialog__headerbtn .el-dialog__close {
    color: #94a3b8;
  }

  .el-dialog__headerbtn .el-dialog__close:hover {
    color: #409eff;
  }
}

/* 筛选面板显示按钮（面板隐藏时） */
.filter-show-btn {
  position: absolute;
  top: 20px;
  right: 360px;
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 12px;
  padding: 12px 16px;
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #f8fafc;
  min-width: auto;
}

.filter-show-btn:hover {
  background: rgba(30, 41, 59, 0.95);
  border-color: rgba(64, 158, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.6);
}

.filter-show-btn .el-icon {
  color: #409eff;
  font-size: 16px;
}

.filter-row {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-row .el-select {
  flex: 1;
}

/* 覆盖筛选器中的选择框样式 */
.map-filters-panel :deep(.el-select .el-input__wrapper) {
  background-color: rgba(15, 23, 42, 0.8) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 6px !important;
  height: 28px !important;
}

.map-filters-panel :deep(.el-select .el-input__inner) {
  color: #f8fafc !important;
  font-size: 12px !important;
}

.map-filters-panel :deep(.el-select .el-select__placeholder) {
  color: #94a3b8 !important;
  font-size: 12px !important;
}



/* 实时告警面板美化 */
.alert-list {
  overflow-y: auto;
  padding: 20px;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  margin-bottom: 12px;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  border-left: 4px solid;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.alert-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(64, 158, 255, 0.5) 50%, transparent 100%);
}

.alert-item:hover {
  background: rgba(15, 23, 42, 0.9);
  border-color: rgba(64, 158, 255, 0.4);
  transform: translateX(4px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.alert-item.alert-high {
  border-left-color: #f56c6c;
  background: rgba(245, 108, 108, 0.1);
}

.alert-item.alert-high:hover {
  background: rgba(245, 108, 108, 0.15);
  border-color: rgba(245, 108, 108, 0.4);
}

.alert-item.alert-medium {
  border-left-color: #e6a23c;
  background: rgba(230, 162, 60, 0.1);
}

.alert-item.alert-medium:hover {
  background: rgba(230, 162, 60, 0.15);
  border-color: rgba(230, 162, 60, 0.4);
}

.alert-item.alert-low {
  border-left-color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
}

.alert-item.alert-low:hover {
  background: rgba(103, 194, 58, 0.15);
  border-color: rgba(103, 194, 58, 0.4);
}

.alert-icon {
  margin-right: 12px;
  margin-top: 2px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.alert-item.alert-high .alert-icon {
  color: #f56c6c;
  background: rgba(245, 108, 108, 0.2);
}

.alert-item.alert-medium .alert-icon {
  color: #e6a23c;
  background: rgba(230, 162, 60, 0.2);
}

.alert-item.alert-low .alert-icon {
  color: #909399;
  background: rgba(144, 147, 153, 0.2);
}

.alert-item:hover .alert-icon {
  transform: scale(1.1);
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
}

.alert-content {
  flex: 1;
  min-width: 0;
}

.alert-actions-sidebar {
  display: flex;
  align-items: center;
  margin-left: 8px;
  opacity: 0;
  transition: all 0.3s ease;
}

.alert-item:hover .alert-actions-sidebar {
  opacity: 1;
}

.alert-actions-sidebar .el-button {
  margin-left: 4px;
  width: 28px;
  height: 28px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

.alert-actions-sidebar .el-button:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 0.2);
}

.alert-actions-sidebar .el-button.el-button--warning {
  color: #e6a23c;
}

.alert-actions-sidebar .el-button.el-button--warning:hover {
  background: rgba(230, 162, 60, 0.2);
  color: #f0a020;
}

.alert-actions-sidebar .el-button.el-button--success {
  color: #67c23a;
}

.alert-actions-sidebar .el-button.el-button--success:hover {
  background: rgba(103, 194, 58, 0.2);
  color: #85ce61;
}

.alert-title {
  font-weight: 600;
  font-size: 14px;
  color: #f8fafc;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.alert-title::after {
  content: '';
  width: 4px;
  height: 4px;
  background: #67c23a;
  border-radius: 50%;
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

.alert-desc {
  font-size: 12px;
  color: #94a3b8;
  margin-bottom: 8px;
  line-height: 1.4;
}

.alert-time {
  font-size: 11px;
  color: #409eff;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.alert-time::before {
  content: '⏰';
  font-size: 10px;
}

/* 告警徽章美化 */
.alert-badge :deep(.el-badge__content) {
  background: linear-gradient(135deg, #f56c6c 0%, #e6a23c 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #fff !important;
  font-weight: 600 !important;
  font-size: 10px !important;
  min-width: 18px !important;
  height: 18px !important;
  line-height: 16px !important;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.4) !important;
  animation: badge-pulse 2s infinite !important;
}

@keyframes badge-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(245, 108, 108, 0.6);
  }
}


/* 车辆列表弹窗样式 */
.vehicle-list-dialog :deep(.el-dialog) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(15px) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
}

.vehicle-list-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.15) 0%, rgba(103, 194, 58, 0.1) 100%) !important;
  border-bottom: 1px solid rgba(64, 158, 255, 0.3) !important;
  padding: 16px 20px !important;
}

.vehicle-list-dialog :deep(.el-dialog__title) {
  color: #f8fafc !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.vehicle-list-dialog :deep(.el-dialog__body) {
  padding: 0 !important;
  background: transparent !important;
}

.vehicle-list-dialog .dialog-header {
  padding: 16px 20px;
  background: rgba(15, 23, 42, 0.8);
  border-bottom: 1px solid rgba(64, 158, 255, 0.2);
}


.vehicle-list-dialog .search-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 车辆表格样式重写 - 匹配大屏主题 */
.vehicle-list-dialog .vehicle-table {
  margin: 0 !important;
  background: transparent !important;
  width: 100% !important;
}

.vehicle-list-dialog :deep(.el-table) {
  background: transparent !important;
  color: #f8fafc !important;
  border: none !important;
  width: 100% !important;
  --el-table-header-bg-color: rgba(15, 23, 42, 0.95) !important;
  --el-table-row-hover-bg-color: rgba(64, 158, 255, 0.15) !important;
}

.vehicle-list-dialog :deep(.el-table__inner-wrapper) {
  background: transparent !important;
}

.vehicle-list-dialog :deep(.el-table__header-wrapper) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.vehicle-list-dialog :deep(.el-table__header) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.vehicle-list-dialog :deep(.el-table__header tr) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.vehicle-list-dialog :deep(.el-table__header th),
.vehicle-list-dialog :deep(.el-table__header th.el-table__cell) {
  background: rgba(15, 23, 42, 0.95) !important;
  background-color: rgba(15, 23, 42, 0.95) !important;
  color: #f8fafc !important;
  font-weight: 600 !important;
  border-bottom: 2px solid rgba(64, 158, 255, 0.3) !important;
  border-right: 1px solid rgba(64, 158, 255, 0.1) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
  font-size: 13px !important;
  padding: 12px 8px !important;
  text-align: center !important;
}

.vehicle-list-dialog :deep(.el-table__header th:before),
.vehicle-list-dialog :deep(.el-table__header th:after) {
  display: none !important;
}

.vehicle-list-dialog :deep(.el-table__header th:last-child) {
  border-right: none !important;
}

.vehicle-list-dialog :deep(.el-table__body-wrapper) {
  background: transparent !important;
}

.vehicle-list-dialog :deep(.el-table__body) {
  background: transparent !important;
}

.vehicle-list-dialog :deep(.el-table__body tr) {
  background: rgba(15, 23, 42, 0.9) !important;
  transition: all 0.3s ease !important;
  border: none !important;
}

.vehicle-list-dialog :deep(.el-table__body tr:nth-child(odd)) {
  background: rgba(15, 23, 42, 0.9) !important;
}

.vehicle-list-dialog :deep(.el-table__body tr:nth-child(even)) {
  background: rgba(30, 41, 59, 0.9) !important;
}

.vehicle-list-dialog :deep(.el-table__body tr:hover) {
  background: rgba(64, 158, 255, 0.15) !important;
  transform: translateX(2px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2) !important;
}

.vehicle-list-dialog :deep(.el-table__body tr:hover td) {
  background: rgba(64, 158, 255, 0.15) !important;
}

.vehicle-list-dialog :deep(.el-table__body td),
.vehicle-list-dialog :deep(.el-table__body td.el-table__cell) {
  background: inherit !important;
  background-color: inherit !important;
  border-bottom: 1px solid rgba(64, 158, 255, 0.1) !important;
  border-right: 1px solid rgba(64, 158, 255, 0.05) !important;
  color: #f8fafc !important;
  padding: 12px 8px !important;
  font-size: 12px !important;
}

.vehicle-list-dialog :deep(.el-table__body td:last-child) {
  border-right: none !important;
}

/* 强制覆盖白色背景 */
.vehicle-list-dialog :deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background: rgba(64, 158, 255, 0.15) !important;
  background-color: rgba(64, 158, 255, 0.15) !important;
}

.vehicle-list-dialog :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: rgba(30, 41, 59, 0.9) !important;
  background-color: rgba(30, 41, 59, 0.9) !important;
}

.vehicle-list-dialog :deep(.el-table--striped .el-table__body tr.el-table__row--striped:hover td) {
  background: rgba(64, 158, 255, 0.15) !important;
  background-color: rgba(64, 158, 255, 0.15) !important;
}

/* 确保表格容器宽度 */
.vehicle-list-dialog :deep(.el-table__header-wrapper),
.vehicle-list-dialog :deep(.el-table__body-wrapper) {
  width: 100% !important;
}

.vehicle-list-dialog :deep(.el-table__header),
.vehicle-list-dialog :deep(.el-table__body) {
  width: 100% !important;
}

/* 额外的表头强制样式 */
.vehicle-list-dialog :deep(.el-table .el-table__header-wrapper .el-table__header thead tr th) {
  background: rgba(15, 23, 42, 0.95) !important;
  background-color: rgba(15, 23, 42, 0.95) !important;
}

.vehicle-list-dialog :deep(.el-table thead) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.vehicle-list-dialog :deep(.el-table thead th) {
  background: rgba(15, 23, 42, 0.95) !important;
  background-color: rgba(15, 23, 42, 0.95) !important;
}

/* 车辆状态标签美化 */
.vehicle-list-dialog :deep(.el-tag) {
  border: none !important;
  font-weight: 600 !important;
  font-size: 11px !important;
  padding: 2px 8px !important;
  border-radius: 12px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.vehicle-list-dialog :deep(.el-tag--success) {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;
  color: #ffffff !important;
}

.vehicle-list-dialog :deep(.el-tag--warning) {
  background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%) !important;
  color: #ffffff !important;
}

.vehicle-list-dialog :deep(.el-tag--danger) {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%) !important;
  color: #ffffff !important;
}

.vehicle-list-dialog :deep(.el-tag--info) {
  background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%) !important;
  color: #ffffff !important;
}

/* 车辆定位按钮美化 */
.vehicle-list-dialog :deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%) !important;
  border: none !important;
  color: #ffffff !important;
  font-size: 11px !important;
  padding: 4px 12px !important;
  border-radius: 6px !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3) !important;
}

.vehicle-list-dialog :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #67c23a 0%, #409eff 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.5) !important;
}

/* 告警表格样式重写 - 匹配大屏主题 */
.alert-list-dialog .alert-table {
  margin: 0 !important;
  background: transparent !important;
  width: 100% !important;
}

.alert-list-dialog :deep(.el-table) {
  background: transparent !important;
  color: #f8fafc !important;
  border: none !important;
  width: 100% !important;
  --el-table-header-bg-color: rgba(15, 23, 42, 0.95) !important;
  --el-table-row-hover-bg-color: rgba(64, 158, 255, 0.15) !important;
}

.alert-list-dialog :deep(.el-table__inner-wrapper) {
  background: transparent !important;
}

.alert-list-dialog :deep(.el-table__header-wrapper) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.alert-list-dialog :deep(.el-table__header) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.alert-list-dialog :deep(.el-table__header tr) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.alert-list-dialog :deep(.el-table__header th),
.alert-list-dialog :deep(.el-table__header th.el-table__cell) {
  background: rgba(15, 23, 42, 0.95) !important;
  background-color: rgba(15, 23, 42, 0.95) !important;
  color: #f8fafc !important;
  font-weight: 600 !important;
  border-bottom: 2px solid rgba(64, 158, 255, 0.3) !important;
  border-right: 1px solid rgba(64, 158, 255, 0.1) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
  font-size: 13px !important;
  padding: 12px 8px !important;
  text-align: center !important;
}

.alert-list-dialog :deep(.el-table__header th:before),
.alert-list-dialog :deep(.el-table__header th:after) {
  display: none !important;
}

.alert-list-dialog :deep(.el-table__header th:last-child) {
  border-right: none !important;
}

.alert-list-dialog :deep(.el-table__body-wrapper) {
  background: transparent !important;
}

.alert-list-dialog :deep(.el-table__body) {
  background: transparent !important;
}

.alert-list-dialog :deep(.el-table__body tr) {
  background: rgba(15, 23, 42, 0.9) !important;
  transition: all 0.3s ease !important;
  border: none !important;
}

.alert-list-dialog :deep(.el-table__body tr:nth-child(odd)) {
  background: rgba(15, 23, 42, 0.9) !important;
}

.alert-list-dialog :deep(.el-table__body tr:nth-child(even)) {
  background: rgba(30, 41, 59, 0.9) !important;
}

.alert-list-dialog :deep(.el-table__body tr:hover) {
  background: rgba(64, 158, 255, 0.15) !important;
  transform: translateX(2px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2) !important;
}

.alert-list-dialog :deep(.el-table__body tr:hover td) {
  background: rgba(64, 158, 255, 0.15) !important;
}

.alert-list-dialog :deep(.el-table__body td),
.alert-list-dialog :deep(.el-table__body td.el-table__cell) {
  background: inherit !important;
  background-color: inherit !important;
  border-bottom: 1px solid rgba(64, 158, 255, 0.1) !important;
  border-right: 1px solid rgba(64, 158, 255, 0.05) !important;
  color: #f8fafc !important;
  padding: 12px 8px !important;
  font-size: 12px !important;
}

.alert-list-dialog :deep(.el-table__body td:last-child) {
  border-right: none !important;
}

/* 强制覆盖白色背景 */
.alert-list-dialog :deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background: rgba(64, 158, 255, 0.15) !important;
  background-color: rgba(64, 158, 255, 0.15) !important;
}

.alert-list-dialog :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: rgba(30, 41, 59, 0.9) !important;
  background-color: rgba(30, 41, 59, 0.9) !important;
}

.alert-list-dialog :deep(.el-table--striped .el-table__body tr.el-table__row--striped:hover td) {
  background: rgba(64, 158, 255, 0.15) !important;
  background-color: rgba(64, 158, 255, 0.15) !important;
}

/* 确保表格容器宽度 */
.alert-list-dialog :deep(.el-table__header-wrapper),
.alert-list-dialog :deep(.el-table__body-wrapper) {
  width: 100% !important;
}

.alert-list-dialog :deep(.el-table__header),
.alert-list-dialog :deep(.el-table__body) {
  width: 100% !important;
}

/* 额外的表头强制样式 */
.alert-list-dialog :deep(.el-table .el-table__header-wrapper .el-table__header thead tr th) {
  background: rgba(15, 23, 42, 0.95) !important;
  background-color: rgba(15, 23, 42, 0.95) !important;
}

.alert-list-dialog :deep(.el-table thead) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.alert-list-dialog :deep(.el-table thead th) {
  background: rgba(15, 23, 42, 0.95) !important;
  background-color: rgba(15, 23, 42, 0.95) !important;
}

/* 告警级别标签美化 */
.alert-list-dialog :deep(.el-tag) {
  border: none !important;
  font-weight: 600 !important;
  font-size: 11px !important;
  padding: 2px 8px !important;
  border-radius: 12px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.alert-list-dialog :deep(.el-tag--success) {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;
  color: #ffffff !important;
}

.alert-list-dialog :deep(.el-tag--warning) {
  background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%) !important;
  color: #ffffff !important;
}

.alert-list-dialog :deep(.el-tag--danger) {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%) !important;
  color: #ffffff !important;
}

.alert-list-dialog :deep(.el-tag--info) {
  background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%) !important;
  color: #ffffff !important;
}

/* 告警定位按钮美化 */
.alert-list-dialog :deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%) !important;
  border: none !important;
  color: #ffffff !important;
  font-size: 11px !important;
  padding: 4px 12px !important;
  border-radius: 6px !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3) !important;
}

.alert-list-dialog :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #67c23a 0%, #409eff 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.5) !important;
}

/* 告警详情按钮美化 */
.alert-list-dialog :deep(.el-button--success) {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;
  border: none !important;
  color: #ffffff !important;
  font-size: 11px !important;
  padding: 4px 12px !important;
  border-radius: 6px !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 6px rgba(103, 194, 58, 0.3) !important;
}

.alert-list-dialog :deep(.el-button--success:hover) {
  background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.5) !important;
}

/* 告警列表分页样式 - 深色主题 */
.alert-list-dialog .pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(147, 197, 253, 0.2);
}


/* 选择器输入框背景色样式 */
:deep(.el-select__wrapper) {
  background-color: #0F172AF2;
}

/* 组织机构树选择器样式 */
.vehicle-list-dialog :deep(.el-tree-select) {
  background-color: #0F172AF2;
}

.vehicle-list-dialog :deep(.el-tree-select .el-select__wrapper) {
  background-color: #0F172AF2 !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
}

.vehicle-list-dialog :deep(.el-tree-select .el-select__input) {
  color: #f8fafc !important;
}

.vehicle-list-dialog :deep(.el-tree-select .el-select__placeholder) {
  color: #94a3b8 !important;
}

/* 分页样式 - 深色主题 */
.vehicle-list-dialog .pagination-container,
.route-list-dialog .pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(147, 197, 253, 0.2);
}




.vehicle-list-dialog .total-count {
  color: #94a3b8;
  font-size: 14px;
  font-weight: 500;
}

/* 告警列表弹窗样式 */
.alert-list-dialog :deep(.el-dialog) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(15px) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
}

.alert-list-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.15) 0%, rgba(103, 194, 58, 0.1) 100%) !important;
  border-bottom: 1px solid rgba(64, 158, 255, 0.3) !important;
  padding: 16px 20px !important;
}

.alert-list-dialog :deep(.el-dialog__title) {
  color: #f8fafc !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.alert-list-dialog :deep(.el-dialog__body) {
  padding: 0 !important;
  background: transparent !important;
}

.alert-list-dialog .dialog-header {
  padding: 16px 20px;
  background: rgba(15, 23, 42, 0.8);
  border-bottom: 1px solid rgba(64, 158, 255, 0.2);
}

.alert-list-dialog .search-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.vehicle-list-dialog :deep(.el-button) {
  background: rgba(15, 23, 42, 0.9) !important;
  border: 1px solid rgba(64, 158, 255, 0.4) !important;
  color: #f8fafc !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.vehicle-list-dialog :deep(.el-button:hover) {
  background: rgba(30, 41, 59, 0.9) !important;
  border-color: #409eff !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
}

/* 查看全部按钮样式 */
.view-all-btn {
  margin-left: 8px !important;
  background: rgba(64, 158, 255, 0.1) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  color: #409eff !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
  height: 24px !important;
}

.view-all-btn:hover {
  background: rgba(64, 158, 255, 0.2) !important;
  border-color: #409eff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.8);
}

::-webkit-scrollbar-thumb {
  background: #409eff;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #67c23a;
}


/* page-sizes选择器 */
::v-deep .el-select-dropdown__item li {
  background-color: transparent !important;
}
/* prev和next箭头的样式 */
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination .btn-prev {
  background: transparent !important;
  background-color: transparent !important;
}
/* prev和next箭头disabled的样式 */
::v-deep .el-pagination button:disabled {
  background-color: transparent !important;
}
/* 页码样式 */
::v-deep .el-pager li {
  background-color: transparent !important;
}
/* active的页码样式 */
::v-deep .el-pager li.active {
  color: #267aff !important;
}

/* 地图点位信息弹窗样式 */
.map-info-dialog :deep(.el-dialog) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(15px) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
}

.map-info-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.15) 0%, rgba(103, 194, 58, 0.1) 100%) !important;
  border-bottom: 1px solid rgba(64, 158, 255, 0.3) !important;
  padding: 16px 20px !important;
}

.map-info-dialog :deep(.el-dialog__title) {
  color: #f8fafc !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.map-info-dialog :deep(.el-dialog__body) {
  padding: 20px !important;
  background: transparent !important;
}

.map-info-content {
  color: #f8fafc;
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.info-item:hover {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(64, 158, 255, 0.4);
  transform: translateX(2px);
}

.info-label {
  font-size: 14px;
  font-weight: 600;
  color: #94a3b8;
  margin-right: 12px;
  min-width: 80px;
  position: relative;
}

.info-label::after {
  content: '';
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 12px;
  background: linear-gradient(135deg, #67c23a 0%, #409eff 100%);
  border-radius: 1px;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #f8fafc;
  flex: 1;
  text-align: right;
}

.info-value.highlight {
  color: #67c23a;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(103, 194, 58, 0.3);
}

.map-info-dialog :deep(.el-button) {
  background: rgba(15, 23, 42, 0.9) !important;
  border: 1px solid rgba(64, 158, 255, 0.4) !important;
  color: #f8fafc !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.map-info-dialog :deep(.el-button:hover) {
  background: rgba(30, 41, 59, 0.9) !important;
  border-color: #409eff !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
}

/* 告警处理操作区样式 */
.alert-actions {
  margin-top: 16px;
  padding: 16px;
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.alert-actions .el-button {
  min-width: 100px;
  font-weight: 600;
}

/* 告警状态样式 */
.alert-item.status-resolved {
  opacity: 0.6;
  background: rgba(103, 194, 58, 0.05) !important;
  border-left-color: #67c23a !important;
}

.alert-item.status-processing {
  background: rgba(230, 162, 60, 0.05) !important;
  border-left-color: #e6a23c !important;
}

.alert-item.status-pending {
  /* 保持原有样式 */
}

.alert-title .status-tag {
  margin-left: 8px;
  font-size: 10px;
  height: 18px;
  line-height: 16px;
}

/* 告警列表中已处理项目的特殊样式 */
.alert-item.status-resolved .alert-title {
  text-decoration: line-through;
  color: #94a3b8 !important;
}

.alert-item.status-resolved .alert-desc {
  color: #64748b !important;
}

.alert-item.status-resolved .alert-icon {
  opacity: 0.5;
}

/* 告警处理表单弹窗样式 */
.alert-process-dialog :deep(.el-dialog) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(15px) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
}

.alert-process-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.15) 0%, rgba(103, 194, 58, 0.1) 100%) !important;
  border-bottom: 1px solid rgba(64, 158, 255, 0.3) !important;
  padding: 16px 20px !important;
}

.alert-process-dialog :deep(.el-dialog__title) {
  color: #f8fafc !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.alert-process-dialog :deep(.el-dialog__body) {
  padding: 20px !important;
  background: transparent !important;
}

.alert-process-dialog :deep(.el-form-item__label) {
  color: #f8fafc !important;
  font-weight: 600 !important;
}

.alert-process-dialog :deep(.el-input__wrapper) {
  background-color: rgba(15, 23, 42, 0.8) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 6px !important;
}

.alert-process-dialog :deep(.el-input__inner) {
  color: #f8fafc !important;
  background: transparent !important;
}

.alert-process-dialog :deep(.el-textarea__inner) {
  background-color: rgba(15, 23, 42, 0.8) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 6px !important;
  color: #f8fafc !important;
}

/*elmentui 文本输入框计数样式*/
:deep(.el-input__count) {
  background: rgba(15, 23, 42, 0.9);
}

.alert-process-dialog :deep(.el-select .el-input__wrapper) {
  background-color: rgba(15, 23, 42, 0.8) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
}

.alert-process-dialog :deep(.el-button) {
  background: rgba(15, 23, 42, 0.9) !important;
  border: 1px solid rgba(64, 158, 255, 0.4) !important;
  color: #f8fafc !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.alert-process-dialog :deep(.el-button:hover) {
  background: rgba(30, 41, 59, 0.9) !important;
  border-color: #409eff !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
}

.alert-process-dialog :deep(.el-button--warning) {
  background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%) !important;
  border: none !important;
  color: #ffffff !important;
}

.alert-process-dialog :deep(.el-button--success) {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;
  border: none !important;
  color: #ffffff !important;
}

/* 处理信息显示区样式 */
.process-info-section {
  margin-top: 16px;
  padding: 16px;
  background: rgba(103, 194, 58, 0.05);
  border: 1px solid rgba(103, 194, 58, 0.2);
  border-radius: 8px;
  position: relative;
}

.process-info-section::before {
  content: '处理信息';
  position: absolute;
  top: -8px;
  left: 12px;
  background: rgba(15, 23, 42, 0.9);
  color: #67c23a;
  font-size: 12px;
  font-weight: 600;
  padding: 0 8px;
}

.process-desc {
  word-break: break-word;
  line-height: 1.5;
  max-height: 60px;
  overflow-y: auto;
}
