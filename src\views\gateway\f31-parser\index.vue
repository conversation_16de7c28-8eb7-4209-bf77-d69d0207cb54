<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Document /></el-icon>
        第三代报站系统文件解析器
      </h1>
      <p class="page-subtitle">支持解析 .f31 格式的锐明报站配置文件</p>
    </div>

    <!-- 文件上传区域 -->
    <el-card class="upload-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>文件上传</span>
          <el-button 
            text 
            type="primary" 
            @click="handleGetSupportedFormats"
            :loading="formatsLoading"
          >
            查看支持格式
          </el-button>
        </div>
      </template>

      <el-upload
        ref="uploadRef"
        class="upload-area"
        drag
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-remove="handleFileRemove"
        :before-upload="beforeUpload"
        accept=".f31,.dat"
        :limit="1"
        :file-list="fileList"
      >
        <el-icon class="upload-icon"><UploadFilled /></el-icon>
        <div class="upload-text">
          <p>点击或拖拽文件到此处上传</p>
          <p class="upload-tip">支持 .f31 和 .dat 格式文件，最大 50MB</p>
        </div>
      </el-upload>

      <!-- 解析按钮 -->
      <div class="upload-actions" v-if="currentFile">
        <el-button 
          type="primary" 
          :loading="parseLoading"
          @click="handleParseFile"
        >
          <el-icon><View /></el-icon>
          解析文件
        </el-button>
        <el-button 
          type="success" 
          :loading="hexLoading"
          @click="handleGetHexDump"
        >
          <el-icon><List /></el-icon>
          查看十六进制
        </el-button>
      </div>
    </el-card>

    <!-- 解析结果 -->
    <el-card v-if="parseResult" class="result-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>解析结果</span>
          <el-tag :type="parseResult.success ? 'success' : 'danger'">
            {{ parseResult.success ? '解析成功' : '解析失败' }}
          </el-tag>
        </div>
      </template>

      <!-- 错误信息 -->
      <el-alert
        v-if="!parseResult.success"
        :title="parseResult.errorMessage"
        type="error"
        show-icon
        :closable="false"
      />

      <!-- 成功结果 -->
      <div v-else>
        <!-- 标签页 -->
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 文件头信息 -->
          <el-tab-pane label="文件头信息" name="fileHeader">
            <el-descriptions title="锐明标准文件头" :column="2" border>
              <el-descriptions-item label="文件标识">
                <el-tag>{{ parseResult.fileHeader?.flag || 'N/A' }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="文件类型">
                <el-tag type="success">{{ parseResult.fileHeader?.fileType || 'N/A' }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="版本号">
                {{ parseResult.fileHeader?.version || 'N/A' }}
              </el-descriptions-item>
              <el-descriptions-item label="声明长度">
                {{ formatFileSize(parseResult.fileHeader?.fileLength || 0) }}
              </el-descriptions-item>
              <el-descriptions-item label="实际长度">
                {{ formatFileSize(parseResult.fileHeader?.actualLength || 0) }}
              </el-descriptions-item>
              <el-descriptions-item label="文件名">
                {{ parseResult.fileName || 'N/A' }}
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <!-- 数据结构 -->
          <el-tab-pane label="数据结构" name="dataStructure" v-if="parseResult.libraryHeader">
            <el-descriptions title="信息库文件头" :column="2" border>
              <el-descriptions-item label="编号规则">
                {{ parseResult.libraryHeader.indexCodeType }}
              </el-descriptions-item>
              <el-descriptions-item label="最大记录数">
                {{ parseResult.libraryHeader.infoSizeCountMax?.toLocaleString() }}
              </el-descriptions-item>
              <el-descriptions-item label="文件头长度">
                {{ parseResult.libraryHeader.hdrLength }} 字节
              </el-descriptions-item>
              <el-descriptions-item label="每条记录长度">
                {{ parseResult.libraryHeader.infoSizeEach }} 字节
              </el-descriptions-item>
              <el-descriptions-item label="版本信息数量">
                {{ parseResult.libraryHeader.infoVersionCount }}
              </el-descriptions-item>
              <el-descriptions-item label="当前版本号">
                {{ parseResult.libraryHeader.updateVersionThis }}
              </el-descriptions-item>
              <el-descriptions-item label="基础版本号">
                {{ parseResult.libraryHeader.updateVersionFrom }}
              </el-descriptions-item>
            </el-descriptions>

            <!-- 版本信息列表 -->
            <div v-if="parseResult.versionList && parseResult.versionList.length > 0" class="mt-4">
              <h3>版本信息列表</h3>
              <el-table :data="parseResult.versionList" stripe border class="version-table">
                <el-table-column prop="infoType" label="信息类型" width="120" />
                <el-table-column prop="infoVersion" label="版本号" width="120" />
              </el-table>
            </div>
          </el-tab-pane>

          <!-- 站点信息 -->
          <el-tab-pane label="站点信息" name="stationInfo" v-if="parseResult.stations">
            <div class="station-header">
              <h3>站点信息列表</h3>
              <el-tag type="info">
                共解析出 {{ parseResult.totalRecords || 0 }} 个站点
              </el-tag>
            </div>
            
            <el-table 
              :data="parseResult.stations" 
              stripe 
              border 
              height="400"
              class="station-table"
            >
              <el-table-column prop="indexCode" label="编号" width="100" />
              <el-table-column prop="stationName" label="站点名称" min-width="200">
                <template #default="{ row }">
                  <span>{{ row.stationName || '(空)' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="infoType" label="类型" width="80" />
              <el-table-column prop="infoTypeName" label="类型说明" min-width="150">
                <template #default="{ row }">
                  <el-tag :type="getStationTypeTag(row.infoType)">
                    {{ row.infoTypeName }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <!-- 十六进制查看 -->
          <el-tab-pane label="十六进制" name="hexDump" v-if="hexDump">
            <div class="hex-container">
              <pre class="hex-dump">{{ hexDump }}</pre>
            </div>
          </el-tab-pane>

          <!-- JSON数据 -->
          <el-tab-pane label="JSON数据" name="jsonData">
            <div class="json-container">
              <el-button 
                size="small" 
                @click="copyToClipboard(JSON.stringify(parseResult, null, 2))"
                class="copy-btn"
              >
                <el-icon><CopyDocument /></el-icon>
                复制JSON
              </el-button>
              <pre class="json-dump">{{ JSON.stringify(parseResult, null, 2) }}</pre>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>

    <!-- 支持格式对话框 -->
    <el-dialog
      v-model="formatsDialogVisible"
      title="支持的文件格式"
      width="600px"
    >
      <el-descriptions v-if="supportedFormats" :column="1" border>
        <el-descriptions-item label="文件扩展名">
          <el-tag v-for="ext in supportedFormats.fileExtensions" :key="ext" class="mr-2">
            {{ ext }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="文件描述">
          {{ supportedFormats.description }}
        </el-descriptions-item>
        <el-descriptions-item label="协议标准">
          {{ supportedFormats.protocol }}
        </el-descriptions-item>
        <el-descriptions-item label="最大文件大小">
          {{ formatFileSize(supportedFormats.maxFileSize) }}
        </el-descriptions-item>
        <el-descriptions-item label="支持的文件类型">
          <el-tag 
            v-for="type in supportedFormats.supportedTypes" 
            :key="type" 
            type="success"
            class="mr-2 mb-2"
          >
            {{ type }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, UploadFilled, View, List, CopyDocument } from '@element-plus/icons-vue'
import type { UploadFile, UploadFiles, UploadInstance } from 'element-plus'
import { parseF31File, getHexDump, getSupportedFormats } from '@/api/gateway/f31Parser'
import type { F31ParseResult, SupportedFormats } from '@/api/gateway/f31Parser'

// 响应式数据
const uploadRef = ref<UploadInstance>()
const fileList = ref<UploadFiles>([])
const currentFile = ref<File | null>(null)
const parseLoading = ref(false)
const hexLoading = ref(false)
const formatsLoading = ref(false)
const parseResult = ref<F31ParseResult | null>(null)
const hexDump = ref<string>('')
const activeTab = ref('fileHeader')
const formatsDialogVisible = ref(false)
const supportedFormats = ref<SupportedFormats | null>(null)

// 文件变化处理
const handleFileChange = (file: UploadFile) => {
  if (file.raw) {
    currentFile.value = file.raw
    // 清空之前的解析结果
    parseResult.value = null
    hexDump.value = ''
  }
}

// 文件移除处理
const handleFileRemove = () => {
  currentFile.value = null
  parseResult.value = null
  hexDump.value = ''
}

// 上传前验证
const beforeUpload = (file: File) => {
  const isValidFormat = file.name.toLowerCase().endsWith('.f31') || 
                       file.name.toLowerCase().endsWith('.dat')
  if (!isValidFormat) {
    ElMessage.error('只支持 .f31 和 .dat 格式的文件')
    return false
  }

  const isValidSize = file.size <= 50 * 1024 * 1024 // 50MB
  if (!isValidSize) {
    ElMessage.error('文件大小不能超过 50MB')
    return false
  }

  return true
}

// 解析文件
const handleParseFile = async () => {
  if (!currentFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }

  parseLoading.value = true
  try {
    const response = await parseF31File(currentFile.value)
    if (response.code === 200) {
      parseResult.value = response.data
      activeTab.value = 'fileHeader'
      ElMessage.success('文件解析成功')
    } else {
      ElMessage.error(response.msg || '解析失败')
    }
  } catch (error) {
    console.error('解析文件失败:', error)
    ElMessage.error('解析文件失败')
  } finally {
    parseLoading.value = false
  }
}

// 获取十六进制dump
const handleGetHexDump = async () => {
  if (!currentFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }

  hexLoading.value = true
  try {
    const response = await getHexDump(currentFile.value, 1024)
    if (response.code === 200) {
      hexDump.value = response.data
      activeTab.value = 'hexDump'
      ElMessage.success('获取十六进制数据成功')
    } else {
      ElMessage.error(response.msg || '获取十六进制数据失败')
    }
  } catch (error) {
    console.error('获取十六进制数据失败:', error)
    ElMessage.error('获取十六进制数据失败')
  } finally {
    hexLoading.value = false
  }
}

// 获取支持格式
const handleGetSupportedFormats = async () => {
  formatsLoading.value = true
  try {
    const response = await getSupportedFormats()
    if (response.code === 200) {
      supportedFormats.value = response.data
      formatsDialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取支持格式失败')
    }
  } catch (error) {
    console.error('获取支持格式失败:', error)
    ElMessage.error('获取支持格式失败')
  } finally {
    formatsLoading.value = false
  }
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取站点类型标签样式
const getStationTypeTag = (infoType: number): string => {
  const typeMap: Record<number, string> = {
    1: 'primary',    // 常规站点
    2: 'info',       // 管理类站点
    3: 'success',    // 主站
    4: 'success',    // 副站
    5: 'warning',    // 停车场
    6: 'warning',    // 加油站
    7: 'warning',    // 维修点
    9: 'info',       // 拐弯点
    10: 'danger',    // 安全黑点
    11: 'primary',   // 字符串发声站点
    13: 'info',      // 两个站点之间的信息
    15: 'info',      // 标示点
    18: 'info',      // 路口
    19: 'danger',    // 停用站
    20: 'info'       // 途经点
  }
  return typeMap[infoType] || 'default'
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  
  .page-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 28px;
    color: var(--el-text-color-primary);
    margin-bottom: 10px;
  }
  
  .page-subtitle {
    color: var(--el-text-color-regular);
    font-size: 16px;
  }
}

.upload-card, .result-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.upload-area {
  :deep(.el-upload-dragger) {
    width: 100%;
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 2px dashed var(--el-border-color);
    border-radius: 8px;
    background: var(--el-bg-color-page);
    transition: all 0.3s;
    
    &:hover {
      border-color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
    }
  }
}

.upload-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 15px;
}

.upload-text {
  text-align: center;
  
  p {
    margin: 5px 0;
    
    &:first-child {
      font-size: 16px;
      color: var(--el-text-color-primary);
    }
  }
  
  .upload-tip {
    font-size: 14px;
    color: var(--el-text-color-regular);
  }
}

.upload-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.version-table, .station-table {
  margin-top: 15px;
}

.station-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  
  h3 {
    margin: 0;
  }
}

.hex-container, .json-container {
  position: relative;
}

.copy-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.hex-dump, .json-dump {
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  padding: 15px;
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  max-height: 500px;
  overflow: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.hex-dump {
  color: var(--el-text-color-primary);
  background: #1e1e1e;
  color: #d4d4d4;
}

.json-dump {
  color: var(--el-text-color-primary);
}

.mr-2 {
  margin-right: 8px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mt-4 {
  margin-top: 20px;
}
</style>