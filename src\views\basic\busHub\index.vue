<template>
  <div class='hub-management-fullscreen'>
    <el-row :gutter='20'>
      <!-- 组织机构树 -->
      <el-col :lg='4' :xs='24'>
        <div class='left-panel'>
          <div class='panel-header'>
            <div class='header-left'>
              <el-icon>
                <Location />
              </el-icon>
              <span>组织机构筛选</span>
            </div>
          </div>
          <div class='panel-content'>
            <div class='search-box'>
              <el-input v-model='deptName' placeholder='请输入组织机构名称' prefix-icon='Search' clearable />
            </div>
            <el-tree
              ref='deptTreeRef'
              class='dept-tree'
              node-key='id'
              :data='deptOptions'
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node='false'
              :filter-node-method='filterNode'
              highlight-current
              default-expand-all
              @node-click='handleNodeClick'
            />
          </div>
        </div>
      </el-col>
      <el-col :lg='20' :xs='24'>
        <div class='right-content'>
          <!-- 顶部工具栏 -->
          <header class='content-header'>
            <div class='header-left'>
              <div class='title-section'>
                <el-icon class='title-icon'>
                  <Location />
                </el-icon>
                <h1 class='page-title'>场站管理</h1>
              </div>
              <div class='stats-cards'>
                <div class='stat-card'>
                  <div class='stat-value'>{{ hubList.length }}</div>
                  <div class='stat-label'>总场站</div>
                </div>
                <div class='stat-card online'>
                  <div class='stat-value'>{{ hubList.filter(h => h.status === '1').length }}</div>
                  <div class='stat-label'>运营中</div>
                </div>
                <div class='stat-card warning'>
                  <div class='stat-value'>{{ hubList.filter(h => h.status === '2').length }}</div>
                  <div class='stat-label'>维护中</div>
                </div>
                <div class='stat-card danger'>
                  <div class='stat-value'>{{ hubList.filter(h => h.status === '0').length }}</div>
                  <div class='stat-label'>停用</div>
                </div>
              </div>
            </div>
          </header>

          <!-- 搜索表单 -->
          <div class='search-section' v-show='showSearch'>
            <el-form :model='queryParams' ref='queryFormRef' :inline='true' label-width='68px'>
              <el-form-item label='场站编号' prop='hubCode'>
                <el-input
                  v-model='queryParams.hubCode'
                  placeholder='请输入场站编号'
                  clearable
                  @keyup.enter='handleQuery'
                />
              </el-form-item>
              <el-form-item label='场站名称' prop='hubName'>
                <el-input
                  v-model='queryParams.hubName'
                  placeholder='请输入场站名称'
                  clearable
                  @keyup.enter='handleQuery'
                />
              </el-form-item>
              <el-form-item label='场站类型' prop='hubType'>
                <el-select v-model='queryParams.hubType' placeholder='请选择场站类型' clearable>
                  <el-option label='公交首末站' value='terminal' />
                  <el-option label='调度中心' value='dispatch' />
                  <el-option label='停车场' value='parking' />
                  <el-option label='维修站' value='maintenance' />
                </el-select>
              </el-form-item>
              <el-form-item label='状态' prop='status'>
                <el-select v-model='queryParams.status' placeholder='请选择状态' clearable>
                  <el-option label='运营中' value='1' />
                  <el-option label='维护中' value='2' />
                  <el-option label='停用' value='0' />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type='primary' icon='Search' @click='handleQuery'>搜索</el-button>
                <el-button icon='Refresh' @click='resetQuery'>重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 操作按钮区域 -->
          <div class='action-section'>
            <div class='action-buttons'>
              <el-button type='primary' plain icon='Plus' @click='handleAdd'>新增</el-button>
              <el-button type='success' plain icon='Edit' :disabled='single' @click='handleUpdate'>修改</el-button>
              <el-button type='danger' plain icon='Delete' :disabled='multiple' @click='handleDelete'>删除</el-button>
            </div>
            <right-toolbar v-model:showSearch='showSearch' @queryTable='getList'></right-toolbar>
          </div>

          <!-- 场站卡片列表 -->
          <div class='hub-cards-container' v-loading='loading'>
            <div class='cards-grid'>
              <div
                v-for='hub in hubList'
                :key='hub.hubId'
                class='hub-card'
                :class="{ 'selected': selectedHubs.includes(hub.hubId) }"
                @click='toggleSelection(hub)'
              >
                <!-- 卡片头部 -->
                <div class='card-header'>
                  <div class='hub-info'>
                    <div class='hub-name'>{{ hub.hubName }}</div>
                    <div class='hub-code'>{{ hub.hubCode }}</div>
                  </div>
                  <div class='status-badge'>
                    <el-tag
                      v-if="hub.status === '1'"
                      type='success'
                      size='small'
                      effect='dark'
                    >运营中
                    </el-tag>
                    <el-tag
                      v-else-if="hub.status === '2'"
                      type='warning'
                      size='small'
                      effect='dark'
                    >维护中
                    </el-tag>
                    <el-tag
                      v-else
                      type='danger'
                      size='small'
                      effect='dark'
                    >停用
                    </el-tag>
                  </div>
                </div>

                <!-- 卡片内容 -->
                <div class='card-content'>
                  <div class='hub-image'>
                    <el-icon size='36' :color='getHubIconColor(hub.hubType)'>
                      <component :is='getHubIcon(hub.hubType)' />
                    </el-icon>
                  </div>

                  <div class='hub-details'>
                    <div class='detail-row'>
                      <div class='detail-item'>
                        <span class='label'>场站类型:</span>
                        <span class='value'>{{ getHubTypeText(hub.hubType) }}</span>
                      </div>
                      <div class='detail-item'>
                        <span class='label'>车位数量:</span>
                        <span class='value'>{{ hub.parkingSpaces }}个</span>
                      </div>
                    </div>
                    <div class='detail-row'>
                      <div class='detail-item'>
                        <span class='label'>已停车辆:</span>
                        <span class='value'>{{ hub.occupiedSpaces }}辆</span>
                      </div>
                      <div class='detail-item'>
                        <span class='label'>管理员:</span>
                        <span class='value'>{{ hub.manager }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 卡片操作按钮 -->
                <div class='card-actions'>
                  <el-button
                    type='primary'
                    size='small'
                    icon='Edit'
                    @click.stop='handleUpdate(hub)'
                  >修改
                  </el-button>
                  <el-button
                    type='danger'
                    size='small'
                    icon='Delete'
                    @click.stop='handleDelete(hub)'
                  >删除
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if='!loading && hubList.length === 0' class='empty-state'>
              <el-empty description='暂无场站数据' />
            </div>
          </div>

          <pagination
            v-show='total>0'
            :total='total'
            v-model:page='queryParams.pageNum'
            v-model:limit='queryParams.pageSize'
            @pagination='getList'
          />
        </div>

        <!-- 添加或修改场站对话框 -->
        <el-dialog
          :title='title'
          v-model='open'
          width='1400px'
          append-to-body
          :close-on-click-modal='false'
          top='5vh'
        >
          <el-row :gutter='20'>
            <!-- 左侧基础信息表单 -->
            <el-col :span='12'>
              <div class='form-section'>
                <h3 class='section-title'>基础信息</h3>
                <el-form ref='hubFormRef' :model='form' :rules='rules' label-width='100px'>
                  <el-form-item label='场站名称' prop='hubName'>
                    <el-input v-model='form.hubName' placeholder='请输入场站名称' />
                  </el-form-item>
                  <el-form-item label='场站编号' prop='hubCode'>
                    <el-input v-model='form.hubCode' placeholder='请输入场站编号' />
                  </el-form-item>
                  <el-form-item label='场站类型' prop='hubType'>
                    <el-select v-model='form.hubType' placeholder='请选择场站类型' style='width: 100%'>
                      <el-option label='公交首末站' value='terminal' />
                      <el-option label='调度中心' value='dispatch' />
                      <el-option label='停车场' value='parking' />
                      <el-option label='维修站' value='maintenance' />
                    </el-select>
                  </el-form-item>
                  <el-form-item label='详细地址' prop='address'>
                    <el-input
                      v-model='form.address'
                      type='textarea'
                      :rows='2'
                      placeholder='请输入详细地址，输入后可点击右侧"定位地址"按钮在地图上定位'
                      @blur='handleAddressChange'
                    />
                    <div class='form-tip'>
                      <el-icon><InfoFilled /></el-icon>
                      <span>提示：输入详细地址后，点击右侧地图区域的"定位地址"按钮可在地图上自动定位</span>
                    </div>
                  </el-form-item>
                  <el-form-item label='车位数量' prop='parkingSpaces'>
                    <el-input-number v-model='form.parkingSpaces' :min='1' :max='999' placeholder='请输入车位数量' style='width: 100%' />
                  </el-form-item>
                  <el-form-item label='所属机构' prop='deptId'>
                    <el-tree-select
                      v-model='form.deptId'
                      :data='deptOptions'
                      :props="{ label: 'label', children: 'children', value: 'id' }"
                      placeholder='请选择所属机构'
                      check-strictly
                      :render-after-expand='false'
                      style='width: 100%'
                    />
                  </el-form-item>
                  <el-form-item label='管理员' prop='manager'>
                    <el-input v-model='form.manager' placeholder='请输入管理员姓名' />
                  </el-form-item>
                  <el-form-item label='联系电话' prop='phone'>
                    <el-input v-model='form.phone' placeholder='请输入联系电话' />
                  </el-form-item>
                  <el-form-item label='状态' prop='status'>
                    <el-select v-model='form.status' placeholder='请选择状态' style='width: 100%'>
                      <el-option label='运营中' value='1' />
                      <el-option label='维护中' value='2' />
                      <el-option label='停用' value='0' />
                    </el-select>
                  </el-form-item>
                  <el-form-item label='备注' prop='remark'>
                    <el-input v-model='form.remark' type='textarea' :rows='3' placeholder='请输入备注信息' />
                  </el-form-item>
                </el-form>
              </div>
            </el-col>

            <!-- 右侧地图区域 -->
            <el-col :span='12'>
              <div class='map-section'>
                <div class='map-header'>
                  <h3 class='section-title'>场站范围绘制</h3>
                  <div class='area-info' v-if='currentPolygon && polygonArea > 0'>
                    <el-tag type='success' effect='dark'>
                      面积: {{ polygonArea }} 平方米
                    </el-tag>
                  </div>
                </div>
                <div class='map-tools'>
                  <el-button
                    type='primary'
                    size='small'
                    icon='Edit'
                    @click='startDrawing'
                    :disabled='!mapReady'
                  >
                    绘制范围
                  </el-button>
                  <el-button
                    type='warning'
                    size='small'
                    icon='Delete'
                    @click='clearDrawing'
                    :disabled='!mapReady || !currentPolygon'
                  >
                    清除范围
                  </el-button>
                  <el-button
                    type='info'
                    size='small'
                    icon='Position'
                    @click='locateByAddress'
                    :disabled='!mapReady || !form.address'
                  >
                    定位地址
                  </el-button>
                </div>
                <div
                  id='hubMap'
                  class='map-container'
                  v-loading='mapLoading'
                  element-loading-text='地图加载中...'
                ></div>
              </div>
            </el-col>
          </el-row>

          <template #footer>
            <div class='dialog-footer'>
              <el-button type='primary' @click='submitForm' :loading='submitting'>确 定</el-button>
              <el-button @click='cancel'>取 消</el-button>
            </div>
          </template>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name='BusHub'>
import { deptTreeSelect } from '@/api/system/user';
import { Location, OfficeBuilding, Position, SetUp, InfoFilled } from '@element-plus/icons-vue';
import {
  createOptimizedMap,
  addOptimizedControls,
  handleMapError,
  suppressMapWarnings
} from '@/utils/mapConfig';

const { proxy } = getCurrentInstance();

// 数据响应式变量
const hubList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const submitting = ref(false);

// 卡片选择相关
const selectedHubs = ref([]);

// 组织机构树相关数据
const deptName = ref('');
const deptOptions = ref([]);
const deptTreeRef = ref();

// 地图相关
const mapInstance = ref(null);
const mapReady = ref(false);
const mapLoading = ref(false);
const currentPolygon = ref(null);
const polygonArea = ref(0);
const polygonPerimeter = ref(0);
const drawingManager = ref(null);

// 表单引用
const queryFormRef = ref();
const hubFormRef = ref();

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 25,
    hubCode: null,
    hubName: null,
    hubType: null,
    status: null,
    deptId: null
  },
  rules: {
    hubName: [
      { required: true, message: '场站名称不能为空', trigger: 'blur' }
    ],
    hubCode: [
      { required: true, message: '场站编号不能为空', trigger: 'blur' }
    ],
    hubType: [
      { required: true, message: '场站类型不能为空', trigger: 'change' }
    ],
    address: [
      { required: true, message: '详细地址不能为空', trigger: 'blur' }
    ],
    parkingSpaces: [
      { required: true, message: '车位数量不能为空', trigger: 'blur' }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 组织机构树筛选
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

// 根据名称筛选组织机构树
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

// 查询组织机构下拉树结构
async function getTreeSelect() {
  try {
    const res = await deptTreeSelect();
    deptOptions.value = res.data;
  } catch (error) {
    console.error('获取组织机构树失败:', error);
    // 模拟组织机构数据
    deptOptions.value = [
      {
        id: 1,
        label: '总公司',
        children: [
          {
            id: 2,
            label: '运营部',
            children: [
              { id: 3, label: '东城区' },
              { id: 4, label: '西城区' }
            ]
          },
          {
            id: 5,
            label: '技术部',
            children: [
              { id: 6, label: '南城区' },
              { id: 7, label: '北城区' }
            ]
          }
        ]
      }
    ];
  }
}

// 节点单击事件
function handleNodeClick(data) {
  queryParams.value.deptId = data.id;
  handleQuery();
}

// 获取场站图标
function getHubIcon(hubType) {
  const iconMap = {
    'terminal': 'OfficeBuilding',
    'dispatch': 'Position',
    'parking': 'OfficeBuilding',
    'maintenance': 'SetUp'
  };
  return iconMap[hubType] || 'OfficeBuilding';
}

// 获取场站图标颜色
function getHubIconColor(hubType) {
  const colorMap = {
    'terminal': '#67C23A',
    'dispatch': '#409EFF',
    'parking': '#E6A23C',
    'maintenance': '#F56C6C'
  };
  return colorMap[hubType] || '#409EFF';
}

// 获取场站类型文本
function getHubTypeText(type) {
  const typeMap = {
    'terminal': '公交首末站',
    'dispatch': '调度中心',
    'parking': '停车场',
    'maintenance': '维修站'
  };
  return typeMap[type] || type;
}

// 查询场站列表
function getList() {
  loading.value = true;
  // 模拟数据
  const mockData = [
    {
      hubId: 1,
      hubName: '东站公交枢纽',
      hubCode: 'HUB001',
      hubType: 'terminal',
      address: '北京市朝阳区东站路88号',
      parkingSpaces: 50,
      occupiedSpaces: 35,
      deptId: 3,
      deptName: '东城区',
      manager: '张三',
      phone: '13800138001',
      status: '1',
      coordinates: [116.434307, 39.90909],
      polygon: [[116.433307, 39.908], [116.435307, 39.908], [116.435307, 39.91], [116.433307, 39.91]],
      remark: '主要公交枢纽站点'
    },
    {
      hubId: 2,
      hubName: '西郊调度中心',
      hubCode: 'HUB002',
      hubType: 'dispatch',
      address: '北京市海淀区西郊路100号',
      parkingSpaces: 30,
      occupiedSpaces: 20,
      deptId: 4,
      deptName: '西城区',
      manager: '李四',
      phone: '13800138002',
      status: '1',
      coordinates: [116.298904, 39.96984],
      polygon: [[116.297904, 39.969], [116.299904, 39.969], [116.299904, 39.971], [116.297904, 39.971]],
      remark: '西部线路调度中心'
    },
    {
      hubId: 3,
      hubName: '南站停车场',
      hubCode: 'HUB003',
      hubType: 'parking',
      address: '北京市丰台区南站南路200号',
      parkingSpaces: 80,
      occupiedSpaces: 65,
      deptId: 6,
      deptName: '南城区',
      manager: '王五',
      phone: '13800138003',
      status: '1',
      coordinates: [116.378243, 39.865616],
      polygon: [[116.377243, 39.864], [116.379243, 39.864], [116.379243, 39.867], [116.377243, 39.867]],
      remark: '大型停车保养场所'
    },
    {
      hubId: 4,
      hubName: '北郊维修站',
      hubCode: 'HUB004',
      hubType: 'maintenance',
      address: '北京市昌平区北七家镇工业路50号',
      parkingSpaces: 25,
      occupiedSpaces: 8,
      deptId: 7,
      deptName: '北城区',
      manager: '赵六',
      phone: '13800138004',
      status: '2',
      coordinates: [116.395645, 40.094296],
      polygon: [[116.394645, 40.093], [116.396645, 40.093], [116.396645, 40.095], [116.394645, 40.095]],
      remark: '车辆维修保养站点'
    },
    {
      hubId: 5,
      hubName: '机场线枢纽',
      hubCode: 'HUB005',
      hubType: 'terminal',
      address: '北京市顺义区机场高速西侧',
      parkingSpaces: 40,
      occupiedSpaces: 40,
      deptId: 3,
      deptName: '东城区',
      manager: '孙七',
      phone: '13800138005',
      status: '1',
      coordinates: [116.585445, 40.072711],
      polygon: [[116.584445, 40.071], [116.586445, 40.071], [116.586445, 40.074], [116.584445, 40.074]],
      remark: '机场线专用场站'
    },
    {
      hubId: 6,
      hubName: 'CBD停车场',
      hubCode: 'HUB006',
      hubType: 'parking',
      address: '北京市朝阳区建国门外大街CBD',
      parkingSpaces: 60,
      occupiedSpaces: 45,
      deptId: 3,
      deptName: '东城区',
      manager: '周八',
      phone: '13800138006',
      status: '1',
      coordinates: [116.447444, 39.909236],
      polygon: [[116.446444, 39.908], [116.448444, 39.908], [116.448444, 39.91], [116.446444, 39.91]],
      remark: 'CBD核心区域停车场'
    }
  ];

  setTimeout(() => {
    let filteredData = mockData;

    // 根据组织机构筛选
    if (queryParams.value.deptId) {
      filteredData = filteredData.filter(item => item.deptId === queryParams.value.deptId);
    }

    // 根据场站编号筛选
    if (queryParams.value.hubCode) {
      filteredData = filteredData.filter(item =>
        item.hubCode.includes(queryParams.value.hubCode)
      );
    }

    // 根据场站名称筛选
    if (queryParams.value.hubName) {
      filteredData = filteredData.filter(item =>
        item.hubName.includes(queryParams.value.hubName)
      );
    }

    // 根据场站类型筛选
    if (queryParams.value.hubType) {
      filteredData = filteredData.filter(item => item.hubType === queryParams.value.hubType);
    }

    // 根据状态筛选
    if (queryParams.value.status) {
      filteredData = filteredData.filter(item => item.status === queryParams.value.status);
    }

    hubList.value = filteredData;
    total.value = filteredData.length;
    loading.value = false;
  }, 500);
}

// 其他方法实现
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  queryFormRef.value?.resetFields();
  queryParams.value.pageNum = 1;
  queryParams.value.deptId = null;
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

// 卡片选择切换
function toggleSelection(hub) {
  const index = selectedHubs.value.indexOf(hub.hubId);
  if (index > -1) {
    selectedHubs.value.splice(index, 1);
  } else {
    selectedHubs.value.push(hub.hubId);
  }

  // 更新选择状态
  ids.value = selectedHubs.value;
  single.value = selectedHubs.value.length !== 1;
  multiple.value = selectedHubs.value.length === 0;
}

function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加场站';
  nextTick(() => {
    initMap();
  });
}

function handleUpdate(row) {
  reset();
  form.value = { ...row };
  open.value = true;
  title.value = '修改场站';
  nextTick(() => {
    initMap();
    if (row.polygon && row.polygon.length > 0) {
      loadPolygonToMap(row.polygon);
    }
    if (row.coordinates) {
      mapInstance.value?.setCenter(row.coordinates);
    }
  });
}

function handleMapDetail(row) {
  // 功能已移除
}

function handleMapView() {
  // 功能已移除
}

function handleDelete(row) {
  const hubIds = row.hubId || ids.value;
  proxy.$modal.confirm('是否确认删除场站编号为"' + (Array.isArray(hubIds) ? hubIds.join(',') : hubIds) + '"的数据项？').then(function() {
    proxy.$modal.msgSuccess('删除成功');
    getList();
  }).catch(() => {
  });
}

function submitForm() {
  hubFormRef.value?.validate(valid => {
    if (valid) {
      submitting.value = true;

      // 保存多边形数据
      if (currentPolygon.value) {
        const path = currentPolygon.value.getPath();
        form.value.polygon = path.map(point => [point.lng, point.lat]);
      }

      setTimeout(() => {
        if (form.value.hubId != null) {
          proxy.$modal.msgSuccess('修改成功');
        } else {
          proxy.$modal.msgSuccess('新增成功');
        }
        submitting.value = false;
        open.value = false;
        getList();
      }, 1000);
    }
  });
}

function cancel() {
  open.value = false;
  reset();
  destroyMap();
}

function reset() {
  form.value = {
    hubId: null,
    hubName: null,
    hubCode: null,
    hubType: null,
    address: null,
    parkingSpaces: null,
    deptId: null,
    manager: null,
    phone: null,
    status: '1',
    coordinates: null,
    polygon: null,
    remark: null
  };
  hubFormRef.value?.resetFields();
  hubFormRef.value?.clearValidate();

  // 重置地图相关状态
  currentPolygon.value = null;
  polygonArea.value = 0;
  polygonPerimeter.value = 0;
}

// 地图相关方法
function initMap() {
  if (typeof AMap === 'undefined') {
    proxy.$modal.msgError('地图服务未加载，请检查网络连接');
    return;
  }

  mapLoading.value = true;

  try {
    // 抑制地图警告
    suppressMapWarnings();

    // 创建地图实例
    mapInstance.value = createOptimizedMap('hubMap', {
      center: [116.397428, 39.90923],
      zoom: 15
    });

    // 添加控件
    addOptimizedControls(mapInstance.value);

    // 初始化绘制管理器
    AMap.plugin(['AMap.MouseTool', 'AMap.PolyEditor'], () => {
      drawingManager.value = new AMap.MouseTool(mapInstance.value);
      mapReady.value = true;
      mapLoading.value = false;
    });

  } catch (error) {
    handleMapError(error, '地图初始化');
    mapLoading.value = false;
  }
}

function destroyMap() {
  if (mapInstance.value) {
    mapInstance.value.destroy();
    mapInstance.value = null;
  }
  if (drawingManager.value) {
    drawingManager.value = null;
  }
  mapReady.value = false;
  currentPolygon.value = null;
}

function startDrawing() {
  if (!drawingManager.value) return;

  // 清除之前的图形
  clearDrawing();

  // 开始绘制多边形
  drawingManager.value.polygon({
    fillColor: '#00b0ff',
    fillOpacity: 0.3,
    strokeColor: '#0066cc',
    strokeWeight: 2
  });

  // 监听绘制完成事件
  drawingManager.value.on('draw', (e) => {
    currentPolygon.value = e.obj;
    calculatePolygonInfo();
    drawingManager.value.close();
  });
}

function clearDrawing() {
  if (currentPolygon.value) {
    mapInstance.value.remove(currentPolygon.value);
    currentPolygon.value = null;
    polygonArea.value = 0;
    polygonPerimeter.value = 0;
  }
}

function loadPolygonToMap(polygonData) {
  if (!mapInstance.value || !polygonData || polygonData.length === 0) return;

  const path = polygonData.map(point => new AMap.LngLat(point[0], point[1]));

  currentPolygon.value = new AMap.Polygon({
    path: path,
    fillColor: '#00b0ff',
    fillOpacity: 0.3,
    strokeColor: '#0066cc',
    strokeWeight: 2
  });

  mapInstance.value.add(currentPolygon.value);
  calculatePolygonInfo();
}

function calculatePolygonInfo() {
  if (!currentPolygon.value) return;

  try {
    // 计算面积和周长
    const path = currentPolygon.value.getPath();
    polygonArea.value = Math.round(AMap.GeometryUtil.ringArea(path));
    polygonPerimeter.value = Math.round(AMap.GeometryUtil.ringPerimeter(path));
  } catch (error) {
    console.warn('计算多边形信息失败:', error);
  }
}

function locateByAddress() {
  if (!form.value.address || !mapInstance.value) {
    proxy.$modal.msgWarning('请先输入地址信息');
    return;
  }

  proxy.$modal.msgInfo('正在定位地址...');

  AMap.plugin('AMap.Geocoder', () => {
    const geocoder = new AMap.Geocoder();
    geocoder.getLocation(form.value.address, (status, result) => {
      if (status === 'complete' && result.geocodes.length > 0) {
        const location = result.geocodes[0].location;
        mapInstance.value.setCenter([location.lng, location.lat]);
        mapInstance.value.setZoom(18);

        // 清除之前的标记点
        mapInstance.value.clearMap();
        if (currentPolygon.value) {
          mapInstance.value.add(currentPolygon.value);
        }

        // 添加新的标记点
        const marker = new AMap.Marker({
          position: [location.lng, location.lat],
          title: form.value.address,
          icon: new AMap.Icon({
            size: new AMap.Size(32, 32),
            image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png'
          })
        });
        mapInstance.value.add(marker);

        // 保存坐标
        form.value.coordinates = [location.lng, location.lat];

        proxy.$modal.msgSuccess('地址定位成功');
      } else {
        proxy.$modal.msgError('地址定位失败，请检查地址是否正确');
      }
    });
  });
}

// 处理地址变化事件
function handleAddressChange() {
  // 当地址发生变化时，可以选择是否自动定位
  // 这里暂时不自动定位，避免频繁调用API
}

// 生命周期
onMounted(() => {
  getList();
  getTreeSelect();
});

onUnmounted(() => {
  destroyMap();
});
</script>

<style scoped>
/* 全屏布局基础样式 - 科技感深色主题 */
.hub-management-fullscreen {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 左侧面板样式 */
.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 24px;
}

.search-box {
  margin-bottom: 16px;
}

.dept-tree {
  background: transparent;
  color: #cbd5e1;
}

/* 右侧内容区域 */
.right-content {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  backdrop-filter: blur(10px);
}

/* 顶部工具栏 */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 24px;
  color: #60a5fa;
}

.page-title {
  font-size: 20px;
  font-weight: 700;
  color: #f8fafc;
  margin: 0;
}

.stats-cards {
  display: flex;
  gap: 12px;
}

.stat-card {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  padding: 12px 16px;
  text-align: center;
  min-width: 80px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
}

.stat-card.online {
  border-color: rgba(34, 197, 94, 0.4);
  background: rgba(34, 197, 94, 0.1);
}

.stat-card.warning {
  border-color: rgba(245, 158, 11, 0.4);
  background: rgba(245, 158, 11, 0.1);
}

.stat-card.danger {
  border-color: rgba(239, 68, 68, 0.4);
  background: rgba(239, 68, 68, 0.1);
}

.stat-value {
  font-size: 22px;
  font-weight: 700;
  color: #f8fafc;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #94a3b8;
  margin-top: 4px;
}

/* 搜索区域 */
.search-section {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 场站卡片容器 */
.hub-cards-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  min-height: 400px;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
}

/* 场站卡片样式 - 科技感设计 */
.hub-card {
  background: linear-gradient(145deg,
  rgba(30, 41, 59, 0.9) 0%,
  rgba(51, 65, 85, 0.8) 100%);
  border: 2px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3),
  inset 0 1px 0 rgba(255, 255, 255, 0.1);
  height: 320px;
  display: flex;
  flex-direction: column;
}

.hub-card:hover {
  transform: translateY(-4px);
  border-color: rgba(96, 165, 250, 0.4);
  box-shadow: 0 12px 40px rgba(96, 165, 250, 0.2),
  0 0 30px rgba(96, 165, 250, 0.1),
  inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.hub-card.selected {
  border-color: #22c55e;
  background: linear-gradient(145deg,
  rgba(34, 197, 94, 0.15) 0%,
  rgba(30, 41, 59, 0.9) 50%,
  rgba(51, 65, 85, 0.8) 100%);
  box-shadow: 0 0 30px rgba(34, 197, 94, 0.3),
  0 8px 25px rgba(0, 0, 0, 0.3),
  inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 科技感装饰线 */
.hub-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg,
  transparent 0%,
  rgba(96, 165, 250, 0.6) 50%,
  transparent 100%);
}

.hub-card.selected::before {
  background: linear-gradient(90deg,
  transparent 0%,
  rgba(34, 197, 94, 0.8) 50%,
  transparent 100%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  flex-shrink: 0;
}

.hub-info {
  flex: 1;
}

.hub-name {
  font-size: 14px;
  font-weight: bold;
  color: #f8fafc;
  margin-bottom: 4px;
  text-shadow: 0 0 10px rgba(96, 165, 250, 0.3);
  line-height: 1.2;
}

.hub-code {
  font-size: 11px;
  color: #94a3b8;
  font-weight: 500;
}

.status-badge {
  margin-left: 8px;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
  flex: 1;
}

.hub-image {
  text-align: center;
  padding: 10px 0;
  background: linear-gradient(135deg,
  rgba(96, 165, 250, 0.1) 0%,
  rgba(59, 130, 246, 0.05) 100%);
  border-radius: 8px;
  border: 1px solid rgba(96, 165, 250, 0.2);
  flex-shrink: 0;
}

.hub-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.detail-row {
  display: flex;
  gap: 8px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 6px;
  background: linear-gradient(135deg,
  rgba(15, 23, 42, 0.8) 0%,
  rgba(30, 41, 59, 0.6) 100%);
  border-radius: 6px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  font-size: 11px;
  flex: 1;
  text-align: center;
}

.detail-item .label {
  color: #94a3b8;
  font-weight: 500;
  font-size: 10px;
  margin-bottom: 4px;
  white-space: nowrap;
}

.detail-item .value {
  color: #f8fafc;
  font-weight: 600;
  font-size: 11px;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  padding-top: 12px;
  border-top: 1px solid rgba(147, 197, 253, 0.2);
  flex-shrink: 0;
}

.card-actions .el-button {
  flex: 1;
  font-size: 11px;
  padding: 8px 10px;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-weight: 600;
  border-width: 1px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 0;
  color: #94a3b8;
}

/* 对话框样式 */
.form-section {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  height: 700px;
  overflow-y: auto;
}

.map-section {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  height: 700px;
  display: flex;
  flex-direction: column;
}

.form-tip {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(96, 165, 250, 0.1);
  border: 1px solid rgba(96, 165, 250, 0.3);
  border-radius: 6px;
  font-size: 12px;
  color: #93c5fd;
}

.form-tip .el-icon {
  color: #60a5fa;
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
}

.area-info {
  display: flex;
  align-items: center;
}

.section-title {
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.map-tools {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.map-container {
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
  min-height: 520px;
  background: #1a1a1a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hub-management-fullscreen {
    padding: 10px;
  }

  .header-left {
    flex-direction: column;
    gap: 16px;
    width: 100%;
  }

  .stats-cards {
    width: 100%;
    justify-content: space-around;
  }

  .cards-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 14px;
  }

  .hub-card {
    height: 300px;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .form-section,
  .map-section {
    height: auto;
    min-height: 400px;
  }
}
</style>
