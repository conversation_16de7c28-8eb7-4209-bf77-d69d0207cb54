<!-- 快速复制模式参数配置组件 -->
<template>
  <div class="copy-config">
    <el-form ref="formRef" :model="config" label-width="120px" size="large">
      <div class="config-section">
        <h4 class="section-title">
          <el-icon><Clock /></el-icon>
          基本设置
        </h4>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="模板名称" required>
              <el-input v-model="config.templateName" placeholder="请输入模板名称" maxlength="50" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="config-section">
        <h4 class="section-title">
          <el-icon><CopyDocument /></el-icon>
          选择源模板
        </h4>
        <div class="source-template-selection">
          <el-select v-model="config.sourceTemplateId" placeholder="请选择要复制的模板" style="width: 100%" @change="handleSourceTemplateChange">
            <el-option
              v-for="template in availableTemplates"
              :key="template.templateId"
              :label="`${template.templateName} (${template.routeName})`"
              :value="template.templateId"
            >
              <div class="template-option">
                <div class="option-header">
                  <span class="template-name">{{ template.templateName }}</span>
                  <el-tag :type="getTemplateTypeTag(template.templateType).type" size="small">
                    {{ getTemplateTypeTag(template.templateType).label }}
                  </el-tag>
                </div>
                <div class="option-details">
                  <span>{{ template.routeName }}</span>
                  <span>{{ template.firstBusTime }}-{{ template.lastBusTime }}</span>
                  <span v-if="template.departureInterval">{{ template.departureInterval }}分钟间隔</span>
                </div>
              </div>
            </el-option>
          </el-select>
        </div>

        <div v-if="selectedTemplate" class="source-template-preview">
          <h5>源模板信息：</h5>
          <div class="template-info-card">
            <div class="info-header">
              <h6>{{ selectedTemplate.templateName }}</h6>
              <div class="tags">
                <el-tag :type="getTemplateTypeTag(selectedTemplate.templateType).type" size="small">
                  {{ getTemplateTypeTag(selectedTemplate.templateType).label }}
                </el-tag>
                <el-tag type="info" size="small">{{ selectedTemplate.routeName }}</el-tag>
              </div>
            </div>
            <div class="info-details">
              <div class="detail-item">
                <span class="label">运营时间：</span>
                <span class="value">{{ selectedTemplate.firstBusTime }} - {{ selectedTemplate.lastBusTime }}</span>
              </div>
              <div class="detail-item" v-if="selectedTemplate.departureInterval">
                <span class="label">发车间隔：</span>
                <span class="value">{{ selectedTemplate.departureInterval }}分钟</span>
              </div>
              <div class="detail-item" v-if="selectedTemplate.dailyTrips">
                <span class="label">日发车次：</span>
                <span class="value">{{ selectedTemplate.dailyTrips }}班</span>
              </div>
              <div class="detail-item" v-if="selectedTemplate.templateDescription">
                <span class="label">描述：</span>
                <span class="value">{{ selectedTemplate.templateDescription }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="config-section" v-if="config.sourceTemplateId">
        <h4 class="section-title">
          <el-icon><Setting /></el-icon>
          调整参数
        </h4>

        <div class="adjustment-options">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="时间偏移">
                <el-input-number v-model="config.timeOffset" :min="-180" :max="180" :step="15" style="width: 100%" />
                <div class="form-tip">整体提前/延后分钟数（负数为提前，正数为延后）</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="班次调整">
                <el-slider
                  v-model="config.tripAdjustment"
                  :min="-50"
                  :max="100"
                  :step="5"
                  show-stops
                  show-input
                  :format-tooltip="formatTripTooltip"
                />
                <div class="form-tip">增减班次百分比（负数为减少，正数为增加）</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="间隔调整">
                <el-input-number v-model="config.intervalAdjustment" :min="-30" :max="30" :step="1" style="width: 100%" />
                <div class="form-tip">发车间隔增减分钟数</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="运营时长调整">
                <el-input-number v-model="config.operatingTimeAdjustment" :min="-120" :max="120" :step="15" style="width: 100%" />
                <div class="form-tip">运营时长增减分钟数</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="quick-adjustments">
          <h5>快速调整：</h5>
          <el-button-group>
            <el-button @click="applyQuickAdjustment('peak')">高峰加密</el-button>
            <el-button @click="applyQuickAdjustment('reduce')">减班运营</el-button>
            <el-button @click="applyQuickAdjustment('extend')">延长运营</el-button>
            <el-button @click="applyQuickAdjustment('reset')">重置调整</el-button>
          </el-button-group>
        </div>
      </div>

      <div class="preview-section" v-if="config.sourceTemplateId">
        <h4 class="section-title">
          <el-icon><DataAnalysis /></el-icon>
          调整效果预览
        </h4>
        <div class="preview-comparison">
          <div class="comparison-item">
            <h5>原始模板</h5>
            <div class="comparison-stats">
              <div class="stat-item">
                <span class="stat-value">{{ originalStats.trips }}</span>
                <span class="stat-label">班次</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ originalStats.interval }}</span>
                <span class="stat-label">平均间隔</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ originalStats.operatingTime }}</span>
                <span class="stat-label">运营时长</span>
              </div>
            </div>
          </div>

          <div class="comparison-arrow">
            <el-icon size="24"><Right /></el-icon>
          </div>

          <div class="comparison-item">
            <h5>调整后效果</h5>
            <div class="comparison-stats">
              <div class="stat-item">
                <span
                  class="stat-value"
                  :class="{ 'increased': adjustedStats.trips > originalStats.trips, 'decreased': adjustedStats.trips < originalStats.trips }"
                >
                  {{ adjustedStats.trips }}
                </span>
                <span class="stat-label">班次</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ adjustedStats.interval }}</span>
                <span class="stat-label">平均间隔</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ adjustedStats.operatingTime }}</span>
                <span class="stat-label">运营时长</span>
              </div>
            </div>
          </div>
        </div>

        <div class="adjustment-summary">
          <h5>调整摘要：</h5>
          <div class="summary-items">
            <div v-if="config.timeOffset !== 0" class="summary-item">
              <el-icon><Timer /></el-icon>
              <span>时间{{ config.timeOffset > 0 ? '延后' : '提前' }}{{ Math.abs(config.timeOffset) }}分钟</span>
            </div>
            <div v-if="config.tripAdjustment !== 0" class="summary-item">
              <el-icon><TrendCharts /></el-icon>
              <span>班次{{ config.tripAdjustment > 0 ? '增加' : '减少' }}{{ Math.abs(config.tripAdjustment) }}%</span>
            </div>
            <div v-if="config.intervalAdjustment !== 0" class="summary-item">
              <el-icon><Clock /></el-icon>
              <span>间隔{{ config.intervalAdjustment > 0 ? '增加' : '减少' }}{{ Math.abs(config.intervalAdjustment) }}分钟</span>
            </div>
            <div v-if="config.operatingTimeAdjustment !== 0" class="summary-item">
              <el-icon><Timer /></el-icon>
              <span>运营时长{{ config.operatingTimeAdjustment > 0 ? '延长' : '缩短' }}{{ Math.abs(config.operatingTimeAdjustment) }}分钟</span>
            </div>
          </div>
        </div>
      </div>

      <div class="config-section">
        <h4 class="section-title">
          <el-icon><EditPen /></el-icon>
          其他设置
        </h4>
        <el-form-item label="模板描述">
          <el-input v-model="config.description" type="textarea" :rows="3" placeholder="请输入模板描述（可选）" maxlength="200" show-word-limit />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Clock, CopyDocument, Setting, DataAnalysis, EditPen, Timer, TrendCharts, ArrowRight, Right } from '@element-plus/icons-vue';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue']);

// Form reference
const formRef = ref();

const config = ref({
  templateName: '',
  routeId: null,
  sourceTemplateId: null,
  timeOffset: 0,
  tripAdjustment: 0,
  intervalAdjustment: 0,
  operatingTimeAdjustment: 0,
  description: '',
  ...props.modelValue
});

const routeOptions = ref([
  { routeId: 1, routeName: '1路' },
  { routeId: 2, routeName: '2路' },
  { routeId: 3, routeName: '3路' },
  { routeId: 4, routeName: '4路' }
]);

// 可用的模板列表（模拟数据）
const availableTemplates = ref([
  {
    templateId: 1,
    templateName: '1路固定间隔模板',
    routeId: 1,
    routeName: '1路',
    templateType: 'fixed',
    departureInterval: 8,
    firstBusTime: '05:30',
    lastBusTime: '22:30',
    dailyTrips: 128,
    templateDescription: '1路线固定间隔发车模板，适用于平峰时段'
  },
  {
    templateId: 2,
    templateName: '2路分时段模板',
    routeId: 2,
    routeName: '2路',
    templateType: 'period',
    departureInterval: null,
    firstBusTime: '06:00',
    lastBusTime: '21:30',
    dailyTrips: 156,
    templateDescription: '2路线高峰时段加密发车模板'
  },
  {
    templateId: 3,
    templateName: '3路智能密度模板',
    routeId: 3,
    routeName: '3路',
    templateType: 'density',
    departureInterval: 12,
    firstBusTime: '06:30',
    lastBusTime: '20:00',
    dailyTrips: 68,
    templateDescription: '3路线智能密度模板'
  }
]);

const selectedTemplate = ref(null);

// 获取模板类型标签
function getTemplateTypeTag(type) {
  const typeMap = {
    'fixed': { label: '固定间隔', type: 'info' },
    'period': { label: '分时段', type: 'warning' },
    'density': { label: '智能密度', type: 'success' },
    'holiday': { label: '节假日', type: 'primary' },
    'custom': { label: '自定义', type: 'success' }
  };
  return typeMap[type] || { label: '未知', type: 'info' };
}

// 处理源模板选择
function handleSourceTemplateChange(templateId) {
  selectedTemplate.value = availableTemplates.value.find((t) => t.templateId === templateId);

  // 自动设置模板名称
  if (selectedTemplate.value && !config.value.templateName) {
    config.value.templateName = `${selectedTemplate.value.templateName}(复制)`;
  }
}

// 原始统计数据
const originalStats = computed(() => {
  if (!selectedTemplate.value) return { trips: '--', interval: '--', operatingTime: '--' };

  return {
    trips: selectedTemplate.value.dailyTrips || '--',
    interval: selectedTemplate.value.departureInterval ? `${selectedTemplate.value.departureInterval}分钟` : '--',
    operatingTime: calculateOperatingTime(selectedTemplate.value.firstBusTime, selectedTemplate.value.lastBusTime)
  };
});

// 调整后统计数据
const adjustedStats = computed(() => {
  if (!selectedTemplate.value) return { trips: '--', interval: '--', operatingTime: '--' };

  const baseTrips = selectedTemplate.value.dailyTrips || 0;
  const baseInterval = selectedTemplate.value.departureInterval || 10;

  // 计算调整后的数据
  const adjustedTrips = Math.round(baseTrips * (1 + config.value.tripAdjustment / 100));
  const adjustedInterval = Math.max(3, baseInterval + config.value.intervalAdjustment);

  const baseOperatingMinutes = getOperatingMinutes(selectedTemplate.value.firstBusTime, selectedTemplate.value.lastBusTime);
  const adjustedOperatingMinutes = baseOperatingMinutes + config.value.operatingTimeAdjustment;

  return {
    trips: adjustedTrips,
    interval: `${adjustedInterval}分钟`,
    operatingTime: formatOperatingTime(adjustedOperatingMinutes)
  };
});

// 计算运营时长
function calculateOperatingTime(startTime, endTime) {
  if (!startTime || !endTime) return '--';

  const [startHour, startMin] = startTime.split(':').map(Number);
  const [endHour, endMin] = endTime.split(':').map(Number);
  const totalMinutes = endHour * 60 + endMin - (startHour * 60 + startMin);

  return formatOperatingTime(totalMinutes);
}

function getOperatingMinutes(startTime, endTime) {
  if (!startTime || !endTime) return 0;

  const [startHour, startMin] = startTime.split(':').map(Number);
  const [endHour, endMin] = endTime.split(':').map(Number);
  return endHour * 60 + endMin - (startHour * 60 + startMin);
}

function formatOperatingTime(totalMinutes) {
  if (totalMinutes <= 0) return '--';

  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  return `${hours}小时${minutes}分钟`;
}

// 快速调整
function applyQuickAdjustment(type) {
  switch (type) {
    case 'peak':
      config.value.tripAdjustment = 25;
      config.value.intervalAdjustment = -3;
      break;
    case 'reduce':
      config.value.tripAdjustment = -30;
      config.value.intervalAdjustment = 5;
      break;
    case 'extend':
      config.value.operatingTimeAdjustment = 60;
      config.value.tripAdjustment = 10;
      break;
    case 'reset':
      config.value.timeOffset = 0;
      config.value.tripAdjustment = 0;
      config.value.intervalAdjustment = 0;
      config.value.operatingTimeAdjustment = 0;
      break;
  }
}

// 格式化班次调整提示
function formatTripTooltip(val) {
  return val > 0 ? `增加${val}%` : val < 0 ? `减少${Math.abs(val)}%` : '不调整';
}

// 监听配置变化
watch(
  config,
  (newValue) => {
    emit('update:modelValue', newValue);
  },
  { deep: true }
);
</script>

<style scoped>
.copy-config {
  padding: 20px 0;
}

.config-section {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.6) 0%, rgba(30, 41, 59, 0.4) 100%);
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
}

.source-template-selection {
  margin-bottom: 20px;
}

.template-option {
  padding: 8px 0;
}

.option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.template-name {
  font-weight: 600;
  color: #1f2937;
}

.option-details {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #6b7280;
}

.source-template-preview {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e5e7eb;
}

.source-template-preview h5 {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 14px;
}

.template-info-card {
  background: #f9fafb;
  border-radius: 6px;
  padding: 16px;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.info-header h6 {
  margin: 0;
  color: #1f2937;
}

.tags {
  display: flex;
  gap: 6px;
}

.info-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.detail-item {
  display: flex;
  font-size: 14px;
}

.detail-item .label {
  color: #6b7280;
  margin-right: 8px;
  min-width: 80px;
}

.detail-item .value {
  color: #1f2937;
  font-weight: 500;
}

.adjustment-options {
  margin-bottom: 20px;
}

.form-tip {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.quick-adjustments {
  background: white;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.quick-adjustments h5 {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 14px;
}

.preview-section {
  background: transparent;
  color: #e2e8f0;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.preview-section .section-title {
  color: #e2e8f0;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
}

.preview-comparison {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.comparison-item {
  flex: 1;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  padding: 16px;
  backdrop-filter: blur(10px);
}

.comparison-item h5 {
  margin: 0 0 12px 0;
  color: white;
  text-align: center;
  font-size: 14px;
}

.comparison-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-value.increased {
  color: #10b981;
}

.stat-value.decreased {
  color: #f59e0b;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
}

.comparison-arrow {
  color: white;
  opacity: 0.8;
}

.adjustment-summary {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 16px;
}

.adjustment-summary h5 {
  margin: 0 0 12px 0;
  color: white;
  font-size: 14px;
}

.summary-items {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 255, 255, 0.1);
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
}

/* Element Plus 深色主题样式覆盖 */
:deep(.el-input .el-input__count .el-input__count-inner) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #94a3b8 !important;
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-form-item__label) {
  color: #e2e8f0 !important;
}

:deep(.el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  box-shadow: none !important;
}

:deep(.el-input__inner) {
  color: #e2e8f0 !important;
}

:deep(.el-input__inner::placeholder) {
  color: #94a3b8 !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

:deep(.el-time-picker .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

:deep(.el-input-number .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  background: rgba(30, 41, 59, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e2e8f0 !important;
}

:deep(.el-textarea__inner) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e2e8f0 !important;
}
</style>
