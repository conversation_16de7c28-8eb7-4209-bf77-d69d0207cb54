<template>
  <div class='big-screen-container'>
    <!-- 顶部标题栏 -->
    <header class='screen-header'>
      <!--      <div class='header-left'>
              <div class='logo-section'>
                <img src='@/assets/logo/logo.png' alt='Logo' class='logo-img' />
                <span class='logo-text'>公交运营大屏监控</span>
              </div>
            </div>

            <div class='header-center'>
              <div class='map-controls'>
                &lt;!&ndash; 显示选项 &ndash;&gt;
                <div class='control-group'>
                  <span class='control-label'>显示选项</span>
                  <el-checkbox-group v-model='displayOptions' size='small' @change='handleDisplayOptionsChange'>
                    <el-checkbox value='stations'>显示站点</el-checkbox>
                    <el-checkbox value='routes'>显示线路</el-checkbox>
                    <el-checkbox value='alerts'>显示告警</el-checkbox>
                    <el-checkbox value='realtime'>实时刷新</el-checkbox>
                  </el-checkbox-group>
                </div>

                &lt;!&ndash; 地图操作 &ndash;&gt;
                <div class='control-group'>
                  <span class='control-label'>地图操作</span>
                  <el-button-group size='small'>
                    <el-button icon='ZoomIn' @click='zoomIn'>放大</el-button>
                    <el-button icon='ZoomOut' @click='zoomOut'>缩小</el-button>
                    <el-button icon='Aim' @click='resetView'>重置</el-button>
                  </el-button-group>
                </div>
              </div>
            </div>

            <div class='header-right'>
              <div class='current-time'>
                <div class='time-display'>{{ currentTime }}</div>
                <div class='date-display'>{{ currentDate }}</div>
              </div>

              &lt;!&ndash; 全屏按钮移到时间右侧 &ndash;&gt;
              <div class='header-actions'>
                <el-button type='primary' icon='FullScreen' circle @click='toggleFullscreen'
                           class='fullscreen-btn-header'></el-button>
              </div>
            </div>-->
    </header>

    <!-- 主体内容区域 -->
    <main class='screen-main'>
      <!-- 地图作为底图占用全屏 -->
      <section class='map-section fullscreen-map'>
        <!-- 地图容器占用全部空间 -->
        <div class='map-container'>
          <div ref='mapContainer' id='container' class='map-canvas'></div>

          <!-- 地图图例 -->
          <div class='map-legend'>
            <div class='legend-title'>图例</div>
            <div class='legend-items'>
              <div class='legend-item'>
                <div class='legend-marker running'></div>
                <span>运营中</span>
              </div>
              <div class='legend-item'>
                <div class='legend-marker stopped'></div>
                <span>停靠中</span>
              </div>
              <div class='legend-item'>
                <div class='legend-marker maintenance'></div>
                <span>维修中</span>
              </div>
              <div class='legend-item'>
                <div class='legend-marker fault'></div>
                <span>故障</span>
              </div>
              <div class='legend-item'>
                <div class='legend-marker station'></div>
                <span>公交站点</span>
              </div>
            </div>
          </div>

          <!-- 轨迹回放控制面板 -->
          <div class="track-playback-controls">
            <div class="playback-header">
              <h4>轨迹回放测试</h4>
              <el-button @click="loadTestTrack" size="small" type="primary">加载测试轨迹</el-button>
            </div>

            <!-- 官方示例播放控制按钮 -->
            <div class="playback-buttons">
              <el-button-group>
                <el-button @click="speed" :disabled="!marker">
                  <el-icon><Odometer /></el-icon>{{ durationName }}
                </el-button>
                <el-button @click="startAnimation()" :disabled="playAble">
                  <el-icon><VideoPlay /></el-icon>开始
                </el-button>
                <el-button @click="pauseAnimation()" :disabled="stopAble">
                  <el-icon><VideoPause /></el-icon>暂停
                </el-button>
                <el-button @click="resumeAnimation()" :disabled="!marker">
                  <el-icon><VideoPlay /></el-icon>继续
                </el-button>
                <el-button @click="stopAnimation()" :disabled="restAble">
                  <el-icon><RefreshLeft /></el-icon>停止
                </el-button>
              </el-button-group>
            </div>

            <!-- 进度滑块 -->
            <div class="playback-slider">
              <el-slider
                :disabled="restAble"
                :show-tooltip="false"
                :min="0.01"
                :max="99"
                :step="0.01"
                style="width: 100%"
                v-model="sliderValue"
                @change="sliderHandle"
              />
            </div>
          </div>
        </div>
      </section>

      <!-- 左侧组件 - 车辆监控和车辆状态 -->
      <div class='side-panel left-panel'
           :style="{
             position: 'absolute',
             top: '20px',
             left: '20px',
             bottom: '20px',
             width: '320px',
             display: 'flex',
             flexDirection: 'column',
             gap: '20px',
             zIndex: 100,
             boxSizing: 'border-box'
           }">

        <!-- 车辆监控区域 -->
        <div class='panel-card monitoring-panel' :style="{ flex: '1', display: 'flex', flexDirection: 'column' }">
          <div class='card-header'>
            <el-icon>
              <TrendCharts />
            </el-icon>
            <span>车辆监控</span>

          </div>
          <div class='monitoring-content' :style="{ flex: '1', overflowY: 'auto' }">
            <div class='vehicle-items-mini'>
              <div class='vehicle-item-mini' v-for='vehicle in displayVehicles' :key='vehicle.id'
                   @click='locateVehicleFromSidebar(vehicle)'>
                <div class='vehicle-status-indicator-mini' :class='vehicle.status'></div>
                <div class='vehicle-info-mini'>
                  <div class='vehicle-number-mini'>{{ vehicle.plateNumber }}</div>
                  <div class='vehicle-route-mini'>{{ vehicle.routeName }}</div>
                </div>
                <div class='vehicle-data-mini'>
                  <span class='data-value-mini'>{{ vehicle.speed }}km/h</span>
                  <span class='data-value-mini'>{{ vehicle.passengers }}人</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 车辆状态区域（饼图） -->
        <div class='panel-card status-panel' :style="{ flex: '1', display: 'flex', flexDirection: 'column' }">
          <div class='card-header'>
            <el-icon>
              <Odometer />
            </el-icon>
            <span>车辆状态</span>
          </div>
        </div>
      </div>

    </main>


  </div>
</template>

<script setup>
import {
  createOptimizedMap,
  addOptimizedControls,
  createOptimizedMarker,
  suppressMapWarnings,
  handleMapError
} from '@/utils/mapConfig';
import { onMounted, onUnmounted, reactive, ref } from 'vue';
import { Odometer, VideoPlay, VideoPause, RefreshLeft } from '@element-plus/icons-vue';

let map = null;
let vehicleMarkers = [];

// 轨迹回放相关变量
const trackData = ref([]);
const marker = ref(null);
const polyline = ref(null);
const passedPolyline = ref(null);
const lineArr = ref([]);
const linePath = ref([]);
const passedPath = ref([]);
const sliderValue = ref(0.01);
const playAble = ref(true);
const stopAble = ref(true);
const restAble = ref(true);
const duration = ref(200);
const durationIndex = ref(1);
const durationName = ref('x1.0');
const durations = [
  { v: 400, k: 'x0.5' },
  { v: 200, k: 'x1.0' },
  { v: 100, k: 'x2.0' },
  { v: 50, k: 'x4.0' }
];
const passedPathIndex = ref(0);

// 响应式数据
const currentTime = ref('');
const currentDate = ref('');
const selectedRoute = ref('');
const selectedStatus = ref('');

// 车辆列表数据
const displayVehicles = reactive([
  {
    id: 1,
    plateNumber: '赣A12345',
    routeName: '1路',
    status: 'running',
    speed: 35,
    passengers: 28,
    lat: 28.6765,
    lng: 115.8921,
    deptId: 1,
    deptName: '第一车队'
  },
  {
    id: 2,
    plateNumber: '赣A67890',
    routeName: '8路',
    status: 'stopped',
    speed: 0,
    passengers: 15,
    lat: 28.6825,
    lng: 115.8975,
    deptId: 1,
    deptName: '第一车队'
  },
  {
    id: 3,
    plateNumber: '赣B54321',
    routeName: '15路',
    status: 'running',
    speed: 42,
    passengers: 32,
    lat: 28.6695,
    lng: 115.9045,
    deptId: 2,
    deptName: '第二车队'
  },
  {
    id: 4,
    plateNumber: '赣B98765',
    routeName: '22路',
    status: 'maintenance',
    speed: 0,
    passengers: 0,
    lat: 28.6835,
    lng: 115.8865,
    deptId: 2,
    deptName: '第二车队'
  },
  {
    id: 5,
    plateNumber: '赣C11223',
    routeName: '6路',
    status: 'running',
    speed: 38,
    passengers: 25,
    lat: 28.6715,
    lng: 115.9085,
    deptId: 3,
    deptName: '第三车队'
  },
  {
    id: 6,
    plateNumber: '赣C44556',
    routeName: '13路',
    status: 'stopped',
    speed: 0,
    passengers: 8,
    lat: 28.6645,
    lng: 115.8795,
    deptId: 3,
    deptName: '第三车队'
  },
  {
    id: 7,
    plateNumber: '赣D77889',
    routeName: '25路',
    status: 'running',
    speed: 32,
    passengers: 19,
    lat: 28.6895,
    lng: 115.9125,
    deptId: 4,
    deptName: '第四车队'
  },
  {
    id: 8,
    plateNumber: '赣D99012',
    routeName: '31路',
    status: 'fault',
    speed: 0,
    passengers: 0,
    lat: 28.6585,
    lng: 115.8755,
    deptId: 4,
    deptName: '第四车队'
  },
  {
    id: 9,
    plateNumber: '赣E33445',
    routeName: '1路',
    status: 'running',
    speed: 28,
    passengers: 31,
    lat: 28.6735,
    lng: 115.8995,
    deptId: 1,
    deptName: '第一车队'
  },
  {
    id: 10,
    plateNumber: '赣E66778',
    routeName: '8路',
    status: 'running',
    speed: 45,
    passengers: 27,
    lat: 28.6785,
    lng: 115.9175,
    deptId: 2,
    deptName: '第二车队'
  }
]);

const initMap = () => {
  setTimeout(() => {
    map = createOptimizedMap('container', {
      zoom: 12,
      center: [115.8921, 28.6765], // 南昌坐标
      mapStyle: 'amap://styles/dark' // 使用暗色主题匹配大屏风格
    });

    // 添加控件
    //addOptimizedControls(map);

    // 添加车辆标记
    //addVehicleMarkers();
  }, 1000);
};

// 添加车辆标记
const addVehicleMarkers = () => {
  if (!map) return;

  // 清除现有标记
  vehicleMarkers.forEach(marker => {
    map.remove(marker);
  });
  vehicleMarkers = [];

  displayVehicles.forEach(vehicle => {

    // 创建车辆标记
    const marker = createOptimizedMarker(
      [vehicle.lng, vehicle.lat],
      `${vehicle.plateNumber} - ${vehicle.routeName}`,
      {
        // 根据车辆状态设置不同的颜色
        icon: createVehicleIcon(vehicle.status),
        clickable: true
      }
    );

    // 为标记添加车辆ID，用于后续识别
    marker.vehicleId = vehicle.id;

    // 设置车辆标记的初始层级
    marker.setzIndex(100);

    // 添加点击事件显示车辆信息
    marker.on('click', () => {
      //showMapInfoDialog('vehicle', `车辆信息 - ${vehicle.plateNumber}`, vehicle);
    });

    map.add(marker);
    vehicleMarkers.push(marker);
  });
};

// 创建车辆图标
const createVehicleIcon = (status) => {
  const colors = {
    running: '#67c23a',
    stopped: '#e6a23c',
    maintenance: '#f56c6c',
    fault: '#909399'
  };

  const color = colors[status] || '#409eff';

  // 创建SVG图标
  const svgIcon = `
    <svg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <rect x='2' y='8' width='20' height='10' rx='2' fill='${color}' stroke='white' stroke-width='1'/>
      <circle cx='7' cy='16' r='2' fill='white' stroke='${color}' stroke-width='1'/>
      <circle cx='17' cy='16' r='2' fill='white' stroke='${color}' stroke-width='1'/>
      <rect x='4' y='10' width='16' height='4' rx='1' fill='white' opacity='0.8'/>
    </svg>
  `;

  return new AMap.Icon({
    image: `data:image/svg+xml;base64,${btoa(svgIcon)}`,
    size: [24, 24],
    imageSize: [24, 24]
  });
};

// 生成测试轨迹数据
const loadTestTrack = () => {
  // 生成测试轨迹数据 - 南昌地区的一条路线
  const testCoords = [
    [115.8921, 28.6765], [115.8935, 28.6780], [115.8950, 28.6795],
    [115.8965, 28.6810], [115.8980, 28.6825], [115.8995, 28.6840],
    [115.9010, 28.6855], [115.9025, 28.6870], [115.9040, 28.6885],
    [115.9055, 28.6900], [115.9070, 28.6915], [115.9085, 28.6930],
    [115.9100, 28.6945], [115.9115, 28.6960], [115.9130, 28.6975],
    [115.9145, 28.6990], [115.9160, 28.7005], [115.9175, 28.7020],
    [115.9190, 28.7035], [115.9205, 28.7050]
  ];

  lineArr.value = testCoords;
  linePath.value = [...testCoords];
  trackData.value = testCoords;

  // 创建车辆标记
  if (marker.value) {
    map.remove(marker.value);
  }
  if (polyline.value) {
    map.remove(polyline.value);
  }
  if (passedPolyline.value) {
    map.remove(passedPolyline.value);
  }

  marker.value = new AMap.Marker({
    map: map,
    position: lineArr.value[0],
    icon: new AMap.Icon({
      image: 'data:image/svg+xml;base64,' + btoa(`
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="2" y="8" width="20" height="10" rx="2" fill="#67c23a" stroke="white" stroke-width="1"/>
          <circle cx="7" cy="16" r="2" fill="white" stroke="#67c23a" stroke-width="1"/>
          <circle cx="17" cy="16" r="2" fill="white" stroke="#67c23a" stroke-width="1"/>
          <rect x="4" y="10" width="16" height="4" rx="1" fill="white" opacity="0.8"/>
        </svg>
      `),
      size: [24, 24],
      imageSize: [24, 24]
    }),
    offset: new AMap.Pixel(-12, -24)
  });

  map.setCenter(lineArr.value[0], false);

  // 绘制轨迹
  polyline.value = new AMap.Polyline({
    map: map,
    path: lineArr.value,
    showDir: true,
    strokeColor: '#28F',
    strokeWeight: 6
  });

  passedPolyline.value = new AMap.Polyline({
    map: map,
    strokeColor: '#AF5',
    strokeWeight: 6
  });

  // 设置移动事件
  let index = 100 / lineArr.value.length;
  let lastIndex = 0;
  marker.value.on('moving', function(e) {
    passedPathIndex.value = e.passedPath.length - 1;
    if (lastIndex !== passedPathIndex.value) {
      sliderValue.value += index;
    }
    lastIndex = passedPathIndex.value;
    passedPolyline.value.setPath([...passedPath.value, ...e.passedPath]);
  });

  playAble.value = false;
  restAble.value = false;
  map.setFitView();

  console.log('测试轨迹已加载，共', lineArr.value.length, '个点');
};

// 定位车辆
const locateVehicleFromSidebar = (vehicle) => {
  console.log('定位车辆:', vehicle.plateNumber);
  if (map && vehicle.lat && vehicle.lng) {
    map.setZoomAndCenter(16, [vehicle.lng, vehicle.lat], true);
  }
};

// 轨迹回放控制函数
// 播放
const startAnimation = () => {
  console.log('开始播放轨迹');
  stopAble.value = false;
  playAble.value = true;
  marker.value.moveAlong(linePath.value, {
    duration: duration.value,
    autoRotation: true
  });
};

// 暂停
const pauseAnimation = () => {
  console.log('暂停轨迹播放');
  marker.value.pauseMove();
  playAble.value = false;
  stopAble.value = true;
};

// 继续
const resumeAnimation = () => {
  console.log('继续轨迹播放');
  marker.value.resumeMove();
  stopAble.value = false;
  playAble.value = true;
};

// 停止
const stopAnimation = () => {
  console.log('停止轨迹播放');
  marker.value.stopMove();
  // 重置状态
  sliderValue.value = 0.01;
  passedPath.value = [];
  linePath.value = [...lineArr.value];
  passedPolyline.value.setPath([]);
  playAble.value = false;
  stopAble.value = true;
  restAble.value = false;
};

// 调节播放速度
const speed = () => {
  durationIndex.value++;
  let durationData = durations[durationIndex.value % durations.length];
  duration.value = durationData.v;
  durationName.value = durationData.k;

  const newSliderValue = sliderValue.value;
  let index = Math.floor(lineArr.value.length * (newSliderValue / 100));
  if (!playAble.value) {
    pauseAnimation();
  }

  passedPath.value = lineArr.value.filter((item, ind) => index > ind);
  passedPolyline.value.setPath(passedPath.value);
  linePath.value = lineArr.value.slice(index);

  setTimeout(() => {
    startAnimation();
  }, 500);
};

// 进度条处理
const sliderHandle = (value) => {
  let index = Math.floor(lineArr.value.length * (value / 100));
  if (!playAble.value) {
    pauseAnimation();
  }

  passedPath.value = lineArr.value.filter((item, ind) => index > ind);
  passedPolyline.value.setPath(passedPath.value);
  linePath.value = lineArr.value.slice(index);

  startAnimation();
  pauseAnimation();
};

// 生命周期
onMounted(() => {
  // 初始化地图
  setTimeout(() => {
    initMap();
  }, 100);
});
</script>

<style scoped>
/* 大屏容器全局样式重置 */
.big-screen-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  color: #f8fafc;
  font-family: 'Microsoft YaHei', sans-serif;
  overflow: hidden;
  position: relative;
}

/* 重置所有子元素的边框样式 */
.big-screen-container * {
  box-sizing: border-box;
}

/* 重置所有Element UI组件在大屏中的默认样式 */
.big-screen-container .el-card {
  background: rgba(30, 41, 59, 0.95) !important;
  border: 1px solid #409eff !important;
  border-radius: 12px !important;
}

.big-screen-container .el-card .el-card__body {
  background: transparent !important;
  border: none !important;
}

/* 顶部标题栏美化 */
.screen-header {
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 100%);
  border-bottom: 2px solid transparent;
  border-image: linear-gradient(90deg, #409eff 0%, #722ed1 40%, #9f7aea 100%) 1;
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
}

.screen-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
  rgba(64, 158, 255, 0.08) 0%,
  rgba(114, 46, 209, 0.12) 50%,
  rgba(159, 122, 234, 0.08) 100%);
  opacity: 0.4;
  z-index: -1;
}

.screen-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, rgba(114, 46, 209, 0.8) 50%, transparent 100%);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.header-left {
  display: flex;
  align-items: center;
  height: 100%;
}

.header-left .logo-section {
  display: flex;
  align-items: center;
  margin-right: 48px;
  transition: all 0.3s ease;
}

.header-left .logo-section:hover {
  transform: translateY(-1px);
}

.logo-section .logo-img {
  height: 36px;
  margin-right: 12px;
  filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.1));
}

.logo-section .logo-text {
  font-size: 20px;
  font-weight: 700;
  color: transparent !important;
  letter-spacing: 0.5px;
  white-space: nowrap;
  display: inline-block;
  background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 30%, #c7d2fe 60%, #a5b4fc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}


.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
}

.map-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(114, 46, 209, 0.3);
  border-radius: 12px;
  padding: 12px 24px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.control-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-group .control-label {
  font-size: 12px;
  font-weight: 600;
  color: #e0e7ff;
  white-space: nowrap;
  opacity: 0.9;
  line-height: 1;
  margin: 0;
  padding: 0;
}

.control-group .el-checkbox-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.control-group .el-checkbox {
  margin-right: 0;
  padding: 4px 8px;
  background: rgba(114, 46, 209, 0.1);
  border: 1px solid rgba(114, 46, 209, 0.2);
  border-radius: 6px;
  transition: all 0.3s ease;
  font-size: 11px;
  height: 28px;
  display: flex;
  align-items: center;
}

.control-group .el-checkbox:hover {
  background: rgba(114, 46, 209, 0.2);
  border-color: #722ed1;
  transform: translateY(-1px);
}

.control-group .el-checkbox.is-checked {
  background: rgba(114, 46, 209, 0.3);
  border-color: #9f7aea;
}

.control-group .el-button-group {
  display: flex;
  gap: 4px;
  align-items: center;
}

.control-group .el-button {
  border-radius: 6px !important;
  transition: all 0.3s ease;
  background: rgba(114, 46, 209, 0.1) !important;
  border: 1px solid rgba(114, 46, 209, 0.2) !important;
  color: #e0e7ff !important;
  font-size: 11px !important;
  padding: 4px 10px !important;
  height: 28px !important;
  display: flex;
  align-items: center;
}

.control-group .el-button:hover {
  background: rgba(114, 46, 209, 0.3) !important;
  border-color: #9f7aea !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(114, 46, 209, 0.3);
}

/* Element UI 深度样式覆盖 - 顶部控制区域 */
:deep(.map-controls .el-checkbox) {
  color: #e0e7ff !important;
  height: 28px !important;
  align-items: center !important;
}

:deep(.map-controls .el-checkbox__label) {
  color: #e0e7ff !important;
  font-size: 11px !important;
  line-height: 1 !important;
  padding-left: 8px !important;
}

:deep(.map-controls .el-checkbox__input) {
  line-height: 1 !important;
}

:deep(.map-controls .el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #9f7aea !important;
  border-color: #9f7aea !important;
}

:deep(.map-controls .el-checkbox__input.is-checked + .el-checkbox__label) {
  color: #ffffff !important;
}

:deep(.map-controls .el-button-group .el-button) {
  height: 28px !important;
  line-height: 1 !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.map-controls .el-button-group .el-button span) {
  color: #e0e7ff !important;
  line-height: 1 !important;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-right .current-time {
  text-align: right;
  position: relative;
  z-index: 1;
}

.header-actions {
  display: flex;
  align-items: center;
}

.fullscreen-btn-header {
  width: 40px !important;
  height: 40px !important;
  background: rgba(64, 158, 255, 0.1) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  transition: all 0.3s ease !important;
}

.fullscreen-btn-header:hover {
  background: rgba(64, 158, 255, 0.2) !important;
  border-color: #409eff !important;
  transform: scale(1.05) !important;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.4) !important;
}

.time-display {
  font-size: 28px;
  font-weight: 800;
  color: #1890ff;
  text-shadow: 0 0 15px rgba(24, 144, 255, 0.6),
  0 2px 4px rgba(0, 0, 0, 0.3);
  font-family: 'Arial', 'Microsoft YaHei', monospace;
  position: relative;
  transition: all 0.3s ease;
}

.time-display::before {
  content: '';
  position: absolute;
  inset: -4px;
  background: radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, transparent 70%);
  border-radius: 6px;
  opacity: 0;
  transition: all 0.3s ease;
}

.current-time:hover .time-display {
  transform: scale(1.05);
  text-shadow: 0 0 25px rgba(24, 144, 255, 0.8),
  0 4px 8px rgba(0, 0, 0, 0.4);
}

.current-time:hover .time-display::before {
  opacity: 1;
}

.date-display {
  font-size: 12px;
  color: #94a3b8;
  margin-top: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.date-display::before {
  content: '📅 ';
  opacity: 0;
  transition: all 0.3s ease;
  transform: translateX(-4px);
}

.current-time:hover .date-display {
  color: #cbd5e1;
  transform: translateX(4px);
}

.current-time:hover .date-display::before {
  opacity: 1;
  transform: translateX(0);
}


/* 主体内容 - 地图作为底图 */
.screen-main {
  height: calc(100vh - 70px);
  position: relative; /* 为悬浮面板提供定位基准 */
  overflow: hidden;
}

/* 地图区域 - 全屏底图 */
.map-section.fullscreen-map {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  z-index: 1; /* 底层 */
}


.map-section.fullscreen-map .map-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* 侧边面板布局 - 左右两个完整面板 */
.side-panel {
  /* 所有位置和尺寸由内联样式控制 */
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.side-panel .panel-card {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.8) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(15px) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
  overflow: hidden !important;
}

/* 确保侧边面板的卡片头部样式正确 */
.side-panel .card-header {
  padding: 12px 15px;
  background: rgba(64, 158, 255, 0.1);
  border-bottom: 1px solid rgba(64, 158, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 14px;
  color: #f8fafc;
}

.side-panel .card-header .el-icon {
  color: #409eff;
  font-size: 16px;
}

/* 控制面板样式 */
.control-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.control-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-label {
  font-size: 13px;
  font-weight: 600;
  color: #67c23a;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.control-label::before {
  content: '';
  width: 3px;
  height: 12px;
  background: linear-gradient(135deg, #67c23a 0%, #409eff 100%);
  border-radius: 2px;
}

.control-section .el-checkbox-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  padding: 8px 0;
}

.control-section .el-checkbox {
  margin-right: 0;
  margin-bottom: 0;
  padding: 6px 8px;
  background: rgba(64, 158, 255, 0.1);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 6px;
  transition: all 0.3s ease;
  text-align: center;
  font-size: 12px;
}

.control-section .el-checkbox:hover {
  background: rgba(64, 158, 255, 0.2);
  border-color: #409eff;
  transform: translateX(2px);
}

.control-section .el-checkbox.is-checked {
  background: rgba(103, 194, 58, 0.2);
  border-color: #67c23a;
}

.control-section .el-button-group {
  display: flex;
  gap: 6px;
}

.control-section .el-button {
  flex: 1;
  border-radius: 6px !important;
  transition: all 0.3s ease;
  background: rgba(64, 158, 255, 0.1) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  color: #f8fafc !important;
  font-size: 12px !important;
  padding: 6px 8px !important;
}

.control-section .el-button:hover {
  background: rgba(64, 158, 255, 0.3) !important;
  border-color: #409eff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 下拉框样式美化 */
:deep(.control-section .el-select) {
  width: 100%;
}

:deep(.control-section .el-select .el-input__inner) {
  background: rgba(15, 23, 42, 0.8) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 6px !important;
  color: #f8fafc !important;
  transition: all 0.3s ease !important;
}

:deep(.control-section .el-select .el-input__inner:hover) {
  border-color: #409eff !important;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3) !important;
}

:deep(.control-section .el-select .el-input__inner:focus) {
  border-color: #67c23a !important;
  box-shadow: 0 0 8px rgba(103, 194, 58, 0.3) !important;
}

:deep(.el-select-dropdown) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item) {
  color: #f8fafc !important;
  background: transparent !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background: rgba(64, 158, 255, 0.2) !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item.selected) {
  background: rgba(103, 194, 58, 0.2) !important;
  color: #67c23a !important;
}


/* 状态面板内容 */
/* 状态面板内容美化 */
.status-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

/* 告警列表样式优化 */
.alert-list {
  padding: 0 20px 20px;
}

/* 车辆监控面板美化 */
.monitoring-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.monitoring-content .vehicle-items-mini {
  /* 移除固定高度，使用flex自适应 */
  overflow-y: auto;
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.vehicle-item-mini {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(64, 158, 255, 0.1);
  border-radius: 6px;
  background: rgba(15, 23, 42, 0.8);
  position: relative;
  overflow: hidden;
  min-height: 60px; /* 固定最小高度 */
  flex-shrink: 0; /* 防止被压缩 */
}

.vehicle-item-mini::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(180deg, #67c23a 0%, #409eff 100%);
  opacity: 0;
  transition: all 0.3s ease;
}

.vehicle-item-mini:last-child {
  margin-bottom: 0;
}

.vehicle-item-mini:hover {
  background: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.3);
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.vehicle-item-mini:hover::before {
  opacity: 1;
}

.vehicle-status-indicator-mini {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
}

.vehicle-status-indicator-mini::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: inherit;
}

.vehicle-status-indicator-mini.running {
  background: #67c23a;
  box-shadow: 0 0 8px rgba(103, 194, 58, 0.6);
  animation: pulse-green 2s infinite;
}

.vehicle-status-indicator-mini.stopped {
  background: #e6a23c;
  box-shadow: 0 0 6px rgba(230, 162, 60, 0.5);
}

.vehicle-status-indicator-mini.maintenance {
  background: #f56c6c;
  box-shadow: 0 0 6px rgba(245, 108, 108, 0.5);
}

.vehicle-status-indicator-mini.fault {
  background: #909399;
  box-shadow: 0 0 4px rgba(144, 147, 153, 0.4);
}

@keyframes pulse-green {
  0%, 100% {
    box-shadow: 0 0 8px rgba(103, 194, 58, 0.6);
  }
  50% {
    box-shadow: 0 0 16px rgba(103, 194, 58, 0.8), 0 0 24px rgba(103, 194, 58, 0.4);
  }
}

.vehicle-item-mini:hover .vehicle-status-indicator-mini {
  transform: scale(1.2);
}

.vehicle-info-mini {
  flex: 1;
  margin-right: 12px;
  min-width: 0;
}

.vehicle-number-mini {
  font-weight: 700;
  color: #f8fafc;
  font-size: 12px;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.vehicle-number-mini::after {
  content: '🚌';
  font-size: 10px;
  opacity: 0.7;
}

.vehicle-route-mini {
  font-size: 10px;
  color: #94a3b8;
  font-weight: 500;
}

.vehicle-data-mini {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: flex-end;
  min-width: 60px;
}

.data-value-mini {
  font-size: 10px;
  font-weight: 600;
  color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid rgba(103, 194, 58, 0.2);
  text-align: center;
  min-width: 50px;
  transition: all 0.3s ease;
}

.vehicle-item-mini:hover .data-value-mini {
  background: rgba(103, 194, 58, 0.2);
  border-color: rgba(103, 194, 58, 0.4);
  transform: scale(1.05);
}

/* 刷新按钮美化 */
.refresh-btn-mini {
  width: 28px !important;
  height: 28px !important;
  background: rgba(64, 158, 255, 0.1) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  color: #409eff !important;
  transition: all 0.3s ease !important;
}

.refresh-btn-mini:hover {
  background: rgba(64, 158, 255, 0.2) !important;
  border-color: #409eff !important;
  transform: rotate(180deg) scale(1.1) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
}


/* 迷你图表容器美化 */
.chart-container-mini {
  height: 180px !important;
  width: 100% !important;
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 8px;
  padding: 10px;
  position: relative;
}

.chart-container-mini::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #67c23a 0%, #409eff 50%, #e6a23c 100%);
  border-radius: 8px 8px 0 0;
}

/* 状态图例 - 2x2网格布局 */
.status-legend-mini {
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 8px;
  padding: 12px;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 8px;
}

.status-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 6px;
  transition: all 0.3s ease;
  min-height: 50px;
}

.status-card:hover {
  border-color: rgba(64, 158, 255, 0.5);
  background: rgba(30, 41, 59, 0.7);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.status-value {
  font-size: 18px;
  font-weight: bold;
  color: #f8fafc;
  margin-bottom: 4px;
  line-height: 1;
}

.status-name {
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  opacity: 0.9;
  line-height: 1;
}

/* 车辆监控 - 迷你版滚动条样式 */
.vehicle-items-mini::-webkit-scrollbar {
  width: 4px;
}

.vehicle-items-mini::-webkit-scrollbar-thumb {
  background: rgba(64, 158, 255, 0.3);
  border-radius: 2px;
}


.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  border: 1px solid #409eff;
  border-radius: 8px;
  overflow: hidden;
}

.map-canvas {
  width: 100%;
  height: 100%;
}

.map-legend {
  position: absolute;
  top: 20px;
  right: 360px;
  background: rgba(30, 41, 59, 0.95);
  border: 1px solid #409eff;
  border-radius: 8px;
  padding: 15px;
  min-width: 120px;
}


.legend-title {
  font-weight: 600;
  margin-bottom: 10px;
  color: #f8fafc;
  font-size: 14px;
}

.map-legend .legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.legend-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.legend-marker.running {
  background: #67c23a;
}

.legend-marker.stopped {
  background: #e6a23c;
}

.legend-marker.maintenance {
  background: #f56c6c;
}

.legend-marker.fault {
  background: #909399;
}

.legend-marker.station {
  background: #409eff;
}

/* 实时告警面板美化 */
.alert-list {
  overflow-y: auto;
  padding: 20px;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  margin-bottom: 12px;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  border-left: 4px solid;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.alert-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(64, 158, 255, 0.5) 50%, transparent 100%);
}

.alert-item:hover {
  background: rgba(15, 23, 42, 0.9);
  border-color: rgba(64, 158, 255, 0.4);
  transform: translateX(4px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.alert-item.alert-high {
  border-left-color: #f56c6c;
  background: rgba(245, 108, 108, 0.1);
}

.alert-item.alert-high:hover {
  background: rgba(245, 108, 108, 0.15);
  border-color: rgba(245, 108, 108, 0.4);
}

.alert-item.alert-medium {
  border-left-color: #e6a23c;
  background: rgba(230, 162, 60, 0.1);
}

.alert-item.alert-medium:hover {
  background: rgba(230, 162, 60, 0.15);
  border-color: rgba(230, 162, 60, 0.4);
}

.alert-item.alert-low {
  border-left-color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
}

.alert-item.alert-low:hover {
  background: rgba(103, 194, 58, 0.15);
  border-color: rgba(103, 194, 58, 0.4);
}

.alert-icon {
  margin-right: 12px;
  margin-top: 2px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.alert-item.alert-high .alert-icon {
  color: #f56c6c;
  background: rgba(245, 108, 108, 0.2);
}

.alert-item.alert-medium .alert-icon {
  color: #e6a23c;
  background: rgba(230, 162, 60, 0.2);
}

.alert-item.alert-low .alert-icon {
  color: #67c23a;
  background: rgba(103, 194, 58, 0.2);
}

.alert-item:hover .alert-icon {
  transform: scale(1.1);
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
}

.alert-content {
  flex: 1;
  min-width: 0;
}

.alert-title {
  font-weight: 600;
  font-size: 14px;
  color: #f8fafc;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.alert-title::after {
  content: '';
  width: 4px;
  height: 4px;
  background: #67c23a;
  border-radius: 50%;
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

.alert-desc {
  font-size: 12px;
  color: #94a3b8;
  margin-bottom: 8px;
  line-height: 1.4;
}

.alert-time {
  font-size: 11px;
  color: #409eff;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.alert-time::before {
  content: '⏰';
  font-size: 10px;
}

/* 告警徽章美化 */
.alert-badge :deep(.el-badge__content) {
  background: linear-gradient(135deg, #f56c6c 0%, #e6a23c 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #fff !important;
  font-weight: 600 !important;
  font-size: 10px !important;
  min-width: 18px !important;
  height: 18px !important;
  line-height: 16px !important;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.4) !important;
  animation: badge-pulse 2s infinite !important;
}

@keyframes badge-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(245, 108, 108, 0.6);
  }
}


/* 车辆列表弹窗样式 */
.vehicle-list-dialog :deep(.el-dialog) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(15px) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
}

.vehicle-list-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.15) 0%, rgba(103, 194, 58, 0.1) 100%) !important;
  border-bottom: 1px solid rgba(64, 158, 255, 0.3) !important;
  padding: 16px 20px !important;
}

.vehicle-list-dialog :deep(.el-dialog__title) {
  color: #f8fafc !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.vehicle-list-dialog :deep(.el-dialog__body) {
  padding: 0 !important;
  background: transparent !important;
}

.vehicle-list-dialog .dialog-header {
  padding: 16px 20px;
  background: rgba(15, 23, 42, 0.8);
  border-bottom: 1px solid rgba(64, 158, 255, 0.2);
}


.vehicle-list-dialog .search-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 车辆表格样式重写 - 匹配大屏主题 */
.vehicle-list-dialog .vehicle-table {
  margin: 0 !important;
  background: transparent !important;
  width: 100% !important;
}

.vehicle-list-dialog :deep(.el-table) {
  background: transparent !important;
  color: #f8fafc !important;
  border: none !important;
  width: 100% !important;
  --el-table-header-bg-color: rgba(15, 23, 42, 0.95) !important;
  --el-table-row-hover-bg-color: rgba(64, 158, 255, 0.15) !important;
}

.vehicle-list-dialog :deep(.el-table__inner-wrapper) {
  background: transparent !important;
}

.vehicle-list-dialog :deep(.el-table__header-wrapper) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.vehicle-list-dialog :deep(.el-table__header) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.vehicle-list-dialog :deep(.el-table__header tr) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.vehicle-list-dialog :deep(.el-table__header th),
.vehicle-list-dialog :deep(.el-table__header th.el-table__cell) {
  background: rgba(15, 23, 42, 0.95) !important;
  background-color: rgba(15, 23, 42, 0.95) !important;
  color: #f8fafc !important;
  font-weight: 600 !important;
  border-bottom: 2px solid rgba(64, 158, 255, 0.3) !important;
  border-right: 1px solid rgba(64, 158, 255, 0.1) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
  font-size: 13px !important;
  padding: 12px 8px !important;
  text-align: center !important;
}

.vehicle-list-dialog :deep(.el-table__header th:before),
.vehicle-list-dialog :deep(.el-table__header th:after) {
  display: none !important;
}

.vehicle-list-dialog :deep(.el-table__header th:last-child) {
  border-right: none !important;
}

.vehicle-list-dialog :deep(.el-table__body-wrapper) {
  background: transparent !important;
}

.vehicle-list-dialog :deep(.el-table__body) {
  background: transparent !important;
}

.vehicle-list-dialog :deep(.el-table__body tr) {
  background: rgba(15, 23, 42, 0.9) !important;
  transition: all 0.3s ease !important;
  border: none !important;
}

.vehicle-list-dialog :deep(.el-table__body tr:nth-child(odd)) {
  background: rgba(15, 23, 42, 0.9) !important;
}

.vehicle-list-dialog :deep(.el-table__body tr:nth-child(even)) {
  background: rgba(30, 41, 59, 0.9) !important;
}

.vehicle-list-dialog :deep(.el-table__body tr:hover) {
  background: rgba(64, 158, 255, 0.15) !important;
  transform: translateX(2px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2) !important;
}

.vehicle-list-dialog :deep(.el-table__body tr:hover td) {
  background: rgba(64, 158, 255, 0.15) !important;
}

.vehicle-list-dialog :deep(.el-table__body td),
.vehicle-list-dialog :deep(.el-table__body td.el-table__cell) {
  background: inherit !important;
  background-color: inherit !important;
  border-bottom: 1px solid rgba(64, 158, 255, 0.1) !important;
  border-right: 1px solid rgba(64, 158, 255, 0.05) !important;
  color: #f8fafc !important;
  padding: 12px 8px !important;
  font-size: 12px !important;
}

.vehicle-list-dialog :deep(.el-table__body td:last-child) {
  border-right: none !important;
}

/* 强制覆盖白色背景 */
.vehicle-list-dialog :deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background: rgba(64, 158, 255, 0.15) !important;
  background-color: rgba(64, 158, 255, 0.15) !important;
}

.vehicle-list-dialog :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: rgba(30, 41, 59, 0.9) !important;
  background-color: rgba(30, 41, 59, 0.9) !important;
}

.vehicle-list-dialog :deep(.el-table--striped .el-table__body tr.el-table__row--striped:hover td) {
  background: rgba(64, 158, 255, 0.15) !important;
  background-color: rgba(64, 158, 255, 0.15) !important;
}

/* 确保表格容器宽度 */
.vehicle-list-dialog :deep(.el-table__header-wrapper),
.vehicle-list-dialog :deep(.el-table__body-wrapper) {
  width: 100% !important;
}

.vehicle-list-dialog :deep(.el-table__header),
.vehicle-list-dialog :deep(.el-table__body) {
  width: 100% !important;
}

/* 额外的表头强制样式 */
.vehicle-list-dialog :deep(.el-table .el-table__header-wrapper .el-table__header thead tr th) {
  background: rgba(15, 23, 42, 0.95) !important;
  background-color: rgba(15, 23, 42, 0.95) !important;
}

.vehicle-list-dialog :deep(.el-table thead) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.vehicle-list-dialog :deep(.el-table thead th) {
  background: rgba(15, 23, 42, 0.95) !important;
  background-color: rgba(15, 23, 42, 0.95) !important;
}

/* 车辆状态标签美化 */
.vehicle-list-dialog :deep(.el-tag) {
  border: none !important;
  font-weight: 600 !important;
  font-size: 11px !important;
  padding: 2px 8px !important;
  border-radius: 12px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.vehicle-list-dialog :deep(.el-tag--success) {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;
  color: #ffffff !important;
}

.vehicle-list-dialog :deep(.el-tag--warning) {
  background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%) !important;
  color: #ffffff !important;
}

.vehicle-list-dialog :deep(.el-tag--danger) {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%) !important;
  color: #ffffff !important;
}

.vehicle-list-dialog :deep(.el-tag--info) {
  background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%) !important;
  color: #ffffff !important;
}

/* 车辆定位按钮美化 */
.vehicle-list-dialog :deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%) !important;
  border: none !important;
  color: #ffffff !important;
  font-size: 11px !important;
  padding: 4px 12px !important;
  border-radius: 6px !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3) !important;
}

.vehicle-list-dialog :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #67c23a 0%, #409eff 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.5) !important;
}

/* 告警表格样式重写 - 匹配大屏主题 */
.alert-list-dialog .alert-table {
  margin: 0 !important;
  background: transparent !important;
  width: 100% !important;
}

.alert-list-dialog :deep(.el-table) {
  background: transparent !important;
  color: #f8fafc !important;
  border: none !important;
  width: 100% !important;
  --el-table-header-bg-color: rgba(15, 23, 42, 0.95) !important;
  --el-table-row-hover-bg-color: rgba(64, 158, 255, 0.15) !important;
}

.alert-list-dialog :deep(.el-table__inner-wrapper) {
  background: transparent !important;
}

.alert-list-dialog :deep(.el-table__header-wrapper) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.alert-list-dialog :deep(.el-table__header) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.alert-list-dialog :deep(.el-table__header tr) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.alert-list-dialog :deep(.el-table__header th),
.alert-list-dialog :deep(.el-table__header th.el-table__cell) {
  background: rgba(15, 23, 42, 0.95) !important;
  background-color: rgba(15, 23, 42, 0.95) !important;
  color: #f8fafc !important;
  font-weight: 600 !important;
  border-bottom: 2px solid rgba(64, 158, 255, 0.3) !important;
  border-right: 1px solid rgba(64, 158, 255, 0.1) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
  font-size: 13px !important;
  padding: 12px 8px !important;
  text-align: center !important;
}

.alert-list-dialog :deep(.el-table__header th:before),
.alert-list-dialog :deep(.el-table__header th:after) {
  display: none !important;
}

.alert-list-dialog :deep(.el-table__header th:last-child) {
  border-right: none !important;
}

.alert-list-dialog :deep(.el-table__body-wrapper) {
  background: transparent !important;
}

.alert-list-dialog :deep(.el-table__body) {
  background: transparent !important;
}

.alert-list-dialog :deep(.el-table__body tr) {
  background: rgba(15, 23, 42, 0.9) !important;
  transition: all 0.3s ease !important;
  border: none !important;
}

.alert-list-dialog :deep(.el-table__body tr:nth-child(odd)) {
  background: rgba(15, 23, 42, 0.9) !important;
}

.alert-list-dialog :deep(.el-table__body tr:nth-child(even)) {
  background: rgba(30, 41, 59, 0.9) !important;
}

.alert-list-dialog :deep(.el-table__body tr:hover) {
  background: rgba(64, 158, 255, 0.15) !important;
  transform: translateX(2px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2) !important;
}

.alert-list-dialog :deep(.el-table__body tr:hover td) {
  background: rgba(64, 158, 255, 0.15) !important;
}

.alert-list-dialog :deep(.el-table__body td),
.alert-list-dialog :deep(.el-table__body td.el-table__cell) {
  background: inherit !important;
  background-color: inherit !important;
  border-bottom: 1px solid rgba(64, 158, 255, 0.1) !important;
  border-right: 1px solid rgba(64, 158, 255, 0.05) !important;
  color: #f8fafc !important;
  padding: 12px 8px !important;
  font-size: 12px !important;
}

.alert-list-dialog :deep(.el-table__body td:last-child) {
  border-right: none !important;
}

/* 强制覆盖白色背景 */
.alert-list-dialog :deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background: rgba(64, 158, 255, 0.15) !important;
  background-color: rgba(64, 158, 255, 0.15) !important;
}

.alert-list-dialog :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: rgba(30, 41, 59, 0.9) !important;
  background-color: rgba(30, 41, 59, 0.9) !important;
}

.alert-list-dialog :deep(.el-table--striped .el-table__body tr.el-table__row--striped:hover td) {
  background: rgba(64, 158, 255, 0.15) !important;
  background-color: rgba(64, 158, 255, 0.15) !important;
}

/* 确保表格容器宽度 */
.alert-list-dialog :deep(.el-table__header-wrapper),
.alert-list-dialog :deep(.el-table__body-wrapper) {
  width: 100% !important;
}

.alert-list-dialog :deep(.el-table__header),
.alert-list-dialog :deep(.el-table__body) {
  width: 100% !important;
}

/* 额外的表头强制样式 */
.alert-list-dialog :deep(.el-table .el-table__header-wrapper .el-table__header thead tr th) {
  background: rgba(15, 23, 42, 0.95) !important;
  background-color: rgba(15, 23, 42, 0.95) !important;
}

.alert-list-dialog :deep(.el-table thead) {
  background: rgba(15, 23, 42, 0.95) !important;
}

.alert-list-dialog :deep(.el-table thead th) {
  background: rgba(15, 23, 42, 0.95) !important;
  background-color: rgba(15, 23, 42, 0.95) !important;
}

/* 告警级别标签美化 */
.alert-list-dialog :deep(.el-tag) {
  border: none !important;
  font-weight: 600 !important;
  font-size: 11px !important;
  padding: 2px 8px !important;
  border-radius: 12px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.alert-list-dialog :deep(.el-tag--success) {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;
  color: #ffffff !important;
}

.alert-list-dialog :deep(.el-tag--warning) {
  background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%) !important;
  color: #ffffff !important;
}

.alert-list-dialog :deep(.el-tag--danger) {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%) !important;
  color: #ffffff !important;
}

.alert-list-dialog :deep(.el-tag--info) {
  background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%) !important;
  color: #ffffff !important;
}

/* 告警定位按钮美化 */
.alert-list-dialog :deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%) !important;
  border: none !important;
  color: #ffffff !important;
  font-size: 11px !important;
  padding: 4px 12px !important;
  border-radius: 6px !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3) !important;
}

.alert-list-dialog :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #67c23a 0%, #409eff 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.5) !important;
}

/* 告警详情按钮美化 */
.alert-list-dialog :deep(.el-button--success) {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;
  border: none !important;
  color: #ffffff !important;
  font-size: 11px !important;
  padding: 4px 12px !important;
  border-radius: 6px !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 6px rgba(103, 194, 58, 0.3) !important;
}

.alert-list-dialog :deep(.el-button--success:hover) {
  background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.5) !important;
}

/* 告警列表分页样式 - 深色主题 */
.alert-list-dialog .pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(147, 197, 253, 0.2);
}


/* 选择器输入框背景色样式 */
:deep(.el-select__wrapper) {
  background-color: #0F172AF2;
}

/* 组织机构树选择器样式 */
.vehicle-list-dialog :deep(.el-tree-select) {
  background-color: #0F172AF2;
}

.vehicle-list-dialog :deep(.el-tree-select .el-select__wrapper) {
  background-color: #0F172AF2 !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
}

.vehicle-list-dialog :deep(.el-tree-select .el-select__input) {
  color: #f8fafc !important;
}

.vehicle-list-dialog :deep(.el-tree-select .el-select__placeholder) {
  color: #94a3b8 !important;
}

/* 分页样式 - 深色主题 */
.vehicle-list-dialog .pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(147, 197, 253, 0.2);
}


.vehicle-list-dialog .total-count {
  color: #94a3b8;
  font-size: 14px;
  font-weight: 500;
}

/* 告警列表弹窗样式 */
.alert-list-dialog :deep(.el-dialog) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(15px) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
}

.alert-list-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.15) 0%, rgba(103, 194, 58, 0.1) 100%) !important;
  border-bottom: 1px solid rgba(64, 158, 255, 0.3) !important;
  padding: 16px 20px !important;
}

.alert-list-dialog :deep(.el-dialog__title) {
  color: #f8fafc !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.alert-list-dialog :deep(.el-dialog__body) {
  padding: 0 !important;
  background: transparent !important;
}

.alert-list-dialog .dialog-header {
  padding: 16px 20px;
  background: rgba(15, 23, 42, 0.8);
  border-bottom: 1px solid rgba(64, 158, 255, 0.2);
}

.alert-list-dialog .search-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.vehicle-list-dialog :deep(.el-button) {
  background: rgba(15, 23, 42, 0.9) !important;
  border: 1px solid rgba(64, 158, 255, 0.4) !important;
  color: #f8fafc !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.vehicle-list-dialog :deep(.el-button:hover) {
  background: rgba(30, 41, 59, 0.9) !important;
  border-color: #409eff !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
}

/* 查看全部按钮样式 */
.view-all-btn {
  margin-left: 8px !important;
  background: rgba(64, 158, 255, 0.1) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  color: #409eff !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
  height: 24px !important;
}

.view-all-btn:hover {
  background: rgba(64, 158, 255, 0.2) !important;
  border-color: #409eff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.8);
}

::-webkit-scrollbar-thumb {
  background: #409eff;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #67c23a;
}


/* page-sizes选择器 */
::v-deep .el-select-dropdown__item li {
  background-color: transparent !important;
}

/* prev和next箭头的样式 */
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination .btn-prev {
  background: transparent !important;
  background-color: transparent !important;
}

/* prev和next箭头disabled的样式 */
::v-deep .el-pagination button:disabled {
  background-color: transparent !important;
}

/* 页码样式 */
::v-deep .el-pager li {
  background-color: transparent !important;
}

/* active的页码样式 */
::v-deep .el-pager li.active {
  color: #267aff !important;
}

/* 地图点位信息弹窗样式 */
.map-info-dialog :deep(.el-dialog) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(15px) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
}

.map-info-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.15) 0%, rgba(103, 194, 58, 0.1) 100%) !important;
  border-bottom: 1px solid rgba(64, 158, 255, 0.3) !important;
  padding: 16px 20px !important;
}

.map-info-dialog :deep(.el-dialog__title) {
  color: #f8fafc !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.map-info-dialog :deep(.el-dialog__body) {
  padding: 20px !important;
  background: transparent !important;
}

.map-info-content {
  color: #f8fafc;
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.info-item:hover {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(64, 158, 255, 0.4);
  transform: translateX(2px);
}

.info-label {
  font-size: 14px;
  font-weight: 600;
  color: #94a3b8;
  margin-right: 12px;
  min-width: 80px;
  position: relative;
}

.info-label::after {
  content: '';
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 12px;
  background: linear-gradient(135deg, #67c23a 0%, #409eff 100%);
  border-radius: 1px;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #f8fafc;
  flex: 1;
  text-align: right;
}

.info-value.highlight {
  color: #67c23a;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(103, 194, 58, 0.3);
}

.map-info-dialog :deep(.el-button) {
  background: rgba(15, 23, 42, 0.9) !important;
  border: 1px solid rgba(64, 158, 255, 0.4) !important;
  color: #f8fafc !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.map-info-dialog :deep(.el-button:hover) {
  background: rgba(30, 41, 59, 0.9) !important;
  border-color: #409eff !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
}

/* 告警处理操作区样式 */
.alert-actions {
  margin-top: 16px;
  padding: 16px;
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.alert-actions .el-button {
  min-width: 100px;
  font-weight: 600;
}

/* 告警状态样式 */
.alert-item.status-resolved {
  opacity: 0.6;
  background: rgba(103, 194, 58, 0.05) !important;
  border-left-color: #67c23a !important;
}

.alert-item.status-processing {
  background: rgba(230, 162, 60, 0.05) !important;
  border-left-color: #e6a23c !important;
}

.alert-item.status-pending {
  /* 保持原有样式 */
}

.alert-title .status-tag {
  margin-left: 8px;
  font-size: 10px;
  height: 18px;
  line-height: 16px;
}

/* 告警列表中已处理项目的特殊样式 */
.alert-item.status-resolved .alert-title {
  text-decoration: line-through;
  color: #94a3b8 !important;
}

.alert-item.status-resolved .alert-desc {
  color: #64748b !important;
}

.alert-item.status-resolved .alert-icon {
  opacity: 0.5;
}

/* 轨迹回放控制面板样式 */
.track-playback-controls {
  position: absolute;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  z-index: 999;
  min-width: 600px;
}

.playback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(64, 158, 255, 0.2);
}

.playback-header h4 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.playback-buttons {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

.playback-buttons .el-button-group {
  display: flex;
  gap: 8px;
}

.playback-buttons .el-button {
  background: rgba(64, 158, 255, 0.1) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  color: #f8fafc !important;
  border-radius: 6px !important;
  padding: 8px 16px !important;
  font-size: 12px !important;
  transition: all 0.3s ease !important;
}

.playback-buttons .el-button:hover {
  background: rgba(64, 158, 255, 0.2) !important;
  border-color: #409eff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.playback-buttons .el-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.playback-slider {
  width: 100%;
}

.playback-slider .el-slider {
  margin: 10px 0;
}

.playback-slider :deep(.el-slider__runway) {
  background-color: rgba(64, 158, 255, 0.2);
  border-radius: 3px;
}

.playback-slider :deep(.el-slider__bar) {
  background: linear-gradient(90deg, #67c23a 0%, #409eff 100%);
  border-radius: 3px;
}

.playback-slider :deep(.el-slider__button) {
  background: #409eff;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
}

.playback-slider :deep(.el-slider__button:hover) {
  background: #67c23a;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.6);
}
.alert-process-dialog :deep(.el-dialog) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(15px) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
}

.alert-process-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.15) 0%, rgba(103, 194, 58, 0.1) 100%) !important;
  border-bottom: 1px solid rgba(64, 158, 255, 0.3) !important;
  padding: 16px 20px !important;
}

.alert-process-dialog :deep(.el-dialog__title) {
  color: #f8fafc !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.alert-process-dialog :deep(.el-dialog__body) {
  padding: 20px !important;
  background: transparent !important;
}

.alert-process-dialog :deep(.el-form-item__label) {
  color: #f8fafc !important;
  font-weight: 600 !important;
}

.alert-process-dialog :deep(.el-input__wrapper) {
  background-color: rgba(15, 23, 42, 0.8) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 6px !important;
}

.alert-process-dialog :deep(.el-input__inner) {
  color: #f8fafc !important;
  background: transparent !important;
}

.alert-process-dialog :deep(.el-textarea__inner) {
  background-color: rgba(15, 23, 42, 0.8) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
  border-radius: 6px !important;
  color: #f8fafc !important;
}

.alert-process-dialog :deep(.el-select .el-input__wrapper) {
  background-color: rgba(15, 23, 42, 0.8) !important;
  border: 1px solid rgba(64, 158, 255, 0.3) !important;
}

.alert-process-dialog :deep(.el-button) {
  background: rgba(15, 23, 42, 0.9) !important;
  border: 1px solid rgba(64, 158, 255, 0.4) !important;
  color: #f8fafc !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.alert-process-dialog :deep(.el-button:hover) {
  background: rgba(30, 41, 59, 0.9) !important;
  border-color: #409eff !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
}

.alert-process-dialog :deep(.el-button--warning) {
  background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%) !important;
  border: none !important;
  color: #ffffff !important;
}

.alert-process-dialog :deep(.el-button--success) {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;
  border: none !important;
  color: #ffffff !important;
}

/* 处理信息显示区样式 */
.process-info-section {
  margin-top: 16px;
  padding: 16px;
  background: rgba(103, 194, 58, 0.05);
  border: 1px solid rgba(103, 194, 58, 0.2);
  border-radius: 8px;
  position: relative;
}

.process-info-section::before {
  content: '处理信息';
  position: absolute;
  top: -8px;
  left: 12px;
  background: rgba(15, 23, 42, 0.9);
  color: #67c23a;
  font-size: 12px;
  font-weight: 600;
  padding: 0 8px;
}

.process-desc {
  word-break: break-word;
  line-height: 1.5;
  max-height: 60px;
  overflow-y: auto;
}

</style>
