<!-- 固定间隔模式参数配置组件 -->
<template>
  <div class="fixed-interval-config">
    <el-form ref="formRef" :model="config" label-width="120px" size="large">
      <div class="config-section">
        <h4 class="section-title">
          <el-icon><Clock /></el-icon>
          基本设置
        </h4>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="模板名称" required>
              <el-input v-model="config.templateName" placeholder="请输入模板名称" maxlength="50" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="config-section">
        <h4 class="section-title">
          <el-icon><Timer /></el-icon>
          运营参数设置
        </h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="首班车时间" required>
              <el-time-picker v-model="config.firstBusTime" placeholder="选择首班车时间" value-format="HH:mm" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="末班车时间" required>
              <el-time-picker v-model="config.lastBusTime" placeholder="选择末班车时间" value-format="HH:mm" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发车间隔" required>
              <el-input-number v-model="config.departureInterval" :min="3" :max="60" :step="1" style="width: 100%" @change="calculateTrips" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="单程时间">
              <el-input-number v-model="config.singleTripTime" :min="10" :max="300" :step="5" style="width: 100%" @change="calculateTrips" />
              <div class="form-tip">用于计算建议配车数（可选）</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板描述">
              <el-input v-model="config.description" placeholder="请输入模板描述（可选）" maxlength="100" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="preview-section">
        <h4 class="section-title">
          <el-icon><DataAnalysis /></el-icon>
          预计效果
        </h4>
        <div class="preview-stats">
          <div class="stat-card">
            <div class="stat-value">{{ estimatedTrips }}</div>
            <div class="stat-label">预计班次</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ estimatedVehicles }}</div>
            <div class="stat-label">建议配车</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ operatingHours }}</div>
            <div class="stat-label">运营时长</div>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Clock, Timer, Setting, DataAnalysis, EditPen } from '@element-plus/icons-vue';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue']);

// Form reference
const formRef = ref();

const config = ref({
  templateName: '',
  firstBusTime: '06:00',
  lastBusTime: '22:00',
  departureInterval: 10,
  singleTripTime: 60,
  description: '',
  ...props.modelValue
});

// 计算预计班次
const estimatedTrips = computed(() => {
  if (!config.value.firstBusTime || !config.value.lastBusTime || !config.value.departureInterval) {
    return '--';
  }

  const [firstHour, firstMin] = config.value.firstBusTime.split(':').map(Number);
  const [lastHour, lastMin] = config.value.lastBusTime.split(':').map(Number);

  const firstMinutes = firstHour * 60 + firstMin;
  const lastMinutes = lastHour * 60 + lastMin;
  const totalMinutes = lastMinutes - firstMinutes;

  if (totalMinutes <= 0) return '--';

  return Math.floor(totalMinutes / config.value.departureInterval) + 1;
});

// 计算建议配车数
const estimatedVehicles = computed(() => {
  if (!config.value.singleTripTime || !config.value.departureInterval) {
    return '--';
  }

  const roundTripTime = config.value.singleTripTime * 2; // 往返时间
  const vehicles = Math.ceil(roundTripTime / config.value.departureInterval);
  return vehicles;
});

// 计算运营时长
const operatingHours = computed(() => {
  if (!config.value.firstBusTime || !config.value.lastBusTime) {
    return '--';
  }

  const [firstHour, firstMin] = config.value.firstBusTime.split(':').map(Number);
  const [lastHour, lastMin] = config.value.lastBusTime.split(':').map(Number);

  const firstMinutes = firstHour * 60 + firstMin;
  const lastMinutes = lastHour * 60 + lastMin;
  const totalMinutes = lastMinutes - firstMinutes;

  if (totalMinutes <= 0) return '--';

  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  return `${hours}小时${minutes}分钟`;
});

// 监听配置变化并向父组件发送
watch(
  config,
  (newValue) => {
    emit('update:modelValue', newValue);
  },
  { deep: true }
);

function calculateTrips() {
  // 触发重新计算
}
</script>

<style scoped>
.fixed-interval-config {
  padding: 20px 0;
}

.config-section {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.6) 0%, rgba(30, 41, 59, 0.4) 100%);
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
}

.form-tip {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.preview-section {
  background: transparent;
  color: #e2e8f0;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.preview-section .section-title {
  color: #e2e8f0;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
}

.preview-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

.stat-card {
  text-align: center;
  padding: 20px 16px;
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.3);
  transition: all 0.3s ease;
}

.stat-card:hover {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(15, 23, 42, 0.5);
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  color: #60a5fa;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #cbd5e1;
  font-weight: 500;
}

/* Element Plus 深色主题样式覆盖 */
:deep(.el-input .el-input__count .el-input__count-inner) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #94a3b8 !important;
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-form-item__label) {
  color: #e2e8f0 !important;
}

:deep(.el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  box-shadow: none !important;
}

:deep(.el-input__inner) {
  color: #e2e8f0 !important;
}

:deep(.el-input__inner::placeholder) {
  color: #94a3b8 !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

:deep(.el-time-picker .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

/* 时间选择器面板样式修复 */
:deep(.el-time-picker__popper),
:deep(.el-picker-panel) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(147, 197, 253, 0.3) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
}

:deep(.el-time-panel) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(147, 197, 253, 0.3) !important;
  color: #e2e8f0 !important;
}

:deep(.el-time-panel__header) {
  border-bottom-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-time-panel__content) {
  background: transparent !important;
}

:deep(.el-time-spinner) {
  background: transparent !important;
}

:deep(.el-time-spinner__wrapper) {
  border-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-time-spinner__list) {
  background: transparent !important;
}

:deep(.el-time-spinner__item) {
  color: #e2e8f0 !important;
  background: transparent !important;
  height: 32px !important;
  line-height: 32px !important;
  font-size: 14px !important;
}

:deep(.el-time-spinner__item:hover) {
  background: rgba(59, 130, 246, 0.2) !important;
  color: #ffffff !important;
}

:deep(.el-time-spinner__item.active) {
  background: rgba(59, 130, 246, 0.3) !important;
  color: #ffffff !important;
  font-weight: 600 !important;
}

:deep(.el-time-spinner__item.disabled) {
  color: #64748b !important;
  background: transparent !important;
  cursor: not-allowed !important;
}

/* 确保时间面板显示在正确的层级 */
:deep(.el-picker-panel) {
  z-index: 9999 !important;
}

:deep(.el-time-panel__footer) {
  border-top-color: rgba(147, 197, 253, 0.2) !important;
  background: rgba(30, 41, 59, 0.5) !important;
}

:deep(.el-time-panel__btn) {
  color: #e2e8f0 !important;
}

:deep(.el-time-panel__btn:hover) {
  color: #3b82f6 !important;
}

:deep(.el-time-panel__btn.confirm) {
  color: #3b82f6 !important;
  font-weight: 600 !important;
}

:deep(.el-input-number .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  background: rgba(30, 41, 59, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e2e8f0 !important;
}

:deep(.el-textarea__inner) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e2e8f0 !important;
}
</style>
