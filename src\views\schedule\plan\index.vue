<template>
  <div class="route-schedule-container">
    <!-- 多层动态背景系统 - 同步车辆监控页面 -->
    <div class="background-system">
      <!-- 基础渐变层 -->
      <div class="bg-gradient-base"></div>
    </div>

    <!-- 顶部状态栏 -->
    <div class="top-status-bar">
      <div class="route-selector-area">
        <div class="selector-label">
          <el-icon><Position /></el-icon>
          <span>当前线路</span>
        </div>
        <div v-if="selectedRoute" class="selected-route-display">
          <el-tag type="success" effect="dark" size="large" class="selected-route-tag">
            {{ selectedRoute.routeName }}
          </el-tag>
          <el-button size="small" text type="primary" @click="openRouteSelector" class="change-route-btn">
            <el-icon><Refresh /></el-icon>
            切换
          </el-button>
        </div>
        <el-button v-else type="primary" @click="openRouteSelector" class="select-route-btn">
          <el-icon><Position /></el-icon>
          选择线路
        </el-button>
      </div>
      <div class="realtime-stats" v-if="selectedRoute">
        <div class="stat-item">
          <span class="stat-label">总时间点</span>
          <span class="stat-value">{{ routeTimePoints.length }}</span>
        </div>
        <div class="stat-item success">
          <span class="stat-label">已分配</span>
          <span class="stat-value">{{ assignedCount }}</span>
        </div>
        <div class="stat-item warning">
          <span class="stat-label">未分配</span>
          <span class="stat-value">{{ routeTimePoints.length - assignedCount }}</span>
        </div>
        <div class="stat-item info">
          <span class="stat-label">完成度</span>
          <span class="stat-value">{{ Math.round((assignedCount / routeTimePoints.length) * 100) || 0 }}%</span>
        </div>
      </div>
    </div>

    <!-- 线路选择面板 -->
    <div class="route-selector-panel" :class="{ visible: routeSelectorVisible }">
      <div class="panel-overlay" @click="closeRouteSelector"></div>
      <div class="panel-content">
        <div class="panel-header">
          <div class="header-left">
            <el-icon><Position /></el-icon>
            <span>选择线路</span>
          </div>
          <div class="header-right">
            <el-button text @click="closeRouteSelector" class="close-btn">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </div>
        <div class="panel-body">
          <div class="search-box">
            <el-input v-model="routeSearchText" placeholder="搜索线路名称或编号..." clearable prefix-icon="Search" />
          </div>
          <div class="routes-list">
            <div
              v-for="route in filteredRoutes"
              :key="route.routeId"
              class="route-item"
              :class="{ selected: selectedRouteId === route.routeId }"
              @click="selectRoute(route)"
            >
              <div class="route-info">
                <div class="route-name">{{ route.routeName }}</div>
                <div class="route-details">
                  <span class="route-code">{{ route.routeCode }}</span>
                  <span class="route-stats">{{ route.distance }}km · {{ route.stationCount }}站</span>
                </div>
              </div>
              <div class="route-actions">
                <el-icon v-if="selectedRouteId === route.routeId" class="selected-icon">
                  <Check />
                </el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 三栏式主要布局 - 选择线路后显示 -->
    <div v-if="selectedRouteId && selectedRoute" class="three-column-layout">
      <!-- 左侧线路信息面板 -->
      <div class="left-route-panel">
        <!-- 线路统计信息卡片 -->
        <div v-if="selectedRoute" class="route-stats-card">
          <div class="card-header">
            <el-icon><DataAnalysis /></el-icon>
            <span>线路概况</span>
          </div>
          <div class="route-basic-info">
            <div class="route-title">
              <span class="route-name">{{ selectedRoute.routeName }}</span>
              <el-tag size="small" type="primary">{{ selectedRoute.routeCode }}</el-tag>
            </div>
            <div class="route-details">
              <span>{{ selectedRoute.distance }}km · {{ selectedRoute.stationCount }}站</span>
            </div>
          </div>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon vehicle">
                <el-icon><Van /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ routeVehicles.length }}</div>
                <div class="stat-label">配属车辆</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon time">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ routeTimePoints.length }}</div>
                <div class="stat-label">发车时间</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon assigned">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ assignedCount }}</div>
                <div class="stat-label">已分配</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速操作面板 -->
        <div v-if="selectedRoute" class="quick-actions-card">
          <div class="card-header">
            <el-icon><Setting /></el-icon>
            <span>快速操作</span>
          </div>
          <div class="action-buttons-vertical">
            <el-button type="success" @click="autoAssignAll" :loading="autoAssigning" class="action-btn" :disabled="routeTimePoints.length === 0">
              <el-icon><MagicStick /></el-icon>
              一键排班
            </el-button>
            <el-button type="primary" @click="saveSchedule" :loading="saving" class="action-btn" :disabled="assignedCount === 0">
              <el-icon><Check /></el-icon>
              保存计划
            </el-button>
            <el-button @click="clearAllAssignments" class="action-btn" :disabled="assignedCount === 0">
              <el-icon><RefreshLeft /></el-icon>
              清空分配
            </el-button>
          </div>
        </div>

        <!-- 时间模板快捷设置 -->
        <div v-if="selectedRoute" class="template-card">
          <div class="card-header">
            <el-icon><Menu /></el-icon>
            <span>时间模板</span>
          </div>
          <div class="template-buttons">
            <el-button size="small" @click="applyTimeTemplate('peak')" class="template-btn"> 高峰期 </el-button>
            <el-button size="small" @click="applyTimeTemplate('normal')" class="template-btn"> 平峰期 </el-button>
            <el-button size="small" @click="applyTimeTemplate('weekend')" class="template-btn"> 周末 </el-button>
            <el-button size="small" @click="applyTimeTemplate('holiday')" class="template-btn"> 节假日 </el-button>
          </div>
        </div>
      </div>

      <!-- 中央时间配置主区 -->
      <div class="center-timeline-area">
        <div class="timeline-header">
          <h3>
            <el-icon><Clock /></el-icon>发车时间配置
          </h3>
          <div class="timeline-controls">
            <el-button-group size="small">
              <el-button @click="zoomOut" :disabled="timelineScale <= 0.8">
                <el-icon><ZoomOut /></el-icon>
              </el-button>
              <el-button @click="resetZoom"> {{ Math.round(timelineScale * 100) }}% </el-button>
              <el-button @click="zoomIn" :disabled="timelineScale >= 1.5">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 优化的时间线可视化 -->
        <div class="enhanced-timeline-visual">
          <div class="timeline-container" :style="{ transform: `scale(${timelineScale})` }">
            <!-- SVG连接线 -->
            <svg
              class="timeline-connections"
              v-if="routeTimePoints.length > 1"
              :viewBox="`0 0 ${8 * (160 + 20) + 40} ${Math.ceil(routeTimePoints.length / 8) * (140 + 20) + 40}`"
              preserveAspectRatio="xMidYMid meet"
            >
              <defs>
                <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color: #3b82f6; stop-opacity: 1" />
                  <stop offset="50%" style="stop-color: #60a5fa; stop-opacity: 1" />
                  <stop offset="100%" style="stop-color: #1d4ed8; stop-opacity: 1" />
                </linearGradient>
                <filter id="connectionShadow" x="-50%" y="-50%" width="200%" height="200%">
                  <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="rgba(59, 130, 246, 0.4)" />
                </filter>
              </defs>
              <path
                :d="generateEnhancedConnectionPath()"
                stroke="url(#connectionGradient)"
                stroke-width="3"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
                filter="url(#connectionShadow)"
                class="connection-path"
              />
            </svg>

            <!-- 优化的时间节点网格 -->
            <div class="enhanced-timeline-track">
              <div
                v-for="(timePoint, index) in routeTimePoints"
                :key="`time-${index}`"
                class="enhanced-timeline-node"
                :class="{
                  'highlight': highlightedTimeIndex === index,
                  'peak-time': timePoint.type === 'peak',
                  'has-vehicle': getAssignedVehicle(timePoint.time),
                  'conflict-warning': hasTimeConflict(timePoint.time)
                }"
                :style="getEnhancedNodeStyle(index)"
                @drop="handleTimePointDrop($event, timePoint.time)"
                @dragover="handleDragOver"
                @dragenter="handleDragEnter($event, index)"
                @dragleave="handleDragLeave"
              >
                <div class="time-info">
                  <div class="time-text">{{ timePoint.time }}</div>
                  <div class="time-sequence">{{ index + 1 }}</div>
                  <div v-if="timePoint.type === 'peak'" class="peak-indicator">高峰</div>
                </div>

                <div class="vehicle-display">
                  <div v-if="getAssignedVehicle(timePoint.time)" class="assigned-vehicle-enhanced">
                    <div class="vehicle-icon-enhanced">
                      <el-icon><Van /></el-icon>
                    </div>
                    <div class="vehicle-info-enhanced">
                      <div class="vehicle-plate">{{ getAssignedVehicle(timePoint.time)?.plateNumber }}</div>
                      <div class="vehicle-trip">第{{ getVehicleTripNumber(timePoint.time) }}趟</div>
                    </div>
                    <el-button type="danger" size="small" circle @click="removeAssignment(timePoint.time)" class="remove-btn-enhanced">
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </div>
                  <div v-else class="empty-slot-enhanced">
                    <div class="empty-icon">
                      <el-icon><Plus /></el-icon>
                    </div>
                    <div class="empty-text">拖拽车辆到此</div>
                    <el-button @click="autoAssignSingle(timePoint.time)" size="small" type="primary" text class="auto-assign-btn">
                      自动分配
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="routeTimePoints.length === 0" class="empty-timeline">
            <el-empty description="暂无发车时间配置">
              <template #image>
                <el-icon size="60" color="#c0c4cc"><Clock /></el-icon>
              </template>
              <template #description>
                <p>请先选择时间模板或手动添加发车时间</p>
              </template>
            </el-empty>
          </div>
        </div>
      </div>

      <!-- 右侧车辆管理面板 -->
      <div class="right-vehicle-panel">
        <!-- 车辆筛选器 -->
        <div class="vehicle-filter-card">
          <div class="card-header">
            <el-icon><Van /></el-icon>
            <span>车辆管理</span>
          </div>
          <div class="filter-controls">
            <el-radio-group v-model="vehicleFilter" size="small">
              <el-radio-button value="all">全部 ({{ routeVehicles.length }})</el-radio-button>
              <el-radio-button value="available">可用 ({{ availableVehiclesCount }})</el-radio-button>
              <el-radio-button value="assigned">已分配 ({{ assignedVehiclesCount }})</el-radio-button>
            </el-radio-group>
          </div>
          <el-input v-model="vehicleSearchText" placeholder="搜索车牌号..." size="small" clearable prefix-icon="Search" style="margin-top: 12px" />
        </div>

        <!-- 车辆列表 -->
        <div class="vehicle-list-card">
          <div class="vehicle-list-header">
            <span>车辆列表</span>
            <el-dropdown @command="handleBatchAction">
              <el-button size="small" text>
                批量操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="selectAll">全选可用车辆</el-dropdown-item>
                  <el-dropdown-item command="clearAll">清空所有分配</el-dropdown-item>
                  <el-dropdown-item command="autoBalance">智能均衡分配</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>

          <div class="enhanced-vehicle-list">
            <div
              v-for="vehicle in filteredVehicles"
              :key="vehicle.vehicleId"
              class="enhanced-vehicle-item"
              :class="{
                'assigned': vehicle.assignedTimes && vehicle.assignedTimes.length > 0,
                'unavailable': vehicle.status !== '1',
                'selected': selectedVehicles.includes(vehicle.vehicleId)
              }"
              draggable="true"
              @dragstart="handleVehicleDragStart(vehicle, $event)"
              @dragend="handleVehicleDragEnd"
              @click="toggleVehicleSelection(vehicle.vehicleId)"
            >
              <div class="vehicle-main-info">
                <div class="vehicle-icon-enhanced">
                  <el-icon><Van /></el-icon>
                </div>
                <div class="vehicle-details">
                  <div class="vehicle-plate">{{ vehicle.plateNumber }}</div>
                  <div class="vehicle-meta">
                    <span class="vehicle-type">{{ getVehicleTypeName(vehicle.vehicleType) }}</span>
                    <span class="vehicle-capacity">{{ vehicle.seatCount }}座</span>
                  </div>
                </div>
                <div class="vehicle-status">
                  <el-tag :type="getStatusType(vehicle.status)" size="small" effect="dark">
                    {{ getStatusText(vehicle.status) }}
                  </el-tag>
                </div>
              </div>

              <div v-if="vehicle.assignedTimes && vehicle.assignedTimes.length > 0" class="assignment-details">
                <div class="assignment-summary">
                  <span class="assignment-count">已分配 {{ vehicle.assignedTimes.length }} 趟</span>
                  <el-progress :percentage="getVehicleUtilization(vehicle)" :color="getUtilizationColor(vehicle)" size="small" :show-text="false" />
                </div>
                <div class="assignment-times">
                  <el-tag v-for="time in vehicle.assignedTimes.slice(0, 3)" :key="time" size="small" type="info" class="time-tag">
                    {{ time }}
                  </el-tag>
                  <span v-if="vehicle.assignedTimes.length > 3" class="more-times"> +{{ vehicle.assignedTimes.length - 3 }} </span>
                </div>
              </div>

              <div v-else class="no-assignment">
                <div class="drag-hint">
                  <el-icon><Rank /></el-icon>
                  <span>拖拽到时间点进行分配</span>
                </div>
              </div>
            </div>
          </div>

          <div v-if="filteredVehicles.length === 0" class="empty-vehicles">
            <el-empty description="暂无符合条件的车辆" :image-size="80">
              <template #image>
                <el-icon size="80" color="#c0c4cc"><Van /></el-icon>
              </template>
            </el-empty>
          </div>
        </div>

        <!-- 分配统计 -->
        <div class="assignment-summary-card">
          <div class="card-header">
            <el-icon><PieChart /></el-icon>
            <span>分配统计</span>
          </div>
          <div class="summary-stats">
            <div class="summary-item">
              <div class="summary-label">车辆利用率</div>
              <div class="summary-value">{{ Math.round((assignedVehiclesCount / routeVehicles.length) * 100) || 0 }}%</div>
              <el-progress :percentage="Math.round((assignedVehiclesCount / routeVehicles.length) * 100) || 0" :color="'#67c23a'" size="small" />
            </div>
            <div class="summary-item">
              <div class="summary-label">平均趟次</div>
              <div class="summary-value">{{ getAverageTripCount() }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">最忙车辆</div>
              <div class="summary-value">{{ getBusiestVehicle()?.plateNumber || '-' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 未选择线路时的空状态 -->
    <div v-else class="empty-state-container">
      <div class="empty-content">
        <div class="empty-icon-wrapper">
          <el-icon class="empty-icon"><Position /></el-icon>
        </div>
        <h3 class="empty-title">开始排班管理</h3>
        <p class="empty-description">
          选择一条公交线路开始制定排班计划
          <br />
          系统将为您提供智能化的车辆分配和时间安排
        </p>
        <div class="empty-actions">
          <el-button type="primary" size="large" @click="openRouteSelector" class="primary-action">
            <el-icon><Position /></el-icon>
            选择线路开始
          </el-button>
        </div>
        <div class="empty-features">
          <div class="feature-item">
            <el-icon class="feature-icon"><Van /></el-icon>
            <span>智能车辆分配</span>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Clock /></el-icon>
            <span>时间模板配置</span>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><DataAnalysis /></el-icon>
            <span>实时统计分析</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 拖拽提示层 -->
    <div v-if="isDragging" class="drag-overlay">
      <div class="drag-hint">
        <el-icon><Rank /></el-icon>
        <span>拖拽到发车时间点进行分配</span>
      </div>
    </div>

    <!-- 排班确认弹窗 -->
    <el-dialog v-model="showScheduleConfirm" title="一键排班确认" width="600px" :close-on-click-modal="false">
      <div class="dialog-content-section">
        <div class="confirm-header">
          <el-icon class="header-icon"><MagicStick /></el-icon>
          <div class="header-text">
            <h3>智能排班方案</h3>
            <p>系统为您生成了以下排班方案，请确认是否应用：</p>
          </div>
        </div>
      </div>

      <div class="dialog-content-section">
        <div class="dialog-section-title">
          <el-icon><DataAnalysis /></el-icon>
          <span>分配统计</span>
        </div>
        <div class="dialog-grid">
          <div class="dialog-detail-item">
            <span class="label">待分配时间点：</span>
            <span class="value">{{ previewAssignments.length }}个</span>
          </div>
          <div class="dialog-detail-item">
            <span class="label">涉及车辆：</span>
            <span class="value">{{ getInvolvedVehicleCount() }}辆</span>
          </div>
          <div class="dialog-detail-item">
            <span class="label">分配成功率：</span>
            <span class="value status-success">{{ getSuccessRate() }}%</span>
          </div>
        </div>
      </div>

      <div class="dialog-content-section">
        <div class="dialog-section-title">
          <el-icon><Setting /></el-icon>
          <span>详细分配方案</span>
        </div>
        <div class="dialog-grid-wide">
          <div
            v-for="(assignment, index) in previewAssignments.slice().sort((a, b) => timeToMinutes(a.timePoint) - timeToMinutes(b.timePoint))"
            :key="`${assignment.timePoint}-${assignment.vehicleId}`"
            class="dialog-detail-item full-width"
            style="display: flex; align-items: center; gap: 12px; padding: 12px 16px"
          >
            <div class="time-info" style="flex: 0 0 auto; min-width: 80px">
              <el-tag :type="getTimePointType(assignment.timePoint)" size="large">
                {{ assignment.timePoint }}
              </el-tag>
            </div>
            <el-icon style="color: #c0c4cc; font-size: 16px; flex: 0 0 auto"><ArrowRight /></el-icon>
            <div class="vehicle-info" style="flex: 1; display: flex; flex-direction: column; gap: 4px">
              <div class="vehicle-selection">
                <el-select
                  v-model="assignment.vehicleId"
                  @change="handleVehicleChange(assignment, $event)"
                  placeholder="选择车辆"
                  size="small"
                  style="width: 120px"
                >
                  <el-option
                    v-for="vehicle in getAvailableVehiclesForTime(assignment.timePoint, assignment.vehicleId)"
                    :key="vehicle.vehicleId"
                    :label="vehicle.plateNumber"
                    :value="vehicle.vehicleId"
                  >
                    <span style="float: left">{{ vehicle.plateNumber }}</span>
                    <span style="float: right; color: #8492a6; font-size: 12px">
                      {{ getVehicleStatusText(vehicle.vehicleId, assignment.timePoint) }}
                    </span>
                  </el-option>
                </el-select>
              </div>
              <div style="font-size: 12px; color: #909399; text-align: left">
                第{{ getVehicleTripCount(assignment.vehicleId, assignment.timePoint) }}趟
              </div>
            </div>
            <div style="flex: 0 0 auto; display: flex; align-items: center">
              <el-button type="danger" size="small" plain @click="removePreviewAssignment(assignment)" :icon="Close"> </el-button>
            </div>
          </div>
        </div>

        <div
          v-if="failedAssignments.length > 0"
          class="dialog-content-section"
          style="background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%); border-color: rgba(239, 68, 68, 0.3)"
        >
          <div class="dialog-section-title" style="color: #ef4444; border-bottom-color: rgba(239, 68, 68, 0.3)">
            <el-icon style="color: #f87171"><Close /></el-icon>
            <span>无法分配的时间点</span>
          </div>
          <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 12px">
            <el-tag v-for="timePoint in failedAssignments" :key="timePoint" type="danger" size="small">
              {{ timePoint }}
            </el-tag>
          </div>
          <div class="dialog-detail-item" style="background: rgba(239, 68, 68, 0.05); border-color: rgba(239, 68, 68, 0.2)">
            <span class="label">失败原因：</span>
            <span class="value status-error">无可用车辆或时间冲突</span>
          </div>
          <div style="margin-top: 12px; text-align: center">
            <el-button type="primary" size="small" plain @click="addManualAssignment"> 手动分配 </el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelScheduleConfirm">取消</el-button>
          <el-button type="primary" @click="confirmScheduleAssign" :loading="applying"> 确认应用 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Share,
  MagicStick,
  Check,
  RefreshLeft,
  Position,
  Van,
  Clock,
  ArrowDown,
  Setting,
  Plus,
  Close,
  MoreFilled,
  Rank,
  ArrowRight,
  DataAnalysis,
  Menu,
  ZoomIn,
  ZoomOut,
  PieChart
} from '@element-plus/icons-vue';

// 接口定义
interface BusRoute {
  routeId: number | string;
  routeName: string;
  routeCode: string;
  distance: number;
  stationCount: number;
  description: string;
  vehicleIds: (number | string)[];
  timePoints: TimePoint[];
}

interface Vehicle {
  vehicleId: number | string;
  plateNumber: string;
  vehicleNumber: string;
  vehicleType: 'bus' | 'electric_bus' | 'hybrid_bus' | 'minibus';
  seatCount: number;
  status: '1' | '0' | '2' | '3';
  routeId?: number | string;
  assignedTimes?: string[]; // 改为数组，记录所有分配的时间点
}

interface TimePoint {
  time: string;
  type?: 'peak' | 'normal' | 'weekend' | 'holiday';
  frequency?: number;
}

interface Assignment {
  timePoint: string;
  vehicleId: number | string;
  plateNumber: string;
}

// 响应式数据
const routes = ref<BusRoute[]>([]);
const vehicles = ref<Vehicle[]>([]);
const selectedRouteId = ref<number | string | null>(null);
const assignments = ref<Assignment[]>([]);
const routesLoading = ref(false);
const autoAssigning = ref(false);
const saving = ref(false);
const isDragging = ref(false);
const highlightedTimeIndex = ref(-1);
const routeSelectorRef = ref();

// 新增的响应式数据
const routeSelectorVisible = ref(false);
const routeSearchText = ref('');

// {{ AURA: Add - 排班确认弹窗相关状态 }}
const showScheduleConfirm = ref(false);
const previewAssignments = ref<Assignment[]>([]);
const failedAssignments = ref<string[]>([]);
const applying = ref(false);
const vehicleFilter = ref('all');

// 新增的响应式数据
const timelineScale = ref(1);
const vehicleSearchText = ref('');
const selectedVehicles = ref<(number | string)[]>([]);

// 计算属性：可用车辆数量
const availableVehiclesCount = computed(() => {
  return routeVehicles.value.filter((v) => v.status === '1').length;
});

// 计算属性：已分配车辆数量
const assignedVehiclesCount = computed(() => {
  return routeVehicles.value.filter((v) => v.assignedTimes && v.assignedTimes.length > 0).length;
});

// 新增方法：缩放控制
const zoomIn = () => {
  if (timelineScale.value < 1.5) {
    timelineScale.value = Math.min(1.5, timelineScale.value + 0.1);
  }
};

const zoomOut = () => {
  if (timelineScale.value > 0.8) {
    timelineScale.value = Math.max(0.8, timelineScale.value - 0.1);
  }
};

const resetZoom = () => {
  timelineScale.value = 1;
};

// 新增方法：车辆选择
const toggleVehicleSelection = (vehicleId: number | string) => {
  const index = selectedVehicles.value.indexOf(vehicleId);
  if (index > -1) {
    selectedVehicles.value.splice(index, 1);
  } else {
    selectedVehicles.value.push(vehicleId);
  }
};

// 新增方法：批量操作
const handleBatchAction = (command: string) => {
  switch (command) {
    case 'selectAll':
      selectedVehicles.value = routeVehicles.value.filter((v) => v.status === '1').map((v) => v.vehicleId);
      ElMessage.success('已选择所有可用车辆');
      break;
    case 'clearAll':
      clearAllAssignments();
      break;
    case 'autoBalance':
      autoAssignAll();
      break;
  }
};

// 新增方法：获取车辆利用率
const getVehicleUtilization = (vehicle: Vehicle): number => {
  const maxTrips = 8; // 假设最大8趟
  const assignedTrips = vehicle.assignedTimes?.length || 0;
  return Math.round((assignedTrips / maxTrips) * 100);
};

// 新增方法：获取利用率颜色
const getUtilizationColor = (vehicle: Vehicle): string => {
  const utilization = getVehicleUtilization(vehicle);
  if (utilization >= 80) return '#f56c6c';
  if (utilization >= 60) return '#e6a23c';
  return '#67c23a';
};

// 新增方法：获取平均趟次
const getAverageTripCount = (): string => {
  const totalTrips = routeVehicles.value.reduce((sum, v) => sum + (v.assignedTimes?.length || 0), 0);
  const avg = routeVehicles.value.length > 0 ? totalTrips / routeVehicles.value.length : 0;
  return avg.toFixed(1);
};

// 新增方法：获取最忙车辆
const getBusiestVehicle = (): Vehicle | null => {
  return routeVehicles.value.reduce(
    (busiest, current) => {
      const currentTrips = current.assignedTimes?.length || 0;
      const busiestTrips = busiest?.assignedTimes?.length || 0;
      return currentTrips > busiestTrips ? current : busiest;
    },
    null as Vehicle | null
  );
};

// 新增方法：检查时间冲突
const hasTimeConflict = (timePoint: string): boolean => {
  // 这里可以添加时间冲突检查逻辑
  return false;
};

// 新增方法：获取车辆趟次编号
const getVehicleTripNumber = (timePoint: string): number => {
  const assignment = assignments.value.find((a) => a.timePoint === timePoint);
  if (!assignment) return 1;

  const vehicle = vehicles.value.find((v) => v.vehicleId === assignment.vehicleId);
  if (!vehicle?.assignedTimes) return 1;

  const sortedTimes = vehicle.assignedTimes.sort();
  return sortedTimes.indexOf(timePoint) + 1;
};

// 优化的蛇形排列计算 - 8列布局
const getEnhancedSnakePosition = (index: number) => {
  const cols = 8; // 改为8列，更好利用宽屏
  const row = Math.floor(index / cols);
  const col = index % cols;
  const isReverseRow = row % 2 === 1;

  return {
    row,
    col: isReverseRow ? cols - 1 - col : col,
    isReverse: isReverseRow
  };
};

// 优化的节点CSS Grid定位
const getEnhancedNodeStyle = (index: number) => {
  const pos = getEnhancedSnakePosition(index);
  return {
    gridRow: pos.row + 1,
    gridColumn: pos.col + 1
  };
};

// 优化的连接路径生成
const generateEnhancedConnectionPath = () => {
  if (routeTimePoints.value.length < 2) return '';

  const cols = 8;
  const nodeWidth = 160;
  const nodeHeight = 140;
  const gap = 20;
  const containerPadding = 20;
  const extensionLength = 120;

  let path = '';

  for (let i = 0; i < routeTimePoints.value.length; i++) {
    const pos = getEnhancedSnakePosition(i);
    const nextPos = i < routeTimePoints.value.length - 1 ? getEnhancedSnakePosition(i + 1) : null;

    const x = containerPadding + pos.col * (nodeWidth + gap) + nodeWidth / 2;
    const y = containerPadding + pos.row * (nodeHeight + gap) + nodeHeight / 2;

    if (i === 0) {
      path += `M ${x} ${y}`;
    } else {
      path += ` L ${x} ${y}`;
    }

    if (nextPos && nextPos.row !== pos.row) {
      if (pos.col === cols - 1) {
        const extensionX = x + extensionLength;
        const nextY = containerPadding + nextPos.row * (nodeHeight + gap) + nodeHeight / 2;
        path += ` L ${extensionX} ${y}`;
        path += ` L ${extensionX} ${nextY}`;
      } else if (pos.col === 0) {
        const extensionX = x - extensionLength;
        const nextY = containerPadding + nextPos.row * (nodeHeight + gap) + nodeHeight / 2;
        path += ` L ${extensionX} ${y}`;
        path += ` L ${extensionX} ${nextY}`;
      }
    }
  }

  return path;
};

// 原有的6列布局保持兼容
const getSnakePosition = (index: number) => {
  const cols = 6;
  const row = Math.floor(index / cols);
  const col = index % cols;
  const isReverseRow = row % 2 === 1;

  return {
    row,
    col: isReverseRow ? cols - 1 - col : col,
    isReverse: isReverseRow
  };
};

// 节点CSS Grid定位
const getNodeStyle = (index: number) => {
  const pos = getSnakePosition(index);
  return {
    gridRow: pos.row + 1,
    gridColumn: pos.col + 1
  };
};

// 生成连接路径 - 在每一行拐弯时延伸出来再拐弯
const generateSimpleConnectionPath = () => {
  if (routeTimePoints.value.length < 2) return '';

  const cols = 6;
  // 需要与CSS完全匹配的数值
  const nodeWidth = 140; // 节点宽度
  const nodeHeight = 120; // 节点高度
  const gap = 20; // CSS gap值
  const containerPadding = 20; // timeline-track的padding
  const extensionLength = 100; // {{ AURA: Modify - 延伸距离增加到约半个卡片加间距的距离 }}

  let path = '';

  for (let i = 0; i < routeTimePoints.value.length; i++) {
    const pos = getSnakePosition(i);
    const nextPos = i < routeTimePoints.value.length - 1 ? getSnakePosition(i + 1) : null;

    // 计算节点的实际中心位置
    const x = containerPadding + pos.col * (nodeWidth + gap) + nodeWidth / 2;
    const y = containerPadding + pos.row * (nodeHeight + gap) + nodeHeight / 2;

    if (i === 0) {
      path += `M ${x} ${y}`;
    } else {
      path += ` L ${x} ${y}`;
    }

    // {{ AURA: Add - 在需要换行时添加延伸效果 }}
    if (nextPos && nextPos.row !== pos.row) {
      // 下一个节点在不同行，需要拐弯
      if (pos.col === cols - 1) {
        // 当前在行末，向右延伸再向下
        const extensionX = x + extensionLength;
        const nextY = containerPadding + nextPos.row * (nodeHeight + gap) + nodeHeight / 2;
        path += ` L ${extensionX} ${y}`; // 向右延伸
        path += ` L ${extensionX} ${nextY}`; // 向下拐弯
      } else if (pos.col === 0) {
        // 当前在行首，向左延伸再向下
        const extensionX = x - extensionLength;
        const nextY = containerPadding + nextPos.row * (nodeHeight + gap) + nodeHeight / 2;
        path += ` L ${extensionX} ${y}`; // 向左延伸
        path += ` L ${extensionX} ${nextY}`; // 向下拐弯
      }
    }
  }

  return path;
};

// 计算属性
const selectedRoute = computed(() => {
  return routes.value.find((r) => r.routeId === selectedRouteId.value) || null;
});

const routeVehicles = computed(() => {
  if (!selectedRoute.value) return [];
  return vehicles.value.filter((v) => selectedRoute.value?.vehicleIds.includes(v.vehicleId));
});

const routeTimePoints = computed(() => {
  return selectedRoute.value?.timePoints || [];
});

const filteredVehicles = computed(() => {
  let filtered = routeVehicles.value;

  switch (vehicleFilter.value) {
    case 'available':
      filtered = filtered.filter((v) => v.status === '1');
      break;
    case 'assigned':
      filtered = filtered.filter((v) => v.assignedTimes && v.assignedTimes.length > 0);
      break;
  }

  return filtered;
});

const filteredRoutes = computed(() => {
  if (!routeSearchText.value) return routes.value;

  const searchText = routeSearchText.value.toLowerCase();
  return routes.value.filter((route) => route.routeName.toLowerCase().includes(searchText) || route.routeCode.toLowerCase().includes(searchText));
});

const assignedCount = computed(() => {
  return assignments.value.length;
});

// 辅助函数：时间转换为分钟数
const timeToMinutes = (time: string): number => {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
};

// 事件处理
const handleRouteChange = (routeId: number | string) => {
  console.log('线路选择:', routeId);
  selectedRouteId.value = routeId;

  // 清空之前的分配
  clearAllAssignments();

  // 确保线路选择成功
  const route = routes.value.find((r) => r.routeId === routeId);
  if (route) {
    ElMessage.success(`已选择线路：${route.routeName}`);
  } else {
    ElMessage.error('线路选择失败，请重试');
  }
};

const handleVehicleDragStart = (vehicle: Vehicle, event: DragEvent) => {
  if (vehicle.status !== '1') {
    event.preventDefault();
    ElMessage.warning('该车辆状态不可用');
    return;
  }

  event.dataTransfer!.setData(
    'text/plain',
    JSON.stringify({
      type: 'vehicle',
      vehicleId: vehicle.vehicleId,
      plateNumber: vehicle.plateNumber
    })
  );

  isDragging.value = true;
};

const handleVehicleDragEnd = () => {
  isDragging.value = false;
  highlightedTimeIndex.value = -1;
};

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
};

const handleDragEnter = (event: DragEvent, index: number) => {
  event.preventDefault();
  highlightedTimeIndex.value = index;
};

const handleDragLeave = (event: DragEvent) => {
  // 检查是否真的离开了拖拽区域
  if (!event.currentTarget || !(event.currentTarget as Element).contains(event.relatedTarget as Node)) {
    highlightedTimeIndex.value = -1;
  }
};

const handleTimePointDrop = (event: DragEvent, timePoint: string) => {
  event.preventDefault();
  highlightedTimeIndex.value = -1;
  isDragging.value = false;

  try {
    const dragData = JSON.parse(event.dataTransfer!.getData('text/plain'));

    if (dragData.type === 'vehicle') {
      assignVehicleToTimePoint(dragData.vehicleId, dragData.plateNumber, timePoint);
    }
  } catch (error) {
    ElMessage.error('分配失败');
  }
};

// 辅助函数：检查车辆在指定时间是否可用（考虑行程时间）
const isVehicleAvailableAtTime = (vehicleId: number | string, targetTime: string): boolean => {
  const vehicle = vehicles.value.find((v) => v.vehicleId === vehicleId);
  if (!vehicle || !vehicle.assignedTimes || vehicle.assignedTimes.length === 0) {
    return true;
  }

  const targetMinutes = timeToMinutes(targetTime);
  const tripDuration = 90; // 假设一趟车需要90分钟（包括行程和休息时间）

  // 检查是否与已分配的时间冲突
  for (const assignedTime of vehicle.assignedTimes) {
    const assignedMinutes = timeToMinutes(assignedTime);
    const timeDiff = Math.abs(targetMinutes - assignedMinutes);

    if (timeDiff < tripDuration) {
      return false; // 时间冲突
    }
  }

  return true;
};

// 辅助函数：获取车辆的分配次数
const getVehicleAssignmentCount = (vehicleId: number | string): number => {
  const vehicle = vehicles.value.find((v) => v.vehicleId === vehicleId);
  return vehicle?.assignedTimes?.length || 0;
};

// 工具函数

const getVehicleTypeName = (type: string): string => {
  const typeMap: Record<string, string> = {
    bus: '普通公交',
    electric_bus: '电动公交',
    hybrid_bus: '混动公交',
    minibus: '小型公交'
  };
  return typeMap[type] || type;
};

const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': 'success',
    '0': 'info',
    '2': 'warning',
    '3': 'danger'
  };
  return statusMap[status] || 'info';
};

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': '正常',
    '0': '停用',
    '2': '维修中',
    '3': '故障'
  };
  return statusMap[status] || '未知';
};

const getAssignedVehicle = (timePoint: string): Vehicle | null => {
  const assignment = assignments.value.find((a) => a.timePoint === timePoint);
  if (!assignment) return null;
  return vehicles.value.find((v) => v.vehicleId === assignment.vehicleId) || null;
};

const assignVehicleToTimePoint = (vehicleId: number | string, plateNumber: string, timePoint: string) => {
  // 检查该时间点是否已有分配
  const existingAssignment = assignments.value.find((a) => a.timePoint === timePoint);
  if (existingAssignment) {
    ElMessage.warning(`时间点 ${timePoint} 已有车辆分配`);
    return;
  }

  // 检查车辆在该时间是否可用（时间冲突检测）
  if (!isVehicleAvailableAtTime(vehicleId, timePoint)) {
    ElMessage.warning(`车辆 ${plateNumber} 在 ${timePoint} 时间冲突，请选择其他时间`);
    return;
  }

  // 创建新分配
  assignments.value.push({
    timePoint,
    vehicleId,
    plateNumber
  });

  // 更新车辆的分配时间数组
  const vehicle = vehicles.value.find((v) => v.vehicleId === vehicleId);
  if (vehicle) {
    if (!vehicle.assignedTimes) {
      vehicle.assignedTimes = [];
    }
    vehicle.assignedTimes.push(timePoint);
    // 按时间排序
    vehicle.assignedTimes.sort();
  }

  const assignmentCount = getVehicleAssignmentCount(vehicleId);
  ElMessage.success(`车辆 ${plateNumber} 已分配到 ${timePoint}（第${assignmentCount}趟）`);
};

const removeAssignment = (timePoint: string) => {
  const assignmentIndex = assignments.value.findIndex((a) => a.timePoint === timePoint);
  if (assignmentIndex === -1) return;

  const assignment = assignments.value[assignmentIndex];

  // 移除分配
  assignments.value.splice(assignmentIndex, 1);

  // 从车辆的分配时间数组中移除该时间点
  const vehicle = vehicles.value.find((v) => v.vehicleId === assignment.vehicleId);
  if (vehicle && vehicle.assignedTimes) {
    const timeIndex = vehicle.assignedTimes.indexOf(timePoint);
    if (timeIndex > -1) {
      vehicle.assignedTimes.splice(timeIndex, 1);
    }
  }

  ElMessage.success(`已取消 ${timePoint} 的车辆分配`);
};

const handleTimePointAction = (command: string, timePoint: string) => {
  switch (command) {
    case 'auto-assign':
      autoAssignSingle(timePoint);
      break;
    case 'clear':
      removeAssignment(timePoint);
      break;
    case 'edit':
      // 编辑单个时间点
      ElMessage.info('编辑功能开发中');
      break;
  }
};

const autoAssignSingle = (timePoint: string) => {
  // 获取在该时间点可用的车辆（按分配次数排序，优先选择分配次数少的）
  const availableVehicles = routeVehicles.value
    .filter((v) => v.status === '1' && isVehicleAvailableAtTime(v.vehicleId, timePoint))
    .sort((a, b) => getVehicleAssignmentCount(a.vehicleId) - getVehicleAssignmentCount(b.vehicleId));

  if (availableVehicles.length === 0) {
    ElMessage.warning('该时间点没有可用的车辆');
    return;
  }

  // 选择分配次数最少的车辆
  const vehicle = availableVehicles[0];
  assignVehicleToTimePoint(vehicle.vehicleId, vehicle.plateNumber, timePoint);
};

const autoAssignAll = async () => {
  if (!selectedRoute.value) return;

  // {{ AURA: Modify - 获取未分配的时间点，并按时间排序 }}
  const unassignedTimePoints = routeTimePoints.value
    .filter((tp) => !assignments.value.some((a) => a.timePoint === tp.time))
    .sort((a, b) => timeToMinutes(a.time) - timeToMinutes(b.time));

  if (unassignedTimePoints.length === 0) {
    ElMessage.warning('所有时间点都已分配');
    return;
  }

  autoAssigning.value = true;

  try {
    // {{ AURA: Modify - 生成排班预览方案，不直接分配 }}
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const tempAssignments: Assignment[] = [];
    const tempFailedAssignments: string[] = [];

    // {{ AURA: Modify - 为每个未分配的时间点寻找最佳车辆，考虑预览过程中的临时分配 }}
    for (const tp of unassignedTimePoints) {
      const availableVehicles = routeVehicles.value
        .filter((v) => {
          // 1. 车辆状态必须可用
          if (v.status !== '1') return false;

          // 2. 检查与现有分配的时间冲突
          if (!isVehicleAvailableAtTime(v.vehicleId, tp.time)) return false;

          // 3. 检查与预览分配的时间冲突
          const hasConflictInPreview = tempAssignments.some((tempAssign) => {
            if (tempAssign.vehicleId !== v.vehicleId) return false;
            const timeDiff = Math.abs(timeToMinutes(tempAssign.timePoint) - timeToMinutes(tp.time));
            return timeDiff < 60; // 60分钟间隔限制
          });

          return !hasConflictInPreview;
        })
        .sort((a, b) => {
          // 按现有分配次数 + 预览分配次数排序
          const aExistingCount = getVehicleAssignmentCount(a.vehicleId);
          const aPreviewCount = tempAssignments.filter((ta) => ta.vehicleId === a.vehicleId).length;
          const bExistingCount = getVehicleAssignmentCount(b.vehicleId);
          const bPreviewCount = tempAssignments.filter((ta) => ta.vehicleId === b.vehicleId).length;

          return aExistingCount + aPreviewCount - (bExistingCount + bPreviewCount);
        });

      if (availableVehicles.length > 0) {
        const vehicle = availableVehicles[0];
        tempAssignments.push({
          timePoint: tp.time,
          vehicleId: vehicle.vehicleId,
          plateNumber: vehicle.plateNumber
        });
      } else {
        tempFailedAssignments.push(tp.time);
      }
    }

    // {{ AURA: Add - 设置预览数据并显示确认弹窗 }}
    previewAssignments.value = tempAssignments;
    failedAssignments.value = tempFailedAssignments;
    showScheduleConfirm.value = true;
  } finally {
    autoAssigning.value = false;
  }
};

const clearAllAssignments = () => {
  assignments.value = [];
  vehicles.value.forEach((v) => {
    v.assignedTimes = [];
  });
  ElMessage.success('已清空所有分配');
};

// {{ AURA: Add - 排班确认弹窗相关方法 }}
const getInvolvedVehicleCount = () => {
  const vehicleIds = new Set(previewAssignments.value.map((a) => a.vehicleId));
  return vehicleIds.size;
};

const getSuccessRate = () => {
  const totalCount = previewAssignments.value.length + failedAssignments.value.length;
  if (totalCount === 0) return 0;
  return Math.round((previewAssignments.value.length / totalCount) * 100);
};

const getTimePointType = (timePoint: string) => {
  const timeMinutes = timeToMinutes(timePoint);
  // 早高峰: 7:00-9:00, 晚高峰: 17:00-19:00
  if ((timeMinutes >= 420 && timeMinutes <= 540) || (timeMinutes >= 1020 && timeMinutes <= 1140)) {
    return 'warning'; // 高峰期用橙色
  }
  return 'primary'; // 普通时段用蓝色
};

const getVehicleTripCount = (vehicleId: number | string, currentTimePoint?: string) => {
  // {{ AURA: Modify - 计算该车辆的总趟次，如果提供当前时间点，则计算到该时间点为止的趟次 }}
  const existingCount = getVehicleAssignmentCount(vehicleId);

  if (currentTimePoint) {
    // 计算在当前时间点之前（包括当前）的预览分配数量
    const previewCountBeforeCurrent = previewAssignments.value
      .filter((a) => a.vehicleId === vehicleId)
      .filter((a) => timeToMinutes(a.timePoint) <= timeToMinutes(currentTimePoint)).length;
    return existingCount + previewCountBeforeCurrent;
  } else {
    // 计算总的预览分配数量
    const previewCount = previewAssignments.value.filter((a) => a.vehicleId === vehicleId).length;
    return existingCount + previewCount;
  }
};

const cancelScheduleConfirm = () => {
  showScheduleConfirm.value = false;
  previewAssignments.value = [];
  failedAssignments.value = [];
};

// {{ AURA: Add - 弹窗编辑功能相关方法 }}
const getAvailableVehiclesForTime = (timePoint: string, currentVehicleId?: number | string) => {
  return routeVehicles.value.filter((v) => {
    // 1. 车辆状态必须可用
    if (v.status !== '1') return false;

    // 2. 如果是当前选中的车辆，允许保持选择
    if (currentVehicleId && v.vehicleId === currentVehicleId) return true;

    // 3. 检查与现有分配的时间冲突
    if (!isVehicleAvailableAtTime(v.vehicleId, timePoint)) return false;

    // 4. 检查与其他预览分配的时间冲突
    const hasConflictInOtherPreview = previewAssignments.value.some((previewAssign) => {
      if (previewAssign.vehicleId !== v.vehicleId) return false;
      if (previewAssign.timePoint === timePoint) return false; // 排除当前时间点
      const timeDiff = Math.abs(timeToMinutes(previewAssign.timePoint) - timeToMinutes(timePoint));
      return timeDiff < 60; // 60分钟间隔限制
    });

    return !hasConflictInOtherPreview;
  });
};

const getVehicleStatusText = (vehicleId: number | string, timePoint: string) => {
  const existingCount = getVehicleAssignmentCount(vehicleId);
  const previewCount = previewAssignments.value.filter(
    (a) => a.vehicleId === vehicleId && timeToMinutes(a.timePoint) <= timeToMinutes(timePoint)
  ).length;
  const totalTrips = existingCount + previewCount;
  return `第${totalTrips}趟`;
};

const handleVehicleChange = (assignment: Assignment, newVehicleId: number | string) => {
  // 更新车牌号
  const newVehicle = vehicles.value.find((v) => v.vehicleId === newVehicleId);
  if (newVehicle) {
    assignment.plateNumber = newVehicle.plateNumber;
  }

  // 触发响应式更新
  const index = previewAssignments.value.findIndex((a) => a.timePoint === assignment.timePoint && a.vehicleId === assignment.vehicleId);
  if (index > -1) {
    previewAssignments.value[index] = { ...assignment };
  }
};

const removePreviewAssignment = (assignment: Assignment) => {
  const index = previewAssignments.value.findIndex((a) => a.timePoint === assignment.timePoint && a.vehicleId === assignment.vehicleId);
  if (index > -1) {
    previewAssignments.value.splice(index, 1);
    // 将该时间点添加到失败列表
    if (!failedAssignments.value.includes(assignment.timePoint)) {
      failedAssignments.value.push(assignment.timePoint);
    }
  }
};

const addManualAssignment = () => {
  // 获取所有失败的时间点，让用户可以手动分配
  if (failedAssignments.value.length === 0) {
    ElMessage.info('所有时间点都已分配');
    return;
  }

  // 这里可以添加手动分配的逻辑
  ElMessage.info('手动分配功能开发中');
};

const confirmScheduleAssign = async () => {
  applying.value = true;

  try {
    // 应用所有预览的分配
    let successCount = 0;
    for (const assignment of previewAssignments.value) {
      // 再次检查车辆是否可用（防止在确认期间发生变化）
      if (isVehicleAvailableAtTime(assignment.vehicleId, assignment.timePoint)) {
        // 静默分配，不显示单个成功消息
        assignments.value.push(assignment);

        // 更新车辆的分配时间数组
        const vehicle = vehicles.value.find((v) => v.vehicleId === assignment.vehicleId);
        if (vehicle) {
          if (!vehicle.assignedTimes) {
            vehicle.assignedTimes = [];
          }
          vehicle.assignedTimes.push(assignment.timePoint);
          vehicle.assignedTimes.sort();
        }
        successCount++;
      }
    }

    // 关闭弹窗
    showScheduleConfirm.value = false;

    // 显示总体结果
    if (successCount > 0) {
      ElMessage.success(`一键排班完成！成功分配 ${successCount} 个班次`);
      if (failedAssignments.value.length > 0) {
        ElMessage.warning(`${failedAssignments.value.length} 个时间点无法分配`);
      }
    } else {
      ElMessage.warning('排班分配失败，请检查车辆可用性');
    }
  } finally {
    applying.value = false;
    previewAssignments.value = [];
    failedAssignments.value = [];
  }
};

const saveSchedule = async () => {
  if (assignments.value.length === 0) {
    ElMessage.warning('请先进行排班分配');
    return;
  }

  saving.value = true;
  try {
    // 模拟保存API调用
    await new Promise((resolve) => setTimeout(resolve, 1500));
    ElMessage.success('排班计划保存成功');
  } catch (error) {
    ElMessage.error('保存失败');
  } finally {
    saving.value = false;
  }
};

const applyTimeTemplate = (template: string) => {
  let templateTimes: TimePoint[] = [];

  switch (template) {
    case 'peak':
      templateTimes = [
        { time: '06:00', type: 'peak' },
        { time: '06:15', type: 'peak' },
        { time: '06:30', type: 'peak' },
        { time: '06:45', type: 'peak' },
        { time: '07:00', type: 'peak' },
        { time: '07:15', type: 'peak' },
        { time: '07:30', type: 'peak' },
        { time: '07:45', type: 'peak' },
        { time: '08:00', type: 'peak' },
        { time: '17:00', type: 'peak' },
        { time: '17:15', type: 'peak' },
        { time: '17:30', type: 'peak' },
        { time: '17:45', type: 'peak' },
        { time: '18:00', type: 'peak' },
        { time: '18:15', type: 'peak' },
        { time: '18:30', type: 'peak' }
      ];
      break;
    case 'normal':
      templateTimes = [
        { time: '06:00', type: 'normal' },
        { time: '06:30', type: 'normal' },
        { time: '07:00', type: 'normal' },
        { time: '08:00', type: 'normal' },
        { time: '09:00', type: 'normal' },
        { time: '10:00', type: 'normal' },
        { time: '11:00', type: 'normal' },
        { time: '12:00', type: 'normal' },
        { time: '14:00', type: 'normal' },
        { time: '15:00', type: 'normal' },
        { time: '16:00', type: 'normal' },
        { time: '17:00', type: 'normal' },
        { time: '18:00', type: 'normal' },
        { time: '19:00', type: 'normal' },
        { time: '20:00', type: 'normal' },
        { time: '21:00', type: 'normal' },
        { time: '22:00', type: 'normal' }
      ];
      break;
    case 'weekend':
      templateTimes = [
        { time: '07:00', type: 'weekend' },
        { time: '08:00', type: 'weekend' },
        { time: '09:00', type: 'weekend' },
        { time: '10:00', type: 'weekend' },
        { time: '11:00', type: 'weekend' },
        { time: '14:00', type: 'weekend' },
        { time: '15:00', type: 'weekend' },
        { time: '16:00', type: 'weekend' },
        { time: '17:00', type: 'weekend' },
        { time: '18:00', type: 'weekend' },
        { time: '19:00', type: 'weekend' },
        { time: '20:00', type: 'weekend' },
        { time: '21:00', type: 'weekend' }
      ];
      break;
    case 'holiday':
      templateTimes = [
        { time: '08:00', type: 'holiday' },
        { time: '09:00', type: 'holiday' },
        { time: '10:00', type: 'holiday' },
        { time: '11:00', type: 'holiday' },
        { time: '14:00', type: 'holiday' },
        { time: '15:00', type: 'holiday' },
        { time: '16:00', type: 'holiday' },
        { time: '17:00', type: 'holiday' },
        { time: '18:00', type: 'holiday' },
        { time: '19:00', type: 'holiday' },
        { time: '20:00', type: 'holiday' }
      ];
      break;
  }

  if (selectedRoute.value) {
    selectedRoute.value.timePoints = templateTimes;
    clearAllAssignments();
    ElMessage.success(`已应用${template}模式的发车时间配置`);
  }
};

const focusRouteSelector = () => {
  // 打开线路选择面板
  openRouteSelector();
};

// 线路选择器相关方法
const openRouteSelector = () => {
  routeSelectorVisible.value = true;
  routeSearchText.value = '';
};

const closeRouteSelector = () => {
  routeSelectorVisible.value = false;
};

const selectRoute = (route: BusRoute) => {
  selectedRouteId.value = route.routeId;
  clearAllAssignments();
  closeRouteSelector();
  ElMessage.success(`已选择线路：${route.routeName}`);
};

// 初始化数据
const initializeData = () => {
  // 模拟线路数据
  routes.value = [
    {
      routeId: 1,
      routeName: '1路公交',
      routeCode: 'BUS-001',
      distance: 15.2,
      stationCount: 28,
      description: '火车站 - 市政府 - 商业中心',
      vehicleIds: [1, 2, 3, 4],
      timePoints: [
        { time: '06:00', type: 'peak' },
        { time: '06:30', type: 'peak' },
        { time: '07:00', type: 'peak' },
        { time: '07:30', type: 'peak' },
        { time: '08:00', type: 'normal' },
        { time: '09:00', type: 'normal' },
        { time: '10:00', type: 'normal' },
        { time: '17:00', type: 'peak' },
        { time: '17:30', type: 'peak' },
        { time: '18:00', type: 'peak' },
        { time: '18:30', type: 'peak' },
        { time: '19:00', type: 'normal' },
        { time: '20:00', type: 'normal' },
        { time: '21:00', type: 'normal' },
        { time: '22:00', type: 'normal' }
      ]
    },
    {
      routeId: 2,
      routeName: '2路公交',
      routeCode: 'BUS-002',
      distance: 12.8,
      stationCount: 22,
      description: '体育场 - 大学城 - 科技园',
      vehicleIds: [5, 6, 7],
      timePoints: [
        { time: '06:30', type: 'normal' },
        { time: '07:00', type: 'peak' },
        { time: '07:30', type: 'peak' },
        { time: '08:00', type: 'peak' },
        { time: '09:00', type: 'normal' },
        { time: '10:00', type: 'normal' },
        { time: '11:00', type: 'normal' },
        { time: '17:00', type: 'peak' },
        { time: '18:00', type: 'peak' },
        { time: '19:00', type: 'normal' },
        { time: '20:00', type: 'normal' },
        { time: '21:30', type: 'normal' }
      ]
    },
    {
      routeId: 3,
      routeName: '3路公交',
      routeCode: 'BUS-003',
      distance: 18.5,
      stationCount: 35,
      description: '北站 - 市中心 - 南站',
      vehicleIds: [8, 9, 10, 11, 12],
      timePoints: [
        { time: '05:30', type: 'normal' },
        { time: '06:00', type: 'peak' },
        { time: '06:20', type: 'peak' },
        { time: '06:40', type: 'peak' },
        { time: '07:00', type: 'peak' },
        { time: '07:20', type: 'peak' },
        { time: '07:40', type: 'peak' },
        { time: '08:00', type: 'peak' },
        { time: '08:30', type: 'normal' },
        { time: '09:00', type: 'normal' },
        { time: '09:30', type: 'normal' },
        { time: '10:00', type: 'normal' },
        { time: '16:30', type: 'peak' },
        { time: '17:00', type: 'peak' },
        { time: '17:20', type: 'peak' },
        { time: '17:40', type: 'peak' },
        { time: '18:00', type: 'peak' },
        { time: '18:20', type: 'peak' },
        { time: '18:40', type: 'peak' },
        { time: '19:00', type: 'normal' },
        { time: '20:00', type: 'normal' },
        { time: '21:00', type: 'normal' },
        { time: '22:30', type: 'normal' }
      ]
    }
  ];

  // 模拟车辆数据
  vehicles.value = [
    { vehicleId: 1, plateNumber: '京A12345', vehicleNumber: 'V001', vehicleType: 'bus', seatCount: 35, status: '1', routeId: 1 },
    { vehicleId: 2, plateNumber: '京A12346', vehicleNumber: 'V002', vehicleType: 'electric_bus', seatCount: 30, status: '1', routeId: 1 },
    { vehicleId: 3, plateNumber: '京A12347', vehicleNumber: 'V003', vehicleType: 'bus', seatCount: 40, status: '1', routeId: 1 },
    { vehicleId: 4, plateNumber: '京A12348', vehicleNumber: 'V004', vehicleType: 'hybrid_bus', seatCount: 32, status: '2', routeId: 1 },
    { vehicleId: 5, plateNumber: '京A12349', vehicleNumber: 'V005', vehicleType: 'electric_bus', seatCount: 28, status: '1', routeId: 2 },
    { vehicleId: 6, plateNumber: '京A12350', vehicleNumber: 'V006', vehicleType: 'bus', seatCount: 35, status: '1', routeId: 2 },
    { vehicleId: 7, plateNumber: '京A12351', vehicleNumber: 'V007', vehicleType: 'electric_bus', seatCount: 30, status: '1', routeId: 2 },
    { vehicleId: 8, plateNumber: '京A12352', vehicleNumber: 'V008', vehicleType: 'bus', seatCount: 45, status: '1', routeId: 3 },
    { vehicleId: 9, plateNumber: '京A12353', vehicleNumber: 'V009', vehicleType: 'bus', seatCount: 45, status: '1', routeId: 3 },
    { vehicleId: 10, plateNumber: '京A12354', vehicleNumber: 'V010', vehicleType: 'electric_bus', seatCount: 35, status: '1', routeId: 3 },
    { vehicleId: 11, plateNumber: '京A12355', vehicleNumber: 'V011', vehicleType: 'hybrid_bus', seatCount: 38, status: '1', routeId: 3 },
    { vehicleId: 12, plateNumber: '京A12356', vehicleNumber: 'V012', vehicleType: 'bus', seatCount: 45, status: '3', routeId: 3 }
  ];
};

// 生命周期
onMounted(() => {
  initializeData();
  console.log('Component mounted, routes:', routes.value);
  console.log('vehicles:', vehicles.value);
});
</script>

<style scoped>
.route-schedule-container {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  bottom: 0;
  color: #e5e7eb;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 1;
  height: calc(100vh - 60px);
}

/* ===== 多层动态背景系统 ===== */
.background-system {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

/* 基础渐变层 - 深色科技感 */
.bg-gradient-base {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #0a1629 0%, #1e293b 25%, #334155 75%, #0f172a 100%);
}

/* ===== 顶部状态栏样式 ===== */
.top-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  color: #e5e7eb;
  z-index: 10;
  position: relative;
  flex-shrink: 0;
  min-height: 60px;
}

.breadcrumb-nav .el-breadcrumb {
  font-weight: 500;
}

.breadcrumb-nav .el-breadcrumb__item {
  color: #cbd5e1;
}

.breadcrumb-nav .el-breadcrumb__item.is-link {
  color: #93c5fd;
}

/* ===== 顶部线路选择器区域 ===== */
.route-selector-area {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 0 0 auto;
}

.selector-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  font-weight: 600;
  color: #f8fafc;
  white-space: nowrap;
}

.selector-label .el-icon {
  font-size: 18px;
  color: #3b82f6;
}

.selected-route-display {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selected-route-tag {
  font-weight: 600;
}

.change-route-btn {
  color: #93c5fd;
}

.change-route-btn:hover {
  color: #dbeafe;
}

.select-route-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.select-route-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* ===== 线路选择面板 ===== */
.route-selector-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.route-selector-panel.visible {
  opacity: 1;
  visibility: visible;
}

.panel-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.panel-content {
  position: relative;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: 600;
  color: #f8fafc;
}

.header-left .el-icon {
  font-size: 20px;
  color: #93c5fd;
}

.close-btn {
  color: #94a3b8;
  font-size: 18px;
}

.close-btn:hover {
  color: #f8fafc;
}

.panel-body {
  flex: 1;
  padding: 20px 24px;
  overflow-y: auto;
  min-height: 0;
}

.search-box {
  margin-bottom: 20px;
}

.search-box .el-input {
  --el-input-bg-color: rgba(255, 255, 255, 0.1);
  --el-input-border-color: rgba(59, 130, 246, 0.3);
  --el-input-focus-border-color: rgba(59, 130, 246, 0.6);
  --el-input-text-color: #f8fafc;
}

.routes-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.route-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.route-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.route-item.selected {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.6);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.route-info {
  flex: 1;
}

.route-name {
  font-size: 16px;
  font-weight: 600;
  color: #f8fafc;
  margin-bottom: 4px;
}

.route-details {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
  color: #cbd5e1;
}

.route-code {
  background: rgba(59, 130, 246, 0.3);
  color: #93c5fd;
  padding: 2px 8px;
  border-radius: 6px;
  font-weight: 500;
}

.route-stats {
  color: #94a3b8;
}

.route-actions {
  flex-shrink: 0;
  width: 24px;
  display: flex;
  justify-content: center;
}

.selected-icon {
  color: #67c23a;
  font-size: 20px;
}

.realtime-stats {
  display: flex;
  gap: 24px;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item.success {
  background: rgba(103, 194, 58, 0.2);
  border-color: rgba(103, 194, 58, 0.3);
}

.stat-item.warning {
  background: rgba(230, 162, 60, 0.2);
  border-color: rgba(230, 162, 60, 0.3);
}

.stat-item.info {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

.stat-label {
  font-size: 12px;
  color: #cbd5e1;
  font-weight: 500;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #f8fafc;
}

/* ===== 确保所有文字颜色足够明亮 ===== */
.left-route-panel,
.right-vehicle-panel,
.center-timeline-area {
  color: #f8fafc !important;
}

.left-route-panel *,
.right-vehicle-panel *,
.center-timeline-area * {
  color: inherit;
}

/* 特殊元素文字颜色强化 */
.vehicle-list-header,
.summary-label,
.summary-value {
  color: #f8fafc !important;
}

/* ===== 三栏式主要布局 ===== */
.three-column-layout {
  display: grid;
  grid-template-columns: 280px 1fr 320px;
  gap: 12px;
  flex: 1;
  min-height: 0;
  padding: 12px;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

/* ===== 左侧线路信息面板 ===== */
.left-route-panel {
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;
  min-height: 0;
  max-height: 100%;
}

.route-selection-card,
.route-stats-card,
.quick-actions-card,
.template-card {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 10px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 15px;
  font-weight: 600;
  color: #f8fafc !important; /* 确保文字颜色足够明亮 */
}

.card-header .el-icon {
  font-size: 18px;
  color: #93c5fd;
}

.route-selector {
  margin-top: 8px;
}

.route-basic-info {
  margin-bottom: 16px;
}

.route-title {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 8px;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
  overflow: hidden;
}

.route-name {
  font-size: 16px;
  font-weight: 600;
  color: #f8fafc !important; /* 确保线路名称颜色明亮 */
  flex: 1;
  min-width: 0;
}

.route-details {
  font-size: 13px;
  color: #e2e8f0 !important; /* 提高线路详情文字可读性 */
}

.stats-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(8px);
  min-width: 0;
}

.stat-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.stat-icon.vehicle {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.stat-icon.time {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.stat-icon.assigned {
  background: linear-gradient(135deg, #67c23a, #52b030);
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 16px;
  font-weight: 600;
  color: #f8fafc !important; /* 确保统计数字颜色明亮 */
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #e2e8f0 !important; /* 提高标签文字可读性 */
  margin-top: 2px;
}

.action-buttons-vertical {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: stretch; /* 确保按钮对齐 */
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
  text-align: center; /* 确保文字居中 */
}

.template-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.template-btn {
  height: 36px;
  border-radius: 6px;
}

/* ===== 中央时间配置主区 ===== */
.center-timeline-area {
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 10px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  min-height: 0;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  flex-shrink: 0;
  background: transparent !important; /* 确保背景透明 */
}

.timeline-header h3 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #f8fafc !important; /* 确保标题颜色明亮 */
  background: transparent !important;
}

.timeline-header .el-icon {
  font-size: 20px;
  color: #93c5fd;
}

.timeline-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.enhanced-timeline-visual {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.enhanced-timeline-track {
  display: grid;
  grid-template-columns: repeat(8, 150px);
  grid-auto-rows: 130px;
  gap: 16px;
  justify-content: center;
  padding: 16px;
  position: relative;
  z-index: 2;
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.enhanced-timeline-node {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  width: 150px;
  height: 130px;
  padding: 8px;
  border-radius: 10px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border: 2px solid rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 3;
}

.enhanced-timeline-node:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #60a5fa;
}

.enhanced-timeline-node.highlight {
  border-color: #3b82f6;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(147, 197, 253, 0.1) 100%);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
}

.enhanced-timeline-node.peak-time {
  border-color: #e6a23c;
  background: linear-gradient(135deg, #fef3e2 0%, #fde68a 100%);
  color: #92400e;
}

.enhanced-timeline-node.has-vehicle {
  border-color: #67c23a;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.enhanced-timeline-node.conflict-warning {
  border-color: #f56c6c;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.peak-indicator {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #e6a23c;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  z-index: 4;
}

.assigned-vehicle-enhanced {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  background: #67c23a;
  color: white;
  border-radius: 20px;
  position: relative;
  width: 100%;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
  border: 2px solid #ffffff;
  z-index: 4;
}

.vehicle-icon-enhanced {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.vehicle-info-enhanced {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.vehicle-plate {
  font-size: 12px;
  font-weight: 600;
  line-height: 1;
}

.vehicle-trip {
  font-size: 10px;
  opacity: 0.9;
  line-height: 1;
}

.remove-btn-enhanced {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  min-height: 16px;
  padding: 0;
  z-index: 5;
}

.empty-slot-enhanced {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: #cbd5e1;
  padding: 8px;
  text-align: center;
}

.empty-timeline {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  min-height: 300px;
}

/* ===== 右侧车辆管理面板 ===== */
.right-vehicle-panel {
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;
  min-height: 0;
  max-height: 100%;
}

.vehicle-filter-card,
.vehicle-list-card,
.assignment-summary-card {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 10px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.filter-controls {
  margin-bottom: 12px;
}

.vehicle-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #f8fafc;
}

.enhanced-vehicle-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 280px;
  overflow-y: auto;
}

.enhanced-vehicle-item {
  padding: 12px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  cursor: grab;
  transition: all 0.3s ease;
}

.enhanced-vehicle-item:hover {
  border-color: #60a5fa;
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.enhanced-vehicle-item.assigned {
  border-color: #67c23a;
  background: rgba(103, 194, 58, 0.2);
}

.enhanced-vehicle-item.unavailable {
  opacity: 0.5;
  cursor: not-allowed;
}

.enhanced-vehicle-item.selected {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.2);
}

.vehicle-main-info {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.vehicle-details {
  flex: 1;
}

.vehicle-plate {
  font-size: 14px;
  font-weight: 600;
  color: #f8fafc;
  margin-bottom: 4px;
}

.vehicle-meta {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #cbd5e1;
}

.vehicle-status {
  flex-shrink: 0;
}

.assignment-details {
  padding-top: 12px;
  border-top: 1px solid rgba(59, 130, 246, 0.2);
}

.assignment-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.assignment-count {
  font-size: 12px;
  color: #67c23a;
  font-weight: 500;
}

.assignment-times {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.time-tag {
  font-size: 10px;
  height: 20px;
  line-height: 18px;
}

.more-times {
  font-size: 10px;
  color: #cbd5e1;
  padding: 2px 6px;
}

.no-assignment {
  padding-top: 12px;
  border-top: 1px solid rgba(59, 130, 246, 0.2);
}

.drag-hint {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #cbd5e1;
  font-style: italic;
}

.empty-vehicles {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 150px;
}

.summary-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-label {
  font-size: 12px;
  color: #cbd5e1;
  font-weight: 500;
}

.summary-value {
  font-size: 20px;
  font-weight: 600;
  color: #f8fafc;
}

/* 内容区域需要添加相对定位和z-index */
.route-and-vehicles-card,
.main-content,
.no-route-selected {
  position: relative;
  z-index: 1;
}

/* 简单SVG时间线样式 */
.timeline-visual {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(25, 35, 50, 0.85) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.timeline-container {
  width: 100%;
  position: relative;
  min-height: 400px;
}

/* SVG连接线样式 - 优化层级关系 */
.timeline-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5; /* {{ AURA: Modify - 大幅提升连接线层级，确保在所有节点元素之上 }} */
}

/* {{ AURA: Modify - 简化的连接路径效果 }} */
.connection-path {
  transition: all 0.3s ease;
  opacity: 0.8;
}

.connection-path:hover {
  opacity: 1;
}

/* {{ AURA: Modify - 节点悬停时绿色连接线高亮效果 }} */
.timeline-node:hover ~ .timeline-connections .connection-path,
.timeline-container:hover .connection-path {
  stroke-width: 4;
  filter: url(#connectionShadow) drop-shadow(0 0 6px rgba(103, 194, 58, 0.6));
}

.timeline-track {
  display: grid;
  grid-template-columns: repeat(6, 140px);
  grid-auto-rows: 120px;
  gap: 20px;
  position: relative;
  z-index: 1; /* {{ AURA: Modify - 确保节点在连接线下方，但保持交互性 }} */
  justify-content: center;
  padding: 20px;
}

.timeline-node {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  width: 140px;
  height: 120px;
  padding: 8px;
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border: 2px solid rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.timeline-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.timeline-node.highlight {
  border-color: #60a5fa;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(147, 197, 253, 0.1) 100%);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
}

.timeline-node.peak-time {
  border-color: #3498db;
  background: linear-gradient(135deg, #e8f4fd 0%, #d6eafc 100%); /* {{ AURA: Modify - 使用简约的蓝色渐变，大气专业 }} */
  color: #2c3e50; /* {{ AURA: Modify - 深蓝色文字，简约专业 }} */
  position: relative;
  z-index: 1; /* 确保层级低于连接线 */
  box-shadow: 0 2px 12px rgba(52, 152, 219, 0.15); /* {{ AURA: Add - 添加蓝色阴影提升质感 }} */
}

/* {{ AURA: Add - 高峰期节点内的按钮样式优化 }} */
.timeline-node.peak-time .auto-assign-btn {
  background: rgba(52, 152, 219, 0.1) !important;
  border-color: rgba(52, 152, 219, 0.3) !important;
  color: #3498db !important;
}

.timeline-node.peak-time .auto-assign-btn:hover {
  background: rgba(52, 152, 219, 0.2) !important;
  border-color: rgba(52, 152, 219, 0.5) !important;
  color: #2980b9 !important;
}

.timeline-node.has-vehicle {
  border-color: #67c23a;
  background: #ffffff !important; /* {{ AURA: Modify - 使用纯白色背景，移除透明效果 }} */
}

.time-info {
  text-align: center;
  padding: 4px 0;
  background: #f8f9fa;
  border-radius: 4px;
  position: relative;
}

.time-text {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

.time-sequence {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #3b82f6;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 6; /* {{ AURA: Modify - 时间序号层级最高，确保始终在最顶层 }} */
}

.vehicle-display {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
  margin: 6px 0;
}

.assigned-vehicle-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #10b981 !important;
  color: white;
  border-radius: 16px;
  position: relative;
  width: 100%;
  justify-content: center;
  /* {{ AURA: Remove - 移除半透明效果，防止线条透过 }} */
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
  border: 2px solid #ffffff !important; /* {{ AURA: Modify - 使用完全不透明的白色边框 }} */
  z-index: 3; /* 确保车辆信息在连接线之上 */
}

.vehicle-icon-compact {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.vehicle-plate-compact {
  font-size: 11px;
  font-weight: 600;
  flex: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remove-btn-compact {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  min-height: 12px;
  padding: 0;
}

.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  color: #c0c4cc;
  padding: 4px;
}

.empty-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-text {
  font-size: 10px;
  text-align: center;
}

.quick-actions {
  display: flex;
  justify-content: center;
  margin-top: 4px;
}

.auto-assign-btn {
  font-size: 10px;
  padding: 2px 8px;
  height: 20px;
}

.action-buttons {
  margin-top: 12px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(25, 35, 50, 0.85) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  text-align: center;
}

/* {{ AURA: Modify - 合并后的线路和车辆卡片样式 }} */
.route-and-vehicles-card {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(20, 28, 46, 0.95) 50%, rgba(15, 23, 42, 0.98) 100%);
  backdrop-filter: blur(20px) saturate(120%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  padding: 24px;
  margin: 16px;
  box-shadow:
    4px 0 20px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(59, 130, 246, 0.05) inset;
}

/* {{ AURA: Add - 线路选择器区域样式 }} */
.route-selector-section {
  margin-bottom: 20px;
}

.selector-header {
  display: flex;
  align-items: center; /* {{ AURA: Modify - 改为居中对齐，与单行信息匹配 }} */
  justify-content: space-between;
  gap: 24px;
  margin-bottom: 16px;
}

.selector-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 0 0 auto;
}

.selector-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  min-width: 140px;
}

/* {{ AURA: Modify - 单行内联线路信息样式 }} */
.route-info-inline {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
  background: #f8fafc;
  border-radius: 6px;
  padding: 0 16px;
  border-left: 4px solid #667eea;
  height: 40px; /* 与el-select默认高度保持一致 */
  overflow: hidden;
}

/* {{ AURA: Modify - 内联元素样式 }} */
.route-info-inline .route-name {
  font-size: 15px;
  font-weight: 600;
  color: #1a202c;
  white-space: nowrap;
  flex-shrink: 0;
}

.route-info-inline .route-code {
  background: #667eea;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.route-info-inline .route-stats {
  color: #64748b;
  font-size: 13px;
  white-space: nowrap;
  flex-shrink: 0;
}

.route-info-inline .el-tag {
  flex-shrink: 0;
  font-size: 11px;
  height: 20px;
  line-height: 18px;
}

/* {{ AURA: Delete - 移除不再使用的旧样式 }} */

.main-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin: 0 16px 16px 16px;
  padding: 16px 0;
  overflow: auto;
  height: calc(100vh - 200px);
}

/* {{ AURA: Add - 紧凑型车辆展示区域 }} */
.vehicles-section-compact {
  margin-top: 0;
}

.vehicles-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.vehicles-header h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.vehicles-grid-compact {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 10px;
  margin-bottom: 12px;
}

.vehicle-item-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 10px;
  border: 1.5px solid #e5e7eb;
  border-radius: 8px;
  cursor: grab;
  transition: all 0.3s ease;
  background: #fafafa;
  min-height: 44px;
}

.vehicle-item-compact:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
  transform: translateY(-1px);
  background: white;
}

.vehicle-item-compact.assigned {
  border-color: #10b981;
  background: #f0fdf4;
}

.vehicle-item-compact.unavailable {
  opacity: 0.5;
  cursor: not-allowed;
}

.vehicle-icon-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #6366f1;
  color: white;
  font-size: 12px;
  flex-shrink: 0;
}

.vehicle-info-compact {
  flex: 1;
  min-width: 0;
}

.plate-number-small {
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 2px;
}

.assignment-info-small {
  font-size: 10px;
  color: #10b981;
  font-weight: 500;
}

.empty-vehicles-compact {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
}

.schedule-section {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.85) 0%, rgba(25, 35, 50, 0.8) 100%);
  border: 1px solid rgba(59, 130, 246, 0.25);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(15px) saturate(120%);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(59, 130, 246, 0.05) inset;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f2f5;
}

.section-header h3 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #1f2937;
}

.vehicle-filters {
  display: flex;
  gap: 12px;
}

.vehicles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 12px;
}

.vehicle-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 2px solid #e4e7ed;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.3s ease;
  position: relative;
  background: white;
}

.vehicle-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.vehicle-item.assigned {
  border-color: #67c23a;
  background: linear-gradient(135deg, #f0f9ff, #ecf5ff);
}

.vehicle-item.unavailable {
  opacity: 0.6;
  cursor: not-allowed;
}

.vehicle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #10b981;
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.vehicle-info {
  flex: 1;
}

.plate-number {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 2px;
}

.vehicle-type {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.seat-info {
  font-size: 12px;
  color: #606266;
}

.assignment-info {
  font-size: 10px;
  color: #67c23a;
  font-weight: 600;
  margin-top: 1px;
}

.assigned-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #67c23a;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.timeline-grid {
  border: 1px solid #e8eaed;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.timeline-header {
  display: grid;
  grid-template-columns: 150px 1fr 80px;
  background: #f8fafb;
  font-weight: 500;
  color: #1f2937;
}

.timeline-header > div {
  padding: 18px 16px;
  border-right: 1px solid #e8eaed;
}

.timeline-header > div:last-child {
  border-right: none;
}

.timeline-row {
  display: grid;
  grid-template-columns: 150px 1fr 80px;
  border-top: 1px solid #f0f2f5;
  transition: background-color 0.2s ease;
}

.timeline-row.highlight {
  background: rgba(59, 130, 246, 0.05);
}

.timeline-row > div {
  padding: 18px 16px;
  border-right: 1px solid #f0f2f5;
  display: flex;
  align-items: center;
}

.timeline-row > div:last-child {
  border-right: none;
  justify-content: center;
}

.time-cell {
  flex-direction: column;
  align-items: flex-start;
}

.time-display {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.time-type {
  font-size: 12px;
  color: #909399;
}

.assignment-cell {
  min-height: 60px;
}

.assigned-vehicle .vehicle-card {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #10b981;
  color: white;
  border-radius: 20px;
  position: relative;
}

.vehicle-icon-small {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vehicle-details .plate {
  font-weight: 600;
  font-size: 12px;
}

.vehicle-details .type {
  font-size: 10px;
  opacity: 0.8;
}

.remove-btn {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  min-height: 16px;
}

.drop-zone {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  color: #c0c4cc;
  font-size: 12px;
  text-align: center;
  transition: all 0.3s ease;
  min-height: 60px;
}

.timeline-row.highlight .drop-zone {
  border-color: #409eff;
  color: #409eff;
  background: rgba(64, 158, 255, 0.1);
}

.empty-vehicles,
.empty-schedule {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* ===== 空状态容器 ===== */
.empty-state-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  position: relative;
  z-index: 1;
}

.empty-content {
  text-align: center;
  max-width: 600px;
}

.empty-icon-wrapper {
  margin-bottom: 24px;
}

.empty-icon {
  font-size: 80px;
  color: #93c5fd;
  opacity: 0.8;
}

.empty-title {
  font-size: 28px;
  font-weight: 600;
  color: #f8fafc;
  margin: 0 0 16px 0;
}

.empty-description {
  font-size: 16px;
  color: #cbd5e1;
  line-height: 1.6;
  margin: 0 0 32px 0;
}

.empty-actions {
  margin-bottom: 40px;
}

.primary-action {
  font-size: 16px;
  padding: 12px 32px;
  height: auto;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.primary-action:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  transform: translateY(-2px);
}

.empty-features {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #cbd5e1;
  font-size: 14px;
}

.feature-icon {
  font-size: 32px;
  color: #93c5fd;
  margin-bottom: 4px;
}

@media (max-width: 768px) {
  .empty-features {
    gap: 20px;
  }

  .feature-item {
    font-size: 12px;
  }

  .feature-icon {
    font-size: 24px;
  }

  .empty-title {
    font-size: 24px;
  }

  .empty-description {
    font-size: 14px;
  }
}

.time-config-content {
  padding: 16px;
}

.config-header {
  margin-bottom: 24px;
}

.time-preview {
  max-height: 120px;
  overflow-y: auto;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.time-tag {
  margin: 2px 4px 2px 0;
}

.preview-empty {
  text-align: center;
  color: #c0c4cc;
  font-size: 14px;
  padding: 20px;
}

.drag-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  pointer-events: none;
}

.drag-hint {
  background: #409eff;
  color: white;
  padding: 16px 24px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 响应式设计 */
/* {{ AURA: Modify - 响应式布局优化 }} */
@media (max-width: 1200px) {
  .vehicles-grid-compact {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    gap: 8px;
  }
}

/* 图形化时间线样式 */
.timeline-visual {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 移除布局控制器样式 */

.timeline-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
  padding: 0; /* 移除额外的padding，让SVG和Grid对齐 */
  position: relative;
  min-height: 200px;
}

/* SVG连接线样式 */
.timeline-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0; /* 确保连接线在最底层 */
}

/* 移除时间轴背景线 */

/* 移除蛇形布局样式 */

/* 移除蛇形节点样式 */

/* 时间车辆显示 */
.time-vehicle-display {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
}

.time-info {
  text-align: center;
  padding: 4px 0;
  background: #f8f9fa;
  border-radius: 4px;
}

/* 移除蛇形时间信息样式 */

.time-text {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

/* 移除蛇形时间文本样式 */

/* 移除原时间序号样式，使用新的绝对定位样式 */

.vehicle-display {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
}

.assigned-vehicle-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #67c23a !important; /* {{ AURA: Modify - 使用完全不透明的纯绿色背景 }} */
  color: white;
  border-radius: 16px;
  position: relative;
  width: 100%;
  justify-content: center;
  border: 2px solid #ffffff !important; /* {{ AURA: Add - 添加不透明白色边框 }} */
  z-index: 3 !important; /* {{ AURA: Add - 确保层级最高 }} */
}

.vehicle-icon-compact {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.vehicle-plate-compact {
  font-size: 11px;
  font-weight: 600;
  flex: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remove-btn-compact {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  min-height: 12px;
  padding: 0;
}

.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  color: #c0c4cc;
  padding: 4px;
}

.empty-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-text {
  font-size: 10px;
  text-align: center;
}

.quick-actions {
  display: flex;
  justify-content: center;
  margin-top: 4px;
}

.auto-assign-btn {
  font-size: 10px;
  padding: 2px 8px;
  height: 20px;
}

/* 时间线节点圆点 */
.timeline-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  background: #409eff;
  border: 3px solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 2; /* 圆点在最顶层 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.timeline-dot.has-vehicle {
  background: #67c23a;
  width: 16px;
  height: 16px;
  border-width: 4px;
}

/* 移除蛇形圆点样式 */

/* 移除旧的连接器样式 */

/* 移除复杂的CSS伪元素连接线系统，使用SVG替代 */

.timeline-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  background: #409eff;
  border: 3px solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.timeline-dot.has-vehicle {
  background: #67c23a;
  width: 16px;
  height: 16px;
  border-width: 4px;
}

.timeline-node:hover .timeline-dot {
  transform: translate(-50%, -50%) scale(1.2);
}

/* 优化时间序号显示 */
.time-sequence {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 3; /* 序号在所有元素之上 */
}

/* ===== 响应式设计 ===== */
@media (max-width: 1400px) {
  .three-column-layout {
    grid-template-columns: 260px 1fr 300px;
    gap: 10px;
  }

  .enhanced-timeline-track {
    grid-template-columns: repeat(6, 140px);
    gap: 14px;
  }

  .enhanced-timeline-node {
    width: 140px;
    height: 120px;
    padding: 6px;
  }
}

@media (max-width: 1200px) {
  .three-column-layout {
    grid-template-columns: 240px 1fr 280px;
    gap: 8px;
  }

  .enhanced-timeline-track {
    grid-template-columns: repeat(5, 130px);
    gap: 12px;
  }

  .enhanced-timeline-node {
    width: 130px;
    height: 110px;
    padding: 6px;
  }
}

@media (max-width: 1024px) {
  .three-column-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr;
    height: auto;
    min-height: calc(100vh - 180px);
  }

  .left-route-panel,
  .right-vehicle-panel {
    grid-column: 1;
    max-height: 250px;
  }

  .center-timeline-area {
    grid-column: 1;
    grid-row: 3;
    min-height: 350px;
  }

  .enhanced-timeline-track {
    grid-template-columns: repeat(4, 140px);
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .route-schedule-container {
    padding: 0;
  }

  .top-status-bar {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }

  .route-selector-area {
    width: 100%;
    justify-content: center;
  }

  .top-route-selector {
    min-width: 250px;
    max-width: 100%;
  }

  .realtime-stats {
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .realtime-stats {
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .three-column-layout {
    padding: 8px;
    gap: 8px;
  }

  .route-selection-card,
  .route-stats-card,
  .quick-actions-card,
  .template-card,
  .vehicle-filter-card,
  .vehicle-list-card,
  .assignment-summary-card,
  .center-timeline-area {
    padding: 16px;
    border-radius: 8px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .template-buttons {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .enhanced-timeline-track {
    grid-template-columns: repeat(3, 140px);
    gap: 12px;
    padding: 16px;
  }

  .enhanced-timeline-node {
    width: 140px;
    height: 110px;
    padding: 6px;
  }

  .timeline-header h3 {
    font-size: 16px;
  }

  .enhanced-vehicle-list {
    max-height: 250px;
  }

  .enhanced-vehicle-item {
    padding: 12px;
  }

  /* {{ AURA: Modify - 移动端合并卡片优化 }} */
  .route-and-vehicles-card {
    padding: 16px;
    margin: 8px;
    border-radius: 6px;
  }

  .selector-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .selector-left {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .selector-label {
    min-width: auto;
    justify-content: center;
  }

  /* {{ AURA: Modify - 移动端内联信息样式 }} */
  .route-info-inline {
    height: auto;
    min-height: 40px;
    flex-wrap: wrap;
    padding: 8px 12px;
    gap: 8px;
  }

  .route-info-inline .route-name,
  .route-info-inline .route-code,
  .route-info-inline .route-stats {
    font-size: 12px;
  }

  .route-info-inline .el-tag {
    font-size: 10px;
    height: 18px;
    line-height: 16px;
  }

  .vehicles-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .vehicles-grid-compact {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
  }

  .vehicle-item-compact {
    padding: 6px 8px;
    min-height: 40px;
  }

  .plate-number-small {
    font-size: 12px;
  }

  .timeline-grid {
    overflow-x: auto;
  }

  .timeline-header,
  .timeline-row {
    min-width: 500px;
  }

  .timeline-track {
    justify-content: center;
    gap: 12px;
    grid-template-columns: repeat(4, 140px);
  }

  .timeline-node {
    min-width: 120px;
    max-width: 140px;
    padding: 6px;
  }

  .time-text {
    font-size: 12px;
  }

  .vehicle-plate-compact {
    font-size: 10px;
  }

  /* {{ AURA: Modify - 移动端网格和连接线优化 }} */
  .timeline-track {
    grid-template-columns: repeat(4, 120px);
    gap: 15px;
  }

  .timeline-node {
    min-width: 110px;
    max-width: 120px;
    padding: 6px;
    height: 110px;
  }

  .time-text {
    font-size: 12px;
  }

  .vehicle-plate-compact {
    font-size: 10px;
    padding: 3px 6px;
  }

  .time-sequence {
    width: 18px;
    height: 18px;
    font-size: 9px;
    top: -6px;
    right: -6px;
  }

  /* 移动端连接线优化 */
  .timeline-connections {
    z-index: 2;
  }

  .connection-path {
    stroke-width: 2;
    opacity: 0.9;
  }

  .timeline-container:hover .connection-path {
    stroke-width: 3;
    opacity: 1;
  }
}
</style>
