<template>
  <div class='route-deviation-analysis'>
    <el-row :gutter='20'>
      <!-- 左侧线路和车辆树 -->
      <el-col :lg='4' :xs='24'>
        <div class='left-panel'>
          <div class='panel-header'>
            <div class='header-left'>
              <el-icon>
                <Connection />
              </el-icon>
              <span>线路车辆</span>
            </div>
          </div>
          <div class='panel-content'>
            <div class='search-box'>
              <el-input v-model='deptName' placeholder='请输入线路或车辆' prefix-icon='Search' clearable />
            </div>

            <el-tree
              ref='deptTreeRef'
              class='dept-tree'
              node-key='id'
              :data='deptOptions'
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node='false'
              :filter-node-method='filterNode'
              highlight-current
              default-expand-all
              @node-click='handleNodeClick'
            >
              <template #default="{ node, data }">
                <span class="tree-node">
                  <el-icon v-if="data.type === 'route'" class="route-icon">
                    <Connection />
                  </el-icon>
                  <el-icon v-else-if="data.type === 'vehicle'" class="vehicle-icon">
                    <Van />
                  </el-icon>
                  <span class="node-label" :class="data.type">{{ data.label }}</span>
                  <el-tag
                    v-if="data.type === 'vehicle' && data.deviationLevel"
                    :type="getDeviationLevelType(data.deviationLevel)"
                    size="small"
                    style="margin-left: 8px;"
                  >
                    {{ getDeviationLevelText(data.deviationLevel) }}
                  </el-tag>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
      </el-col>

      <el-col :lg='20' :xs='24'>
        <div class='right-content'>
          <!-- 筛选条件区域 -->
          <div class='filter-section'>
            <el-card class='filter-card'>
              <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
                <el-form-item label="时间范围" prop="dateRange">
                  <el-date-picker
                    v-model="queryParams.dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="YYYY-MM-DD"
                    style="width: 280px"
                    @change="handleDateRangeChange"
                  />
                </el-form-item>
                <el-form-item label="线路编号" prop="routeNumber">
                  <el-input
                    v-model="queryParams.routeNumber"
                    placeholder="请输入线路编号"
                    clearable
                    style="width: 120px"
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="车牌号" prop="plateNumber">
                  <el-input
                    v-model="queryParams.plateNumber"
                    placeholder="请输入车牌号"
                    clearable
                    style="width: 160px"
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="偏移等级" prop="deviationLevel">
                  <el-select v-model="queryParams.deviationLevel" placeholder="请选择等级" clearable style="width: 120px">
                    <el-option label="全部" value="" />
                    <el-option label="轻微" value="light" />
                    <el-option label="中度" value="moderate" />
                    <el-option label="严重" value="severe" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
                  <el-button @click="resetQuery" icon="Refresh">重置</el-button>
                  <el-button type="success" plain @click="handleExport" icon="Download">导出</el-button>
                  <el-button type="primary" plain @click="handlePrint" icon="Printer">打印</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>

          <!-- Tab切换区域 -->
          <div class='tabs-section'>
            <!-- 时间范围显示 - 放在所有Tab外面 -->
            <div class="time-range-info" v-if="queryParams.dateRange && queryParams.dateRange.length === 2">
              <el-alert
                :title="`统计时间范围：${queryParams.dateRange[0]} 至 ${queryParams.dateRange[1]}`"
                type="info"
                :closable="false"
                show-icon
                style="margin-bottom: 16px;"
              />
            </div>

            <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
              <!-- 线路偏移报表 -->
              <el-tab-pane label="线路偏移报表" name="routeDeviationSummary">
                <div class="tab-content">
                  <!-- 统计卡片区域 -->
                  <div class='stats-section'>
                    <el-row :gutter='16'>
                      <el-col :span='6'>
                        <div class='stat-card total'>
                          <div class='stat-icon'>
                            <el-icon><Connection /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.totalRoutes }}</div>
                            <div class='stat-label'>总线路数</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card vehicles'>
                          <div class='stat-icon'>
                            <el-icon><Van /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.totalVehicles }}</div>
                            <div class='stat-label'>运营车辆</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card rate'>
                          <div class='stat-icon'>
                            <el-icon><TrendCharts /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.avgDeviationRate }}%</div>
                            <div class='stat-label'>平均偏移率</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card distance'>
                          <div class='stat-icon'>
                            <el-icon><Place /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.avgDeviationDistance }}m</div>
                            <div class='stat-label'>平均偏移距离</div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 图表区域 -->
                  <div class='charts-section'>
                    <el-row :gutter='16'>
                      <el-col :span='24'>
                        <div class='chart-card'>
                          <div class='chart-header'>
                            <h3>线路偏移统计图</h3>
                            <span v-if="queryParams.dateRange && queryParams.dateRange.length === 2" class="chart-subtitle">
                              {{ queryParams.dateRange[0] }} 至 {{ queryParams.dateRange[1] }}
                            </span>
                          </div>
                          <div ref="routeDeviationChartRef" class='chart-container'></div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 汇总表格 -->
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>线路偏移报表</h4>
                        <span v-if="queryParams.dateRange && queryParams.dateRange.length === 2" class="table-subtitle">
                          统计时间：{{ queryParams.dateRange[0] }} 至 {{ queryParams.dateRange[1] }}
                        </span>
                      </div>
                    </div>
                    <el-table :data="routeDeviationList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="线路编号" align="center" prop="routeNumber" width="100" />
                      <el-table-column label="线路名称" align="center" prop="routeName" min-width="150" />
                      <el-table-column label="运营车辆" align="center" prop="vehicleCount" width="100" />
                      <el-table-column label="检测次数" align="center" prop="detectionCount" width="100" />
                      <el-table-column label="偏移次数" align="center" prop="deviationCount" width="100">
                        <template #default="scope">
                          <span class="deviation-count">{{ scope.row.deviationCount }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="偏移率" align="center" prop="deviationRate" width="100">
                        <template #default="scope">
                          <el-tag :type="getDeviationRateType(scope.row.deviationRate)" size="small">
                            {{ scope.row.deviationRate }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="平均偏移距离" align="center" prop="avgDeviationDistance" width="120">
                        <template #default="scope">
                          <span class="distance-value">{{ scope.row.avgDeviationDistance }}m</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="最大偏移距离" align="center" prop="maxDeviationDistance" width="120">
                        <template #default="scope">
                          <span class="max-distance">{{ scope.row.maxDeviationDistance }}m</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="routeDeviationTotal > 0"
                      :total="routeDeviationTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getRouteDeviationList"
                    />
                  </div>
                </div>
              </el-tab-pane>

              <!-- 线路偏移日报表 -->
              <el-tab-pane label="线路偏移日报表" name="routeDeviationDaily">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>线路偏移日报表</h4>
                        <span v-if="queryParams.dateRange && queryParams.dateRange.length === 2" class="table-subtitle">
                          统计时间：{{ queryParams.dateRange[0] }} 至 {{ queryParams.dateRange[1] }}
                        </span>
                      </div>
                    </div>
                    <el-table :data="routeDeviationDailyList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="统计日期" align="center" prop="statisticDate" width="120" />
                      <el-table-column label="线路编号" align="center" prop="routeNumber" width="100" />
                      <el-table-column label="线路名称" align="center" prop="routeName" min-width="120" />
                      <el-table-column label="当日车次" align="center" prop="dailyTrips" width="100" />
                      <el-table-column label="检测次数" align="center" prop="detectionCount" width="100" />
                      <el-table-column label="偏移次数" align="center" prop="deviationCount" width="100">
                        <template #default="scope">
                          <span class="deviation-count">{{ scope.row.deviationCount }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="偏移率" align="center" prop="deviationRate" width="100">
                        <template #default="scope">
                          <el-tag :type="getDeviationRateType(scope.row.deviationRate)" size="small">
                            {{ scope.row.deviationRate }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="平均偏移距离" align="center" prop="avgDeviationDistance" width="120">
                        <template #default="scope">
                          <span class="distance-value">{{ scope.row.avgDeviationDistance }}m</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="天气状况" align="center" prop="weather" width="100">
                        <template #default="scope">
                          <el-tag :type="getWeatherType(scope.row.weather)" size="small">
                            {{ scope.row.weather }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="routeDeviationDailyTotal > 0"
                      :total="routeDeviationDailyTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getRouteDeviationDailyList"
                    />
                  </div>
                </div>
              </el-tab-pane>

              <!-- 车辆偏移报表 -->
              <el-tab-pane label="车辆偏移报表" name="vehicleDeviationSummary">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>车辆偏移报表</h4>
                        <span v-if="queryParams.dateRange && queryParams.dateRange.length === 2" class="table-subtitle">
                          统计时间：{{ queryParams.dateRange[0] }} 至 {{ queryParams.dateRange[1] }}
                        </span>
                      </div>
                    </div>
                    <el-table :data="vehicleDeviationList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="车牌号" align="center" prop="plateNumber" width="120" />
                      <el-table-column label="车辆编号" align="center" prop="vehicleNumber" width="100" />
                      <el-table-column label="所属线路" align="center" prop="routeName" min-width="120" />
                      <el-table-column label="驾驶员" align="center" prop="driverName" min-width="100" />
                      <el-table-column label="检测次数" align="center" prop="detectionCount" width="100" />
                      <el-table-column label="偏移次数" align="center" prop="deviationCount" width="100">
                        <template #default="scope">
                          <span class="deviation-count">{{ scope.row.deviationCount }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="偏移率" align="center" prop="deviationRate" width="100">
                        <template #default="scope">
                          <el-tag :type="getDeviationRateType(scope.row.deviationRate)" size="small">
                            {{ scope.row.deviationRate }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="平均偏移距离" align="center" prop="avgDeviationDistance" width="120">
                        <template #default="scope">
                          <span class="distance-value">{{ scope.row.avgDeviationDistance }}m</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="偏移等级" align="center" prop="deviationLevel" width="100">
                        <template #default="scope">
                          <el-tag :type="getDeviationLevelType(scope.row.deviationLevel)" size="small">
                            {{ getDeviationLevelText(scope.row.deviationLevel) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="vehicleDeviationTotal > 0"
                      :total="vehicleDeviationTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getVehicleDeviationList"
                    />
                  </div>
                </div>
              </el-tab-pane>

              <!-- 车辆偏移日报表 -->
              <el-tab-pane label="车辆偏移日报表" name="vehicleDeviationDaily">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>车辆偏移日报表</h4>
                        <span v-if="queryParams.dateRange && queryParams.dateRange.length === 2" class="table-subtitle">
                          统计时间：{{ queryParams.dateRange[0] }} 至 {{ queryParams.dateRange[1] }}
                        </span>
                      </div>
                    </div>
                    <el-table :data="vehicleDeviationDailyList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="统计日期" align="center" prop="statisticDate" width="120" />
                      <el-table-column label="车牌号" align="center" prop="plateNumber" width="120" />
                      <el-table-column label="车辆编号" align="center" prop="vehicleNumber" width="100" />
                      <el-table-column label="所属线路" align="center" prop="routeName" min-width="120" />
                      <el-table-column label="驾驶员" align="center" prop="driverName" min-width="100" />
                      <el-table-column label="当日班次" align="center" prop="dailyShifts" width="100" />
                      <el-table-column label="偏移次数" align="center" prop="deviationCount" width="100">
                        <template #default="scope">
                          <span class="deviation-count">{{ scope.row.deviationCount }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="偏移率" align="center" prop="deviationRate" width="100">
                        <template #default="scope">
                          <el-tag :type="getDeviationRateType(scope.row.deviationRate)" size="small">
                            {{ scope.row.deviationRate }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="平均偏移距离" align="center" prop="avgDeviationDistance" width="120">
                        <template #default="scope">
                          <span class="distance-value">{{ scope.row.avgDeviationDistance }}m</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="vehicleDeviationDailyTotal > 0"
                      :total="vehicleDeviationDailyTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getVehicleDeviationDailyList"
                    />
                  </div>
                </div>
              </el-tab-pane>

              <!-- 车辆偏移明细 -->
              <el-tab-pane label="车辆偏移明细" name="vehicleDeviationDetails">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>车辆偏移明细表</h4>
                        <span v-if="queryParams.dateRange && queryParams.dateRange.length === 2" class="table-subtitle">
                          统计时间：{{ queryParams.dateRange[0] }} 至 {{ queryParams.dateRange[1] }}
                        </span>
                      </div>
                    </div>
                    <el-table :data="vehicleDeviationDetailsList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="偏移时间" align="center" prop="deviationTime" width="160" />
                      <el-table-column label="线路" align="center" prop="routeName" min-width="100" />
                      <el-table-column label="车牌号" align="center" prop="plateNumber" width="120" />
                      <el-table-column label="班次编号" align="center" prop="tripNumber" width="100" />
                      <el-table-column label="偏移位置" align="center" prop="deviationLocation" min-width="150" show-overflow-tooltip />
                      <el-table-column label="偏移距离" align="center" prop="deviationDistance" width="100">
                        <template #default="scope">
                          <span :class="getDeviationDistanceClass(scope.row.deviationDistance)">
                            {{ scope.row.deviationDistance }}m
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column label="偏移等级" align="center" prop="deviationLevel" width="100">
                        <template #default="scope">
                          <el-tag :type="getDeviationLevelType(scope.row.deviationLevel)" size="small">
                            {{ getDeviationLevelText(scope.row.deviationLevel) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="驾驶员" align="center" prop="driverName" min-width="100" />
                      <el-table-column label="处理状态" align="center" prop="handleStatus" width="100">
                        <template #default="scope">
                          <el-tag :type="getHandleStatusType(scope.row.handleStatus)" size="small">
                            {{ getHandleStatusText(scope.row.handleStatus) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="备注" align="center" prop="remark" min-width="120" show-overflow-tooltip />
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleDeviationDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="vehicleDeviationDetailsTotal > 0"
                      :total="vehicleDeviationDetailsTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getVehicleDeviationDetailsList"
                    />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 详情弹窗 -->
    <el-dialog :title="dialogTitle" v-model="showDetailDialog" width="800px" append-to-body>
      <div v-if="detailData" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item v-if="detailData.routeName" label="线路名称">{{ detailData.routeName }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.routeNumber" label="线路编号">{{ detailData.routeNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.plateNumber" label="车牌号">{{ detailData.plateNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.vehicleNumber" label="车辆编号">{{ detailData.vehicleNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.driverName" label="驾驶员">{{ detailData.driverName }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.statisticDate" label="统计日期">{{ detailData.statisticDate }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.detectionCount" label="检测次数">{{ detailData.detectionCount }}次</el-descriptions-item>
          <el-descriptions-item v-if="detailData.deviationCount" label="偏移次数">{{ detailData.deviationCount }}次</el-descriptions-item>
          <el-descriptions-item v-if="detailData.deviationRate" label="偏移率">
            <el-tag :type="getDeviationRateType(detailData.deviationRate)" size="small">
              {{ detailData.deviationRate }}%
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item v-if="detailData.avgDeviationDistance" label="平均偏移距离">{{ detailData.avgDeviationDistance }}m</el-descriptions-item>
          <el-descriptions-item v-if="detailData.maxDeviationDistance" label="最大偏移距离">{{ detailData.maxDeviationDistance }}m</el-descriptions-item>
          <el-descriptions-item v-if="detailData.deviationLevel" label="偏移等级">
            <el-tag :type="getDeviationLevelType(detailData.deviationLevel)" size="small">
              {{ getDeviationLevelText(detailData.deviationLevel) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item v-if="detailData.vehicleCount" label="运营车辆">{{ detailData.vehicleCount }}辆</el-descriptions-item>
          <el-descriptions-item v-if="detailData.dailyTrips" label="当日车次">{{ detailData.dailyTrips }}次</el-descriptions-item>
          <el-descriptions-item v-if="detailData.weather" label="天气状况">{{ detailData.weather }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.deviationLocation" label="偏移位置">{{ detailData.deviationLocation }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.handleStatus" label="处理状态">
            <el-tag :type="getHandleStatusType(detailData.handleStatus)" size="small">
              {{ getHandleStatusText(detailData.handleStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="备注" span="2">{{ detailData.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="RouteDeviationAnalysis">
import { ref, reactive, onMounted, nextTick, watchEffect, computed } from 'vue';
import { Connection, Van, TrendCharts, Place } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import Pagination from '@/components/Pagination/index.vue';

// 定义组件
defineOptions({
  components: {
    Pagination
  }
});

const loading = ref(true);
const showDetailDialog = ref(false);
const detailData = ref(null);
const activeTab = ref('routeDeviationSummary');

// 树形相关
const deptName = ref('');
const deptTreeRef = ref();

// 图表引用
const routeDeviationChartRef = ref(null);
let routeDeviationChart = null;

// 表单引用
const queryRef = ref();

// 分页数据
const routeDeviationTotal = ref(0);
const routeDeviationDailyTotal = ref(0);
const vehicleDeviationTotal = ref(0);
const vehicleDeviationDailyTotal = ref(0);
const vehicleDeviationDetailsTotal = ref(0);

// 各个Tab的数据列表
const routeDeviationList = ref([]);
const routeDeviationDailyList = ref([]);
const vehicleDeviationList = ref([]);
const vehicleDeviationDailyList = ref([]);
const vehicleDeviationDetailsList = ref([]);

// 统计数据
const summaryStats = ref({
  totalRoutes: '28',
  totalVehicles: '256',
  avgDeviationRate: '12.8',
  avgDeviationDistance: '45.6'
});

// 线路和车辆树数据
const deptOptions = ref([]);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  dateRange: null,
  routeNumber: null,
  plateNumber: null,
  deviationLevel: null,
  routeId: null,
  vehicleId: null
});

// 计算弹窗标题
const dialogTitle = computed(() => {
  if (!detailData.value) return '详情';
  if (detailData.value.routeName && !detailData.value.plateNumber) {
    return `${detailData.value.routeName} - 线路偏移详情`;
  }
  if (detailData.value.plateNumber && detailData.value.routeName) {
    return `${detailData.value.plateNumber} - ${detailData.value.routeName} 偏移详情`;
  }
  return '线路偏移详情';
});

onMounted(() => {
  getTreeSelect();
  handleTabChange(activeTab.value);
  nextTick(() => {
    initCharts();
  });
});

/** 组织机构树筛选 */
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选组织机构树 */
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

/** 查询线路车辆下拉树结构 */
async function getTreeSelect() {
  try {
    deptOptions.value = [
      {
        id: 'route_115',
        label: '115路',
        type: 'route',
        routeNumber: '115',
        children: [
          { id: 'vehicle_1', label: '京A12345 (V001)', type: 'vehicle', plateNumber: '京A12345', vehicleNumber: 'V001', routeId: 'route_115', deviationLevel: 'light' },
          { id: 'vehicle_2', label: '京A12346 (V002)', type: 'vehicle', plateNumber: '京A12346', vehicleNumber: 'V002', routeId: 'route_115', deviationLevel: 'moderate' },
          { id: 'vehicle_3', label: '京A12347 (V003)', type: 'vehicle', plateNumber: '京A12347', vehicleNumber: 'V003', routeId: 'route_115', deviationLevel: 'light' }
        ]
      },
      {
        id: 'route_135',
        label: '135路',
        type: 'route',
        routeNumber: '135',
        children: [
          { id: 'vehicle_4', label: '京A12348 (V004)', type: 'vehicle', plateNumber: '京A12348', vehicleNumber: 'V004', routeId: 'route_135', deviationLevel: 'severe' },
          { id: 'vehicle_5', label: '京A12349 (V005)', type: 'vehicle', plateNumber: '京A12349', vehicleNumber: 'V005', routeId: 'route_135', deviationLevel: 'light' },
          { id: 'vehicle_6', label: '京A12350 (V006)', type: 'vehicle', plateNumber: '京A12350', vehicleNumber: 'V006', routeId: 'route_135', deviationLevel: 'moderate' }
        ]
      },
      {
        id: 'route_201',
        label: '201路',
        type: 'route',
        routeNumber: '201',
        children: [
          { id: 'vehicle_7', label: '京A12351 (V007)', type: 'vehicle', plateNumber: '京A12351', vehicleNumber: 'V007', routeId: 'route_201', deviationLevel: 'light' },
          { id: 'vehicle_8', label: '京A12352 (V008)', type: 'vehicle', plateNumber: '京A12352', vehicleNumber: 'V008', routeId: 'route_201', deviationLevel: 'moderate' }
        ]
      },
      {
        id: 'route_301',
        label: '301路',
        type: 'route',
        routeNumber: '301',
        children: [
          { id: 'vehicle_9', label: '京A12353 (V009)', type: 'vehicle', plateNumber: '京A12353', vehicleNumber: 'V009', routeId: 'route_301', deviationLevel: 'severe' },
          { id: 'vehicle_10', label: '京A12354 (V010)', type: 'vehicle', plateNumber: '京A12354', vehicleNumber: 'V010', routeId: 'route_301', deviationLevel: 'light' }
        ]
      }
    ];
  } catch (error) {
    console.error('获取线路车辆树失败:', error);
  }
}

/** 节点单击事件 */
function handleNodeClick(data) {
  if (data.type === 'route') {
    queryParams.routeId = data.id;
    queryParams.routeNumber = data.routeNumber;
    queryParams.vehicleId = null;
    queryParams.plateNumber = null;
  } else if (data.type === 'vehicle') {
    queryParams.vehicleId = data.id;
    queryParams.plateNumber = data.plateNumber;
    queryParams.routeId = null;
    queryParams.routeNumber = null;
  }
  handleQuery();
}

/** 处理日期范围变化 */
function handleDateRangeChange(dateRange) {
  // 日期范围变化时的处理逻辑
  if (dateRange && dateRange.length === 2) {
    // 可以在这里添加业务逻辑，比如验证日期范围是否合理
    console.log('选择的时间范围:', dateRange);
  }
}

/** Tab切换事件 */
function handleTabChange(tabName) {
  queryParams.pageNum = 1;
  activeTab.value = tabName;
  loading.value = true;

  switch (tabName) {
    case 'routeDeviationSummary':
      getRouteDeviationList();
      break;
    case 'routeDeviationDaily':
      getRouteDeviationDailyList();
      break;
    case 'vehicleDeviationSummary':
      getVehicleDeviationList();
      break;
    case 'vehicleDeviationDaily':
      getVehicleDeviationDailyList();
      break;
    case 'vehicleDeviationDetails':
      getVehicleDeviationDetailsList();
      break;
  }
}

// 获取线路偏移报表数据
function getRouteDeviationList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateRouteDeviationData();
    routeDeviationList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    routeDeviationTotal.value = mockData.length;
    loading.value = false;

    // 更新图表
    nextTick(() => {
      updateRouteDeviationChart();
    });
  }, 500);
}

// 获取线路偏移日报表数据
function getRouteDeviationDailyList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateRouteDeviationDailyData();
    routeDeviationDailyList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    routeDeviationDailyTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 获取车辆偏移报表数据
function getVehicleDeviationList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateVehicleDeviationData();
    vehicleDeviationList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    vehicleDeviationTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 获取车辆偏移日报表数据
function getVehicleDeviationDailyList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateVehicleDeviationDailyData();
    vehicleDeviationDailyList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    vehicleDeviationDailyTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 获取车辆偏移明细数据
function getVehicleDeviationDetailsList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateVehicleDeviationDetailsData();
    vehicleDeviationDetailsList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    vehicleDeviationDetailsTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 生成线路偏移报表模拟数据
function generateRouteDeviationData() {
  const routes = [
    { routeNumber: '115', routeName: '115路' },
    { routeNumber: '135', routeName: '135路' },
    { routeNumber: '201', routeName: '201路' },
    { routeNumber: '301', routeName: '301路' },
    { routeNumber: '202', routeName: '202路' }
  ];

  const data = [];
  let id = 1;

  routes.forEach(route => {
    const vehicleCount = Math.floor(Math.random() * 15) + 8;
    const detectionCount = Math.floor(Math.random() * 500) + 800;
    const deviationCount = Math.floor(detectionCount * (0.05 + Math.random() * 0.25));
    const deviationRate = ((deviationCount / detectionCount) * 100).toFixed(1);
    const avgDeviationDistance = (Math.random() * 60 + 20).toFixed(1);
    const maxDeviationDistance = (parseFloat(avgDeviationDistance) + Math.random() * 80 + 20).toFixed(1);

    data.push({
      id: id++,
      ...route,
      vehicleCount,
      detectionCount,
      deviationCount,
      deviationRate,
      avgDeviationDistance,
      maxDeviationDistance,
      remark: Math.random() > 0.7 ? '运行正常' : null
    });
  });

  return data;
}

// 生成线路偏移日报表模拟数据
function generateRouteDeviationDailyData() {
  const data = [];
  const routes = ['115路', '135路', '201路', '301路'];
  const weathers = ['晴天', '多云', '小雨', '阴天'];

  for (let i = 0; i < 30; i++) {
    routes.forEach((routeName, routeIndex) => {
      const baseDate = new Date();
      baseDate.setDate(baseDate.getDate() - i);

      const dailyTrips = Math.floor(Math.random() * 50) + 80;
      const detectionCount = Math.floor(Math.random() * 200) + 300;
      const deviationCount = Math.floor(detectionCount * (0.05 + Math.random() * 0.20));
      const deviationRate = ((deviationCount / detectionCount) * 100).toFixed(1);
      const avgDeviationDistance = (Math.random() * 50 + 25).toFixed(1);
      const weather = weathers[Math.floor(Math.random() * weathers.length)];

      data.push({
        id: i * routes.length + routeIndex + 1,
        statisticDate: baseDate.toISOString().split('T')[0],
        routeNumber: routeName.replace('路', ''),
        routeName,
        dailyTrips,
        detectionCount,
        deviationCount,
        deviationRate,
        avgDeviationDistance,
        weather,
        remark: Math.random() > 0.8 ? (weather === '小雨' ? '雨天影响' : '运行正常') : null
      });
    });
  }

  return data.sort((a, b) => new Date(b.statisticDate) - new Date(a.statisticDate));
}

// 生成车辆偏移报表模拟数据
function generateVehicleDeviationData() {
  const vehicles = [
    { plateNumber: '京A12345', vehicleNumber: 'V001', routeName: '115路', driverName: '张志明' },
    { plateNumber: '京A12346', vehicleNumber: 'V002', routeName: '135路', driverName: '李华强' },
    { plateNumber: '京A12347', vehicleNumber: 'V003', routeName: '201路', driverName: '王建国' },
    { plateNumber: '京A12348', vehicleNumber: 'V004', routeName: '301路', driverName: '陈美丽' },
    { plateNumber: '京A12349', vehicleNumber: 'V005', routeName: '115路', driverName: '刘德华' }
  ];

  const data = [];
  let id = 1;

  vehicles.forEach(vehicle => {
    const detectionCount = Math.floor(Math.random() * 200) + 150;
    const deviationCount = Math.floor(detectionCount * (0.03 + Math.random() * 0.25));
    const deviationRate = ((deviationCount / detectionCount) * 100).toFixed(1);
    const avgDeviationDistance = (Math.random() * 70 + 15).toFixed(1);

    let deviationLevel;
    if (parseFloat(avgDeviationDistance) < 30) deviationLevel = 'light';
    else if (parseFloat(avgDeviationDistance) < 60) deviationLevel = 'moderate';
    else deviationLevel = 'severe';

    data.push({
      id: id++,
      ...vehicle,
      detectionCount,
      deviationCount,
      deviationRate,
      avgDeviationDistance,
      deviationLevel,
      remark: Math.random() > 0.7 ? '表现良好' : null
    });
  });

  return data;
}

// 生成车辆偏移日报表模拟数据
function generateVehicleDeviationDailyData() {
  const data = [];
  const vehicles = [
    { plateNumber: '京A12345', vehicleNumber: 'V001', routeName: '115路', driverName: '张志明' },
    { plateNumber: '京A12346', vehicleNumber: 'V002', routeName: '135路', driverName: '李华强' },
    { plateNumber: '京A12347', vehicleNumber: 'V003', routeName: '201路', driverName: '王建国' }
  ];

  for (let i = 0; i < 15; i++) {
    vehicles.forEach((vehicle, vehicleIndex) => {
      const baseDate = new Date();
      baseDate.setDate(baseDate.getDate() - i);

      const dailyShifts = Math.floor(Math.random() * 3) + 2;
      const deviationCount = Math.floor(Math.random() * 8);
      const deviationRate = dailyShifts > 0 ? ((deviationCount / (dailyShifts * 4)) * 100).toFixed(1) : '0.0';
      const avgDeviationDistance = deviationCount > 0 ? (Math.random() * 60 + 20).toFixed(1) : '0.0';

      data.push({
        id: i * vehicles.length + vehicleIndex + 1,
        statisticDate: baseDate.toISOString().split('T')[0],
        ...vehicle,
        dailyShifts,
        deviationCount,
        deviationRate,
        avgDeviationDistance,
        remark: Math.random() > 0.8 ? '正常运营' : null
      });
    });
  }

  return data.sort((a, b) => new Date(b.statisticDate) - new Date(a.statisticDate));
}

// 生成车辆偏移明细模拟数据
function generateVehicleDeviationDetailsData() {
  const data = [];
  const routes = [
    { routeName: '115路' },
    { routeName: '135路' },
    { routeName: '201路' },
    { routeName: '301路' }
  ];
  const vehicles = ['京A12345', '京A12346', '京A12347', '京A12348'];
  const drivers = ['张志明', '李华强', '王建国', '陈美丽'];
  const locations = ['市中心路段', '开发区路段', '商业街路段', '工业园路段', '住宅区路段'];

  for (let i = 0; i < 50; i++) {
    const routeIndex = Math.floor(Math.random() * routes.length);
    const vehicleIndex = Math.floor(Math.random() * vehicles.length);
    const locationIndex = Math.floor(Math.random() * locations.length);

    const baseDate = new Date();
    baseDate.setDate(baseDate.getDate() - Math.floor(i / 10));
    const hours = Math.floor(Math.random() * 16) + 6;
    const minutes = Math.floor(Math.random() * 60);
    baseDate.setHours(hours, minutes, 0);

    const deviationDistance = Math.floor(Math.random() * 150) + 10;
    let deviationLevel, handleStatus;

    if (deviationDistance < 30) {
      deviationLevel = 'light';
      handleStatus = 'completed';
    } else if (deviationDistance < 80) {
      deviationLevel = 'moderate';
      handleStatus = Math.random() > 0.5 ? 'completed' : 'processing';
    } else {
      deviationLevel = 'severe';
      handleStatus = Math.random() > 0.3 ? 'processing' : 'pending';
    }

    data.push({
      id: i + 1,
      deviationTime: baseDate.toISOString().replace('T', ' ').substring(0, 19),
      routeName: routes[routeIndex].routeName,
      plateNumber: vehicles[vehicleIndex],
      tripNumber: `T${(2000 + i).toString()}`,
      deviationLocation: locations[locationIndex],
      deviationDistance,
      deviationLevel,
      driverName: drivers[vehicleIndex],
      handleStatus,
      remark: Math.random() > 0.7 ? (deviationLevel === 'severe' ? '需要关注' : '已处理') : null
    });
  }

  return data.sort((a, b) => new Date(b.deviationTime) - new Date(a.deviationTime));
}

// 初始化图表
function initCharts() {
  initRouteDeviationChart();
}

// 初始化线路偏移统计图表
function initRouteDeviationChart() {
  if (!routeDeviationChartRef.value) return;

  routeDeviationChart = echarts.init(routeDeviationChartRef.value);
  updateRouteDeviationChart();
}

// 更新线路偏移统计图表
function updateRouteDeviationChart() {
  if (!routeDeviationChart) return;

  const routes = routeDeviationList.value.slice(0, 8);
  const routeNames = routes.map(r => r.routeName);
  const deviationRates = routes.map(r => parseFloat(r.deviationRate));
  const avgDistances = routes.map(r => parseFloat(r.avgDeviationDistance));

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' },
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' }
    },
    legend: {
      data: ['偏移率', '平均偏移距离'],
      textStyle: { color: '#e5e7eb' },
      top: '5%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: routeNames,
      axisLabel: { color: '#94a3b8', rotate: 30 },
      axisLine: { lineStyle: { color: '#374151' } }
    },
    yAxis: [
      {
        type: 'value',
        name: '偏移率(%)',
        position: 'left',
        axisLabel: { color: '#94a3b8' },
        axisLine: { lineStyle: { color: '#374151' } },
        splitLine: { lineStyle: { color: '#374151' } }
      },
      {
        type: 'value',
        name: '平均偏移距离(m)',
        position: 'right',
        axisLabel: { color: '#94a3b8' },
        axisLine: { lineStyle: { color: '#374151' } },
        splitLine: { show: false }
      }
    ],
    series: [
      {
        name: '偏移率',
        type: 'bar',
        data: deviationRates,
        itemStyle: { color: '#F56C6C' },
        yAxisIndex: 0
      },
      {
        name: '平均偏移距离',
        type: 'line',
        data: avgDistances,
        itemStyle: { color: '#E6A23C' },
        lineStyle: { color: '#E6A23C' },
        yAxisIndex: 1
      }
    ]
  };

  routeDeviationChart.setOption(option);
}

// 查询
function handleQuery() {
  queryParams.pageNum = 1;
  handleTabChange(activeTab.value);
}

// 重置
function resetQuery() {
  queryRef.value?.resetFields();
  queryParams.routeId = null;
  queryParams.vehicleId = null;
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

// 表格选择
function handleSelectionChange(selection) {
  // 处理表格选择
}

// 查看详情
function handleDetail(row) {
  detailData.value = row;
  showDetailDialog.value = true;
}

// 查看偏移详情
function handleDeviationDetail(row) {
  detailData.value = {
    ...row,
    routeName: row.routeName,
    plateNumber: row.plateNumber
  };
  showDetailDialog.value = true;
}

// 导出数据
function handleExport() {
  ElMessage.success('导出成功');
}

// 打印报表
function handlePrint() {
  window.print();
}

// 获取偏移率类型
function getDeviationRateType(rate) {
  const numRate = parseFloat(rate);
  if (numRate <= 5) return 'success';
  if (numRate <= 15) return 'warning';
  return 'danger';
}

// 获取偏移等级类型
function getDeviationLevelType(level) {
  const typeMap = {
    'light': 'success',
    'moderate': 'warning',
    'severe': 'danger'
  };
  return typeMap[level] || 'info';
}

// 获取偏移等级文本
function getDeviationLevelText(level) {
  const textMap = {
    'light': '轻微',
    'moderate': '中度',
    'severe': '严重'
  };
  return textMap[level] || '未知';
}

// 获取偏移距离样式类
function getDeviationDistanceClass(distance) {
  const numDistance = parseFloat(distance);
  if (numDistance < 30) return 'distance-light';
  if (numDistance < 80) return 'distance-moderate';
  return 'distance-severe';
}

// 获取处理状态类型
function getHandleStatusType(status) {
  const typeMap = {
    'completed': 'success',
    'processing': 'warning',
    'pending': 'danger'
  };
  return typeMap[status] || 'info';
}

// 获取处理状态文本
function getHandleStatusText(status) {
  const textMap = {
    'completed': '已处理',
    'processing': '处理中',
    'pending': '待处理'
  };
  return textMap[status] || '未知';
}

// 获取天气类型
function getWeatherType(weather) {
  const typeMap = {
    '晴天': 'success',
    '多云': '',
    '阴天': 'info',
    '小雨': 'warning'
  };
  return typeMap[weather] || 'info';
}
</script>

<style scoped>
.route-deviation-analysis {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 左侧面板样式 */
.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 24px;
}

.search-box {
  margin-bottom: 16px;
}

/* 右侧内容区域 */
.right-content {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  backdrop-filter: blur(10px);
}

/* 筛选条件区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  background: rgba(15, 23, 42, 0.98) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

/* Tab区域 */
.tabs-section {
  margin-bottom: 20px;
}

.tab-content {
  padding-top: 20px;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.time-range-info {
  margin-bottom: 16px;
}

.stat-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(41, 52, 70, 0.6) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
  border-color: rgba(96, 165, 250, 0.4);
}

.stat-card.total {
  border-left: 4px solid #409EFF;
}

.stat-card.vehicles {
  border-left: 4px solid #E6A23C;
}

.stat-card.rate {
  border-left: 4px solid #F56C6C;
}

.stat-card.distance {
  border-left: 4px solid #67C23A;
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #60a5fa;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #f8fafc;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #94a3b8;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  height: 400px;
}

.chart-card .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.chart-card .chart-header h3 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.chart-subtitle {
  font-size: 12px;
  color: #60a5fa;
  opacity: 0.8;
}

.chart-container {
  height: 320px;
  width: 100%;
}

/* 表格样式 */
.table-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  min-height: 620px;
  height: calc(100vh - 480px);
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.table-header h4 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.table-subtitle {
  font-size: 12px;
  color: #60a5fa;
  opacity: 0.8;
}

/* 数据样式 */
.deviation-count {
  color: #F56C6C;
  font-weight: 600;
}

.distance-value {
  color: #E6A23C;
  font-weight: 600;
}

.max-distance {
  color: #F56C6C;
  font-weight: 600;
}

.distance-light {
  color: #67C23A;
  font-weight: 600;
}

.distance-moderate {
  color: #E6A23C;
  font-weight: 600;
}

.distance-severe {
  color: #F56C6C;
  font-weight: 600;
}

/* 详情弹窗 */
.detail-content {
  max-height: 500px;
  overflow-y: auto;
}

/* 树形组件样式 */
.dept-tree {
  background: transparent;
  color: #e5e7eb;
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
}

.tree-node {
  display: flex;
  align-items: center;
  font-size: 14px;
  flex: 1;
}

.route-icon {
  margin-right: 8px;
  color: #60a5fa;
  font-size: 16px;
  flex-shrink: 0;
}

.vehicle-icon {
  margin-right: 8px;
  color: #E6A23C;
  font-size: 16px;
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  min-width: 0;
  white-space: nowrap;
}

.node-label.route {
  font-weight: 500;
  color: #f1f5f9;
}

.node-label.vehicle {
  color: #94a3b8;
}

/* Element UI组件覆盖 */
:deep(.el-form-item__label) {
  color: #e5e7eb !important;
}

:deep(.el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e5e7eb !important;
}

:deep(.el-input__inner) {
  color: #e5e7eb !important;
}

:deep(.el-input__inner::placeholder) {
  color: #64748b !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
}

:deep(.el-table) {
  background: transparent !important;
  color: #e5e7eb !important;
  height: 100% !important;
  flex: 1 !important;
}

:deep(.el-table tr) {
  background: rgba(15, 23, 42, 0.6) !important;
}

:deep(.el-table tr:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
}

:deep(.el-table th.el-table__cell) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #f8fafc !important;
  border-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-table td.el-table__cell) {
  border-color: rgba(147, 197, 253, 0.1) !important;
  color: #e5e7eb !important;
}

:deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.8);
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  background: transparent;
  border: none;
  color: #94a3b8;
  transition: all 0.3s ease;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item:hover) {
  background: rgba(59, 130, 246, 0.1);
  color: #93c5fd;
}

/* Element Tree组件覆盖样式 */
:deep(.el-tree-node) {
  background: transparent;
}

:deep(.el-tree-node:hover > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 6px;
}

:deep(.el-tree-node__content) {
  background: transparent !important;
  border: none !important;
  color: #e5e7eb !important;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

:deep(.el-tree-node__content:hover) {
  background-color: rgba(59, 130, 246, 0.15) !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.2) !important;
  color: #93c5fd !important;
}

:deep(.el-tree-node__expand-icon) {
  color: #64748b !important;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-tree .el-tree-node__children) {
  overflow: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .route-deviation-analysis {
    padding: 10px;
  }

  .stats-section .el-col {
    margin-bottom: 16px;
  }

  .chart-card {
    height: 300px;
  }

  .chart-container {
    height: 220px;
  }
}
</style>
