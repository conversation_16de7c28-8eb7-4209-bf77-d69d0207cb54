import request from '@/utils/request';
import { ScheduleTemplateQuery, ScheduleTemplateVO, ScheduleTemplateForm, ScheduleTemplateImportForm } from './types';

// 查询发车计划模板列表
export function listScheduleTemplate(query: ScheduleTemplateQuery) {
  return request({
    url: '/schedule/template/list',
    method: 'get',
    params: query
  });
}

// 查询发车计划模板详细
export function getScheduleTemplate(templateId: string | number) {
  return request({
    url: '/schedule/template/' + templateId,
    method: 'get'
  });
}

// 新增发车计划模板
export function addScheduleTemplate(data: ScheduleTemplateForm) {
  return request({
    url: '/schedule/template',
    method: 'post',
    data: data
  });
}

// 修改发车计划模板
export function updateScheduleTemplate(data: ScheduleTemplateForm) {
  return request({
    url: '/schedule/template',
    method: 'put',
    data: data
  });
}

// 删除发车计划模板
export function delScheduleTemplate(templateId: string | number | Array<string | number>) {
  return request({
    url: '/schedule/template/' + templateId,
    method: 'delete'
  });
}

// 批量启用/停用模板
export function toggleScheduleTemplateStatus(templateIds: Array<string | number>, status: string) {
  return request({
    url: '/schedule/template/status',
    method: 'put',
    data: {
      templateIds,
      status
    }
  });
}

// 复制模板
export function copyScheduleTemplate(templateId: string | number, newTemplateName: string) {
  return request({
    url: '/schedule/template/copy',
    method: 'post',
    data: {
      templateId,
      newTemplateName
    }
  });
}

// 应用模板到排班计划
export function applyTemplateToSchedule(templateId: string | number, scheduleData: any) {
  return request({
    url: '/schedule/template/apply',
    method: 'post',
    data: {
      templateId,
      ...scheduleData
    }
  });
}

// 预览模板生成的发车时刻表
export function previewTemplateSchedule(templateId: string | number, date?: string) {
  return request({
    url: '/schedule/template/preview',
    method: 'get',
    params: {
      templateId,
      date
    }
  });
}

// 导入模板
export function importScheduleTemplate(data: ScheduleTemplateImportForm) {
  return request({
    url: '/schedule/template/import',
    method: 'post',
    data: data
  });
}

// 导出模板
export function exportScheduleTemplate(templateIds: Array<string | number>) {
  return request({
    url: '/schedule/template/export',
    method: 'post',
    data: {
      templateIds
    },
    responseType: 'blob'
  });
}

// 获取模板使用统计
export function getTemplateUsageStats(templateId: string | number) {
  return request({
    url: '/schedule/template/stats/' + templateId,
    method: 'get'
  });
}
