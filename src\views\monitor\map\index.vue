﻿<template>
  <div class="monitor-map-page">
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">地图监控</h2>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>实时监控</el-breadcrumb-item>
          <el-breadcrumb-item>地图监控</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <el-space>
          <el-button type="primary" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </el-space>
      </div>
    </div>

    <div class="map-section">
      <el-card shadow="hover" class="map-card">
        <template #header>
          <span>实时监控地图</span>
        </template>
        <div class="map-container">
          <div id="amap-container" class="amap-wrapper"></div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup name="MonitorMap" lang="ts">
import { Refresh } from '@element-plus/icons-vue';
import { createOptimizedMap, addOptimizedControls, createOptimizedMarker, suppressMapWarnings, handleMapError } from '@/utils/mapConfig';

// 响应式数据
const mapInstance = ref<any>(null);
const mapLoaded = ref(false);

const refreshData = () => {
  ElMessage.success('数据已刷新');
};

// 初始化地图
const initMap = () => {
  try {
    // 抑制警告
    suppressMapWarnings();

    // 创建优化的地图实例
    const map = createOptimizedMap('amap-container');

    // 添加优化的控件
    addOptimizedControls(map);

    // 添加优化的标记点
    const marker = createOptimizedMarker([116.397428, 39.90923], '天安门站');
    map.add(marker);

    // 地图加载完成事件
    map.on('complete', () => {
      console.log('地图加载完成');
      mapLoaded.value = true;
      ElMessage.success('地图加载成功');
    });

    mapInstance.value = map;
  } catch (error) {
    handleMapError(error, '地图初始化');
  }
};

// 生命周期
onMounted(() => {
  // 确保高德地图API已加载
  if (typeof AMap !== 'undefined') {
    initMap();
  } else {
    // 等待API加载
    const checkAMap = setInterval(() => {
      if (typeof AMap !== 'undefined') {
        clearInterval(checkAMap);
        initMap();
      }
    }, 100);

    // 10秒后超时
    setTimeout(() => {
      clearInterval(checkAMap);
      if (!mapLoaded.value) {
        ElMessage.error('地图API加载超时');
      }
    }, 10000);
  }
});

onUnmounted(() => {
  if (mapInstance.value) {
    mapInstance.value.destroy();
  }
});
</script>

<style scoped>
.monitor-map-page {
  padding: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.map-section {
  margin-bottom: 16px;
}

.map-container {
  width: 100%;
  height: 600px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.amap-wrapper {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}
</style>
