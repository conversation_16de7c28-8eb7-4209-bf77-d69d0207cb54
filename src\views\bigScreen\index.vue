<template>
  <div class='big-screen-container'>
    <!-- 顶部标题栏 -->
    <header class='screen-header'>
      <div class='header-left'>
        <div class='logo-section'>
          <img src='@/assets/logo/logo.png' alt='Logo' class='logo-img' />
          <span class='logo-text'>公交运营大屏监控</span>
        </div>
      </div>

      <div class='header-center'>
        <div class='map-controls'>
          <!-- 显示选项 -->
          <div class='control-group'>
            <span class='control-label'>显示选项</span>
            <el-checkbox-group v-model='displayOptions' size='small' @change='handleDisplayOptionsChange'>
              <el-checkbox value='stations'>显示站点</el-checkbox>
              <el-checkbox value='routes'>显示线路</el-checkbox>
              <el-checkbox value='alerts'>显示告警</el-checkbox>
              <el-checkbox value='realtime'>实时刷新</el-checkbox>
            </el-checkbox-group>
          </div>

          <!-- 地图操作 -->
          <div class='control-group'>
            <span class='control-label'>地图操作</span>
            <el-button-group size='small'>
              <el-button icon='ZoomIn' @click='zoomIn'>放大</el-button>
              <el-button icon='ZoomOut' @click='zoomOut'>缩小</el-button>
              <el-button icon='Aim' @click='resetView'>重置</el-button>
            </el-button-group>
          </div>

          <!-- 地图图例 -->
          <div class='control-group legend-group'>
            <span class='control-label'>图例</span>
            <div class='header-legend-items'>
              <div class='header-legend-item'>
                <div class='header-legend-marker running'></div>
                <span>运营中</span>
              </div>
              <div class='header-legend-item'>
                <div class='header-legend-marker stopped'></div>
                <span>停靠中</span>
              </div>
              <div class='header-legend-item'>
                <div class='header-legend-marker maintenance'></div>
                <span>维修中</span>
              </div>
              <div class='header-legend-item'>
                <div class='header-legend-marker fault'></div>
                <span>故障</span>
              </div>
              <div class='header-legend-item'>
                <div class='header-legend-marker station'></div>
                <span>站点</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class='header-right'>
        <div class='current-time'>
          <div class='time-display'>{{ currentTime }}</div>
          <div class='date-display'>{{ currentDate }}</div>
        </div>

        <!-- 全屏按钮移到时间右侧 -->
        <div class='header-actions'>
          <el-button type='primary' icon='FullScreen' circle @click='toggleFullscreen'
                     class='fullscreen-btn-header'></el-button>
        </div>
      </div>
    </header>

    <!-- 主体内容区域 -->
    <main class='screen-main'>
      <!-- 地图作为底图占用全屏 -->
      <section class='map-section fullscreen-map'>
        <!-- 地图容器占用全部空间 -->
        <div class='map-container'>
          <div ref='mapContainer' id='big-screen-map' class='map-canvas'></div>

          <!-- 地图筛选器 -->
          <div class='map-filters-panel' v-show='filterPanelVisible'>
            <div class='filter-header'>
              <div class='filter-title'>
                <el-icon><Filter /></el-icon>
                <span>地图筛选</span>
              </div>
              <el-button
                size='small'
                circle
                @click='toggleFilterPanel'
                class='filter-hide-btn'
                title='隐藏筛选面板'
              >
                <span class='close-icon'></span>
              </el-button>
              </div>
            <div class='filter-row'>
              <el-select v-model='selectedRoute' placeholder='线路' size='small' @change='handleRouteFilter' clearable filterable>
                <el-option v-for='route in routeOptions' :key='route.id' :label='route.label' :value='route.id'></el-option>
              </el-select>
              <el-select v-model='selectedStatus' placeholder='状态' size='small' @change='handleStatusFilter' clearable filterable>
                <el-option label='运营中' value='running'></el-option>
                <el-option label='停靠中' value='stopped'></el-option>
                <el-option label='维修中' value='maintenance'></el-option>
                <el-option label='故障' value='fault'></el-option>
              </el-select>
              </div>
            <div class='filter-row'>
              <el-select v-model='selectedAlertType' placeholder='告警' size='small' @change='handleAlertTypeFilter' clearable filterable>
                <el-option label='高级' value='high'></el-option>
                <el-option label='中级' value='medium'></el-option>
                <el-option label='低级' value='low'></el-option>
              </el-select>
              <el-select v-model='selectedStation' placeholder='站点' size='small' @change='handleStationFilter' clearable filterable>
                <el-option v-for='station in stationOptions' :key='station.id' :label='station.label' :value='station.id'></el-option>
              </el-select>
              </div>
              </div>

          <!-- 筛选面板显示按钮（当面板隐藏时显示） -->
          <div class='filter-show-btn' v-show='!filterPanelVisible' @click='toggleFilterPanel'>
            <el-icon><Filter /></el-icon>
            <span>筛选</span>
          </div>
        </div>
      </section>

      <!-- 左侧组件 - 车辆监控和车辆状态 -->
      <div class='side-panel left-panel' v-show='!sidePanelsHidden'
           :style="{
             position: 'absolute',
             top: '20px',
             left: '20px',
             bottom: '20px',
             width: '320px',
             display: 'flex',
             flexDirection: 'column',
             gap: '20px',
             zIndex: 100,
             boxSizing: 'border-box'
           }">

        <!-- 车辆监控区域 -->
        <div class='panel-card monitoring-panel' :style="{ flex: '1', display: 'flex', flexDirection: 'column' }">
          <div class='card-header'>
            <el-icon>
              <TrendCharts />
            </el-icon>
            <span>车辆监控</span>
            <div class='header-actions' style='margin-left: auto; display: flex; gap: 8px;'>
              <el-button size='small' icon='List' @click='showAllVehicles' class='view-all-btn'>查看全部</el-button>
              <el-button size='small' icon='Refresh' circle @click='refreshVehicleData'
                         class='refresh-btn-mini'></el-button>
            </div>
          </div>
          <div class='monitoring-content' :style="{ flex: '1', overflowY: 'auto' }">
            <div class='vehicle-items-mini'>
              <div class='vehicle-item-mini' v-for='vehicle in displayVehicles' :key='vehicle.id'
                   @click='locateVehicleFromSidebar(vehicle)'>
                <div class='vehicle-status-indicator-mini' :class='vehicle.status'></div>
                <div class='vehicle-info-mini'>
                  <div class='vehicle-number-mini'>{{ vehicle.plateNumber }}</div>
                  <div class='vehicle-route-mini'>{{ vehicle.routeName }}</div>
                </div>
                <div class='vehicle-data-mini'>
                  <span class='data-value-mini'>{{ vehicle.speed }}km/h</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 车辆状态区域（饼图） -->
        <div class='panel-card status-panel' :style="{ flex: '1', display: 'flex', flexDirection: 'column' }">
          <div class='card-header'>
            <el-icon>
              <Odometer />
            </el-icon>
            <span>车辆状态</span>
          </div>
          <div class='status-content' :style="{ flex: '1', overflowY: 'auto' }">
            <div class='status-chart-mini'>
              <div ref='vehicleStatusChart' class='chart-container-mini'></div>
            </div>
            <div class='status-legend-mini status-grid'>
              <div class='status-card' v-for='item in vehicleStatusData' :key='item.name'>
                <div class='status-value'>{{ item.value }}</div>
                <div class='status-name' :style='{ color: item.color }'>{{ item.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧组件 - 实时告警和地图控制 -->
      <div class='side-panel right-panel' v-show='!sidePanelsHidden'
           :style="{
             position: 'absolute',
             top: '20px',
             right: '20px',
             bottom: '20px',
             width: '320px',
             display: 'flex',
             flexDirection: 'column',
             gap: '20px',
             zIndex: 100,
             boxSizing: 'border-box'
           }">

        <!-- 实时告警区域 -->
        <div class='panel-card alert-panel' :style="{ flex: '1', display: 'flex', flexDirection: 'column' }">
          <div class='card-header'>
            <el-icon>
              <Warning />
            </el-icon>
            <span>实时告警</span>
            <div class='header-actions' style='margin-left: auto; display: flex; gap: 8px; align-items: center;'>
              <el-badge :value='unresolvedAlerts.length' class='alert-badge'></el-badge>
              <el-button size='small' icon='List' @click='showAllAlerts' class='view-all-btn'>查看全部</el-button>
              <el-button size='small' icon='Refresh' circle @click='refreshAlertData' class='refresh-btn-mini'></el-button>
            </div>
          </div>
          <div class='alert-list' :style="{ flex: '1', overflowY: 'auto' }">
            <div class='alert-item' v-for='alert in unresolvedAlerts' :key='alert.id'
                 :class="['alert-' + alert.level, 'status-' + alert.status]">
              <div class='alert-icon'>
                <el-icon>
                  <WarningFilled v-if="alert.level === 'high'" />
                  <InfoFilled v-else />
                </el-icon>
              </div>
              <div class='alert-content' @click='locateAlertFromSidebar(alert)' style='cursor: pointer; flex: 1;'>
                <div class='alert-title'>
                  {{ alert.title }}
                  <el-tag :type='getAlertStatusTagType(alert.status)' size='small' class='status-tag'>
                    {{ getAlertStatusText(alert.status) }}
                  </el-tag>
                </div>
                <div class='alert-desc'>{{ alert.description }}</div>
                <div class='alert-time'>{{ alert.time }}</div>
              </div>
              <div class='alert-actions-sidebar'>
                <el-button
                  v-if="alert.status === 'pending'"
                  type='warning'
                  size='small'
                  @click.stop='handleSidebarAlertProcess(alert)'
                  icon='Timer'
                  circle
                  :title="'开始处理'"
                />
                <el-button
                  v-if="alert.status === 'processing'"
                  type='success'
                  size='small'
                  @click.stop='directCompleteAlert(alert)'
                  icon='Check'
                  circle
                  :title="'完成处理'"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 路线信息区域 -->
        <div class='panel-card route-info-panel' :style="{ flex: '1', display: 'flex', flexDirection: 'column' }">
          <div class='card-header'>
            <el-icon>
              <TrendCharts />
            </el-icon>
            <span>路线信息</span>
            <div class='header-actions' style='margin-left: auto; display: flex; gap: 8px;'>
              <el-button size='small' icon='List' @click='showAllRoutes' class='view-all-btn'>查看全部</el-button>
              <el-button size='small' icon='Refresh' circle @click='refreshRouteData'
                         class='refresh-btn-mini'></el-button>
          </div>
            </div>
          <div class='route-list-content' :style="{ flex: '1', overflowY: 'auto' }">
            <div class='route-list-items'>
              <div class='route-list-item' v-for='route in displayRoutes.slice(0, 6)' :key='route.id'
                   @click='locateRouteFromSidebar(route)'>
                <div class='route-status-indicator' :style="{ backgroundColor: route.color }"></div>
                <div class='route-info'>
                  <div class='route-name'>{{ route.name }}</div>
                  <div class='route-description'>{{ route.description }}</div>
            </div>
                <div class='route-data'>
                  <span class='data-value'>{{ route.vehicles }}辆</span>
            </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 车辆列表弹窗 -->
    <el-dialog
      v-model='vehicleListDialogVisible'
      title='车辆列表'
      width='60%'
      :modal='true'
      :close-on-click-modal='false'
      class='vehicle-list-dialog'
    >
      <div class='dialog-header'>
        <div class='search-controls'>
          <el-input
            v-model='searchKeyword'
            placeholder='搜索车牌号'
            size='small'
            style='width: 200px; margin-right: 12px;'
            clearable
            @keyup.enter='handleSearch'
          />

          <!-- 组织机构树选择器 -->
          <el-tree-select
            v-model='dialogDeptFilter'
            :data='deptTreeData'
            :props='{ value: "id", label: "name", children: "children" }'
            placeholder='选择组织机构'
            size='small'
            style='width: 150px; margin-right: 12px;'
            clearable
            check-strictly
            :render-after-expand='false'
          />

          <el-select v-model='dialogRouteFilter' placeholder='筛选线路' size='small'
                     style='width: 150px; margin-right: 12px;' clearable>
            <el-option label='全部线路' value=''></el-option>
            <el-option v-for='route in routeOptions' :key='route.id' :label='route.label'
                       :value='route.label'></el-option>
          </el-select>
          <el-select v-model='dialogStatusFilter' placeholder='筛选状态' size='small' style='width: 120px; margin-right: 12px;' clearable>
            <el-option label='全部状态' value=''></el-option>
            <el-option label='运营中' value='running'></el-option>
            <el-option label='停靠中' value='stopped'></el-option>
            <el-option label='维修中' value='maintenance'></el-option>
            <el-option label='故障' value='fault'></el-option>
          </el-select>

          <!-- 查询和重置按钮 -->
          <el-button type='primary' size='small' @click='handleSearch' icon='Search' style='margin-right: 8px;'>
            查询
          </el-button>
          <el-button size='small' @click='handleReset' icon='Refresh'>
            重置
          </el-button>
        </div>
      </div>

      <el-table
        :data='paginatedVehicles'
        size='small'
        max-height='400'
        height='400'
        class='vehicle-table'
        :stripe='false'
        style='width: 100%'
        :header-cell-style='headerCellStyle'
        :row-style='rowStyle'
        :cell-style='cellStyle'
      >
        <el-table-column prop='plateNumber' label='车牌号' min-width='120' />
        <el-table-column prop='routeName' label='线路' min-width='80' />
        <el-table-column prop='deptName' label='车队' min-width='100' />
        <el-table-column prop='status' label='状态' min-width='100'>
          <template #default='{ row }'>
            <el-tag :type='getStatusTagType(row.status)' size='small'>
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop='speed' label='速度(km/h)' min-width='100' />
        <el-table-column label='位置' min-width='180'>
          <template #default='{ row }'>
            {{ row.lat.toFixed(4) }}, {{ row.lng.toFixed(4) }}
          </template>
        </el-table-column>
        <el-table-column label='操作' min-width='120'>
          <template #default='{ row }'>
            <el-button
              size='small'
              type='primary'
              icon='Location'
              @click='locateVehicleOnMap(row)'
            >
              定位
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class='pagination-container'>
        <pagination
          v-show='filteredAllVehicles.length > 0'
          :total='filteredAllVehicles.length'
          v-model:page='currentPage'
          v-model:limit='pageSize'
          @pagination='handlePaginationChange'
          background
          layout='total, sizes, prev, pager, next, jumper'
        />
      </div>

      <template #footer>
        <el-button style='background-color: rgba(7,20,61,0.26)' @click='vehicleListDialogVisible = false'>关闭</el-button>
      </template>
    </el-dialog>

    <!-- 地图点位信息弹窗 -->
    <el-dialog
      v-model='mapInfoDialogVisible'
      :title='mapInfoTitle'
      width='400px'
      :modal='true'
      :close-on-click-modal='true'
      class='map-info-dialog'
    >
      <div class='map-info-content'>
        <!-- 车辆信息 -->
        <div v-if="mapInfoType === 'vehicle'" class='info-section'>
          <div class='info-item'>
            <span class='info-label'>车牌号：</span>
            <span class='info-value'>{{ mapInfoData.plateNumber }}</span>
          </div>
          <div class='info-item'>
            <span class='info-label'>线路：</span>
            <span class='info-value'>{{ mapInfoData.routeName }}</span>
          </div>
          <div class='info-item'>
            <span class='info-label'>车队：</span>
            <span class='info-value'>{{ mapInfoData.deptName }}</span>
          </div>
          <div class='info-item'>
            <span class='info-label'>速度：</span>
            <span class='info-value highlight'>{{ mapInfoData.speed }}km/h</span>
          </div>

          <div class='info-item'>
            <span class='info-label'>状态：</span>
            <el-tag :type='getStatusTagType(mapInfoData.status)' size='small'>
              {{ getStatusText(mapInfoData.status) }}
            </el-tag>
          </div>
        </div>

        <!-- 站点信息 -->
        <div v-else-if="mapInfoType === 'station'" class='info-section'>
          <div class='info-item'>
            <span class='info-label'>站点名称：</span>
            <span class='info-value'>{{ mapInfoData.name }}</span>
          </div>
          <div class='info-item'>
            <span class='info-label'>类型：</span>
            <span class='info-value'>公交站点</span>
          </div>
          <div class='info-item'>
            <span class='info-label'>经度：</span>
            <span class='info-value'>{{ mapInfoData.lng?.toFixed(6) }}</span>
          </div>
          <div class='info-item'>
            <span class='info-label'>纬度：</span>
            <span class='info-value'>{{ mapInfoData.lat?.toFixed(6) }}</span>
          </div>
        </div>

        <!-- 路线信息 -->
        <div v-else-if="mapInfoType === 'route'" class='info-section'>
          <div class='info-item'>
            <span class='info-label'>线路名称：</span>
            <span class='info-value highlight' :style="{ color: mapInfoData.color }">{{ mapInfoData.name }}</span>
          </div>
          <div class='info-item'>
            <span class='info-label'>类型：</span>
            <span class='info-value'>公交线路</span>
          </div>
          <div class='info-item'>
            <span class='info-label'>起终点：</span>
            <span class='info-value'>{{ mapInfoData.stationNames }}</span>
          </div>
        </div>

        <!-- 告警信息 -->
        <div v-else-if="mapInfoType === 'alert'" class='info-section'>
          <div class='info-item'>
            <span class='info-label'>告警标题：</span>
            <span class='info-value'>{{ mapInfoData.title }}</span>
          </div>
          <div class='info-item'>
            <span class='info-label'>级别：</span>
            <el-tag :type='getAlertTagType(mapInfoData.level)' size='small'>
              {{ getAlertLevelText(mapInfoData.level) }}
            </el-tag>
          </div>
          <div class='info-item'>
            <span class='info-label'>状态：</span>
            <el-tag :type='getAlertStatusTagType(mapInfoData.status)' size='small'>
              {{ getAlertStatusText(mapInfoData.status) }}
            </el-tag>
          </div>
          <div class='info-item'>
            <span class='info-label'>描述：</span>
            <span class='info-value'>{{ mapInfoData.description }}</span>
          </div>
          <div class='info-item'>
            <span class='info-label'>时间：</span>
            <span class='info-value'>{{ mapInfoData.time }}</span>
          </div>

          <!-- 处理信息显示区 -->
          <div v-if="mapInfoData.processInfo" class='process-info-section'>
            <div class='info-item'>
              <span class='info-label'>处理方式：</span>
              <span class='info-value'>{{ mapInfoData.processInfo.methodText }}</span>
            </div>
            <div class='info-item'>
              <span class='info-label'>处理人：</span>
              <span class='info-value'>{{ mapInfoData.processInfo.handler }}</span>
            </div>
            <div class='info-item'>
              <span class='info-label'>处理说明：</span>
              <span class='info-value process-desc'>{{ mapInfoData.processInfo.description }}</span>
            </div>
            <div class='info-item' v-if="mapInfoData.processInfo.startTime">
              <span class='info-label'>开始时间：</span>
              <span class='info-value'>{{ mapInfoData.processInfo.startTime }}</span>
            </div>
            <div class='info-item' v-if="mapInfoData.processInfo.completeTime">
              <span class='info-label'>完成时间：</span>
              <span class='info-value'>{{ mapInfoData.processInfo.completeTime }}</span>
            </div>
          </div>

          <!-- 处理操作区 -->
          <div class='alert-actions' v-if="mapInfoData.status !== 'resolved'">
            <el-button
              v-if="mapInfoData.status === 'pending'"
              type='warning'
              size='small'
              @click='handleAlertProcessing(mapInfoData)'
              icon='Timer'
            >
              开始处理
            </el-button>
            <el-button
              v-if="mapInfoData.status === 'processing'"
              type='success'
              size='small'
              @click='directCompleteAlert(mapInfoData)'
              icon='Check'
            >
              处理完成
            </el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click='mapInfoDialogVisible = false'>关闭</el-button>
      </template>
    </el-dialog>

    <!-- 告警列表弹窗 -->
    <el-dialog
      v-model='alertListDialogVisible'
      title='告警列表'
      width='70%'
      :modal='true'
      :close-on-click-modal='false'
      class='alert-list-dialog'
    >
      <div class='dialog-header'>
        <div class='search-controls'>
          <el-input
            v-model='alertSearchKeyword'
            placeholder='搜索告警标题'
            size='small'
            style='width: 200px; margin-right: 12px;'
            clearable
            @keyup.enter='handleAlertSearch'
          />

          <el-select v-model='alertLevelFilter' placeholder='筛选级别' size='small'
                     style='width: 150px; margin-right: 12px;' clearable>
            <el-option label='全部级别' value=''></el-option>
            <el-option label='高级' value='high'></el-option>
            <el-option label='中级' value='medium'></el-option>
            <el-option label='低级' value='low'></el-option>
          </el-select>

          <el-select v-model='alertStatusFilter' placeholder='筛选状态' size='small'
                     style='width: 150px; margin-right: 12px;' clearable>
            <el-option label='全部状态' value=''></el-option>
            <el-option label='待处理' value='pending'></el-option>
            <el-option label='处理中' value='processing'></el-option>
            <el-option label='已处理' value='resolved'></el-option>
          </el-select>

          <!-- 查询和重置按钮 -->
          <el-button type='primary' size='small' @click='handleAlertSearch' icon='Search' style='margin-right: 8px;'>
            查询
          </el-button>
          <el-button size='small' @click='handleAlertReset' icon='Refresh'>
            重置
          </el-button>
        </div>
      </div>

      <el-table
        :data='paginatedAlerts'
        size='small'
        max-height='400'
        height='400'
        class='alert-table'
        :stripe='false'
        style='width: 100%'
        :header-cell-style='alertHeaderCellStyle'
        :row-style='alertRowStyle'
        :cell-style='alertCellStyle'
      >
        <el-table-column prop='id' label='ID' min-width='80' />
        <el-table-column prop='title' label='标题' min-width='150' />
        <el-table-column prop='description' label='描述' min-width='200' show-overflow-tooltip />
        <el-table-column prop='level' label='级别' min-width='100'>
          <template #default='{ row }'>
            <el-tag :type='getAlertTagType(row.level)' size='small'>
              {{ getAlertLevelText(row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop='status' label='状态' min-width='100'>
          <template #default='{ row }'>
            <el-tag :type='getAlertStatusTagType(row.status)' size='small'>
              {{ getAlertStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop='time' label='时间' min-width='120' />
        <el-table-column label='操作' min-width='160'>
          <template #default='{ row }'>
            <el-button
              size='small'
              type='primary'
              icon='Location'
              @click='locateAlertOnMapOnly(row)'
              style='margin-right: 4px;'
            >
              定位
            </el-button>
            <el-button
              size='small'
              type='info'
              icon='View'
              @click='showAlertDetail(row)'
              style='margin-right: 4px;'
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class='pagination-container'>
        <pagination
          v-show='filteredAllAlerts.length > 0'
          :total='filteredAllAlerts.length'
          v-model:page='alertCurrentPage'
          v-model:limit='alertPageSize'
          @pagination='handleAlertPaginationChange'
          background
          layout='total, sizes, prev, pager, next, jumper'
        />
      </div>

      <template #footer>
        <el-button style='background-color: rgba(7,20,61,0.26)' @click='alertListDialogVisible = false'>关闭</el-button>
      </template>
    </el-dialog>

    <!-- 路线列表弹窗 -->
    <el-dialog
      v-model='routeListDialogVisible'
      title='路线列表'
      width='70%'
      :modal='true'
      :close-on-click-modal='false'
      class='route-list-dialog'
    >
      <div class='dialog-header'>
        <div class='search-controls'>
          <el-input
            v-model='routeSearchKeyword'
            placeholder='搜索路线名称'
            size='small'
            style='width: 200px; margin-right: 12px;'
            clearable
            @keyup.enter='handleRouteSearch'
          />

          <el-select v-model='routeStatusFilter' placeholder='筛选状态' size='small'
                     style='width: 150px; margin-right: 12px;' clearable>
            <el-option label='全部状态' value=''></el-option>
            <el-option label='正常运营' value='normal'></el-option>
            <el-option label='繁忙' value='busy'></el-option>
            <el-option label='维护中' value='maintenance'></el-option>
          </el-select>

          <!-- 查询和重置按钮 -->
          <el-button type='primary' size='small' @click='handleRouteSearch' icon='Search' style='margin-right: 8px;'>
            查询
          </el-button>
          <el-button size='small' @click='handleRouteReset' icon='Refresh'>
            重置
          </el-button>
  </div>
      </div>

      <el-table
        :data='paginatedRoutes'
        size='small'
        max-height='400'
        height='400'
        class='route-table'
        :stripe='false'
        style='width: 100%'
        :header-cell-style='routeHeaderCellStyle'
        :row-style='routeRowStyle'
        :cell-style='routeCellStyle'
      >
        <el-table-column prop='name' label='路线名称' min-width='100' />
        <el-table-column prop='description' label='线路描述' min-width='200' />
        <el-table-column prop='vehicles' label='车辆数' min-width='80' />
        <el-table-column prop='status' label='状态' min-width='100'>
          <template #default='{ row }'>
            <el-tag :type='getRouteStatusTagType(row.status)' size='small'>
              {{ getRouteStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop='distance' label='总长度' min-width='100' />
        <el-table-column label='线路颜色' min-width='80'>
          <template #default='{ row }'>
            <div class='route-color-indicator' :style="{ backgroundColor: row.color }"></div>
          </template>
        </el-table-column>
        <el-table-column label='操作' min-width='120'>
          <template #default='{ row }'>
            <el-button
              size='small'
              type='primary'
              icon='Location'
              @click='locateRouteOnMap(row)'
            >
              定位
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class='pagination-container'>
        <pagination
          v-show='filteredAllRoutes.length > 0'
          :total='filteredAllRoutes.length'
          v-model:page='routeCurrentPage'
          v-model:limit='routePageSize'
          @pagination='handleRoutePaginationChange'
          background
          layout='total, sizes, prev, pager, next, jumper'
        />
      </div>

      <template #footer>
        <el-button style='background-color: rgba(7,20,61,0.26)' @click='routeListDialogVisible = false'>关闭</el-button>
      </template>
    </el-dialog>

    <!-- 告警处理表单弹窗 -->
    <el-dialog
      v-model='alertProcessDialogVisible'
      :title='alertProcessTitle'
      width='500px'
      :modal='true'
      :close-on-click-modal='false'
      class='alert-process-dialog'
    >
      <el-form
        ref='alertProcessForm'
        :model='alertProcessData'
        :rules='alertProcessRules'
        label-width='100px'
        label-position='left'
      >
        <el-form-item label='告警标题'>
          <el-input v-model='currentProcessAlert.title' readonly />
        </el-form-item>

        <el-form-item label='处理方式' prop='method' required>
          <el-select v-model='alertProcessData.method' placeholder='请选择处理方式' style='width: 100%'>
            <el-option label='现场处理' value='onsite'></el-option>
            <el-option label='远程处理' value='remote'></el-option>
            <el-option label='调度处理' value='dispatch'></el-option>
            <el-option label='转发处理' value='forward'></el-option>
            <el-option label='暂时忽略' value='ignore'></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label='处理人' prop='handler' required>
          <el-input v-model='alertProcessData.handler' placeholder='请输入处理人姓名' />
        </el-form-item>

        <el-form-item label='处理说明' prop='description'>
          <el-input
            v-model='alertProcessData.description'
            type='textarea'
            :rows='4'
            placeholder='请描述处理过程和结果（可选）...'
            maxlength='500'
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='处理时间'>
          <el-input v-model='alertProcessData.processTime' readonly />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click='alertProcessDialogVisible = false'>取消</el-button>
        <el-button
          v-if="alertProcessType === 'start'"
          type='warning'
          @click='confirmStartProcess'
          :loading='processLoading'
        >
          开始处理
        </el-button>
        <el-button
          v-if="alertProcessType === 'complete'"
          type='success'
          @click='confirmCompleteProcess'
          :loading='processLoading'
        >
          完成处理
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  createOptimizedMap,
  addOptimizedControls,
  createOptimizedMarker,
  suppressMapWarnings,
  handleMapError
} from '@/utils/mapConfig';
import {
  TrendCharts,
  Odometer,
  Warning,
  WarningFilled,
  InfoFilled,
  Filter,
  DataAnalysis,
  Close
} from '@element-plus/icons-vue';
import * as echarts from 'echarts';

/**
 * 地图相关对象 - 在Vue组件外定义，完全避免响应式管理
 *
 * 优势：
 * 1. 避免Vue监听大量地图对象属性变化，提升性能
 * 2. 减少内存占用，Vue不会为这些对象创建代理
 * 3. 避免地图操作时的卡顿和延迟
 * 4. 符合高德地图官方推荐的使用方式
 */
let map = null;
let stationMarkers = [];
let routePolylines = [];
let vehicleMarkers = [];
let alertMarkers = [];


// 响应式数据
const currentTime = ref('');
const currentDate = ref('');
const selectedRoute = ref('');
const selectedStatus = ref('');
const selectedAlertType = ref(''); // 新增：选中的告警类型
const selectedStation = ref(''); // 新增：选中的站点
const displayOptions = ref(['stations', 'routes', 'alerts', 'realtime']);
const sidePanelsHidden = ref(false); // 默认显示侧边面板
const filterPanelVisible = ref(false); // 筛选面板显示状态，默认隐藏

// 车辆列表弹窗相关数据
const vehicleListDialogVisible = ref(false);
const searchKeyword = ref('');
const dialogRouteFilter = ref('');
const dialogStatusFilter = ref('');
const dialogDeptFilter = ref(''); // 组织机构筛选

// 实际用于筛选的条件（只有点击查询时才更新）
const activeSearchKeyword = ref('');
const activeRouteFilter = ref('');
const activeStatusFilter = ref('');
const activeDeptFilter = ref('');

// 组织机构树数据
const deptTreeData = reactive([
  {
    id: 0,
    name: '全部组织机构',
    children: [
      { id: 1, name: '第一车队' },
      { id: 2, name: '第二车队' },
      { id: 3, name: '第三车队' },
      { id: 4, name: '第四车队' }
    ]
  }
]);

// 告警列表弹窗相关数据
const alertListDialogVisible = ref(false);
const alertSearchKeyword = ref('');
const alertLevelFilter = ref('');
const alertStatusFilter = ref('');

// 实际用于筛选告警的条件
const activeAlertSearchKeyword = ref('');
const activeAlertLevelFilter = ref('');
const activeAlertStatusFilter = ref('');

// 地图点位信息弹窗相关数据
const mapInfoDialogVisible = ref(false);
const mapInfoType = ref(''); // 'vehicle', 'station', 'route', 'alert'
const mapInfoTitle = ref('');
const mapInfoData = ref({});

// 告警处理表单弹窗相关数据
const alertProcessDialogVisible = ref(false);
const alertProcessTitle = ref('');
const alertProcessType = ref(''); // 'start' or 'complete'
const currentProcessAlert = ref({});
const processLoading = ref(false);
const alertProcessForm = ref(null);

// 告警处理表单数据
const alertProcessData = reactive({
  method: '',
  handler: '',
  description: '',
  processTime: ''
});

// 表单验证规则
const alertProcessRules = {
  method: [
    { required: true, message: '请选择处理方式', trigger: 'change' }
  ],
  handler: [
    { required: true, message: '请输入处理人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '处理人姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '处理说明长度不能超过 500 个字符', trigger: 'blur' }
  ]
};

// 告警分页相关数据
const alertCurrentPage = ref(1);
const alertPageSize = ref(20);

// 分页相关数据
const currentPage = ref(1);
const pageSize = ref(10);

// 概况数据
const overviewData = reactive({
  totalVehicles: 286,
  activeVehicles: 234,
  totalRoutes: 45
});

// 车辆状态数据 - 使用项目标准色彩
const vehicleStatusData = reactive([
  { name: '运营中', value: 186, color: '#67c23a' }, // 成功绿色
  { name: '停靠中', value: 48, color: '#e6a23c' }, // 警告黄色
  { name: '维修中', value: 32, color: '#f56c6c' }, // 危险红色
  { name: '故障', value: 20, color: '#909399' } // 信息灰色
]);

// 路线详细数据
const displayRoutes = reactive([
  { id: 1, name: '1路', description: '南昌火车站 ↔ 八一广场', vehicles: 12, status: 'normal', distance: '15.6km', color: '#67c23a' },
  { id: 2, name: '8路', description: '南昌大学 ↔ 红谷滩', vehicles: 10, status: 'busy', distance: '18.2km', color: '#409eff' },
  { id: 3, name: '15路', description: '高新区 ↔ 东湖区', vehicles: 8, status: 'normal', distance: '12.8km', color: '#e6a23c' },
  { id: 4, name: '22路', description: '昌北机场 ↔ 青山湖', vehicles: 6, status: 'maintenance', distance: '25.4km', color: '#f56c6c' },
  { id: 5, name: '6路', description: '经开区 ↔ 西湖区', vehicles: 9, status: 'normal', distance: '14.3km', color: '#909399' },
  { id: 6, name: '13路', description: '青山湖 ↔ 昌北机场', vehicles: 7, status: 'busy', distance: '22.1km', color: '#e6a23c' },
  { id: 7, name: '25路', description: '红谷滩 ↔ 南昌大学', vehicles: 5, status: 'normal', distance: '16.7km', color: '#67c23a' },
  { id: 8, name: '31路', description: '西湖区 ↔ 高新区', vehicles: 8, status: 'normal', distance: '13.9km', color: '#409eff' }
]);

// 路线列表弹窗相关数据
const routeListDialogVisible = ref(false);
const routeSearchKeyword = ref('');
const routeStatusFilter = ref('');

// 实际用于筛选路线的条件
const activeRouteSearchKeyword = ref('');
const activeRouteStatusFilter = ref('');

// 路线分页相关数据
const routeCurrentPage = ref(1);
const routePageSize = ref(10);

// 线路选项
const routeOptions = reactive([
  { id: 1, label: '1路' },
  { id: 2, label: '8路' },
  { id: 3, label: '15路' },
  { id: 4, label: '22路' },
  { id: 5, label: '6路' },
  { id: 6, label: '13路' },
  { id: 7, label: '25路' },
  { id: 8, label: '31路' }
]);

// 站点选项
const stationOptions = reactive([
  { id: 1, label: '南昌火车站' },
  { id: 2, label: '八一广场' },
  { id: 3, label: '南昌大学' },
  { id: 4, label: '红谷滩' },
  { id: 5, label: '高新区' },
  { id: 6, label: '东湖区' },
  { id: 7, label: '昌北机场' },
  { id: 8, label: '青山湖' },
  { id: 9, label: '经开区' },
  { id: 10, label: '西湖区' }
]);

// 告警数据
const alertData = reactive([
  {
    id: 1,
    level: 'high',
    title: '车辆故障',
    description: '赣A12345 发动机异常',
    time: '2分钟前',
    lat: 28.6765,
    lng: 115.8921,
    status: 'pending' // pending: 待处理, processing: 处理中, resolved: 已处理
  },
  {
    id: 2,
    level: 'medium',
    title: '延误预警',
    description: '8路线延误超过5分钟',
    time: '5分钟前',
    lat: 28.6825,
    lng: 115.9075,
    status: 'processing'
  },
  {
    id: 3,
    level: 'low',
    title: '维护提醒',
    description: '15路 赣B67890 需要保养',
    time: '12分钟前',
    lat: 28.6695,
    lng: 115.9145,
    status: 'resolved',
    processInfo: {
      method: 'dispatch',
      methodText: '调度处理',
      handler: '张维修',
      description: '已安排车辆回厂进行定期保养，预计明日完成保养后重新投入使用。保养项目包括发动机检查、刹车系统检测、轮胎更换等。',
      startTime: '2024-01-15 09:30:00',
      completeTime: '2024-01-15 10:15:00'
    }
  },
  {
    id: 4,
    level: 'high',
    title: '紧急事故',
    description: '22路 赣B98765 发生交通事故',
    time: '15分钟前',
    lat: 28.6835,
    lng: 115.8865,
    status: 'pending'
  },
  {
    id: 5,
    level: 'medium',
    title: '设备故障',
    description: '6路 赣C11223 GPS设备离线',
    time: '18分钟前',
    lat: 28.6715,
    lng: 115.9085,
    status: 'processing'
  },
  {
    id: 6,
    level: 'low',
    title: '路线拥堵',
    description: '13路沿线道路拥堵严重',
    time: '25分钟前',
    lat: 28.6645,
    lng: 115.8795,
    status: 'pending'
  },
  {
    id: 7,
    level: 'high',
    title: '超速告警',
    description: '25路 赣D77889 超速行驶',
    time: '28分钟前',
    lat: 28.6895,
    lng: 115.9125,
    status: 'resolved',
    processInfo: {
      method: 'remote',
      methodText: '远程处理',
      handler: '李调度',
      description: '已通过车载通讯系统联系驾驶员，提醒其注意行车安全，控制车速。驾驶员承认超速并承诺严格按照限速行驶。已记录在案并进行安全教育。',
      startTime: '2024-01-15 08:45:00',
      completeTime: '2024-01-15 08:52:00'
    }
  },

  {
    id: 8,
    level: 'low',
    title: '油量不足',
    description: '1路 赣E33445 油量低于20%',
    time: '35分钟前',
    lat: 28.6735,
    lng: 115.8995,
    status: 'pending'
  },
  {
    id: 9,
    level: 'high',
    title: '驾驶员异常',
    description: '8路 赣E66778 驾驶员疲劳驾驶',
    time: '38分钟前',
    lat: 28.6785,
    lng: 115.9175,
    status: 'pending'
  }
]);

// 车辆列表数据
const displayVehicles = reactive([
  {
    id: 1,
    plateNumber: '赣A12345',
    routeName: '1路',
    status: 'running',
    speed: 35,

    lat: 28.6765,
    lng: 115.8921,
    deptId: 1,
    deptName: '第一车队'
  },
  {
    id: 2,
    plateNumber: '赣A67890',
    routeName: '8路',
    status: 'stopped',
    speed: 0,

    lat: 28.6825,
    lng: 115.8975,
    deptId: 1,
    deptName: '第一车队'
  },
  {
    id: 3,
    plateNumber: '赣B54321',
    routeName: '15路',
    status: 'running',
    speed: 42,

    lat: 28.6695,
    lng: 115.9045,
    deptId: 2,
    deptName: '第二车队'
  },
  {
    id: 4,
    plateNumber: '赣B98765',
    routeName: '22路',
    status: 'maintenance',
    speed: 0,

    lat: 28.6835,
    lng: 115.8865,
    deptId: 2,
    deptName: '第二车队'
  },
  {
    id: 5,
    plateNumber: '赣C11223',
    routeName: '6路',
    status: 'running',
    speed: 38,

    lat: 28.6715,
    lng: 115.9085,
    deptId: 3,
    deptName: '第三车队'
  },
  {
    id: 6,
    plateNumber: '赣C44556',
    routeName: '13路',
    status: 'stopped',
    speed: 0,

    lat: 28.6645,
    lng: 115.8795,
    deptId: 3,
    deptName: '第三车队'
  },
  {
    id: 7,
    plateNumber: '赣D77889',
    routeName: '25路',
    status: 'running',
    speed: 32,

    lat: 28.6895,
    lng: 115.9125,
    deptId: 4,
    deptName: '第四车队'
  },
  {
    id: 8,
    plateNumber: '赣D99012',
    routeName: '31路',
    status: 'fault',
    speed: 0,

    lat: 28.6585,
    lng: 115.8755,
    deptId: 4,
    deptName: '第四车队'
  },
  {
    id: 9,
    plateNumber: '赣E33445',
    routeName: '1路',
    status: 'running',
    speed: 28,

    lat: 28.6735,
    lng: 115.8995,
    deptId: 1,
    deptName: '第一车队'
  },
  {
    id: 10,
    plateNumber: '赣E66778',
    routeName: '8路',
    status: 'running',
    speed: 45,

    lat: 28.6785,
    lng: 115.9175,
    deptId: 2,
    deptName: '第二车队'
  }
]);

const vehicleStatusChart = ref(null);
// 站点数据
const busStations = reactive([
  { id: 1, name: '南昌火车站', lng: 115.8921, lat: 28.6765 },
  { id: 2, name: '八一广场', lng: 115.9075, lat: 28.6825 },
  { id: 3, name: '南昌大学', lng: 115.9145, lat: 28.6695 },
  { id: 4, name: '红谷滩', lng: 115.8865, lat: 28.6835 },
  { id: 5, name: '高新区', lng: 115.8795, lat: 28.6645 },
  { id: 6, name: '东湖区', lng: 115.9235, lat: 28.6895 },
  { id: 7, name: '昌北机场', lng: 115.8755, lat: 28.6585 },
  { id: 8, name: '青山湖', lng: 115.9195, lat: 28.6735 },
  { id: 9, name: '经开区', lng: 115.9325, lat: 28.6945 },
  { id: 10, name: '西湖区', lng: 115.8685, lat: 28.6525 }
]);

// 路线数据
const busRoutes = reactive([
  {
    id: 1,
    name: '1路',
    color: '#67c23a',
    stations: [1, 2], // 南昌火车站 ↔ 八一广场
    path: [
      [115.8921, 28.6765],
      [115.8998, 28.6795],
      [115.9075, 28.6825]
    ]
  },
  {
    id: 8,
    name: '8路',
    color: '#409eff',
    stations: [3, 4], // 南昌大学 ↔ 红谷滩
    path: [
      [115.9145, 28.6695],
      [115.9005, 28.6765],
      [115.8865, 28.6835]
    ]
  },
  {
    id: 15,
    name: '15路',
    color: '#e6a23c',
    stations: [5, 6], // 高新区 ↔ 东湖区
    path: [
      [115.8795, 28.6645],
      [115.9015, 28.6770],
      [115.9235, 28.6895]
    ]
  },
  {
    id: 22,
    name: '22路',
    color: '#f56c6c',
    stations: [7, 8], // 昌北机场 ↔ 青山湖
    path: [
      [115.8755, 28.6585],
      [115.8975, 28.6660],
      [115.9195, 28.6735]
    ]
  }
]);



// 时间更新
const updateTime = () => {
  const now = new Date();
  currentTime.value = now.toLocaleTimeString('zh-CN', { hour12: false });
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  });
};

// 初始化地图
const initMap = () => {
  try {
    // 抑制警告
    suppressMapWarnings();

    // 创建高德地图实例
    map = createOptimizedMap('big-screen-map', {
      zoom: 12,
      center: [115.8921, 28.6765], // 南昌坐标
      mapStyle: 'amap://styles/dark' // 使用暗色主题匹配大屏风格
    });

    // 添加控件
    addOptimizedControls(map);

    // 添加车辆标记
    addVehicleMarkers();

    // 根据显示选项添加站点和路线
    updateMapDisplay();

    // 如果启用实时刷新
    if (displayOptions.value.includes('realtime')) {
      setInterval(updateVehiclePositions, 60000); // 改为1分钟（60秒）
    }

    console.log('地图初始化成功');
  } catch (error) {
    handleMapError(error, '地图初始化');
  }
};

// 添加车辆标记
const addVehicleMarkers = () => {
  if (!map) return;

  // 清除现有标记
  vehicleMarkers.forEach(marker => {
    map.remove(marker);
  });
  vehicleMarkers = [];

  displayVehicles.forEach(vehicle => {
    if (selectedRoute.value && vehicle.routeName !== getRouteNameById(selectedRoute.value)) return;
    if (selectedStatus.value && vehicle.status !== selectedStatus.value) return;

    // 创建车辆标记
    const marker = createOptimizedMarker(
      [vehicle.lng, vehicle.lat],
      `${vehicle.plateNumber} - ${vehicle.routeName}`,
      {
        // 根据车辆状态设置不同的颜色
        icon: createVehicleIcon(vehicle.status),
        clickable: true
      }
    );

    // 为标记添加车辆ID，用于后续识别
    marker.vehicleId = vehicle.id;

    // 设置车辆标记的初始层级
    marker.setzIndex(100);

    // 添加点击事件显示车辆信息
    marker.on('click', () => {
      showMapInfoDialog('vehicle', `车辆信息 - ${vehicle.plateNumber}`, vehicle);
    });

    map.add(marker);
    vehicleMarkers.push(marker);
  });
};

// 更新地图显示内容
const updateMapDisplay = () => {
  if (!map) return;

  // 根据选项显示/隐藏站点
  if (displayOptions.value.includes('stations')) {
    showBusStations();
  } else {
    hideBusStations();
  }

  // 根据选项显示/隐藏路线
  if (displayOptions.value.includes('routes')) {
    showBusRoutes();
  } else {
    hideBusRoutes();
  }

  // 根据选项显示/隐藏告警点
  if (displayOptions.value.includes('alerts')) {
    showAlertPoints();
  } else {
    hideAlertPoints();
  }
};

// 显示公交站点
const showBusStations = () => {
  busStations.forEach(station => {
    // 根据选中的站点进行筛选
    if (selectedStation.value && station.name !== getStationNameById(selectedStation.value)) return;

    const marker = createOptimizedMarker(
      [station.lng, station.lat],
      station.name,
      {
        icon: createStationIcon(),
        clickable: true
      }
    );

    // 设置站点标记的初始层级（站点层级最低）
    marker.setzIndex(50);

    // 添加站点信息窗口
    marker.on('click', () => {
      showMapInfoDialog('station', `站点信息 - ${station.name}`, station);
    });

    map.add(marker);
    stationMarkers.push(marker);
  });
};

// 隐藏公交站点
const hideBusStations = () => {
  stationMarkers.forEach(marker => {
    map.remove(marker);
  });
  stationMarkers = [];
};

// 显示公交路线
const showBusRoutes = () => {
  busRoutes.forEach(route => {
    const polyline = new AMap.Polyline({
      path: route.path,
      strokeColor: route.color,
      strokeWeight: 4,
      strokeOpacity: 0.8,
      strokeStyle: 'solid'
    });

    // 添加路线点击事件
    polyline.on('click', () => {
      const routeData = {
        ...route,
        stationNames: getRouteStationNames(route)
      };
      showMapInfoDialog('route', `线路信息 - ${route.name}`, routeData);
    });

    map.add(polyline);
    routePolylines.push(polyline);
  });
};

// 隐藏公交路线
const hideBusRoutes = () => {
  routePolylines.forEach(polyline => {
    map.remove(polyline);
  });
  routePolylines = [];
};

// 显示告警点
const showAlertPoints = () => {
  alertData.forEach(alert => {
    // 过滤掉已处理的告警
    if (alert.status === 'resolved') return;

    // 根据选中的告警类型进行筛选
    if (selectedAlertType.value && alert.level !== selectedAlertType.value) return;

    const marker = createOptimizedMarker(
      [alert.lng, alert.lat],
      `${alert.title} - ${alert.description}`,
      {
        icon: createAlertIcon(alert.level),
        clickable: true
      }
    );

    // 为标记添加告警ID，用于后续识别
    marker.alertId = alert.id;

    // 设置告警标记的初始层级（告警比车辆优先级高）
    marker.setzIndex(200);

    // 添加点击事件显示告警信息
    marker.on('click', () => {
      showMapInfoDialog('alert', `告警信息 - ${alert.title}`, alert);
    });

    map.add(marker);
    alertMarkers.push(marker);
  });
};

// 隐藏告警点
const hideAlertPoints = () => {
  alertMarkers.forEach(marker => {
    map.remove(marker);
  });
  alertMarkers = [];
};

// 创建告警图标
const createAlertIcon = (level) => {
  const colorMap = {
    high: '#ff4757',    // 红色
    medium: '#ffa502',  // 橙色
    low: '#747d8c'      // 灰色
  };

  const color = colorMap[level] || '#747d8c';

  const svgIcon = `
    <svg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <circle cx='12' cy='12' r='10' fill='${color}' stroke='#ffffff' stroke-width='2'/>
      <path d='M12 8v4m0 4h.01' stroke='#ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/>
    </svg>
  `;

  return new AMap.Icon({
    image: 'data:image/svg+xml;base64,' + btoa(svgIcon),
    size: new AMap.Size(24, 24),
    imageSize: new AMap.Size(24, 24),
    imageOffset: new AMap.Pixel(0, 0)
  });
};

// 创建站点图标
const createStationIcon = () => {
  const svgIcon = `
    <svg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <circle cx='8' cy='8' r='7' fill='#409eff' stroke='white' stroke-width='2'/>
      <circle cx='8' cy='8' r='3' fill='white'/>
    </svg>
  `;

  return new AMap.Icon({
    image: `data:image/svg+xml;base64,${btoa(svgIcon)}`,
    size: [16, 16],
    imageSize: [16, 16]
  });
};

// 获取路线站点名称
const getRouteStationNames = (route) => {
  const stationNames = route.stations.map(stationId => {
    const station = busStations.find(s => s.id === stationId);
    return station ? station.name : '';
  }).filter(name => name);

  return stationNames.join(' ↔ ');
};

// 初始化图表
const initCharts = () => {
  // 车辆状态饼图
  if (vehicleStatusChart.value) {
    const statusChart = echarts.init(vehicleStatusChart.value);
    const statusOption = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(15, 23, 42, 0.9)',
        borderColor: '#409eff',
        textStyle: {
          color: '#f8fafc'
        }
      },
      legend: {
        show: false // 隐藏默认图例，使用自定义图例
      },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'], // 环形饼图
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}',
          color: '#f8fafc',
          fontSize: 12,
          fontWeight: 'bold'
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          lineStyle: {
            color: '#f8fafc',
            width: 1
          }
        },
        data: vehicleStatusData.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color,
            borderWidth: 2,
            borderColor: 'rgba(255, 255, 255, 0.1)'
          }
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 20,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.8)',
            borderWidth: 3,
            borderColor: 'rgba(255, 255, 255, 0.3)'
          },
          scale: true,
          scaleSize: 5
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function(idx) {
          return Math.random() * 200;
        }
      }]
    };
    statusChart.setOption(statusOption);

    // 窗口大小变化时重新调整图表
    window.addEventListener('resize', () => {
      statusChart.resize();
    });
  }
};

// 创建车辆图标
const createVehicleIcon = (status) => {
  const colors = {
    running: '#67c23a',
    stopped: '#e6a23c',
    maintenance: '#f56c6c',
    fault: '#909399'
  };

  const color = colors[status] || '#409eff';

  // 创建SVG图标
  const svgIcon = `
    <svg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <rect x='2' y='8' width='20' height='10' rx='2' fill='${color}' stroke='white' stroke-width='1'/>
      <circle cx='7' cy='16' r='2' fill='white' stroke='${color}' stroke-width='1'/>
      <circle cx='17' cy='16' r='2' fill='white' stroke='${color}' stroke-width='1'/>
      <rect x='4' y='10' width='16' height='4' rx='1' fill='white' opacity='0.8'/>
    </svg>
  `;

  return new AMap.Icon({
    image: `data:image/svg+xml;base64,${btoa(svgIcon)}`,
    size: [24, 24],
    imageSize: [24, 24]
  });
};

// 工具函数
const getStatusText = (status) => {
  const statusMap = {
    running: '运营中',
    stopped: '停靠中',
    maintenance: '维修中',
    fault: '故障'
  };
  return statusMap[status] || status;
};

const getRouteNameById = (id) => {
  const route = routeOptions.find(r => r.id === parseInt(id));
  return route ? route.label : '';
};

const getStationNameById = (id) => {
  const station = stationOptions.find(s => s.id === parseInt(id));
  return station ? station.label : '';
};

// 事件处理
const handleRouteFilter = () => {
  addVehicleMarkers();
};

const handleStatusFilter = () => {
  addVehicleMarkers();
};

const handleAlertTypeFilter = () => {
  // 重新显示告警点，根据选中的告警类型进行筛选
  if (displayOptions.value.includes('alerts')) {
    hideAlertPoints();
    showAlertPoints();
  }
};

const handleStationFilter = () => {
  // 重新显示站点，根据选中的站点进行筛选
  if (displayOptions.value.includes('stations')) {
    hideBusStations();
    showBusStations();
  }
};

const handleDisplayOptionsChange = () => {
  updateMapDisplay();
};

const zoomIn = () => {
  if (map) map.zoomIn();
};

const zoomOut = () => {
  if (map) map.zoomOut();
};

const resetView = () => {
  if (map) {
    map.setCenter([115.8921, 28.6765]); // 南昌坐标
    map.setZoom(12);
  }
};

const refreshVehicleData = () => {
  // 模拟数据更新
  displayVehicles.forEach(vehicle => {
    if (vehicle.status === 'running') {
      vehicle.speed = Math.floor(Math.random() * 20 + 20);
    }
  });
  addVehicleMarkers();
};

const updateVehiclePositions = () => {
  // 模拟车辆位置更新
  displayVehicles.forEach(vehicle => {
    if (vehicle.status === 'running') {
      vehicle.lat += (Math.random() - 0.5) * 0.001;
      vehicle.lng += (Math.random() - 0.5) * 0.001;
    }
  });
  addVehicleMarkers();
};

const toggleFullscreen = () => {
  if (document.fullscreenElement) {
    document.exitFullscreen();
  } else {
    document.documentElement.requestFullscreen();
  }
};

// 切换筛选面板显示状态
const toggleFilterPanel = () => {
  filterPanelVisible.value = !filterPanelVisible.value;
};

// 车辆列表弹窗功能
const showAllVehicles = () => {
  vehicleListDialogVisible.value = true;
};

// 过滤车辆列表
const filteredAllVehicles = computed(() => {
  let filtered = [...displayVehicles];

  // 按关键词搜索
  if (activeSearchKeyword.value) {
    const keyword = activeSearchKeyword.value.toLowerCase();
    filtered = filtered.filter(vehicle =>
      vehicle.plateNumber.toLowerCase().includes(keyword) ||
      vehicle.routeName.toLowerCase().includes(keyword)
    );
  }

  // 按组织机构筛选
  if (activeDeptFilter.value && activeDeptFilter.value !== 0) {
    filtered = filtered.filter(vehicle => vehicle.deptId === activeDeptFilter.value);
  }

  // 按线路筛选
  if (activeRouteFilter.value) {
    filtered = filtered.filter(vehicle => vehicle.routeName === activeRouteFilter.value);
  }

  // 按状态筛选
  if (activeStatusFilter.value) {
    filtered = filtered.filter(vehicle => vehicle.status === activeStatusFilter.value);
  }

  return filtered;
});

// 分页后的车辆列表
const paginatedVehicles = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredAllVehicles.value.slice(start, end);
});

// 未处理的告警列表（用于侧边显示）
const unresolvedAlerts = computed(() => {
  return alertData.filter(alert => alert.status !== 'resolved');
});

// 定位车辆到地图
const locateVehicleOnMap = (vehicle) => {
  if (map) {
    map.setCenter([vehicle.lng, vehicle.lat]);
    map.setZoom(16);

    // 关闭弹窗
    vehicleListDialogVisible.value = false;

    // 让对应的车辆点位闪烁
    blinkVehicleMarker(vehicle);

    // 显示定位成功提示
    ElMessage.success(`已定位到车辆：${vehicle.plateNumber}`);
  }
};

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusTagMap = {
    running: 'success',
    stopped: 'warning',
    maintenance: 'danger',
    fault: 'info'
  };
  return statusTagMap[status] || 'info';
};

// 分页处理函数
const handlePaginationChange = (pagination) => {
  currentPage.value = pagination.page;
  pageSize.value = pagination.limit;
};

// 查询按钮事件
const handleSearch = () => {
  // 将用户输入的条件同步到实际筛选条件
  activeSearchKeyword.value = searchKeyword.value;
  activeDeptFilter.value = dialogDeptFilter.value;
  activeRouteFilter.value = dialogRouteFilter.value;
  activeStatusFilter.value = dialogStatusFilter.value;

  // 重置到第一页
  currentPage.value = 1;
};

// 重置按钮事件
const handleReset = () => {
  // 清空用户输入
  searchKeyword.value = '';
  dialogDeptFilter.value = '';
  dialogRouteFilter.value = '';
  dialogStatusFilter.value = '';

  // 清空实际筛选条件
  activeSearchKeyword.value = '';
  activeDeptFilter.value = '';
  activeRouteFilter.value = '';
  activeStatusFilter.value = '';

  // 重置到第一页
  currentPage.value = 1;
};

// 告警弹窗功能
const showAllAlerts = () => {
  alertListDialogVisible.value = true;
};

// 刷新告警数据
const refreshAlertData = () => {
  // 这里可以添加刷新告警数据的逻辑
  console.log('刷新告警数据');
};

// 过滤告警列表
const filteredAllAlerts = computed(() => {
  let filtered = [...alertData];

  // 按关键词搜索
  if (activeAlertSearchKeyword.value) {
    const keyword = activeAlertSearchKeyword.value.toLowerCase();
    filtered = filtered.filter(alert =>
      alert.title.toLowerCase().includes(keyword) ||
      alert.description.toLowerCase().includes(keyword)
    );
  }

  // 按级别筛选
  if (activeAlertLevelFilter.value) {
    filtered = filtered.filter(alert => alert.level === activeAlertLevelFilter.value);
  }

  // 按状态筛选
  if (activeAlertStatusFilter.value) {
    filtered = filtered.filter(alert => alert.status === activeAlertStatusFilter.value);
  }

  return filtered;
});

// 分页后的告警列表
const paginatedAlerts = computed(() => {
  const start = (alertCurrentPage.value - 1) * alertPageSize.value;
  const end = start + alertPageSize.value;
  return filteredAllAlerts.value.slice(start, end);
});

// 告警查询按钮事件
const handleAlertSearch = () => {
  // 将用户输入的条件同步到实际筛选条件
  activeAlertSearchKeyword.value = alertSearchKeyword.value;
  activeAlertLevelFilter.value = alertLevelFilter.value;
  activeAlertStatusFilter.value = alertStatusFilter.value;

  // 重置到第一页
  alertCurrentPage.value = 1;
};

// 告警重置按钮事件
const handleAlertReset = () => {
  // 清空用户输入
  alertSearchKeyword.value = '';
  alertLevelFilter.value = '';
  alertStatusFilter.value = '';

  // 清空实际筛选条件
  activeAlertSearchKeyword.value = '';
  activeAlertLevelFilter.value = '';
  activeAlertStatusFilter.value = '';

  // 重置到第一页
  alertCurrentPage.value = 1;
};

// 路线弹窗功能
const showAllRoutes = () => {
  routeListDialogVisible.value = true;
};

// 刷新路线数据
const refreshRouteData = () => {
  // 这里可以添加刷新路线数据的逻辑
  console.log('刷新路线数据');
};

// 过滤路线列表
const filteredAllRoutes = computed(() => {
  let filtered = [...displayRoutes];

  // 按关键词搜索
  if (activeRouteSearchKeyword.value) {
    const keyword = activeRouteSearchKeyword.value.toLowerCase();
    filtered = filtered.filter(route =>
      route.name.toLowerCase().includes(keyword) ||
      route.description.toLowerCase().includes(keyword)
    );
  }

  // 按状态筛选
  if (activeRouteStatusFilter.value) {
    filtered = filtered.filter(route => route.status === activeRouteStatusFilter.value);
  }

  return filtered;
});

// 分页后的路线列表
const paginatedRoutes = computed(() => {
  const start = (routeCurrentPage.value - 1) * routePageSize.value;
  const end = start + routePageSize.value;
  return filteredAllRoutes.value.slice(start, end);
});

// 路线查询按钮事件
const handleRouteSearch = () => {
  // 将用户输入的条件同步到实际筛选条件
  activeRouteSearchKeyword.value = routeSearchKeyword.value;
  activeRouteStatusFilter.value = routeStatusFilter.value;

  // 重置到第一页
  routeCurrentPage.value = 1;
};

// 路线重置按钮事件
const handleRouteReset = () => {
  // 清空用户输入
  routeSearchKeyword.value = '';
  routeStatusFilter.value = '';

  // 清空实际筛选条件
  activeRouteSearchKeyword.value = '';
  activeRouteStatusFilter.value = '';

  // 重置到第一页
  routeCurrentPage.value = 1;
};

// 路线分页处理函数
const handleRoutePaginationChange = (pagination) => {
  routeCurrentPage.value = pagination.page;
  routePageSize.value = pagination.limit;
};

// 获取路线状态标签类型
const getRouteStatusTagType = (status) => {
  const statusTagMap = {
    normal: 'success',
    busy: 'warning',
    maintenance: 'danger'
  };
  return statusTagMap[status] || 'info';
};

// 获取路线状态文本
const getRouteStatusText = (status) => {
  const statusTextMap = {
    normal: '正常运营',
    busy: '繁忙',
    maintenance: '维护中'
  };
  return statusTextMap[status] || '未知';
};

// 定位路线到地图
const locateRouteOnMap = (route) => {
  if (map) {
    // 找到对应的路线
    const routeData = busRoutes.find(r => r.name === route.name);
    if (routeData && routeData.path && routeData.path.length > 0) {
      // 计算路线的中心点
      const centerLng = routeData.path.reduce((sum, point) => sum + point[0], 0) / routeData.path.length;
      const centerLat = routeData.path.reduce((sum, point) => sum + point[1], 0) / routeData.path.length;

      map.setCenter([centerLng, centerLat]);
      map.setZoom(14);

      // 关闭弹窗
      routeListDialogVisible.value = false;

      // 显示定位成功提示
      ElMessage.success(`已定位到路线：${route.name}`);
    }
  }
};

// 从侧边面板定位路线
const locateRouteFromSidebar = (route) => {
  if (!map) {
    console.error('地图未初始化');
    return;
  }

  // 找到对应的路线
  const routeData = busRoutes.find(r => r.name === route.name);
  if (routeData && routeData.path && routeData.path.length > 0) {
    // 计算路线的中心点
    const centerLng = routeData.path.reduce((sum, point) => sum + point[0], 0) / routeData.path.length;
    const centerLat = routeData.path.reduce((sum, point) => sum + point[1], 0) / routeData.path.length;

    map.setCenter([centerLng, centerLat]);
    map.setZoom(14);

    // 显示定位成功的提示
    ElMessage.success(`已定位到路线：${route.name}`);
  }
};

// 告警分页处理函数
const handleAlertPaginationChange = (pagination) => {
  alertCurrentPage.value = pagination.page;
  alertPageSize.value = pagination.limit;
};

// 获取告警级别标签类型
const getAlertTagType = (level) => {
  const levelTagMap = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  };
  return levelTagMap[level] || 'info';
};

// 获取告警级别文本
const getAlertLevelText = (level) => {
  const levelTextMap = {
    high: '高级',
    medium: '中级',
    low: '低级'
  };
  return levelTextMap[level] || '未知';
};

// 获取告警状态标签类型
const getAlertStatusTagType = (status) => {
  const statusTagMap = {
    pending: 'danger',
    processing: 'warning',
    resolved: 'success'
  };
  return statusTagMap[status] || 'info';
};

// 获取告警状态文本
const getAlertStatusText = (status) => {
  const statusTextMap = {
    pending: '待处理',
    processing: '处理中',
    resolved: '已处理'
  };
  return statusTextMap[status] || '未知';
};

// 打开告警处理表单
const openAlertProcessForm = (alert, type) => {
  currentProcessAlert.value = alert;
  alertProcessType.value = type;

  if (type === 'start') {
    alertProcessTitle.value = '开始处理告警';
  } else {
    alertProcessTitle.value = '完成处理告警';
  }

  // 重置表单数据
  alertProcessData.method = '';
  alertProcessData.handler = '';
  alertProcessData.description = '';
  alertProcessData.processTime = new Date().toLocaleString('zh-CN');

  // 如果是完成处理，可以预填一些信息
  if (type === 'complete' && alert.processInfo) {
    alertProcessData.method = alert.processInfo.method || '';
    alertProcessData.handler = alert.processInfo.handler || '';
  }

  alertProcessDialogVisible.value = true;
};

// 开始处理告警
const handleAlertProcessing = (alert) => {
  openAlertProcessForm(alert, 'start');
};

// 直接完成处理告警（不弹出表单）
const directCompleteAlert = (alert) => {
  // 更新原始数据中的状态
  const originalAlert = alertData.find(item => item.id === alert.id);
  if (originalAlert) {
    originalAlert.status = 'resolved';

    // 更新处理信息，保留原有信息并添加完成时间
    if (originalAlert.processInfo) {
      originalAlert.processInfo.completeTime = new Date().toLocaleString('zh-CN');
    } else {
      // 如果没有处理信息，创建基本的完成记录
      originalAlert.processInfo = {
        method: 'manual',
        methodText: '手动完成',
        handler: '系统操作员',
        description: '告警已处理完成',
        completeTime: new Date().toLocaleString('zh-CN')
      };
    }
  }

  // 更新弹窗显示的数据
  if (mapInfoData.value.id === alert.id) {
    mapInfoData.value.status = 'resolved';
    mapInfoData.value.processInfo = originalAlert.processInfo;
  }

  // 从地图上移除已处理的告警点
  if (displayOptions.value.includes('alerts')) {
    hideAlertPoints();
    showAlertPoints();
  }

  ElMessage.success('告警处理完成');

  // 关闭地图信息弹窗
  setTimeout(() => {
    mapInfoDialogVisible.value = false;
  }, 1000);
};

// 确认开始处理
const confirmStartProcess = () => {
  alertProcessForm.value?.validate((valid) => {
    if (valid) {
      processLoading.value = true;

      // 模拟API调用
      setTimeout(() => {
        // 更新原始数据中的状态和处理信息
        const originalAlert = alertData.find(item => item.id === currentProcessAlert.value.id);
        if (originalAlert) {
          originalAlert.status = 'processing';
          originalAlert.processInfo = {
            method: alertProcessData.method,
            handler: alertProcessData.handler,
            description: alertProcessData.description,
            startTime: alertProcessData.processTime,
            methodText: getProcessMethodText(alertProcessData.method)
          };
        }

        // 更新弹窗显示的数据
        if (mapInfoData.value.id === currentProcessAlert.value.id) {
          mapInfoData.value.status = 'processing';
          mapInfoData.value.processInfo = originalAlert.processInfo;
        }

        processLoading.value = false;
        alertProcessDialogVisible.value = false;

        ElMessage.success('已开始处理该告警');
      }, 1000);
    }
  });
};

// 确认完成处理
const confirmCompleteProcess = () => {
  alertProcessForm.value?.validate((valid) => {
    if (valid) {
      processLoading.value = true;

      // 模拟API调用
      setTimeout(() => {
        // 更新原始数据中的状态和处理信息
        const originalAlert = alertData.find(item => item.id === currentProcessAlert.value.id);
        if (originalAlert) {
          originalAlert.status = 'resolved';
          if (!originalAlert.processInfo) {
            originalAlert.processInfo = {};
          }

          // 更新或添加完成处理的信息
          Object.assign(originalAlert.processInfo, {
            method: alertProcessData.method,
            handler: alertProcessData.handler,
            description: alertProcessData.description,
            completeTime: alertProcessData.processTime,
            methodText: getProcessMethodText(alertProcessData.method)
          });
        }

        // 更新弹窗显示的数据
        if (mapInfoData.value.id === currentProcessAlert.value.id) {
          mapInfoData.value.status = 'resolved';
          mapInfoData.value.processInfo = originalAlert.processInfo;
        }

        // 从地图上移除已处理的告警点
        if (displayOptions.value.includes('alerts')) {
          hideAlertPoints();
          showAlertPoints();
        }

        processLoading.value = false;
        alertProcessDialogVisible.value = false;

        ElMessage.success('告警处理完成');

        // 关闭地图信息弹窗
        setTimeout(() => {
          mapInfoDialogVisible.value = false;
        }, 1000);
      }, 1000);
    }
  });
};

// 获取处理方式文本
const getProcessMethodText = (method) => {
  const methodMap = {
    onsite: '现场处理',
    remote: '远程处理',
    dispatch: '调度处理',
    forward: '转发处理',
    ignore: '暂时忽略'
  };
  return methodMap[method] || method;
};

// 让车辆标记闪烁
const blinkVehicleMarker = (vehicle) => {
  // 找到对应的车辆标记
  const targetMarker = vehicleMarkers.find(marker => marker.vehicleId === vehicle.id);
  if (!targetMarker) return;

  // 直接设置层级到最上层，避免删除重新添加
  targetMarker.setzIndex(99999);

  // 创建闪烁效果
  let blinkCount = 0;
  const maxBlinks = 6; // 闪烁3次（显示-隐藏-显示-隐藏-显示-隐藏）

  const blinkInterval = setInterval(() => {
    if (blinkCount >= maxBlinks) {
      clearInterval(blinkInterval);
      // 确保最后是显示状态
      targetMarker.show();

      // 尝试再次设置层级
      targetMarker.setzIndex(99999);

      return;
    }

    // 交替显示和隐藏
    if (blinkCount % 2 === 0) {
      targetMarker.hide();
    } else {
      targetMarker.show();
    }

    blinkCount++;
  }, 300); // 每300ms切换一次
};

// 让告警标记闪烁
const blinkAlertMarker = (alert) => {
  // 找到对应的告警标记
  const targetMarker = alertMarkers.find(marker => marker.alertId === alert.id);
  if (!targetMarker) return;

  // 直接设置层级到最上层，避免删除重新添加
  targetMarker.setzIndex(99999);

  // 创建闪烁效果
  let blinkCount = 0;
  const maxBlinks = 6; // 闪烁3次（显示-隐藏-显示-隐藏-显示-隐藏）

  const blinkInterval = setInterval(() => {
    if (blinkCount >= maxBlinks) {
      clearInterval(blinkInterval);
      // 确保最后是显示状态
      targetMarker.show();

      // 尝试再次设置层级
      targetMarker.setzIndex(99999);

      return;
    }

    // 交替显示和隐藏
    if (blinkCount % 2 === 0) {
      targetMarker.hide();
    } else {
      targetMarker.show();
    }

    blinkCount++;
  }, 300); // 每300ms切换一次
};

// 从侧边面板定位告警（不关闭侧边面板）
const locateAlertFromSidebar = (alert) => {
  if (!map) {
    console.error('地图未初始化');
    return;
  }

  // 定位到告警位置
  map.setCenter([alert.lng, alert.lat]);
  map.setZoom(16);

  // 确保告警点显示
  if (!displayOptions.value.includes('alerts')) {
    displayOptions.value.push('alerts');
    showAlertPoints();
  }

  // 让对应的告警点位闪烁
  blinkAlertMarker(alert);

  // 显示定位成功的提示
  ElMessage.success(`已定位到告警：${alert.title}`);
};

// 从侧边栏处理告警
const handleSidebarAlertProcess = (alert) => {
  // 调用现有的告警处理方法
  openAlertProcessForm(alert, 'start');
};

// 从侧边面板定位车辆（不关闭侧边面板）
const locateVehicleFromSidebar = (vehicle) => {
  if (!map) {
    console.error('地图未初始化');
    return;
  }

  // 定位到车辆位置
  map.setCenter([vehicle.lng, vehicle.lat]);
  map.setZoom(16);

  // 让对应的车辆点位闪烁
  blinkVehicleMarker(vehicle);

  // 显示定位成功的提示
  ElMessage.success(`已定位到车辆：${vehicle.plateNumber}`);
};

// 显示地图信息弹窗
const showMapInfoDialog = (type, title, data) => {
  mapInfoType.value = type;
  mapInfoTitle.value = title;
  mapInfoData.value = data;
  mapInfoDialogVisible.value = true;
};

// 仅定位告警到地图（不弹出详情）
const locateAlertOnMapOnly = (alert) => {
  if (!map) {
    console.error('地图未初始化');
    return;
  }

  // 定位到告警位置
  map.setCenter([alert.lng, alert.lat]);
  map.setZoom(16);

  // 关闭告警列表弹窗
  alertListDialogVisible.value = false;

  // 确保告警点显示
  if (!displayOptions.value.includes('alerts')) {
    displayOptions.value.push('alerts');
    showAlertPoints();
  }

  // 让对应的告警点位闪烁
  blinkAlertMarker(alert);

  // 只显示定位成功的提示
  ElMessage.success(`已定位到告警：${alert.title}`);
};

// 显示告警详情
const showAlertDetail = (alert) => {
  // 不关闭告警列表弹窗，保持打开状态

  // 显示告警详情弹窗
  showMapInfoDialog('alert', `告警详情 - ${alert.title}`, alert);
};

// 告警表格样式函数
const alertHeaderCellStyle = () => {
  return {
    backgroundColor: 'rgba(15, 23, 42, 0.95) !important',
    background: 'rgba(15, 23, 42, 0.95) !important',
    color: '#f8fafc !important',
    borderBottom: '2px solid rgba(64, 158, 255, 0.3) !important',
    fontWeight: '600 !important',
    textAlign: 'center',
    padding: '12px 8px !important'
  };
};

const alertRowStyle = ({ rowIndex }) => {
  return {
    backgroundColor: rowIndex % 2 === 0 ? 'rgba(15, 23, 42, 0.9) !important' : 'rgba(30, 41, 59, 0.9) !important',
    background: rowIndex % 2 === 0 ? 'rgba(15, 23, 42, 0.9) !important' : 'rgba(30, 41, 59, 0.9) !important'
  };
};

const alertCellStyle = () => {
  return {
    backgroundColor: 'inherit !important',
    background: 'inherit !important',
    color: '#f8fafc !important',
    borderBottom: '1px solid rgba(64, 158, 255, 0.1) !important',
    padding: '12px 8px !important'
  };
};

// 车辆表格样式函数
const headerCellStyle = () => {
  return {
    backgroundColor: 'rgba(15, 23, 42, 0.95) !important',
    background: 'rgba(15, 23, 42, 0.95) !important',
    color: '#f8fafc !important',
    borderBottom: '2px solid rgba(64, 158, 255, 0.3) !important',
    fontWeight: '600 !important',
    textAlign: 'center',
    padding: '12px 8px !important'
  };
};

const rowStyle = ({ rowIndex }) => {
  return {
    backgroundColor: rowIndex % 2 === 0 ? 'rgba(15, 23, 42, 0.9) !important' : 'rgba(30, 41, 59, 0.9) !important',
    background: rowIndex % 2 === 0 ? 'rgba(15, 23, 42, 0.9) !important' : 'rgba(30, 41, 59, 0.9) !important'
  };
};

const cellStyle = () => {
  return {
    backgroundColor: 'inherit !important',
    background: 'inherit !important',
    color: '#f8fafc !important',
    borderBottom: '1px solid rgba(64, 158, 255, 0.1) !important',
    padding: '12px 8px !important'
  };
};

// 路线表格样式函数
const routeHeaderCellStyle = () => {
  return {
    backgroundColor: 'rgba(15, 23, 42, 0.95) !important',
    background: 'rgba(15, 23, 42, 0.95) !important',
    color: '#f8fafc !important',
    borderBottom: '2px solid rgba(64, 158, 255, 0.3) !important',
    fontWeight: '600 !important',
    textAlign: 'center',
    padding: '12px 8px !important'
  };
};

const routeRowStyle = ({ rowIndex }) => {
  return {
    backgroundColor: rowIndex % 2 === 0 ? 'rgba(15, 23, 42, 0.9) !important' : 'rgba(30, 41, 59, 0.9) !important',
    background: rowIndex % 2 === 0 ? 'rgba(15, 23, 42, 0.9) !important' : 'rgba(30, 41, 59, 0.9) !important'
  };
};

const routeCellStyle = () => {
  return {
    backgroundColor: 'inherit !important',
    background: 'inherit !important',
    color: '#f8fafc !important',
    borderBottom: '1px solid rgba(64, 158, 255, 0.1) !important',
    padding: '12px 8px !important'
  };
};

// 生命周期
onMounted(() => {
  updateTime();
  const timeInterval = setInterval(updateTime, 1000);

  // 初始化地图和图表
  setTimeout(() => {
    initMap();
    initCharts();
  }, 100);

  // 清理地图资源函数
  const cleanupMapResources = () => {
    // 清理标记
    if (stationMarkers) {
      stationMarkers.forEach(marker => {
        if (map && marker) map.remove(marker);
      });
      stationMarkers = [];
    }

    if (vehicleMarkers) {
      vehicleMarkers.forEach(marker => {
        if (map && marker) map.remove(marker);
      });
      vehicleMarkers = [];
    }

    if (alertMarkers) {
      alertMarkers.forEach(marker => {
        if (map && marker) map.remove(marker);
      });
      alertMarkers = [];
    }

    if (routePolylines) {
      routePolylines.forEach(polyline => {
        if (map && polyline) map.remove(polyline);
      });
      routePolylines = [];
    }

    // 销毁地图实例
    if (map) {
      map.destroy();
      map = null;
    }
  };

  onUnmounted(() => {
    clearInterval(timeInterval);
    // 清理地图资源
    cleanupMapResources();
  });
});
</script>

<style scoped lang="scss">
@import "@/views/bigScreen/big-screen-ui.scss";
</style>


