import request from '@/utils/request';

// 报站文件生成相关接口
export interface AnnouncementFileApi {
  // 生成报站配置文件包
  generateFiles: (version?: number) => Promise<any>;
  // 预览报站文件结构
  previewFileStructure: (version?: number) => Promise<any>;
  // 验证报站配置完整性
  validateConfig: () => Promise<any>;
  // 下载信息库文件
  downloadInfoLibrary: (version?: number) => Promise<any>;
  // 下载系统配置文件
  downloadSystemFiles: () => Promise<any>;
  // 获取支持的文件版本列表
  getSupportedVersions: () => Promise<any>;
}

// 生成报站配置文件包
export function generateAnnouncementFiles(version: number = 20231221) {
  return request({
    url: '/announcement/file/generate',
    method: 'post',
    params: { version },
    responseType: 'blob'
  });
}

// 预览报站文件结构
export function previewFileStructure(version: number = 20231221) {
  return request({
    url: '/announcement/file/preview',
    method: 'get',
    params: { version }
  });
}

// 验证报站配置完整性
export function validateAnnouncementConfig() {
  return request({
    url: '/announcement/file/validate',
    method: 'post'
  });
}

// 下载信息库文件
export function downloadInfoLibrary(version: number = 20231221) {
  return request({
    url: '/announcement/file/download/library',
    method: 'post',
    params: { version },
    responseType: 'blob'
  });
}

// 下载系统配置文件
export function downloadSystemFiles() {
  return request({
    url: '/announcement/file/download/system',
    method: 'post',
    responseType: 'blob'
  });
}

// 获取支持的文件版本列表
export function getSupportedVersions() {
  return request({
    url: '/announcement/file/versions',
    method: 'get'
  });
}

// 报站系统配置相关接口
export interface AnnouncementConfigApi {
  // 获取系统配置信息
  getSystemConfig: () => Promise<any>;
  // 获取统计信息
  getStats: () => Promise<any>;
  // 获取配置向导信息
  getConfigWizard: () => Promise<any>;
  // 重置配置
  resetConfig: () => Promise<any>;
  // 导入配置
  importConfig: (configData: any) => Promise<any>;
}

// 获取系统配置信息
export function getSystemConfig() {
  return request({
    url: '/announcement/config/system',
    method: 'get'
  });
}

// 获取统计信息
export function getAnnouncementStats() {
  return request({
    url: '/announcement/config/stats',
    method: 'get'
  });
}

// 获取配置向导信息
export function getConfigWizard() {
  return request({
    url: '/announcement/config/wizard',
    method: 'get'
  });
}

// 重置配置
export function resetAnnouncementConfig() {
  return request({
    url: '/announcement/config/reset',
    method: 'post'
  });
}

// 导入配置
export function importAnnouncementConfig(configData: any) {
  return request({
    url: '/announcement/config/import',
    method: 'post',
    data: configData
  });
}