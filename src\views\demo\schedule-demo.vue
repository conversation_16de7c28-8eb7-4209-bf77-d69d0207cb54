<template>
  <div class="demo-container">
    <div class="demo-header">
      <h1>图形化拖拽排班演示</h1>
      <p>这是一个演示页面，展示图形化拖拽排班功能的基本使用</p>
    </div>

    <!-- 时间选择器演示 -->
    <div class="demo-section">
      <h2>1. 时间选择器</h2>
      <TimeSelector v-model="selectedTimes" @times-changed="handleTimesChanged" @time-selected="handleTimeSelected" />
      <div class="demo-info">
        <p>已选择时间点: {{ selectedTimes.join(', ') || '无' }}</p>
      </div>
    </div>

    <!-- 车辆列表演示 -->
    <div class="demo-section">
      <h2>2. 车辆列表</h2>
      <VehicleList
        :vehicles="vehicles"
        :loading="vehiclesLoading"
        @vehicle-drag-start="handleVehicleDragStart"
        @vehicle-drag-end="handleVehicleDragEnd"
        @refresh="loadVehicles"
      />
    </div>

    <!-- 时间线演示 -->
    <div class="demo-section">
      <h2>3. 排班时间线</h2>
      <ScheduleTimeline
        :time-points="selectedTimes"
        :vehicles="vehicles"
        @vehicle-assigned="handleVehicleAssigned"
        @vehicle-unassigned="handleVehicleUnassigned"
        @schedule-changed="handleScheduleChanged"
        @save="handleSaveSchedule"
      />
    </div>

    <!-- 调试信息 -->
    <div class="demo-section">
      <h2>4. 调试信息</h2>
      <el-card>
        <div class="debug-info">
          <div class="debug-item">
            <strong>选择的时间点:</strong>
            <pre>{{ JSON.stringify(selectedTimes, null, 2) }}</pre>
          </div>
          <div class="debug-item">
            <strong>车辆状态:</strong>
            <pre>{{
              JSON.stringify(
                vehicles.map((v) => ({ id: v.vehicleId, plate: v.plateNumber, assigned: v.isAssigned })),
                null,
                2
              )
            }}</pre>
          </div>
          <div class="debug-item">
            <strong>排班项目:</strong>
            <pre>{{ JSON.stringify(scheduleItems, null, 2) }}</pre>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts" name="ScheduleDemo">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import TimeSelector from '@/components/schedule/TimeSelector.vue';
import VehicleList from '@/components/schedule/VehicleList.vue';
import ScheduleTimeline from '@/components/schedule/ScheduleTimeline.vue';

// 接口定义
interface Vehicle {
  vehicleId: number | string;
  plateNumber: string;
  vehicleNumber: string;
  vehicleType: string;
  brandModel?: string;
  seatCount?: number;
  status: string;
  isAssigned?: boolean;
  assignedTime?: string;
  totalMileage?: number;
  remark?: string;
}

interface ScheduleItem {
  id: string | number;
  content: string;
  start: Date;
  className?: string;
  vehicleId: number | string;
  plateNumber: string;
}

// 响应式数据
const selectedTimes = ref<string[]>([]);
const vehicles = ref<Vehicle[]>([]);
const vehiclesLoading = ref(false);
const scheduleItems = ref<ScheduleItem[]>([]);

// 方法
const loadVehicles = async () => {
  vehiclesLoading.value = true;
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 模拟车辆数据
    vehicles.value = [
      {
        vehicleId: 1,
        plateNumber: '京A12345',
        vehicleNumber: 'V001',
        vehicleType: 'bus',
        brandModel: '宇通ZK6105HNG',
        seatCount: 35,
        status: '1',
        totalMileage: 125000,
        remark: '1路公交车'
      },
      {
        vehicleId: 2,
        plateNumber: '京A12346',
        vehicleNumber: 'V002',
        vehicleType: 'electric_bus',
        brandModel: '比亚迪K9',
        seatCount: 30,
        status: '1',
        totalMileage: 85000,
        remark: '2路电动公交车'
      },
      {
        vehicleId: 3,
        plateNumber: '京A12347',
        vehicleNumber: 'V003',
        vehicleType: 'bus',
        brandModel: '金龙XMQ6127G',
        seatCount: 40,
        status: '1',
        totalMileage: 95000,
        remark: '3路公交车'
      },
      {
        vehicleId: 4,
        plateNumber: '京A12348',
        vehicleNumber: 'V004',
        vehicleType: 'hybrid_bus',
        brandModel: '福田BJ6105C7BHB',
        seatCount: 32,
        status: '2', // 维修中
        totalMileage: 78000,
        remark: '4路混动公交车'
      },
      {
        vehicleId: 5,
        plateNumber: '京A12349',
        vehicleNumber: 'V005',
        vehicleType: 'electric_bus',
        brandModel: '比亚迪K8',
        seatCount: 28,
        status: '1',
        totalMileage: 45000,
        remark: '5路电动公交车'
      }
    ];

    ElMessage.success('车辆数据加载成功');
  } catch (error) {
    ElMessage.error('加载车辆数据失败');
    console.error('Load vehicles error:', error);
  } finally {
    vehiclesLoading.value = false;
  }
};

const handleTimesChanged = (times: string[]) => {
  console.log('Times changed:', times);
  ElMessage.info(`时间点已更新: ${times.length}个`);
};

const handleTimeSelected = (index: number, time: string) => {
  console.log('Time selected:', time);
  ElMessage.info(`选择了时间: ${time}`);
};

const handleVehicleDragStart = (vehicle: Vehicle, event: DragEvent) => {
  console.log('Vehicle drag start:', vehicle.plateNumber);
  ElMessage.info(`开始拖拽车辆: ${vehicle.plateNumber}`);
};

const handleVehicleDragEnd = (vehicle: Vehicle, event: DragEvent) => {
  console.log('Vehicle drag end:', vehicle.plateNumber);
};

const handleVehicleAssigned = (vehicle: Vehicle, time: string) => {
  console.log('Vehicle assigned:', vehicle.plateNumber, 'to', time);

  // 更新车辆状态
  const vehicleIndex = vehicles.value.findIndex((v) => v.vehicleId === vehicle.vehicleId);
  if (vehicleIndex !== -1) {
    vehicles.value[vehicleIndex].isAssigned = true;
    vehicles.value[vehicleIndex].assignedTime = time;
  }

  ElMessage.success(`车辆 ${vehicle.plateNumber} 已分配到 ${time}`);
};

const handleVehicleUnassigned = (vehicle: Vehicle) => {
  console.log('Vehicle unassigned:', vehicle.plateNumber);

  // 更新车辆状态
  const vehicleIndex = vehicles.value.findIndex((v) => v.vehicleId === vehicle.vehicleId);
  if (vehicleIndex !== -1) {
    vehicles.value[vehicleIndex].isAssigned = false;
    vehicles.value[vehicleIndex].assignedTime = undefined;
  }

  ElMessage.info(`车辆 ${vehicle.plateNumber} 已取消分配`);
};

const handleScheduleChanged = (items: ScheduleItem[]) => {
  scheduleItems.value = items;
  console.log('Schedule changed:', items);
};

const handleSaveSchedule = (items: ScheduleItem[]) => {
  console.log('Save schedule:', items);
  ElMessage.success('排班计划已保存');
};

// 生命周期
onMounted(() => {
  loadVehicles();

  // 添加一些默认时间点用于演示
  selectedTimes.value = ['06:00', '07:00', '08:00', '18:00', '19:00', '22:00'];
});
</script>

<style scoped>
.demo-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 20px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 12px;
}

.demo-header h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
}

.demo-header p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.demo-section {
  margin-bottom: 40px;
}

.demo-section h2 {
  color: #303133;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e4e7ed;
}

.demo-info {
  margin-top: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.demo-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.debug-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.debug-item {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.debug-item strong {
  display: block;
  margin-bottom: 8px;
  color: #303133;
  font-size: 14px;
}

.debug-item pre {
  margin: 0;
  padding: 8px;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-container {
    padding: 16px;
  }

  .demo-header h1 {
    font-size: 24px;
  }

  .demo-header p {
    font-size: 14px;
  }

  .demo-section h2 {
    font-size: 18px;
  }
}
</style>
