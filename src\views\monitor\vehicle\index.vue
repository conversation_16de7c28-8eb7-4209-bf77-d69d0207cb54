<template>
  <div class="vehicle-monitor-fullscreen" :class="performanceClass">
    <!-- 简化背景系统 -->
    <div class="background-system">
      <!-- 基础渐变层 -->
      <div class="bg-gradient-base"></div>
    </div>
    <!-- 顶部工具栏 -->
    <header class="monitor-header">
      <div class="header-left">
        <div class="logo-section">
          <el-icon class="logo-icon"><Monitor /></el-icon>
          <h1 class="page-title">车辆实时监控</h1>
        </div>
        <div class="stats-cards">
          <div class="stat-card">
            <div class="stat-value">{{ totalVehicles }}</div>
            <div class="stat-label">总车辆</div>
          </div>
          <div class="stat-card online">
            <div class="stat-value">{{ onlineVehicles }}</div>
            <div class="stat-label">在线</div>
          </div>
          <div class="stat-card warning">
            <div class="stat-value">{{ offlineVehicles }}</div>
            <div class="stat-label">离线</div>
          </div>
          <div class="stat-card success">
            <div class="stat-value">{{ activeRoutes }}</div>
            <div class="stat-label">运营线路</div>
          </div>
        </div>
      </div>
      <div class="header-right">
        <div class="control-group">
          <!-- 已选线路显示 -->
          <div class="selected-routes-display" v-if="selectedRouteIds.length > 0">
            <span class="selected-label">已选线路：</span>
            <div class="routes-tags-container">
              <el-tag
                v-for="routeId in selectedRouteIds"
                :key="routeId"
                type="success"
                closable
                @close="removeRoute(routeId)"
                class="selected-route-tag"
              >
                {{ getRouteNameById(routeId) }}
              </el-tag>
            </div>
            <el-button size="small" text type="primary" @click="clearAllRoutes" v-if="selectedRouteIds.length > 1" class="clear-all-btn">
              清空全部
            </el-button>
          </div>

          <el-button :loading="loadingRoutes" @click="refreshRoutes" type="primary">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="monitor-main">
      <div class="main-content-wrapper">
        <!-- 左侧线路选择面板 -->
        <div class="left-route-panel" :class="{ collapsed: routePanelCollapsed }">
          <div class="panel-main">
            <div class="panel-header">
              <div class="header-left">
                <el-icon><Location /></el-icon>
                <span>线路选择</span>
              </div>
              <div class="header-right">
                <el-tag v-if="selectedRouteIds.length > 0" type="success" size="small"> 已选 {{ selectedRouteIds.length }} 条 </el-tag>
                <el-button
                  class="panel-collapse-btn"
                  text
                  @click.stop="toggleRoutePanel"
                  :disabled="routePanelCollapsed"
                  v-if="!routePanelCollapsed"
                  title="收起面板"
                >
                  <el-icon><ArrowLeft /></el-icon>
                  收起
                </el-button>
              </div>
            </div>
            <div class="panel-content">
              <div class="search-box">
                <el-input v-model="treeFilter" placeholder="搜索线路..." clearable prefix-icon="Search" @input="onTreeFilter" />
              </div>
              <el-tree
                ref="treeRef"
                class="route-tree"
                :data="treeData"
                :props="treeProps"
                node-key="id"
                default-expand-all
                :filter-node-method="filterTreeNode"
                @node-click="handleTreeSelect"
              >
                <template #default="{ node, data }">
                  <div class="tree-node-content" :class="{ 'is-selected': isRouteSelected(data) }">
                    <span class="node-label">{{ node.label }}</span>
                    <el-icon v-if="isRouteSelected(data)" class="selected-icon">
                      <Check />
                    </el-icon>
                  </div>
                </template>
              </el-tree>
            </div>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="right-content-area" :class="{ 'panel-collapsed': routePanelCollapsed }">
          <!-- 唤出按钮 -->
          <div class="route-panel-trigger" :class="{ visible: routePanelCollapsed }" @click.stop="openRoutePanel" title="打开线路选择">
            <el-icon class="trigger-icon"><Location /></el-icon>
            <span class="trigger-text">线路</span>
          </div>

          <div class="map-container">
            <!-- 未选择线路时的空状态 -->
            <div v-if="selectedRouteIds.length === 0" class="empty-state">
              <div class="empty-content">
                <el-icon class="empty-icon"><Location /></el-icon>
                <h3 class="empty-title">请选择要监控的线路</h3>
                <p class="empty-description">
                  从左侧面板中选择一条或多条线路开始监控
                  <br />
                  支持搜索功能快速定位线路
                </p>
                <el-button type="primary" @click="openRoutePanel" v-if="routePanelCollapsed">
                  <el-icon><Location /></el-icon>
                  打开线路选择
                </el-button>
              </div>
            </div>

            <div v-else class="multi-route-view">
              <div v-for="route in displayRoutes" :key="route.routeId" class="route-detail-block">
                <!-- 线路标题和统计信息 -->
                <div class="route-block-header">
                  <div class="route-title-section">
                    <el-tag type="success" effect="dark" size="large">{{ route.routeName }}</el-tag>
                    <div class="route-stats">
                      <span class="stat-item"
                        >配车数: <strong>{{ getRouteMetrics(route).assigned }}</strong></span
                      >
                      <span class="stat-item"
                        >运营数: <strong>{{ getRouteMetrics(route).operating }}</strong></span
                      >
                      <span class="stat-item"
                        >上行: <strong>{{ getRouteMetrics(route).up }}</strong></span
                      >
                      <span class="stat-item"
                        >下行: <strong>{{ getRouteMetrics(route).down }}</strong></span
                      >
                      <span class="stat-item"
                        >离线数: <strong>{{ getRouteMetrics(route).offline }}</strong></span
                      >
                      <span class="stat-item"
                        >完成/计划趟次: <strong>{{ getRouteMetrics(route).completed }}</strong></span
                      >
                      <span class="stat-item"
                        >发车模式: <strong>{{ getRouteMetrics(route).dispatchMode }}</strong></span
                      >
                    </div>
                  </div>
                  <div class="route-actions">
                    <el-button size="small" @click="removeRoute(route.routeId)">移除</el-button>
                  </div>
                </div>

                <!-- 线路详细内容 -->
                <div class="route-content">
                  <!-- 线路实时图 - 紧凑型双轨并行布局 -->
                  <div class="route-realtime-section no-header">
                    <!-- 紧凑型双轨道容器 -->
                    <div class="compact-dual-track-container">
                      <!-- 上行方向 -->
                      <div class="direction-track up-direction">
                        <div class="track-header">
                          <el-icon><CaretTop /></el-icon>
                          <span>上行</span>
                          <span class="vehicle-count">{{ route.up.vehicles.length }}辆</span>
                        </div>
                        <div class="track-container compact">
                          <div class="track-wrapper" :ref="setTrackRef(`up-${route.routeId}`)">
                            <div class="track-content" :style="{ width: getTrackWidth(route.up.stations.length) }">
                              <!-- 轨道线层 - 最底层 -->
                              <div class="track-line up-line" :style="getLineStyle(route.up.stations.length)"></div>

                              <!-- 站点圆点层 - 中间层，与轨道线同级 -->
                              <div class="stations-layer">
                                <div
                                  v-for="(s, idx) in route.up.stations"
                                  :key="`dot-${s.stationId}`"
                                  class="station-dot-wrapper"
                                  :style="getPosStyle(route.up.stations.length, idx)"
                                >
                                  <div
                                    class="station-dot"
                                    :class="{
                                      key: s.isKeyStation,
                                      'start-station': idx === 0,
                                      'end-station': idx === route.up.stations.length - 1
                                    }"
                                  ></div>
                                </div>
                              </div>

                              <!-- 站点信息层 - 上层 -->
                              <div class="stations-info-layer">
                                <div
                                  v-for="(s, idx) in route.up.stations"
                                  :key="`info-${s.stationId}`"
                                  class="station up-station"
                                  :class="{
                                    'first-station': idx === 0,
                                    'last-station': idx === route.up.stations.length - 1
                                  }"
                                  :style="getPosStyle(route.up.stations.length, idx)"
                                  @click="handleStationClick(route, s)"
                                >
                                  <div v-if="showStationNames" class="station-name vertical-text" :title="s.stationName">
                                    {{ truncateVerticalText(s.stationName) }}
                                  </div>
                                  <!-- 隐藏站点序号以简化显示 -->
                                  <!-- <div class="station-sequence">{{ s.sequence }}</div> -->
                                </div>
                              </div>

                              <!-- 车辆标记层 - 最高层 -->
                              <div class="vehicles-layer">
                                <div
                                  v-for="v in route.up.vehicles"
                                  :key="v.vehicleId"
                                  class="vehicle-marker up-vehicle"
                                  :class="v.status"
                                  :style="getVehicleStyle(route.up.stations.length, v)"
                                  @click="selectVehicle(v)"
                                  @contextmenu="(e) => handleVehicleRightClick(e, v)"
                                >
                                  <div class="vehicle-icon">🚌</div>
                                  <div v-if="showVehicleInfo" class="vehicle-plate">{{ v.plateNumber }}</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 下行方向 -->
                      <div class="direction-track down-direction">
                        <div class="track-header">
                          <el-icon><CaretBottom /></el-icon>
                          <span>下行</span>
                          <span class="vehicle-count">{{ route.down.vehicles.length }}辆</span>
                        </div>
                        <div class="track-container compact">
                          <div class="track-wrapper" :ref="setTrackRef(`down-${route.routeId}`)">
                            <div class="track-content" :style="{ width: getTrackWidth(route.down.stations.length) }">
                              <!-- 轨道线层 - 最底层 -->
                              <div class="track-line down-line" :style="getLineStyle(route.down.stations.length)"></div>

                              <!-- 站点圆点层 - 中间层，与轨道线同级 -->
                              <div class="stations-layer">
                                <div
                                  v-for="(s, idx) in route.down.stations"
                                  :key="`dot-${s.stationId}`"
                                  class="station-dot-wrapper"
                                  :style="getPosStyle(route.down.stations.length, idx)"
                                >
                                  <div
                                    class="station-dot"
                                    :class="{
                                      key: s.isKeyStation,
                                      'start-station': idx === 0,
                                      'end-station': idx === route.down.stations.length - 1
                                    }"
                                  ></div>
                                </div>
                              </div>

                              <!-- 站点信息层 - 上层 -->
                              <div class="stations-info-layer">
                                <div
                                  v-for="(s, idx) in route.down.stations"
                                  :key="`info-${s.stationId}`"
                                  class="station down-station"
                                  :class="{
                                    'first-station': idx === 0,
                                    'last-station': idx === route.down.stations.length - 1
                                  }"
                                  :style="getPosStyle(route.down.stations.length, idx)"
                                  @click="handleStationClick(route, s)"
                                >
                                  <div v-if="showStationNames" class="station-name vertical-text" :title="s.stationName">
                                    {{ truncateVerticalText(s.stationName) }}
                                  </div>
                                  <!-- 隐藏站点序号以简化显示 -->
                                  <!-- <div class="station-sequence">{{ s.sequence }}</div> -->
                                </div>
                              </div>

                              <!-- 车辆标记层 - 最高层 -->
                              <div class="vehicles-layer">
                                <div
                                  v-for="v in route.down.vehicles"
                                  :key="v.vehicleId"
                                  class="vehicle-marker down-vehicle"
                                  :class="v.status"
                                  :style="getVehicleStyle(route.down.stations.length, v)"
                                  @click="selectVehicle(v)"
                                  @contextmenu="(e) => handleVehicleRightClick(e, v)"
                                >
                                  <div class="vehicle-icon">🚌</div>
                                  <div v-if="showVehicleInfo" class="vehicle-plate">{{ v.plateNumber }}</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 待发区 - 左右并排布局 -->
                  <div class="dispatch-zones-container">
                    <!-- 上行待发区 -->
                    <div class="dispatch-zone up-dispatch">
                      <div class="zone-header">
                        <el-icon><CaretTop /></el-icon>
                        <span>上行待发区</span>
                        <span class="zone-count">{{ route.up.vehicles.length }}辆</span>
                      </div>
                      <div class="zone-content">
                        <div class="dispatch-parking-lot">
                          <div
                            v-for="v in route.up.vehicles"
                            :key="v.vehicleId"
                            class="dispatch-vehicle-slot"
                            :title="`${v.plateNumber} - 状态: ${getStatusText(v.status)}`"
                            @click="selectVehicle(v)"
                          >
                            {{ v.plateNumber }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 下行待发区 -->
                    <div class="dispatch-zone down-dispatch">
                      <div class="zone-header">
                        <el-icon><CaretBottom /></el-icon>
                        <span>下行待发区</span>
                        <span class="zone-count">{{ route.down.vehicles.length }}辆</span>
                      </div>
                      <div class="zone-content">
                        <div class="dispatch-parking-lot">
                          <div
                            v-for="v in route.down.vehicles"
                            :key="v.vehicleId"
                            class="dispatch-vehicle-slot"
                            :title="`${v.plateNumber} - 状态: ${getStatusText(v.status)}`"
                            @click="selectVehicle(v)"
                          >
                            {{ v.plateNumber }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 其他状态车辆 -->
                  <div class="other-vehicles-section">
                    <div class="vehicle-status-grid">
                      <div class="status-group">
                        <div class="status-title">
                          <el-icon><Monitor /></el-icon>
                          <span>停车</span>
                        </div>
                        <div class="parking-lot compact">
                          <div
                            v-for="v in route.parkedVehicles || []"
                            :key="v.vehicleId"
                            class="parking-slot"
                            :title="`${v.plateNumber} - 停车`"
                            @click="selectVehicle(v)"
                          >
                            <div class="vehicle-in-slot">
                              <div class="vehicle-icon">🚌</div>
                              <div class="vehicle-plate">{{ v.plateNumber }}</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="status-group">
                        <div class="status-title">
                          <el-icon><Monitor /></el-icon>
                          <span>充能</span>
                        </div>
                        <div class="parking-lot compact">
                          <div
                            v-for="v in route.chargingVehicles || []"
                            :key="v.vehicleId"
                            class="parking-slot charging"
                            :title="`${v.plateNumber} - 充能中`"
                            @click="selectVehicle(v)"
                          >
                            <div class="vehicle-in-slot">
                              <div class="vehicle-icon">🚌</div>
                              <div class="vehicle-plate">{{ v.plateNumber }}</div>
                              <div class="charging-indicator">⚡</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="status-group">
                        <div class="status-title">
                          <el-icon><Monitor /></el-icon>
                          <span>修车</span>
                        </div>
                        <div class="parking-lot compact">
                          <div
                            v-for="v in route.repairVehicles || []"
                            :key="v.vehicleId"
                            class="parking-slot repair"
                            :title="`${v.plateNumber} - 维修中`"
                            @click="selectVehicle(v)"
                          >
                            <div class="vehicle-in-slot">
                              <div class="vehicle-icon">🚌</div>
                              <div class="vehicle-plate">{{ v.plateNumber }}</div>
                              <div class="repair-indicator">🔧</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="status-group">
                        <div class="status-title">
                          <el-icon><Monitor /></el-icon>
                          <span>包车</span>
                        </div>
                        <div class="parking-lot compact">
                          <div
                            v-for="v in route.charterVehicles || []"
                            :key="v.vehicleId"
                            class="parking-slot charter"
                            :title="`${v.plateNumber} - 包车`"
                            @click="selectVehicle(v)"
                          >
                            <div class="vehicle-in-slot">
                              <div class="vehicle-icon">🚌</div>
                              <div class="vehicle-plate">{{ v.plateNumber }}</div>
                              <div class="charter-indicator">📋</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div v-if="false" class="route-detail-view">
              <div class="route-detail-header">
                <div class="route-title">
                  <el-button @click="resetSelection" text type="primary">
                    <el-icon><ArrowLeft /></el-icon>
                    返回概览
                  </el-button>
                  <el-tag type="success" effect="dark" size="large">{{ currentRoute?.routeName }}</el-tag>
                  <div class="route-summary-info">
                    <span>{{ summary.startStation }} → {{ summary.endStation }}</span>
                    <span>{{ summary.stationCount }} 站点</span>
                    <span>{{ summary.vehicleCount }} 车辆在线</span>
                  </div>
                </div>
                <div class="route-controls">
                  <el-switch v-model="showStationNames" inline-prompt active-text="站名" inactive-text="站名" />
                  <el-switch v-model="showVehicleInfo" inline-prompt active-text="车牌" inactive-text="车牌" />
                  <el-select v-model="displayDensity" placeholder="显示密度" size="small" style="width: 100px">
                    <el-option label="紧凑" value="compact" />
                    <el-option label="标准" value="normal" />
                    <el-option label="宽松" value="loose" />
                  </el-select>
                </div>
              </div>

              <div class="route-directions">
                <div class="direction-container">
                  <div class="direction-header">
                    <el-icon><CaretTop /></el-icon>
                    <span>上行方向</span>
                    <div class="direction-stats">
                      <span>{{ currentRoute?.up.vehicles.length || 0 }} 辆车</span>
                    </div>
                  </div>
                  <div class="track-container">
                    <div class="track-wrapper" :ref="setTrackRef('detail-up')">
                      <div class="track-content" :style="{ width: getTrackWidth(currentRoute?.up.stations.length || 0) }">
                        <!-- 轨道线层 - 最底层 -->
                        <div class="track-line" :style="getLineStyle(currentRoute?.up.stations.length || 0)"></div>

                        <!-- 站点圆点层 - 中间层，与轨道线同级 -->
                        <div class="stations-layer">
                          <div
                            v-for="(s, idx) in currentRoute?.up.stations || []"
                            :key="`dot-${s.stationId}`"
                            class="station-dot-wrapper"
                            :style="getPosStyle(currentRoute?.up.stations.length || 0, idx)"
                          >
                            <div
                              class="station-dot"
                              :class="{
                                key: s.isKeyStation,
                                'start-station': idx === 0,
                                'end-station': idx === (currentRoute?.up.stations.length || 0) - 1
                              }"
                            ></div>
                          </div>
                        </div>

                        <!-- 站点信息层 - 上层 -->
                        <div class="stations-info-layer">
                          <div
                            v-for="(s, idx) in currentRoute?.up.stations || []"
                            :key="`info-${s.stationId}`"
                            class="station"
                            :style="getPosStyle(currentRoute?.up.stations.length || 0, idx)"
                            @click="handleStationClick(currentRoute, s)"
                          >
                            <div v-if="showStationNames" class="station-name" :title="s.stationName">
                              {{ s.stationName.length > 5 ? s.stationName.substring(0, 4) + '...' : s.stationName }}
                            </div>
                            <!-- 隐藏站点序号以简化显示 -->
                            <!-- <div class="station-sequence">{{ s.sequence }}</div> -->
                          </div>
                        </div>

                        <!-- 车辆标记层 - 最高层 -->
                        <div class="vehicles-layer">
                          <div
                            v-for="v in currentRoute?.up.vehicles || []"
                            :key="v.vehicleId"
                            class="vehicle-marker"
                            :class="v.status"
                            :style="getVehicleStyle(currentRoute?.up.stations.length || 0, v)"
                            @click="selectVehicle(v)"
                            @contextmenu="(e) => handleVehicleRightClick(e, v)"
                            style="pointer-events: auto; cursor: pointer"
                          >
                            <div class="vehicle-icon" style="pointer-events: none">🚌</div>
                            <div v-if="showVehicleInfo" class="vehicle-plate" style="pointer-events: none">{{ v.plateNumber }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="direction-divider"></div>

                <div class="direction-container">
                  <div class="direction-header">
                    <el-icon><CaretBottom /></el-icon>
                    <span>下行方向</span>
                    <div class="direction-stats">
                      <span>{{ currentRoute?.down.vehicles.length || 0 }} 辆车</span>
                    </div>
                  </div>
                  <div class="track-container">
                    <div class="track-wrapper" :ref="setTrackRef('detail-down')">
                      <div class="track-content" :style="{ width: getTrackWidth(currentRoute?.down.stations.length || 0) }">
                        <!-- 轨道线层 - 最底层 -->
                        <div class="track-line" :style="getLineStyle(currentRoute?.down.stations.length || 0)"></div>

                        <!-- 站点圆点层 - 中间层，与轨道线同级 -->
                        <div class="stations-layer">
                          <div
                            v-for="(s, idx) in currentRoute?.down.stations || []"
                            :key="`dot-${s.stationId}`"
                            class="station-dot-wrapper"
                            :style="getPosStyle(currentRoute?.down.stations.length || 0, idx)"
                          >
                            <div
                              class="station-dot"
                              :class="{
                                key: s.isKeyStation,
                                'start-station': idx === 0,
                                'end-station': idx === (currentRoute?.down.stations.length || 0) - 1
                              }"
                            ></div>
                          </div>
                        </div>

                        <!-- 站点信息层 - 上层 -->
                        <div class="stations-info-layer">
                          <div
                            v-for="(s, idx) in currentRoute?.down.stations || []"
                            :key="`info-${s.stationId}`"
                            class="station"
                            :style="getPosStyle(currentRoute?.down.stations.length || 0, idx)"
                            @click="handleStationClick(currentRoute, s)"
                          >
                            <div v-if="showStationNames" class="station-name" :title="s.stationName">
                              {{ s.stationName.length > 5 ? s.stationName.substring(0, 4) + '...' : s.stationName }}
                            </div>
                            <!-- 隐藏站点序号以简化显示 -->
                            <!-- <div class="station-sequence">{{ s.sequence }}</div> -->
                          </div>
                        </div>

                        <!-- 车辆标记层 - 最高层 -->
                        <div class="vehicles-layer">
                          <div
                            v-for="v in currentRoute?.down.vehicles || []"
                            :key="v.vehicleId"
                            class="vehicle-marker"
                            :class="v.status"
                            :style="getVehicleStyle(currentRoute?.down.stations.length || 0, v)"
                            @click="selectVehicle(v)"
                            @contextmenu="(e) => handleVehicleRightClick(e, v)"
                            style="pointer-events: auto; cursor: pointer"
                          >
                            <div class="vehicle-icon" style="pointer-events: none">🚌</div>
                            <div v-if="showVehicleInfo" class="vehicle-plate" style="pointer-events: none">{{ v.plateNumber }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 车辆详情弹窗 - 科技感设计 -->
    <el-dialog
      v-model="showVehicleDialog"
      :title="vehicleDialogData?.plateNumber || '车辆详细信息'"
      width="1200px"
      top="5vh"
      :modal="true"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      class="vehicle-detail-dialog tech-style"
      :modal-class="'tech-modal'"
      :transition-name="performanceMode === 'performance-mode' ? '' : 'el-dialog-fade'"
      :style="{
        '--el-dialog-bg-color': '#1e293b',
        'background': '#1e293b',
        'border': '1px solid rgba(59, 130, 246, 0.3)',
        'border-radius': '16px',
        'color': '#e2e8f0'
      }"
    >
      <div v-if="vehicleDialogData" class="vehicle-detail-content">
        <!-- 基本信息 -->
        <div class="detail-section">
          <div class="section-title">
            <el-icon><Monitor /></el-icon>
            <span>基本信息</span>
          </div>
          <div class="detail-grid-wide">
            <div class="detail-item">
              <span class="label">车牌号：</span>
              <span class="value">{{ vehicleDialogData.plateNumber }}</span>
            </div>
            <div class="detail-item">
              <span class="label">自编号：</span>
              <span class="value">{{ vehicleDialogData.plateNumber }}</span>
            </div>
            <div class="detail-item">
              <span class="label">所属组织：</span>
              <span class="value">{{ vehicleDialogData.routeName }}</span>
            </div>
            <div class="detail-item">
              <span class="label">车辆状态：</span>
              <span class="value status-online" :class="vehicleDialogData.status">
                <el-icon><Check /></el-icon>
                在线
              </span>
            </div>
            <div class="detail-item">
              <span class="label">行驶速度：</span>
              <span class="value">{{ vehicleDialogData.speed || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">捕获序：</span>
              <span class="value">{{ vehicleDialogData.captureSeq || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">电池电量：</span>
              <span class="value">{{ vehicleDialogData.batteryLevel || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">运营线路：</span>
              <span class="value">{{ vehicleDialogData.operatingRoute || '115路-115路' }}</span>
            </div>
            <div class="detail-item full-width">
              <span class="label">ACC状态：</span>
              <span class="value">{{ vehicleDialogData.accLocation || '江西省赣州市南康区龙岭工业园区路口(公交站)附近' }}</span>
            </div>
          </div>
        </div>

        <!-- 运营与时间信息 - 合并为一个section -->
        <div class="detail-section">
          <div class="section-title">
            <el-icon><Location /></el-icon>
            <span>运营与时间信息</span>
          </div>
          <div class="detail-grid-wide">
            <div class="detail-item">
              <span class="label">班次号：</span>
              <span class="value">{{ vehicleDialogData.tripNumber || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">行驶方向：</span>
              <span class="value">{{ vehicleDialogData.direction || '南康公交总站(上行)' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">营运状态：</span>
              <span class="value status-running">趟中</span>
            </div>
            <div class="detail-item">
              <span class="label">计划司机：</span>
              <span class="value">{{ vehicleDialogData.plannedDriver || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">经到司机：</span>
              <span class="value">[15100]{{ vehicleDialogData.driverName || '李来福' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">联系方式：</span>
              <span class="value">{{ vehicleDialogData.driverPhone || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">调度发车时间：</span>
              <span class="value">{{ vehicleDialogData.scheduledDepartureTime || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">实际发车时间：</span>
              <span class="value">{{ vehicleDialogData.actualDepartureTime || '17:03' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">累计里程：</span>
              <span class="value">{{ vehicleDialogData.totalMileage || '112.9/116.9km' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">累计趟次：</span>
              <span class="value">{{ vehicleDialogData.totalTrips || '3.5/-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">违规：</span>
              <span class="value">{{ vehicleDialogData.violations || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 站点与车辆位置信息 -->
        <div class="detail-section">
          <div class="section-title">
            <el-icon><Location /></el-icon>
            <span>站点与车辆位置信息</span>
          </div>
          <div class="detail-grid-wide">
            <div class="detail-item">
              <span class="label">当前站：</span>
              <span class="value">{{ vehicleDialogData.currentStation || '龙岭工业园区路口' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">到站时间：</span>
              <span class="value">{{ vehicleDialogData.arrivalTime || '18:09' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">离站时间：</span>
              <span class="value">{{ vehicleDialogData.departureTime || '18:10' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">预计到达时间：</span>
              <span class="value">{{ vehicleDialogData.estimatedArrival || '18:10' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">距下一站：</span>
              <span class="value">{{ vehicleDialogData.distanceToNext || '492m' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">下站预计到达：</span>
              <span class="value">{{ vehicleDialogData.nextEstimatedArrival || '18:10' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">停车时间：</span>
              <span class="value">{{ vehicleDialogData.parkingTime || '37s' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">停车场：</span>
              <span class="value">{{ vehicleDialogData.parkingLot || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">前车：</span>
              <span class="value">{{ vehicleDialogData.frontVehicle || 'B14399' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">前车距离：</span>
              <span class="value">{{ vehicleDialogData.frontDistance || '2.16km' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">后车距离：</span>
              <span class="value">{{ vehicleDialogData.rearDistance || '3.46km' }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 车辆右键菜单 -->
    <div v-if="showContextMenu" ref="contextMenuRef" class="vehicle-context-menu" :style="contextMenuStyle" @click.stop>
      <div class="context-menu-header">
        <span>🚌 {{ contextMenuVehicle?.plateNumber || 'TEST' }}</span>
      </div>
      <div class="context-menu-divider"></div>
      <div class="context-menu-item" @click="sendSpeedCommand('accelerate')">
        <span>⬆️ 请加速</span>
      </div>
      <div class="context-menu-item" @click="sendSpeedCommand('decelerate')">
        <span>⬇️ 请减速</span>
      </div>
      <div class="context-menu-divider"></div>
      <div class="context-menu-item" @click="showVoiceMessageDialog">
        <span>🎤 发送语音消息</span>
      </div>
    </div>

    <!-- 语音消息弹窗 -->
    <el-dialog
      v-model="showVoiceDialog"
      title="发送语音消息"
      width="500px"
      :modal="true"
      class="voice-message-dialog"
      :transition-name="performanceMode === 'performance-mode' ? '' : 'el-dialog-fade'"
    >
      <div class="voice-message-content">
        <div class="target-vehicle">
          <el-icon><Monitor /></el-icon>
          <span>目标车辆：{{ contextMenuVehicle?.plateNumber }}</span>
        </div>

        <div class="message-input-section">
          <el-input v-model="voiceMessage" type="textarea" :rows="4" placeholder="请输入要发送的消息内容..." maxlength="200" show-word-limit />
        </div>

        <div class="quick-messages">
          <div class="quick-message-title">快捷消息：</div>
          <div class="quick-message-buttons">
            <el-button v-for="msg in quickMessages" :key="msg" size="small" @click="voiceMessage = msg">
              {{ msg }}
            </el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showVoiceDialog = false">取消</el-button>
          <el-button type="primary" @click="sendVoiceMessage">发送消息</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import {
  CaretTop,
  CaretBottom,
  Monitor,
  Location,
  Refresh,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  Search,
  Check,
  InfoFilled,
  Microphone
} from '@element-plus/icons-vue';
import type { BusStation } from '@/types/monitor';

const router = useRouter();

type VehicleLite = {
  vehicleId: number | string;
  plateNumber: string;
  segmentIndex: number;
  offset: number;
  status: 'running' | 'locked' | 'stopped';
  routeName?: string;
  currentStation?: string;
  lastUpdate?: string;
  // 新增字段
  driverName?: string;
  driverPhone?: string;
  nextStation?: string;
  speed?: string | number;
  mileage?: number;
  passengers?: number;
  accLocation?: string;
  captureSeq?: string;
  batteryLevel?: string;
  operatingRoute?: string;
  tripNumber?: string;
  direction?: string;
  plannedDriver?: string;
  violations?: string;
  scheduledDepartureTime?: string;
  actualDepartureTime?: string;
  totalMileage?: string;
  totalTrips?: string;
  arrivalTime?: string;
  departureTime?: string;
  estimatedArrival?: string;
  parkingLot?: string;
  distanceToNext?: string;
  nextEstimatedArrival?: string;
  parkingTime?: string;
  frontVehicle?: string;
  rearDistance?: string;
  frontDistance?: string;
};

type DirectionData = {
  stations: BusStation[];
  vehicles: VehicleLite[];
};

type RoutePro = {
  routeId: number | string;
  routeName: string;
  expanded: boolean;
  status: 'active' | 'inactive';
  metrics: {
    assigned: number;
    operating: number;
    upCount: number;
    downCount: number;
    completed: string;
    dispatchMode: string;
  };
  up: DirectionData;
  down: DirectionData;
  parkedVehicles?: VehicleLite[];
  chargingVehicles?: VehicleLite[];
  repairVehicles?: VehicleLite[];
  charterVehicles?: VehicleLite[];
};

// 响应式状态
const allRoutes = ref<RoutePro[]>([]);
const selectedRouteIds = ref<(number | string)[]>([]);
const loadingRoutes = ref(false);
const routePanelCollapsed = ref(false); // 默认展开左侧面板
const showStationNames = ref(true);
const showVehicleInfo = ref(true);
const displayDensity = ref('normal'); // 显示密度：compact, normal, loose

// 性能优化相关 - 默认启用高性能模式
const performanceMode = ref('performance-mode');

// 车辆详情弹窗相关
const showVehicleDialog = ref(false);
const vehicleDialogData = ref<VehicleLite | null>(null);

// 右键菜单相关
const showContextMenu = ref(false);
const contextMenuRef = ref<HTMLElement>();
const contextMenuVehicle = ref<VehicleLite | null>(null);
const contextMenuStyle = ref({
  left: '0px',
  top: '0px'
});

// 语音消息相关
const showVoiceDialog = ref(false);
const voiceMessage = ref('');
const quickMessages = ref([
  '请注意安全驾驶',
  '请按时到站',
  '请减速慢行',
  '请加快速度',
  '请注意乘客上下车',
  '请保持车距',
  '请注意路况',
  '收到，正在执行'
]);

// 树状选择器相关
const treeData = ref<any[]>([]);
const treeProps = { label: 'label', children: 'children' };
const treeFilter = ref('');
const treeRef = ref();

// 轨道容器引用 - 使用Map来管理多个ref
const trackRefs = new Map<string, HTMLElement>();
const setTrackRef = (key: string) => (el: HTMLElement | null) => {
  if (el) {
    trackRefs.set(key, el);
  } else {
    trackRefs.delete(key);
  }
};

onMounted(() => {
  loadRoutes();
  console.log('Component mounted, context menu functions available');

  // 自动检测设备性能并设置优化模式
  autoDetectPerformanceMode();

  // 添加全局右键事件监听器用于调试
  const contextMenuHandler = (e) => {
    console.log('Global right click detected at:', e.clientX, e.clientY);
  };
  document.addEventListener('contextmenu', contextMenuHandler);

  // 保存监听器引用以便清理
  (document as any)._vehicleMonitorContextMenuHandler = contextMenuHandler;
});

// 组件卸载时清理事件监听器
onBeforeUnmount(() => {
  // 清理全局事件监听器
  const contextMenuHandler = (document as any)._vehicleMonitorContextMenuHandler;
  if (contextMenuHandler) {
    document.removeEventListener('contextmenu', contextMenuHandler);
    delete (document as any)._vehicleMonitorContextMenuHandler;
  }

  // 清理其他可能的监听器
  document.removeEventListener('click', hideContextMenu);

  // 强制清理缓存
  trackRefs.clear();
  densityConfigCache.clear();

  // 强制清理可能残留的全局样式
  const techModalOverlays = document.querySelectorAll('.el-overlay.tech-modal');
  techModalOverlays.forEach((overlay) => overlay.remove());

  // 移除可能添加到body上的样式类
  document.body.classList.remove('el-popup-parent--hidden');

  console.log('车辆监控组件已卸载，所有事件监听器和样式已清理');
});

// 监听弹窗关闭事件，强制清理性能
watch(showVehicleDialog, (newVal, oldVal) => {
  if (oldVal === true && newVal === false) {
    // 弹窗刚关闭，强制清理任何可能影响性能的元素
    nextTick(() => {
      // 强制垃圾回收（如果浏览器支持）
      if (window.gc && typeof window.gc === 'function') {
        window.gc();
      }

      // 清理可能残留的DOM元素
      const techModalOverlays = document.querySelectorAll('.el-overlay.tech-modal');
      techModalOverlays.forEach((overlay) => {
        if (!overlay.querySelector('.el-dialog')) {
          overlay.remove();
        }
      });

      // 强制重绘以恢复性能
      document.body.offsetHeight; // 触发重排

      console.log('🚀 弹窗已关闭，性能清理完成');
    });
  }
});

// 监听显示密度变化，触发重新渲染并清除缓存
watch(displayDensity, () => {
  // 清除配置缓存
  densityConfigCache.clear();
  // 强制重新渲染轨道布局
  nextTick(() => {
    // 可以在这里添加额外的布局更新逻辑
  });
});

// 计算属性
const routeOptions = computed(() => allRoutes.value.filter((r) => r.status === 'active').map((r) => ({ label: r.routeName, value: r.routeId })));

const displayRoutes = computed(() => {
  if (selectedRouteIds.value.length === 0) return [];
  return allRoutes.value.filter((r) => selectedRouteIds.value.includes(r.routeId));
});

const currentRoute = computed(() => {
  if (selectedRouteIds.value.length === 0) return null;
  return allRoutes.value.find((r) => r.routeId === selectedRouteIds.value[0]) || null;
});

const summary = computed(() => {
  const route = currentRoute.value;
  if (!route) return { startStation: '--', endStation: '--', stationCount: 0, vehicleCount: 0 };
  const up = route.up.stations;
  const down = route.down.stations;
  return {
    startStation: up[0]?.stationName || '--',
    endStation: up[up.length - 1]?.stationName || '--',
    stationCount: new Set([...up, ...down].map((s) => s.stationId)).size,
    vehicleCount: route.up.vehicles.length + route.down.vehicles.length
  };
});

// 性能模式计算属性
const performanceClass = computed(() => performanceMode.value);

// 统计数据
const totalVehicles = computed(() => {
  return allRoutes.value.reduce((total, route) => total + route.up.vehicles.length + route.down.vehicles.length, 0);
});

const onlineVehicles = computed(() => {
  return allRoutes.value.reduce((total, route) => {
    const running = [...route.up.vehicles, ...route.down.vehicles].filter((v) => v.status === 'running').length;
    return total + running;
  }, 0);
});

const offlineVehicles = computed(() => totalVehicles.value - onlineVehicles.value);

const activeRoutes = computed(() => allRoutes.value.filter((r) => r.status === 'active').length);

// 方法定义

function getRouteMetrics(route: RoutePro) {
  const upVehicles = route.up.vehicles;
  const downVehicles = route.down.vehicles;
  const total = upVehicles.length + downVehicles.length;
  const running = [...upVehicles, ...downVehicles].filter((v) => v.status === 'running').length;
  const stopped = [...upVehicles, ...downVehicles].filter((v) => v.status === 'stopped').length;
  const locked = total - running - stopped;
  return {
    assigned: route.metrics.assigned,
    operating: running,
    up: upVehicles.length,
    down: downVehicles.length,
    offline: locked,
    completed: route.metrics.completed,
    dispatchMode: route.metrics.dispatchMode
  };
}

function handleRouteChange() {
  // 选择线路后，保持展开并可在此请求该线路下的车辆/站点数据
  selectedRouteIds.value.forEach((routeId) => {
    const target = allRoutes.value.find((r) => r.routeId === routeId);
    if (target && !target.expanded) target.expanded = true;
  });
}

function resetSelection() {
  selectedRouteIds.value = [];
}

function removeRoute(routeId: number | string) {
  selectedRouteIds.value = selectedRouteIds.value.filter((id) => id !== routeId);
}

function refreshRoutes() {
  loadRoutes();
}

function loadRoutes() {
  loadingRoutes.value = true;
  setTimeout(() => {
    allRoutes.value = createMockRoutes();
    buildTreeData();
    loadingRoutes.value = false;
  }, 200);
}

function buildTreeData() {
  treeData.value = [
    {
      id: 'org-1',
      label: '黔州公交',
      children: [
        {
          id: 'company-1',
          label: '一公司',
          children: allRoutes.value.slice(0, Math.ceil(allRoutes.value.length / 2)).map((r) => ({
            id: 'route-' + r.routeId,
            label: r.routeName,
            type: 'route',
            routeId: r.routeId
          }))
        },
        {
          id: 'company-2',
          label: '二公司',
          children: allRoutes.value.slice(Math.ceil(allRoutes.value.length / 2)).map((r) => ({
            id: 'route-' + r.routeId,
            label: r.routeName,
            type: 'route',
            routeId: r.routeId
          }))
        }
      ]
    }
  ];
}

function filterTreeNode(value: string, data: any) {
  if (!value) return true;
  return String(data.label).toLowerCase().includes(String(value).toLowerCase());
}

function onTreeFilter() {
  if (treeRef.value) treeRef.value.filter(treeFilter.value);
}

function handleTreeSelect(data: any) {
  if (data?.type === 'route') {
    toggleRouteSelection(data.routeId);
  }
}

function selectRoute(id: number | string) {
  if (!selectedRouteIds.value.includes(id)) {
    selectedRouteIds.value.push(id);
  }
}

// 新增方法：切换线路选择状态（支持多选）
function toggleRouteSelection(routeId: number | string) {
  const index = selectedRouteIds.value.indexOf(routeId);
  if (index > -1) {
    // 如果已选中，则取消选择
    selectedRouteIds.value.splice(index, 1);
  } else {
    // 如果未选中，则添加到选择列表
    selectedRouteIds.value.push(routeId);
  }
}

// 新增方法：判断线路是否已选中
function isRouteSelected(data: any): boolean {
  if (data?.type !== 'route') return false;
  return selectedRouteIds.value.includes(data.routeId);
}

// 新增方法：根据线路ID获取线路名称
function getRouteNameById(routeId: number | string): string {
  const route = allRoutes.value.find((r) => r.routeId === routeId);
  return route?.routeName || '未知线路';
}

// 新增方法：清空所有选中的线路
function clearAllRoutes() {
  selectedRouteIds.value = [];
}

function toggleRoutePanel() {
  // 优化动画时序：收起时先淡出内容，展开时先显示面板再淡入内容
  if (!routePanelCollapsed.value) {
    // 收起面板：立即开始收起动画
    routePanelCollapsed.value = true;
  } else {
    // 展开面板：立即展开，内容会通过CSS延迟淡入
    routePanelCollapsed.value = false;
  }
}

function openRoutePanel() {
  routePanelCollapsed.value = false;
}

// 优化的站点创建函数，支持更灵活的配置
function createStations(
  names: string[],
  config?: {
    keyStationInterval?: number;
    transferStations?: number[];
  }
): BusStation[] {
  const { keyStationInterval = 5, transferStations = [] } = config || {};

  return names.map((name, idx) => {
    const isFirst = idx === 0;
    const isLast = idx === names.length - 1;
    const isTransfer = transferStations.includes(idx);
    const isKeyByInterval = idx % keyStationInterval === 0;

    return {
      stationId: idx + 1,
      stationName: name,
      stationCode: `S${String(idx + 1).padStart(3, '0')}`,
      sequence: idx + 1,
      coordinates: { x: 0, y: 0 },
      stationType: isFirst || isLast ? 'terminal' : isTransfer ? 'transfer' : 'normal',
      isKeyStation: isFirst || isLast || isKeyByInterval || isTransfer
    };
  });
}

function createMockRoutes(): RoutePro[] {
  // 定义线路配置
  const routeConfigs = [
    {
      id: 115,
      name: '115路',
      upStations: [
        '贵阳站',
        '黔灵山',
        '甲秀楼',
        '河滨公园',
        '大营坡',
        '小河区',
        '金阳客站',
        '观山湖',
        '会展城',
        '奥体中心',
        '阳关大道',
        '长岭南路',
        '金融城',
        '白云区',
        '修文县'
      ],
      downStations: [
        '花溪公园',
        '青岩古镇',
        '天河潭',
        '贵州大学',
        '花溪大学城',
        '石板镇',
        '党武镇',
        '燕楼镇',
        '麦坪镇',
        '久安乡',
        '高坡乡',
        '黔陶乡',
        '马铃乡',
        '孟关乡',
        '湖潮乡'
      ],
      upVehicleCount: 12,
      downVehicleCount: 15,
      platePrefix: 'B',
      transferStations: { up: [2, 6], down: [3, 8] },
      assigned: 10,
      dispatchMode: '计划发车'
    },
    {
      id: 135,
      name: '135路',
      upStations: ['花溪公园', '青岩古镇', '天河潭', '贵州大学', '花溪大学城', '石板镇', '党武镇', '燕楼镇', '麦坪镇', '久安乡'],
      downStations: ['贵阳站', '黔灵山', '甲秀楼', '河滨公园', '大营坡', '小河区', '金阳客站', '观山湖', '会展城', '奥体中心'],
      upVehicleCount: 10,
      downVehicleCount: 13,
      platePrefix: 'C',
      transferStations: { up: [1, 4], down: [2, 5] },
      assigned: 8,
      dispatchMode: '计划发车'
    },
    {
      id: 201,
      name: '201路',
      upStations: ['遵义路', '中华路', '延安路', '博爱路', '瑞金路', '文昌路', '中山路', '解放路', '民生路', '富水路', '喷水池', '大十字'],
      downStations: ['大十字', '喷水池', '富水路', '民生路', '解放路', '中山路', '文昌路', '瑞金路', '博爱路', '延安路', '中华路', '遵义路'],
      upVehicleCount: 8,
      downVehicleCount: 11,
      platePrefix: 'D',
      transferStations: { up: [5], down: [6] },
      assigned: 6,
      dispatchMode: '计划发车'
    },
    {
      id: 999,
      name: '999路测试线',
      upStations: [
        '测试起点站',
        '第一站',
        '第二站',
        '第三站',
        '第四站',
        '第五站',
        '第六站',
        '第七站',
        '第八站',
        '第九站',
        '第十站',
        '第十一站',
        '第十二站',
        '第十三站',
        '第十四站',
        '第十五站',
        '第十六站',
        '第十七站',
        '第十八站',
        '第十九站',
        '第二十站',
        '第二十一站',
        '第二十二站',
        '第二十三站',
        '第二十四站',
        '第二十五站',
        '第二十六站',
        '第二十七站',
        '第二十八站',
        '第二十九站',
        '第三十站',
        '第三十一站',
        '第三十二站',
        '第三十三站',
        '第三十四站',
        '第三十五站',
        '第三十六站',
        '第三十七站',
        '第三十八站',
        '第三十九站',
        '第四十站',
        '第四十一站',
        '第四十二站',
        '第四十三站',
        '第四十四站',
        '第四十五站',
        '第四十六站',
        '第四十七站',
        '第四十八站',
        '测试终点站'
      ],
      downStations: [
        '测试终点站',
        '第四十八站',
        '第四十七站',
        '第四十六站',
        '第四十五站',
        '第四十四站',
        '第四十三站',
        '第四十二站',
        '第四十一站',
        '第四十站',
        '第三十九站',
        '第三十八站',
        '第三十七站',
        '第三十六站',
        '第三十五站',
        '第三十四站',
        '第三十三站',
        '第三十二站',
        '第三十一站',
        '第三十站',
        '第二十九站',
        '第二十八站',
        '第二十七站',
        '第二十六站',
        '第二十五站',
        '第二十四站',
        '第二十三站',
        '第二十二站',
        '第二十一站',
        '第二十站',
        '第十九站',
        '第十八站',
        '第十七站',
        '第十六站',
        '第十五站',
        '第十四站',
        '第十三站',
        '第十二站',
        '第十一站',
        '第十站',
        '第九站',
        '第八站',
        '第七站',
        '第六站',
        '第五站',
        '第四站',
        '第三站',
        '第二站',
        '第一站',
        '测试起点站'
      ],
      upVehicleCount: 8,
      downVehicleCount: 12,
      platePrefix: 'T',
      transferStations: { up: [5, 15, 25, 35, 45], down: [5, 15, 25, 35, 45] },
      assigned: 20,
      dispatchMode: '智能调度'
    }
  ];

  // 创建车辆生成函数
  const createVehicles = (count: number, prefix: string, routeId: number, maxStations: number): VehicleLite[] => {
    return Array.from({ length: count }, (_, i) => ({
      vehicleId: `${prefix}${routeId}-${String(i + 1).padStart(2, '0')}`,
      plateNumber: `${prefix}${String(14000 + routeId * 100 + i).padStart(5, '0')}`,
      segmentIndex: Math.min(maxStations - 2, Math.floor(Math.random() * (maxStations - 1))),
      offset: Math.random() * 0.8 + 0.1, // 限制在0.1-0.9之间，避免重叠
      status: generateVehicleStatus()
    }));
  };

  // 生成车辆状态
  const generateVehicleStatus = (): 'running' | 'stopped' | 'locked' => {
    const rand = Math.random();
    if (rand < 0.7) return 'running';
    if (rand < 0.9) return 'stopped';
    return 'locked';
  };

  // 生成其他状态车辆
  const generateOtherVehicles = (prefix: string, routeId: number, count: number): VehicleLite[] => {
    return Array.from({ length: count }, (_, i) => ({
      vehicleId: `${prefix}${routeId}-other-${i + 1}`,
      plateNumber: `${prefix}${String(15000 + routeId * 100 + i).padStart(5, '0')}`
    }));
  };

  return routeConfigs.map((config) => {
    const upStations = createStations(config.upStations, {
      keyStationInterval: 4,
      transferStations: config.transferStations.up
    });
    const downStations = createStations(config.downStations, {
      keyStationInterval: 4,
      transferStations: config.transferStations.down
    });

    const upVehicles = createVehicles(config.upVehicleCount, config.platePrefix, config.id, config.upStations.length);
    const downVehicles = createVehicles(config.downVehicleCount, config.platePrefix, config.id, config.downStations.length);

    const totalOperating = upVehicles.filter((v) => v.status === 'running').length + downVehicles.filter((v) => v.status === 'running').length;

    return {
      routeId: config.id,
      routeName: config.name,
      expanded: true,
      status: 'active' as const,
      metrics: {
        assigned: config.assigned,
        operating: totalOperating,
        upCount: config.upVehicleCount,
        downCount: config.downVehicleCount,
        completed: `${(Math.random() * 40 + 10).toFixed(1)}/0.0`,
        dispatchMode: config.dispatchMode
      },
      up: { stations: upStations, vehicles: upVehicles },
      down: { stations: downStations, vehicles: downVehicles },
      parkedVehicles: generateOtherVehicles(config.platePrefix, config.id, Math.floor(Math.random() * 3) + 1),
      chargingVehicles: generateOtherVehicles(config.platePrefix, config.id, Math.floor(Math.random() * 3) + 1),
      repairVehicles: generateOtherVehicles(config.platePrefix, config.id, Math.floor(Math.random() * 2)),
      charterVehicles: generateOtherVehicles(config.platePrefix, config.id, Math.floor(Math.random() * 2))
    };
  });
}

function handleStationClick(route: RoutePro | null, s: BusStation) {
  if (route) {
    ElMessage.info(`${route.routeName} - ${s.stationName}`);
  }
}

// 纵向文字截断函数 - 超过5个字显示省略号
function truncateVerticalText(text: string, maxLength: number = 5): string {
  if (text.length <= maxLength) {
    return text;
  }
  return text.substring(0, maxLength - 1) + '⋮'; // 使用纵向省略号
}

function selectVehicle(v: VehicleLite) {
  console.log('selectVehicle called with:', v);
  // 设置弹窗数据并显示
  vehicleDialogData.value = {
    ...v,
    // 补充详细信息
    driverName: '李来福',
    driverPhone: '-',
    currentStation: '龙岭工业园区路口',
    nextStation: '下一站点',
    speed: '-',
    mileage: Math.floor(Math.random() * 500) + 100, // 模拟里程
    passengers: Math.floor(Math.random() * 30) + 5, // 模拟乘客数
    lastUpdate: new Date().toLocaleString(),
    routeName: allRoutes.value.find((r) => r.up.vehicles.includes(v) || r.down.vehicles.includes(v))?.routeName || '115路',
    // 新增字段
    accLocation: '江西省赣州市南康区龙岭工业园区路口(公交站)附近',
    captureSeq: '-',
    batteryLevel: '-',
    operatingRoute: '115路-115路',
    tripNumber: '-',
    direction: '南康公交总站(上行)',
    plannedDriver: '-',
    violations: '-',
    scheduledDepartureTime: '-',
    actualDepartureTime: '17:03',
    totalMileage: '112.9/116.9km',
    totalTrips: '3.5/-',
    arrivalTime: '18:09',
    departureTime: '18:10',
    estimatedArrival: '18:10',
    parkingLot: '-',
    distanceToNext: '492m',
    nextEstimatedArrival: '18:10',
    parkingTime: '37s',
    frontVehicle: 'B14399',
    rearDistance: '3.46km',
    frontDistance: '2.16km'
  };
  showVehicleDialog.value = true;
}

// 处理车辆右键点击
function handleVehicleRightClick(event: MouseEvent, vehicle: VehicleLite) {
  console.log('🚌 Right click detected on vehicle:', vehicle.plateNumber);

  // 阻止默认右键菜单
  event.preventDefault();
  event.stopPropagation();

  contextMenuVehicle.value = vehicle;

  // 计算菜单位置
  const x = event.clientX;
  const y = event.clientY;

  console.log('📍 Menu position:', x, y);

  contextMenuStyle.value = {
    left: `${x}px`,
    top: `${y}px`
  };

  showContextMenu.value = true;
  console.log('👁️ Context menu visible:', showContextMenu.value);

  // 点击其他地方关闭菜单
  setTimeout(() => {
    document.addEventListener('click', hideContextMenu, { once: true });
  }, 100);

  return false; // 额外保险
}

// 隐藏右键菜单
function hideContextMenu() {
  console.log('Hiding context menu');
  showContextMenu.value = false;
  contextMenuVehicle.value = null;
}

// 发送速度指令
function sendSpeedCommand(command: 'accelerate' | 'decelerate') {
  if (!contextMenuVehicle.value) return;

  const vehicle = contextMenuVehicle.value;
  const commandText = command === 'accelerate' ? '加速' : '减速';

  ElMessage.success(`已向车辆 ${vehicle.plateNumber} 发送${commandText}指令`);

  // 这里可以调用实际的API
  console.log(`发送${commandText}指令到车辆:`, vehicle.plateNumber);

  hideContextMenu();
}

// 显示语音消息弹窗
function showVoiceMessageDialog() {
  if (!contextMenuVehicle.value) return;

  voiceMessage.value = '';
  showVoiceDialog.value = true;
  hideContextMenu();
}

// 发送语音消息
function sendVoiceMessage() {
  if (!contextMenuVehicle.value || !voiceMessage.value.trim()) {
    ElMessage.warning('请输入消息内容');
    return;
  }

  const vehicle = contextMenuVehicle.value;
  const message = voiceMessage.value.trim();

  ElMessage.success(`已向车辆 ${vehicle.plateNumber} 发送语音消息`);

  // 这里可以调用实际的API
  console.log(`发送语音消息到车辆 ${vehicle.plateNumber}:`, message);

  showVoiceDialog.value = false;
  voiceMessage.value = '';
}

// 测试右键菜单
function testContextMenu() {
  console.log('Testing context menu');
  contextMenuVehicle.value = {
    vehicleId: 'test',
    plateNumber: 'TEST001',
    segmentIndex: 0,
    offset: 0,
    status: 'running'
  };

  contextMenuStyle.value = {
    left: '200px',
    top: '200px'
  };

  showContextMenu.value = true;
  console.log('Context menu visibility:', showContextMenu.value);
}

function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'running': '运营中',
    'stopped': '停靠中',
    'locked': '离线',
    'parked': '停车',
    'charging': '充能',
    'repair': '修车',
    'charter': '包车'
  };
  return statusMap[status] || '未知状态';
}

function getVehicleTagType(status: string): string {
  const typeMap: Record<string, string> = {
    'running': 'success',
    'stopped': 'warning',
    'locked': 'danger',
    'parked': 'info',
    'charging': 'primary',
    'repair': 'danger',
    'charter': ''
  };
  return typeMap[status] || 'info';
}

// 简化的位置样式计算 - 优化性能
function getPosStyle(total: number, index: number) {
  const config = getDensityConfig();
  const { spacing, padding, maxVisible } = config;

  if (total <= 1) return { left: `${padding}px` };

  if (total <= maxVisible) {
    // 使用简化的百分比计算
    const percent = total === 2 ? (index === 0 ? 0 : 100) : (index / (total - 1)) * 100;
    return { left: `calc(${padding}px + (100% - ${padding * 2}px) * ${percent / 100})` };
  } else {
    // 固定间距布局
    return { left: `${padding + index * spacing}px` };
  }
}

function getVehicleStyle(totalStations: number, v: VehicleLite) {
  const config = getDensityConfig();
  const { spacing, padding, maxVisible } = config;
  const segments = Math.max(1, totalStations - 1);

  if (totalStations <= 1) return { left: `${padding}px` };

  // 计算车辆位置 - 简化逻辑
  const progress = Math.min(1, Math.max(0, (v.segmentIndex + v.offset) / segments));

  if (totalStations <= maxVisible) {
    return { left: `calc(${padding}px + (100% - ${padding * 2}px) * ${progress})` };
  } else {
    const left = Math.min(padding + (totalStations - 1) * spacing, Math.max(padding, padding + progress * (totalStations - 1) * spacing));
    return { left: `${left}px` };
  }
}

// 优化的密度配置获取 - 使用缓存，调整为更紧凑的布局
const densityConfigCache = new Map();
const getDensityConfig = () => {
  const key = displayDensity.value;
  if (densityConfigCache.has(key)) {
    return densityConfigCache.get(key);
  }

  const configs = {
    compact: { spacing: 25, padding: 40, maxVisible: 80 }, // 增加padding：8px → 40px，为边缘车辆预留空间
    normal: { spacing: 35, padding: 50, maxVisible: 60 }, // 增加padding：12px → 50px
    loose: { spacing: 50, padding: 60, maxVisible: 40 } // 增加padding：16px → 60px
  };

  const config = configs[key] || configs.normal;
  densityConfigCache.set(key, config);
  return config;
};

// 简化的轨道宽度计算
function getTrackWidth(total: number) {
  const { spacing, padding, maxVisible } = getDensityConfig();
  return total <= maxVisible ? '100%' : `${total * spacing + padding * 2}px`;
}

// 简化的线条样式获取
function getLineStyle(total: number) {
  const { spacing, padding, maxVisible } = getDensityConfig();

  if (total <= 1) return { left: `${padding}px`, width: '0px' };

  const width = total <= maxVisible ? `calc(100% - ${padding * 2}px)` : `${(total - 1) * spacing}px`;

  return { left: `${padding}px`, width };
}

// 自动检测设备性能并设置优化模式
function autoDetectPerformanceMode() {
  // 强制启用高性能模式
  performanceMode.value = 'performance-mode';
  console.log('🚀 强制启用高性能模式以获得最佳性能');
}
</script>

<style scoped>
/* 使用深度选择器强制覆盖Element UI弹窗样式 */
:deep(.el-dialog.vehicle-detail-dialog) {
  background: linear-gradient(145deg, #1e293b 0%, #334155 50%, #1e293b 100%) !important;
  border: 2px solid rgba(59, 130, 246, 0.4) !important;
  border-radius: 16px !important;
  color: #e2e8f0 !important;
}

:deep(.el-dialog.vehicle-detail-dialog .el-dialog__header) {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 50%, rgba(30, 41, 59, 0.95) 100%) !important;
  border-bottom: 2px solid rgba(59, 130, 246, 0.3) !important;
  color: #e2e8f0 !important;
}

:deep(.el-dialog.vehicle-detail-dialog .el-dialog__body) {
  background: transparent !important;
  color: #e2e8f0 !important;
}
/* 全屏布局基础样式 */
.vehicle-monitor-fullscreen {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  bottom: 0;
  color: #e5e7eb;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 1;
}

/* ===== 多层动态背景系统 ===== */
.background-system {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

/* 基础渐变层 - 深色科技感 */
.bg-gradient-base {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(ellipse at top left, rgba(15, 23, 42, 0.9) 0%, transparent 50%),
    radial-gradient(ellipse at bottom right, rgba(30, 41, 59, 0.9) 0%, transparent 50%),
    linear-gradient(135deg, #0a0e1a 0%, #0f172a 25%, #1e293b 75%, #0f172a 100%);
  animation: gradientShift 20s ease-in-out infinite;
}

@keyframes gradientShift {
  0%,
  100% {
    filter: hue-rotate(0deg) brightness(1);
  }
  33% {
    filter: hue-rotate(-10deg) brightness(1.05);
  }
  66% {
    filter: hue-rotate(10deg) brightness(0.95);
  }
}

/* ===== 性能优化模式样式 ===== */
/* 高性能模式 - 禁用所有动画和视觉效果 */
.vehicle-monitor-fullscreen.performance-mode {
  /* 强制GPU加速 */
  will-change: auto;
  transform: translateZ(0);

  /* 简化背景系统 */
  .background-system {
    /* 只保留基础渐变，移除所有动画 */
    .bg-gradient-base {
      animation: none !important;
      background: linear-gradient(135deg, #0a0e1a 0%, #0f172a 25%, #1e293b 75%, #0f172a 100%) !important;
    }
  }

  /* 禁用充电指示器动画 */
  .charging-indicator {
    animation: none !important;
  }

  /* 简化统计卡片动画 */
  .stat-card {
    transition: none !important;

    &::before {
      display: none !important;
    }

    &:hover {
      transform: none !important;
      box-shadow: 0 2px 8px rgba(96, 165, 250, 0.1) !important;
    }
  }

  /* 简化面板动画 */
  .left-route-panel {
    transition: width 0.2s ease !important;

    &::before {
      display: none !important;
    }

    .panel-main,
    .panel-header,
    .panel-content,
    .search-box,
    .route-tree {
      transition: none !important;
    }
  }

  /* 简化路线卡片效果 */
  .route-detail-block {
    backdrop-filter: none !important;

    &::before {
      display: none !important;
    }
  }

  /* 优化轨道渲染性能 */
  .track-wrapper {
    /* 启用硬件加速 */
    will-change: scroll-position;
    transform: translateZ(0);
  }

  /* 禁用所有站点和车辆悬停效果 */
  .station:hover,
  .vehicle-marker:hover {
    transform: translateX(-50%) !important;
    z-index: auto !important;
  }

  .station:hover .station-dot {
    transform: translateX(-50%) !important;
  }

  .vehicle-marker:hover .vehicle-icon {
    filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6)) !important;
  }

  /* 简化站点名称和车牌号样式 */
  .station-name,
  .vehicle-plate {
    transition: none !important;

    &:hover {
      background: rgba(15, 23, 42, 0.95) !important;
      border-color: rgba(147, 197, 253, 0.2) !important;
      box-shadow: none !important;
      z-index: auto !important;
    }
  }

  /* 禁用停车场动画 */
  .dispatch-vehicle-slot,
  .parking-slot {
    transition: none !important;

    &:hover {
      transform: none !important;
      box-shadow: 0 1px 3px rgba(59, 130, 246, 0.2) !important;
    }
  }

  /* 禁用所有关键帧动画 */
  * {
    animation-duration: 0s !important;
    animation-delay: 0s !important;
    animation-iteration-count: 1 !important;
  }
}

/* 平衡模式 - 保留基本动画，移除复杂效果 */
.vehicle-monitor-fullscreen.balanced-mode {
  /* 简化背景动画 */
  .background-system {
    .bg-gradient-base {
      animation-duration: 40s !important; /* 减慢动画速度 */
    }
  }

  /* 减少过渡时间 */
  .left-route-panel,
  .stat-card,
  .route-detail-block {
    transition-duration: 0.2s !important;
  }

  /* 简化悬停效果 */
  .vehicle-marker:hover,
  .station:hover {
    transition-duration: 0.1s !important;
  }
}

/* 顶部工具栏 - 优化为更紧凑 */
.monitor-header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px; /* 减少内边距 */
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 50%, rgba(15, 23, 42, 0.98) 100%);
  backdrop-filter: blur(8px) saturate(120%); /* 简化模糊效果 */
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 0 40px rgba(59, 130, 246, 0.1) inset;
  z-index: 100;
  min-height: 60px; /* 设置最小高度保持一致性 */
}

/* 顶部工具栏装饰线 */
.monitor-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.6) 20%,
    rgba(59, 130, 246, 0.8) 50%,
    rgba(59, 130, 246, 0.6) 80%,
    transparent 100%
  );
  /* 移除装饰动画以提升性能 */
  /* animation: headerGlow 3s ease-in-out infinite; */
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px; /* 减少间距 */
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 10px; /* 减少间距 */
}

.logo-icon {
  font-size: 24px; /* 减小图标尺寸 */
  color: #60a5fa;
}

.page-title {
  font-size: 20px; /* 减小标题字体 */
  font-weight: 700;
  color: #f8fafc;
  margin: 0;
}

.stats-cards {
  display: flex;
  gap: 12px; /* 减少卡片间距 */
}

.stat-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(41, 52, 70, 0.8) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px; /* 减小圆角 */
  padding: 8px 12px; /* 减少内边距 */
  text-align: center;
  min-width: 70px; /* 减小最小宽度 */
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 统计卡片光效 */
.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
  transition: left 0.5s ease;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
}

.stat-card.online {
  border-color: rgba(34, 197, 94, 0.4);
  background: rgba(34, 197, 94, 0.1);
}

.stat-card.warning {
  border-color: rgba(245, 158, 11, 0.4);
  background: rgba(245, 158, 11, 0.1);
}

.stat-card.success {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(59, 130, 246, 0.1);
}

.stat-value {
  font-size: 20px; /* 减小数值字体 */
  font-weight: 700;
  color: #f8fafc;
  line-height: 1;
}

.stat-label {
  font-size: 11px; /* 减小标签字体 */
  color: #94a3b8;
  margin-top: 2px; /* 减少间距 */
}

.header-right {
  display: flex;
  align-items: center;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 已选线路显示区域 - 优化布局 */
.selected-routes-display {
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 600px;
  min-width: 0; /* 允许内容收缩 */
}

.routes-tags-container {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  max-height: 60px; /* 限制最大高度避免过多换行 */
  overflow-y: auto;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.6);
    border-radius: 2px;
    transition: background 0.2s ease;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.8);
  }
}

.clear-all-btn {
  flex-shrink: 0; /* 防止按钮被压缩 */
  white-space: nowrap; /* 防止按钮文字换行 */
  margin-left: 8px;
}

.selected-label {
  color: #94a3b8;
  font-size: 14px;
  white-space: nowrap;
  flex-shrink: 0; /* 防止标签被压缩 */
}

.selected-route-tag {
  margin: 2px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.selected-route-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

/* 主要内容区域 */
.monitor-main {
  flex: 1;
  position: relative;
  overflow: hidden;
  height: 100%;
  z-index: 1;
}

/* 主内容包装器 */
.main-content-wrapper {
  display: flex;
  height: 100%;
  position: relative;
}

/* 左侧线路选择面板 */
.left-route-panel {
  width: 350px;
  height: 100%;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(20, 28, 46, 0.95) 50%, rgba(15, 23, 42, 0.98) 100%);
  backdrop-filter: blur(8px) saturate(110%); /* 简化模糊效果 */
  border-right: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow:
    4px 0 20px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(59, 130, 246, 0.05) inset;
  transition:
    width 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.3s ease-out 0.1s,
    transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  /* 优化字体渲染 */
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 面板内部装饰光效 */
.left-route-panel::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(180deg, transparent 0%, rgba(59, 130, 246, 0.4) 50%, transparent 100%);
  /* 移除面板装饰动画以提升性能 */
  /* animation: panelGlow 4s ease-in-out infinite; */
}

.left-route-panel.collapsed {
  width: 0;
  opacity: 0;
  transform: translateX(-10px);
  pointer-events: none;
}

.panel-main {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition:
    opacity 0.25s ease-out,
    transform 0.3s ease-out;
  transform-origin: left center;
}

/* 面板收起时内容动画 */
.left-route-panel.collapsed .panel-main {
  opacity: 0;
  transform: translateX(-20px) scale(0.95);
  transition:
    opacity 0.2s ease-in,
    transform 0.2s ease-in;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
  transition:
    opacity 0.3s ease-out 0.05s,
    transform 0.3s ease-out 0.05s;
  white-space: nowrap;
  overflow: hidden;
}

/* 面板收起时头部动画 */
.left-route-panel.collapsed .panel-header {
  opacity: 0;
  transform: translateY(-10px);
  transition:
    opacity 0.15s ease-in,
    transform 0.15s ease-in;
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.panel-header .header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 收起/唤出按钮水平对齐与美化 */
.panel-collapse-btn {
  margin-left: 8px;
  padding: 6px 10px;
  border-radius: 8px;
  color: #cbd5e1;
}

.panel-collapse-btn:hover:not([disabled]) {
  background: rgba(59, 130, 246, 0.15);
  color: #60a5fa;
}

.panel-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  transition:
    opacity 0.3s ease-out 0.1s,
    transform 0.3s ease-out 0.1s;
}

/* 面板收起时内容区域动画 */
.left-route-panel.collapsed .panel-content {
  opacity: 0;
  transform: translateX(-15px);
  transition:
    opacity 0.1s ease-in,
    transform 0.1s ease-in;
}

/* 右侧内容区域 */
.right-content-area {
  flex: 1;
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  margin-left: 0;
  padding-left: 8px; /* 给内容留一点内边距，避免被触发器边缘覆盖 */
}

.right-content-area.panel-collapsed {
  margin-left: 0;
  padding-left: 60px; /* 面板收起时为唤出按钮预留空间，避免遮挡内容（竖向按钮更窄） */
}

/* 唤出按钮 */
.route-panel-trigger {
  position: absolute;
  top: 20px;
  left: 0;
  width: 36px; /* 减小宽度 */
  height: 80px; /* 减小高度 */
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: none; /* 完全移除模糊效果以提升性能 */
  border: 1px solid rgba(147, 197, 253, 0.3);
  border-left: none;
  border-radius: 0 8px 8px 0; /* 减小圆角 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px; /* 减小间距 */
  cursor: pointer;
  z-index: 10;
  transition:
    all 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.4s ease-out 0.2s,
    transform 0.4s ease-out 0.2s;
  color: #94a3b8;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.15); /* 减小阴影 */
  opacity: 0;
  pointer-events: none;
  user-select: none;
  transform: translateX(-10px);
}

.route-panel-trigger.visible {
  left: 0; /* 通过右侧容器 padding-left 让内容右移，不遮挡 */
  opacity: 1;
  pointer-events: auto;
  transform: translateX(0);
}

.route-panel-trigger:hover {
  background: rgba(59, 130, 246, 0.25);
  color: #60a5fa;
  border-color: rgba(96, 165, 250, 0.5);
  transform: translateX(2px); /* 轻微向右移动 */
  box-shadow: 3px 0 15px rgba(96, 165, 250, 0.2);
}

/* 优化的脉冲动画，更加轻微 */
.route-panel-trigger.visible {
  /* 移除脉冲动画以提升性能 */
  /* animation: subtlePulse 3s ease-in-out infinite; */
}

.trigger-icon {
  font-size: 16px;
  transition: transform 0.2s ease;
}

.route-panel-trigger:hover .trigger-icon {
  transform: scale(1.1);
}

.trigger-text {
  font-size: 10px; /* 减小字体 */
  font-weight: 500;
  color: #cbd5e1;
  writing-mode: vertical-rl;
  text-orientation: mixed;
  letter-spacing: 1px;
  transition: color 0.2s ease;
}

.route-panel-trigger:hover .trigger-text {
  color: #e5e7eb;
}

.search-box {
  margin-bottom: 16px;
  transition:
    opacity 0.3s ease-out 0.12s,
    transform 0.3s ease-out 0.12s;
}

/* 面板收起时搜索框动画 */
.left-route-panel.collapsed .search-box {
  opacity: 0;
  transform: translateY(-8px);
  transition:
    opacity 0.08s ease-in,
    transform 0.08s ease-in;
}

.route-tree {
  flex: 1;
  background: transparent;
  color: #cbd5e1;
  transition:
    opacity 0.3s ease-out 0.15s,
    transform 0.3s ease-out 0.15s;
}

/* 面板收起时树形组件动画 */
.left-route-panel.collapsed .route-tree {
  opacity: 0;
  transform: translateX(-10px) scale(0.98);
  transition:
    opacity 0.05s ease-in,
    transform 0.05s ease-in;
}

.route-tree :deep(.el-tree-node__content) {
  height: 40px;
  background: transparent;
  color: #e5e7eb;
  border-radius: 8px;
  margin-bottom: 2px;
  transition: all 0.2s ease;
  padding: 0 8px !important;
}

.route-tree :deep(.el-tree-node__content:hover) {
  background: transparent !important;
}

.route-tree :deep(.el-tree-node__expand-icon) {
  color: #93c5fd;
}

.route-tree :deep(.el-tree-node__label) {
  font-weight: 500;
}

/* 自定义树节点内容样式 */
.tree-node-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.tree-node-content.is-selected {
  background: rgba(34, 197, 94, 0.2) !important;
  border-left: 3px solid #22c55e;
  color: #22c55e !important;
  font-weight: 600;
}

.tree-node-content.is-selected .node-label {
  color: #22c55e !important;
}

.tree-node-content:hover {
  background: rgba(59, 130, 246, 0.1);
}

.tree-node-content.is-selected:hover {
  background: rgba(34, 197, 94, 0.3) !important;
}

.node-label {
  flex: 1;
  font-weight: 500;
}

.selected-icon {
  color: #22c55e;
  font-size: 16px;
  margin-left: 8px;
}

/* 覆盖Element Plus默认样式 */
.route-tree :deep(.el-tree-node__content) {
  padding: 0 !important;
  background: transparent !important;
}

.route-tree :deep(.el-tree-node__content:hover) {
  background: transparent !important;
}

/* 地图容器 - 优化为更紧凑 */
.map-container {
  flex: 1;
  height: 100%;
  padding: 16px; /* 减少内边距 */
  position: relative;
  background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.02) 0%, transparent 70%);
}

/* 选择线路后才显示滚动条 */
.map-container:has(.multi-route-view) {
  overflow: auto;
}

/* 多线路视图 - 减少间距 */
.multi-route-view {
  display: flex;
  flex-direction: column;
  gap: 12px; /* 减少线路卡片间距 */
}

.route-detail-block {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.85) 0%, rgba(25, 35, 50, 0.8) 100%);
  border: 1px solid rgba(59, 130, 246, 0.25);
  border-radius: 12px; /* 减小圆角 */
  padding: 12px; /* 减少内边距 */
  backdrop-filter: blur(6px); /* 大幅简化模糊效果 */
  position: relative;
  overflow: hidden;
}

/* 线路卡片光效 */
.route-detail-block::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.6) 50%, transparent 100%);
  /* 移除路线卡片光效动画以提升性能 */
  /* animation: routeCardGlow 3s ease-in-out infinite; */
}

.route-block-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px; /* 减少底部间距 */
  padding-bottom: 10px; /* 减少内边距 */
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
}

.route-title-section {
  flex: 1;
}

.route-stats {
  display: grid; /* 改为网格布局更紧凑 */
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px; /* 减少间距 */
  margin-top: 8px; /* 减少顶部间距 */
  color: #94a3b8;
  font-size: 13px; /* 减小字体 */
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-item strong {
  color: #f8fafc;
  font-weight: 600;
}

.route-actions {
  display: flex;
  gap: 8px;
}

.route-content {
  display: flex;
  flex-direction: column;
  gap: 12px; /* 减少内容区域间距 */
}

/* 线路实时图 - 紧凑型双轨并行布局 */
.route-realtime-section {
  background: rgba(10, 22, 48, 0.8);
  border: 2px solid rgba(34, 197, 94, 0.3);
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 12px;
}

/* 无标题版本 - 更紧凑 */
.route-realtime-section.no-header {
  padding: 8px 12px;
  margin-bottom: 8px;
}

/* 紧凑型双轨道容器 */
.compact-dual-track-container {
  display: flex;
  flex-direction: column;
  gap: 8px; /* 上下行轨道间距很小 */
}

/* 方向轨道 */
.direction-track {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 8px;
  padding: 8px 12px;
  border: 1px solid rgba(147, 197, 253, 0.2);
}

.direction-track.up-direction {
  border-left: 3px solid #22c55e; /* 上行绿色标识 */
}

.direction-track.down-direction {
  border-left: 3px solid #3b82f6; /* 下行蓝色标识 */
}

/* 轨道头部 */
.track-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  font-weight: 600;
  color: #f8fafc;
}

.vehicle-count {
  margin-left: auto;
  color: #94a3b8;
  font-size: 11px;
  font-weight: 400;
}

/* 移除realtime-header相关样式，为下方留取更多空间 */

/* 待发区容器 - 左右并排布局 */
.dispatch-zones-container {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}

/* 单个待发区 */
.dispatch-zone {
  flex: 1;
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  padding: 12px;
  min-height: 200px; /* 确保有足够高度显示内容 */
}

.dispatch-zone.up-dispatch {
  border-left: 3px solid #22c55e; /* 上行绿色标识 */
}

.dispatch-zone.down-dispatch {
  border-left: 3px solid #3b82f6; /* 下行蓝色标识 */
}

/* 待发区头部 */
.zone-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #f8fafc;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
}

.zone-count {
  margin-left: auto;
  color: #94a3b8;
  font-size: 12px;
  font-weight: 400;
}

/* 待发区内容 - 停车场布局 */
.zone-content {
  padding: 12px;
}

/* 待发区专用停车场布局 - 高密度显示 */
.dispatch-parking-lot {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(45px, 1fr));
  gap: 3px;
  padding: 4px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.4) 0%, rgba(30, 41, 59, 0.3) 100%);
  border-radius: 6px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  min-height: 35px;
  max-height: 140px;
  overflow-y: auto;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(15, 23, 42, 0.3);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.5);
    border-radius: 2px;

    &:hover {
      background: rgba(59, 130, 246, 0.7);
    }
  }

  /* 响应式调整 */
  @media (max-width: 1200px) {
    grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
    gap: 2px;
  }

  @media (max-width: 800px) {
    grid-template-columns: repeat(auto-fill, minmax(35px, 1fr));
    gap: 2px;
    padding: 3px;
  }

  @media (max-width: 600px) {
    grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
    gap: 1px;
    padding: 2px;
  }
}

/* 其他状态车辆的停车场布局 */
.parking-lot {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
  padding: 8px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.4) 0%, rgba(30, 41, 59, 0.3) 100%);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  min-height: 60px;
}

/* 紧凑型停车场（用于其他状态车辆） */
.parking-lot.compact {
  grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
  gap: 6px;
  padding: 6px;
  min-height: 50px;
}

/* 待发区车辆槽 - 紧凑型 */
.dispatch-vehicle-slot {
  background: linear-gradient(135deg, rgba(51, 65, 85, 0.8) 0%, rgba(71, 85, 105, 0.6) 100%);
  border: 1px solid rgba(148, 163, 184, 0.5);
  border-radius: 3px;
  padding: 2px 1px;
  min-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 9px;
  font-weight: 700;
  color: #f1f5f9;
  text-align: center;
  line-height: 1.1;
  word-break: break-all;
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.dispatch-vehicle-slot:hover {
  border-color: rgba(59, 130, 246, 0.8);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3) 0%, rgba(99, 102, 241, 0.2) 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

/* 待发区车辆槽响应式调整 */
@media (max-width: 1200px) {
  .dispatch-vehicle-slot {
    font-size: 8px;
    min-height: 22px;
    padding: 1px;
  }
}

@media (max-width: 800px) {
  .dispatch-vehicle-slot {
    font-size: 7px;
    min-height: 20px;
    padding: 1px;
    font-weight: 800;
  }
}

@media (max-width: 600px) {
  .dispatch-vehicle-slot {
    font-size: 6px;
    min-height: 18px;
    padding: 0px;
    line-height: 1;
  }
}

/* 其他状态车辆的停车位 */
.parking-slot {
  position: relative;
  background: linear-gradient(135deg, rgba(51, 65, 85, 0.6) 0%, rgba(71, 85, 105, 0.4) 100%);
  border: 2px dashed rgba(148, 163, 184, 0.3);
  border-radius: 6px;
  padding: 6px;
  min-height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.parking-slot:hover {
  border-color: rgba(59, 130, 246, 0.6);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(99, 102, 241, 0.1) 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 车辆在停车位中的显示 */
.vehicle-in-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  position: relative;
  width: 100%;
}

.vehicle-in-slot .vehicle-icon {
  font-size: 20px;
  filter: grayscale(0);
  transition: all 0.3s ease;
}

.vehicle-in-slot .vehicle-plate {
  font-size: 10px;
  font-weight: 600;
  color: #e2e8f0;
  text-align: center;
  line-height: 1.2;
  word-break: break-all;
  max-width: 100%;
}

/* 状态指示器 */
.charging-indicator,
.repair-indicator,
.charter-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 充能状态特殊效果 */
.parking-slot.charging {
  border-color: rgba(34, 197, 94, 0.4);
}

.parking-slot.charging:hover {
  border-color: rgba(34, 197, 94, 0.8);
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.1) 100%);
}

.charging-indicator {
  animation: charging-pulse 2s infinite;
}

@keyframes charging-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 维修状态特殊效果 */
.parking-slot.repair {
  border-color: rgba(239, 68, 68, 0.4);
}

.parking-slot.repair:hover {
  border-color: rgba(239, 68, 68, 0.8);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(220, 38, 38, 0.1) 100%);
}

/* 包车状态特殊效果 */
.parking-slot.charter {
  border-color: rgba(245, 158, 11, 0.4);
}

.parking-slot.charter:hover {
  border-color: rgba(245, 158, 11, 0.8);
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(217, 119, 6, 0.1) 100%);
}

/* ===== 车辆右键菜单样式 ===== */
.vehicle-context-menu {
  position: fixed !important;
  z-index: 99999 !important;
  background: linear-gradient(145deg, rgba(30, 41, 59, 0.98) 0%, rgba(51, 65, 85, 0.98) 100%) !important;
  border: 2px solid rgba(59, 130, 246, 0.6) !important;
  border-radius: 8px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.6),
    0 0 20px rgba(59, 130, 246, 0.4) !important;
  backdrop-filter: none; /* 完全移除模糊效果以提升性能 */
  min-width: 160px;
  padding: 8px 0;
  font-size: 14px;
  pointer-events: auto !important;
}

.context-menu-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  color: #3b82f6;
  font-weight: 600;
  font-size: 13px;
}

.context-menu-header .el-icon {
  font-size: 16px;
}

.context-menu-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.3) 50%, transparent 100%);
  margin: 4px 0;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 16px;
  color: #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 13px;
}

.context-menu-item:hover {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.2) 0%, rgba(99, 102, 241, 0.1) 100%);
  color: #60a5fa;
  transform: translateX(2px);
}

.context-menu-item .el-icon {
  font-size: 14px;
  color: #94a3b8;
}

.context-menu-item:hover .el-icon {
  color: #60a5fa;
}

/* ===== 语音消息弹窗样式 ===== */
.voice-message-dialog {
  .el-dialog {
    background: linear-gradient(145deg, #1e293b 0%, #334155 50%, #1e293b 100%) !important;
    border: 2px solid rgba(59, 130, 246, 0.4) !important;
    border-radius: 12px !important;
  }

  .el-dialog__header {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 100%) !important;
    border-bottom: 1px solid rgba(59, 130, 246, 0.3) !important;
    color: #e2e8f0 !important;
  }

  .el-dialog__body {
    background: transparent !important;
    color: #e2e8f0 !important;
  }
}

.voice-message-content {
  .target-vehicle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.05) 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    margin-bottom: 16px;
    color: #3b82f6;
    font-weight: 600;
  }

  .message-input-section {
    margin-bottom: 16px;
  }

  .quick-messages {
    .quick-message-title {
      color: #94a3b8;
      font-size: 13px;
      margin-bottom: 8px;
    }

    .quick-message-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .quick-message-buttons .el-button {
      font-size: 12px;
      padding: 4px 8px;
    }
  }
}

/* 保留原有的direction-section样式用于其他地方 */
.direction-section {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  padding: 10px;
}

.direction-header {
  display: flex;
  align-items: center;
  gap: 6px; /* 减少间距 */
  margin-bottom: 10px; /* 减少底部间距 */
  color: #f8fafc;
  font-weight: 600;
  justify-content: space-between;
  font-size: 14px; /* 添加字体大小控制 */
}

.direction-stats {
  margin-left: auto;
  color: #94a3b8;
  font-size: 12px; /* 减小字体 */
}

.vehicle-zone {
  margin-bottom: 10px; /* 减少底部间距 */
}

.zone-title {
  font-size: 12px; /* 减小字体 */
  color: #cbd5e1;
  margin-bottom: 6px; /* 减少底部间距 */
  font-weight: 500;
}

.vehicle-plates {
  display: flex;
  flex-wrap: wrap;
  gap: 6px; /* 减少间距 */
}

.vehicle-plate-tag {
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px; /* 添加字体大小控制 */
}

.vehicle-plate-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.other-vehicles-section {
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 8px; /* 减小圆角 */
  padding: 10px; /* 减少内边距 */
}

.vehicle-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); /* 减小最小宽度 */
  gap: 10px; /* 减少间距 */
}

.status-group {
  background: rgba(30, 41, 59, 0.4);
  border-radius: 6px; /* 减小圆角 */
  padding: 8px; /* 减少内边距 */
}

.status-title {
  display: flex;
  align-items: center;
  gap: 4px; /* 减少间距 */
  margin-bottom: 6px; /* 减少底部间距 */
  color: #e5e7eb;
  font-weight: 500;
  font-size: 12px; /* 减小字体 */
}

/* 线路概览网格 */
.route-overview {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  max-width: 1200px;
  width: 100%;
}

.route-card {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: none; /* 完全移除模糊效果以提升性能 */
}

.route-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(96, 165, 250, 0.2);
  border-color: rgba(96, 165, 250, 0.4);
}

.route-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.route-metrics .metric {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #94a3b8;
  font-size: 14px;
}

.route-preview {
  margin-top: 16px;
}

.mini-track {
  position: relative;
  height: 40px;
  margin-bottom: 12px;
}

.mini-line {
  position: absolute;
  top: 18px;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #16a34a 0%, #22c55e 50%, #16a34a 100%);
  border-radius: 2px;
}

.mini-vehicle {
  position: absolute;
  top: 8px;
  font-size: 24px;
  z-index: 2;
}

.route-info {
  display: flex;
  justify-content: space-between;
  color: #94a3b8;
  font-size: 14px;
}

/* 线路详情视图 */
.route-detail-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.route-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 20px 0;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  margin-bottom: 24px;
}

.route-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.route-summary-info {
  display: flex;
  gap: 16px;
  color: #94a3b8;
  font-size: 14px;
}

.route-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.route-directions {
  flex: 1;
  overflow: auto;
}

.direction-container {
  margin-bottom: 32px;
}

.direction-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: #f8fafc;
  font-weight: 600;
}

.direction-stats {
  margin-left: auto;
  color: #94a3b8;
  font-size: 14px;
}

.track-container {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  padding: 12px;
  padding-left: 10px;
  padding-right: 10px;
}

/* 优化轨道容器 - 精确计算高度，确保无滚动条 */
.track-container.compact {
  background: rgba(5, 15, 35, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.15);
  padding: 8px 10px;
  height: 130px; /* 固定高度，确保容纳所有内容 */
  overflow: hidden; /* 隐藏溢出，防止滚动条 */
  position: relative;
}

.track-wrapper {
  position: relative;
  height: 114px; /* 容器高度减去padding：130-16=114px */
  overflow-x: auto;
  overflow-y: hidden; /* 隐藏垂直滚动条 */
  scrollbar-width: thin;
  scrollbar-color: rgba(147, 197, 253, 0.3) rgba(10, 22, 48, 0.6);
  padding-bottom: 0; /* 移除底部padding */
}

/* 改进的站点名称显示 - 支持省略号和悬停显示全名 */
.station-name {
  position: absolute;
  top: 55px; /* 进一步上移站点名称：65px → 55px */
  left: 50%;
  transform: translateX(-50%);
  font-size: 11px; /* 稍微减小字体：12px → 11px，避免过宽 */
  color: #e5e7eb;
  background: rgba(15, 23, 42, 0.95);
  padding: 2px 4px; /* 恢复水平内边距，确保有足够空间显示文字 */
  border-radius: 3px;

  max-height: 65px; /* 增加高度：55px → 65px，确保5个字完整显示 */
  max-width: 45px; /* 恢复合理的最大宽度 */
  overflow: hidden;
  line-height: 1.1; /* 恢复合理的行高 */
  font-weight: 500;
  z-index: 20; /* 站点信息层，在圆点之上 */
  border: 1px solid rgba(147, 197, 253, 0.2);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  text-align: center;
  cursor: pointer; /* 添加鼠标指针，提示可交互 */
  transition: all 0.2s ease; /* 添加过渡动画 */
}

/* 站点名称悬停效果 */
.station-name:hover {
  background: rgba(15, 23, 42, 1);
  border-color: rgba(147, 197, 253, 0.5);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 25; /* 悬停时提升层级 */
}

/* 竖直文本改进 - 纵向显示，通过JavaScript实现省略号 */
.vertical-text {
  writing-mode: vertical-rl;
  text-orientation: upright;
  white-space: nowrap; /* 恢复，防止纵向文字换行 */
  -webkit-writing-mode: vertical-rl;
  -ms-writing-mode: tb-rl;
  -moz-writing-mode: vertical-rl;
  letter-spacing: 0.2px; /* 减小字符间距 */
  /* JavaScript处理超过5个字的截断，显示纵向省略号 ⋮ */
}

/* 优化车辆信息显示 - 与车辆图标位置协调 */
.vehicle-plate {
  font-size: 7px; /* 减小字体避免超出容器 */
  color: #f8fafc;
  background: rgba(15, 23, 42, 0.95);
  padding: 1px 4px;
  border-radius: 3px;
  margin-bottom: 2px;
  white-space: nowrap;
  order: -1; /* 显示在车辆图标上方 */
  z-index: 22;
  border: 1px solid rgba(147, 197, 253, 0.3);
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  min-width: 35px; /* 减小最小宽度 */
  text-align: center;
  max-width: 45px; /* 减小最大宽度避免超出容器 */
  overflow: hidden;
  text-overflow: ellipsis;
}

.track-wrapper::-webkit-scrollbar {
  height: 6px;
}

.track-wrapper::-webkit-scrollbar-track {
  background: rgba(10, 22, 48, 0.6);
  border-radius: 3px;
}

.track-wrapper::-webkit-scrollbar-thumb {
  background: rgba(147, 197, 253, 0.3);
  border-radius: 3px;
}

.track-wrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(147, 197, 253, 0.5);
}

.track-content {
  position: relative;
  height: 100%;
  min-width: 100%;
}

/* ===== 新的分层架构样式 ===== */

/* 各个层级的基础样式 - 关键：每个层级容器都需要设置z-index */
.stations-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* 默认不接收鼠标事件，由子元素控制 */
  z-index: 10; /* 站点圆点层 */
}

.stations-info-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 20; /* 站点信息层 */
}

.vehicles-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 30; /* 车辆标记层 */
}

/* 站点圆点包装器 */
.station-dot-wrapper {
  position: absolute;
  top: 0;
  width: 0;
  transform: translateX(-50%);
  pointer-events: none; /* 圆点本身不需要交互 */
}

/* 站点信息和车辆标记需要接收鼠标事件 */
.stations-info-layer .station,
.vehicles-layer .vehicle-marker {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* 车辆标记内部元素不接收事件，避免事件冲突 */
.vehicle-marker .vehicle-icon,
.vehicle-marker .vehicle-plate {
  pointer-events: none !important;
}

/* 通用轨道线基础样式 - 最底层 z-index: 1 */
.track-line {
  position: absolute;
  top: 49px; /* 与站点圆点中心对齐：圆点中心在45px + 4px(半径) = 49px */
  height: 2px;
  border-radius: 1px;
  z-index: 1;
}

/* 上行轨道线 - 绿色渐变 */
.track-line.up-line {
  background: linear-gradient(90deg, #16a34a 0%, #22c55e 40%, #16a34a 100%);
  box-shadow:
    0 0 0 1px rgba(34, 197, 94, 0.2),
    0 0 8px rgba(34, 197, 94, 0.2);
}

/* 下行轨道线 - 蓝色渐变 */
.track-line.down-line {
  background: linear-gradient(90deg, #1e40af 0%, #3b82f6 40%, #1e40af 100%);
  box-shadow:
    0 0 0 1px rgba(59, 130, 246, 0.2),
    0 0 8px rgba(59, 130, 246, 0.2);
}

.station {
  position: absolute;
  top: 0;
  width: 0;
  transform: translateX(-50%);
  text-align: center;
}

/* 统一站点圆点基础样式 - 紧凑版本，现在在独立层级中 z-index: 10 */
.station-dot {
  position: absolute;
  top: 45px; /* 站点圆点垂直位置基准 */
  left: 0; /* 移除手动偏移，完全依赖 transform 居中 */
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #93c5fd;
  border: 1px solid #1e293b;
  box-shadow: 0 0 0 1px rgba(147, 197, 253, 0.4);
  transition: all 0.2s ease;
  z-index: 10; /* 在轨道线之上 */
  pointer-events: none; /* 圆点不接收鼠标事件 */
  transform: translateX(-50%); /* 使用transform居中，消除手动偏移 */
}

/* 站点圆点悬停效果 - 通过父级站点触发 */
.station:hover .station-dot {
  transform: translateX(-50%) scale(1.3); /* 保持居中的同时放大 */
  z-index: 15;
}

/* 关键站点样式 - 紧凑版本 z-index: 11 */
.station-dot.key {
  background: #f59e0b;
  box-shadow: 0 0 0 1px rgba(245, 158, 11, 0.4);
  width: 10px;
  height: 10px;
  top: 44px; /* 保持与轨道线对齐：44px + 5px(半径) = 49px */
  z-index: 11;
}

/* 起始站样式 - 紧凑版本 z-index: 12 */
.station-dot.start-station {
  background: #22c55e;
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.5);
  width: 12px;
  height: 12px;
  top: 43px; /* 保持与轨道线对齐：43px + 6px(半径) = 49px */
  border: 2px solid #1e293b;
  z-index: 12;
}

/* 终点站样式 - 紧凑版本 z-index: 12 */
.station-dot.end-station {
  background: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.5);
  width: 12px;
  height: 12px;
  top: 43px; /* 保持与轨道线对齐：43px + 6px(半径) = 49px */
  border: 2px solid #1e293b;
  z-index: 12;
}

/* 起始站和终点站的站点名称特殊样式 */
.station.first-station .station-name,
.station.last-station .station-name {
  font-weight: 700;
  color: #f8fafc;
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid rgba(147, 197, 253, 0.3);
  padding: 4px 3px;
  z-index: 21; /* 特殊站点名称稍高一点 */
}

/* 起始站名称 - 绿色边框 */
.station.first-station .station-name {
  border-color: rgba(34, 197, 94, 0.5);
  box-shadow: 0 0 8px rgba(34, 197, 94, 0.2);
}

/* 终点站名称 - 红色边框 */
.station.last-station .station-name {
  border-color: rgba(239, 68, 68, 0.5);
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.2);
}

/* 起始站和终点站序号样式 */
.station.first-station .station-sequence,
.station.last-station .station-sequence {
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid rgba(147, 197, 253, 0.4);
  color: #f8fafc;
  font-weight: 600;
}

.station-sequence {
  position: absolute;
  top: 42px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 9px; /* 增大字体以提高可读性 */
  color: #64748b;
  background: rgba(15, 23, 42, 0.8);
  padding: 1px 3px; /* 减小内边距 */
  border-radius: 2px; /* 减小圆角 */
  z-index: 20; /* 站点信息层，在圆点之上 */
  border: 1px solid rgba(147, 197, 253, 0.2);
}

/* 优化车辆标记样式 - 最高层级 z-index: 30 */
.vehicle-marker {
  position: absolute;
  top: 15px; /* 上移车辆标记：20px → 15px */
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  z-index: 30; /* 车辆标记在最高层 */
  transition: all 0.2s ease;
}

.vehicle-marker:hover {
  transform: translateX(-50%) translateY(-3px);
  z-index: 35; /* 悬停时更高 */
}

.vehicle-icon {
  font-size: 18px; /* 稍微减小尺寸避免过于突兀 */
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.4));
  transition: all 0.2s ease;
}

/* 统一车辆颜色 - 不再根据状态区分颜色 */
.vehicle-marker .vehicle-icon {
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6));
}

.vehicle-marker:hover .vehicle-icon {
  filter: drop-shadow(0 0 12px rgba(59, 130, 246, 0.8));
}

.direction-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(147, 197, 253, 0.3) 50%, transparent 100%);
  margin: 16px 0; /* 减少上下间距 */
}

/* 空状态样式 */
.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(15, 23, 42, 0.3);
  border-radius: 16px;
  margin: 24px;
}

.empty-content {
  text-align: center;
  max-width: 400px;
  padding: 40px;
}

.empty-icon {
  font-size: 64px;
  color: #64748b;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 24px;
  color: #f8fafc;
  margin: 0 0 12px 0;
  font-weight: 600;
}

.empty-description {
  color: #94a3b8;
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 24px 0;
}

/* 响应式设计优化 */
@media (max-width: 1200px) {
  .left-route-panel {
    width: 300px;
  }

  .route-panel-trigger {
    width: 40px;
    height: 80px;
  }

  .map-container {
    padding: 12px;
  }

  .stats-cards {
    gap: 8px;
  }

  .stat-card {
    min-width: 60px;
    padding: 6px 10px;
  }
}

@media (max-width: 768px) {
  .vehicle-monitor-fullscreen {
    position: relative;
    top: 0;
  }

  .monitor-header {
    padding: 6px 12px;
    min-height: 50px;
    flex-direction: column;
    gap: 8px;
  }

  .header-left {
    width: 100%;
    justify-content: center;
  }

  .header-right {
    width: 100%;
    justify-content: center;
  }

  .stats-cards {
    gap: 6px;
  }

  .stat-card {
    min-width: 50px;
    padding: 4px 8px;
  }

  .stat-value {
    font-size: 16px;
  }

  .stat-label {
    font-size: 10px;
  }

  .left-route-panel {
    width: 280px;
  }

  /* 移动端默认收起左侧面板 */
  .left-route-panel:not(.collapsed) {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    height: 100vh;
    box-shadow: 8px 0 30px rgba(0, 0, 0, 0.5);
  }

  .panel-content {
    padding: 16px;
  }

  .route-panel-trigger {
    width: 36px;
    height: 70px;
    top: 10px;
  }

  .trigger-text {
    font-size: 9px;
  }

  .right-content-area {
    width: 100%;
    padding-left: 50px;
  }

  .right-content-area.panel-collapsed {
    padding-left: 50px;
  }

  .map-container {
    padding: 8px;
  }

  .route-detail-block {
    padding: 8px;
    border-radius: 8px;
  }

  .route-block-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .route-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
    font-size: 12px;
  }

  .track-container.compact {
    height: 90px; /* 减小轨道容器高度 */
    padding: 4px 6px;
  }

  .track-wrapper {
    height: 90px;
  }

  .station-name {
    font-size: 9px; /* 移动端适中字体大小 */
    padding: 2px 3px;
    max-width: 35px;
  }

  .station-sequence {
    font-size: 6px;
    padding: 0px 2px;
  }

  .station-dot {
    width: 6px; /* 移动端进一步减小站点圆点 */
    height: 6px;
    /* 移除left偏移，使用transform居中 */
  }

  .station-dot.key {
    width: 8px;
    height: 8px;
    /* 移除left偏移，使用transform居中 */
  }

  .station-dot.start-station,
  .station-dot.end-station {
    width: 10px;
    height: 10px;
    /* 移除left偏移，使用transform居中 */
  }

  .vehicle-plate {
    font-size: 8px;
    padding: 1px 3px;
  }

  .dispatch-zones-container {
    flex-direction: column;
    gap: 12px;
  }

  .dispatch-zone {
    min-height: 150px;
    padding: 10px;
  }

  .vehicle-status-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 16px;
  }

  .logo-icon {
    font-size: 20px;
  }

  .left-route-panel {
    width: 100%;
  }

  .right-content-area {
    padding-left: 40px;
  }

  .route-panel-trigger {
    width: 32px;
    height: 60px;
  }

  .route-stats {
    grid-template-columns: 1fr;
    font-size: 11px;
  }

  .track-container.compact {
    height: 70px; /* 小屏幕进一步压缩高度 */
  }

  .track-wrapper {
    height: 70px;
  }

  .station-name {
    font-size: 8px; /* 小屏幕保持一定可读性 */
    max-width: 30px;
    padding: 1px 2px;
  }

  .station-sequence {
    font-size: 5px;
    padding: 0px 1px;
  }

  .station-dot {
    width: 5px; /* 最小站点圆点 */
    height: 5px;
    border-width: 0.5px;
    /* 移除left偏移，使用transform居中 */
  }

  .station-dot.key {
    width: 6px;
    height: 6px;
    /* 移除left偏移，使用transform居中 */
  }

  .station-dot.start-station,
  .station-dot.end-station {
    width: 8px;
    height: 8px;
    border-width: 1px;
    /* 移除left偏移，使用transform居中 */
  }

  .vehicle-icon {
    font-size: 16px;
  }

  .vehicle-plate {
    font-size: 7px;
    padding: 1px 3px;
    min-width: 35px;
    max-width: 50px;
  }
}

/* Element Plus 组件样式覆盖 */
:deep(.el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8);
  border-color: rgba(147, 197, 253, 0.3);
  box-shadow: none;
}

:deep(.el-input__wrapper:hover) {
  border-color: #60a5fa;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 1px rgba(96, 165, 250, 0.4) inset;
}

:deep(.el-input__inner) {
  color: #e5e7eb;
}

:deep(.el-input__inner::placeholder) {
  color: #64748b;
}

:deep(.el-select__popper) {
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: none; /* 完全移除模糊效果以提升性能 */
  border: 1px solid rgba(147, 197, 253, 0.2);
}

:deep(.el-select-dropdown__item) {
  color: #e5e7eb;
}

:deep(.el-select-dropdown__item:hover) {
  background: rgba(59, 130, 246, 0.2);
}

:deep(.el-button) {
  border-color: rgba(147, 197, 253, 0.3);
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: #3b82f6;
}

/* ===== 新的分层架构 - 清晰的层级管理 ===== */
/*
层级说明（关键：容器层级决定了子元素的显示顺序）：
- z-index: 1   - 轨道线 (.track-line)
- z-index: 10  - 站点圆点层容器 (.stations-layer)
  - 内部元素：普通站点圆点、关键站点圆点、起始/终点站圆点
- z-index: 20  - 站点信息层容器 (.stations-info-layer)
  - 内部元素：站点序号 (.station-sequence)、站点名称 (.station-name)
- z-index: 30  - 车辆标记层容器 (.vehicles-layer)
  - 内部元素：车辆标记 (.vehicle-marker)、悬停效果

通过重新设计DOM结构，将不同类型的元素分别放在独立的层级容器中，
并为每个容器设置正确的z-index，确保层级关系正确，避免了堆叠上下文的问题。
*/

/* ===== 车辆详情弹窗样式 - 科技感设计 ===== */

/* 全局覆盖Element UI弹窗样式 - 最高优先级 */
:deep(.el-overlay.tech-modal) {
  background: rgba(0, 0, 0, 0.85) !important;
  backdrop-filter: none !important; /* 移除模糊效果提升性能 */
}

/* 强制覆盖所有Element UI弹窗默认样式 - 使用更高优先级 */
:deep(.el-overlay .el-overlay-dialog .el-dialog.vehicle-detail-dialog),
:deep(.el-overlay .el-overlay-dialog .vehicle-detail-dialog),
:deep(.el-overlay .el-overlay-dialog .tech-style) {
  /* 使用纯色背景替代渐变 */
  background: #1e293b !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  border-radius: 16px !important;
  /* 简化阴影 */
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5) !important;
  margin: 0 !important;
  color: #e2e8f0 !important;
}

/* 确保弹窗容器样式 */
:deep(.el-overlay .el-overlay-dialog .el-dialog) {
  /* 使用纯色背景 */
  background: #1e293b !important;
}

/* 针对车辆详情弹窗的特定样式 */
:deep(.el-dialog[class*='vehicle-detail-dialog']) {
  /* 使用纯色背景 */
  background: #1e293b !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  border-radius: 16px !important;
}

/* 移除科技感边框效果以提升性能 */
:deep(.vehicle-detail-dialog.tech-style .el-dialog::before) {
  display: none !important;
}

:deep(.vehicle-detail-dialog.tech-style) {
  .el-dialog__header {
    /* 使用纯色背景 */
    background: #0f172a !important;
    color: #f1f5f9 !important;
    border-bottom: 1px solid rgba(59, 130, 246, 0.2) !important;
    padding: 20px 24px !important;
    position: relative;
    z-index: 2;

    /* 简化标题效果 */
    .el-dialog__title {
      font-size: 18px !important;
      font-weight: 600 !important;
      /* 移除文字阴影效果 */
      /* text-shadow: 0 0 10px rgba(59, 130, 246, 0.5) !important; */
      letter-spacing: 0.5px !important;
      color: #f1f5f9 !important;
    }
  }

  .el-dialog__body {
    background: transparent !important;
    color: #e2e8f0 !important;
    padding: 24px !important;
    position: relative;
    z-index: 2;
  }
}

/* 边框发光动画已移除以提升性能 */

.vehicle-detail-content {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
  /* 添加CSS containment隔离渲染 */
  contain: layout style paint;
  /* 强制GPU加速 */
  will-change: scroll-position;
  transform: translateZ(0);

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(15, 23, 42, 0.3);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.5);
    border-radius: 3px;

    &:hover {
      background: rgba(59, 130, 246, 0.7);
    }
  }

  .detail-section {
    margin-bottom: 16px;
    /* 使用纯色背景替代渐变 */
    background: rgba(20, 28, 46, 0.6) !important;
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 12px;
    padding: 16px;
    position: relative;
    overflow: hidden;
    /* 移除过渡动画以提升性能 */
    /* transition: all 0.3s ease; */

    &:last-child {
      margin-bottom: 0;
    }

    /* 简化悬停效果，移除动画 */
    &:hover {
      border-color: rgba(59, 130, 246, 0.4);
      /* 移除复杂阴影和变换动画 */
      /* box-shadow:
        0 4px 20px rgba(59, 130, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
      transform: translateY(-2px); */
    }

    /* 完全移除科技感装饰线以提升性能 */
    &::before {
      display: none !important;
    }
  }

  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #3b82f6;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(59, 130, 246, 0.3);
    text-shadow: 0 0 8px rgba(59, 130, 246, 0.3);

    .el-icon {
      font-size: 16px;
      color: #60a5fa;
      filter: drop-shadow(0 0 4px rgba(96, 165, 250, 0.5));
    }
  }

  .detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;

    @media (max-width: 650px) {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }

  .detail-grid-wide {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px 16px;

    @media (max-width: 1200px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media (max-width: 900px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 650px) {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }

  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    /* 完全移除渐变背景，使用纯色 */
    background: rgba(30, 41, 59, 0.5) !important;
    border-radius: 6px;
    border: 1px solid rgba(59, 130, 246, 0.15);
    position: relative;
    /* 移除过渡动画以提升性能 */
    /* transition: all 0.3s ease; */
    min-height: 36px;

    &.full-width {
      grid-column: 1 / -1;
    }

    /* 简化悬停效果，移除动画 */
    &:hover {
      border-color: rgba(59, 130, 246, 0.3);
      /* 移除复杂背景和阴影效果 */
      /* background: linear-gradient(135deg,
        rgba(15, 23, 42, 1) 0%,
        rgba(30, 41, 59, 0.8) 100%);
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1); */
    }

    /* 完全移除装饰线以提升性能 */
    &::before {
      display: none !important;
    }

    .label {
      font-weight: 500;
      color: #94a3b8;
      font-size: 12px;
      letter-spacing: 0.3px;
      white-space: nowrap;
      margin-right: 6px;
      flex-shrink: 0;
    }

    .value {
      font-weight: 600;
      color: #f1f5f9;
      font-size: 12px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      text-align: right;
      flex: 1;
      word-break: break-all;

      /* 车辆状态颜色 - 增强科技感 */
      &.running {
        color: #10b981;
        text-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
      }

      &.stopped {
        color: #ef4444;
        text-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
      }

      &.maintenance {
        color: #f59e0b;
        text-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
      }

      &.status-online {
        color: #10b981;
        display: flex;
        align-items: center;
        gap: 6px;
        text-shadow: 0 0 8px rgba(16, 185, 129, 0.4);

        .el-icon {
          font-size: 16px;
        }
      }

      &.status-running {
        color: #3b82f6;
        font-weight: 600;
        text-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
      }

      &.offline {
        color: #6b7280;
        text-shadow: 0 0 8px rgba(107, 114, 128, 0.4);
      }
    }
  }
}

/* ===== 全局Element UI样式强制覆盖 ===== */
/* 确保所有弹窗相关元素都使用深色主题 - 使用更高优先级 */
:deep(.el-overlay .el-overlay-dialog .el-dialog.vehicle-detail-dialog) {
  background: linear-gradient(145deg, #1e293b 0%, #334155 50%, #1e293b 100%) !important;
  color: #e2e8f0 !important;
}

:deep(.el-overlay .el-overlay-dialog .el-dialog.vehicle-detail-dialog .el-dialog__header) {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 50%, rgba(30, 41, 59, 0.95) 100%) !important;
  border-bottom: 2px solid rgba(59, 130, 246, 0.3) !important;
  color: #e2e8f0 !important;
}

:deep(.el-overlay .el-overlay-dialog .el-dialog.vehicle-detail-dialog .el-dialog__body) {
  background: transparent !important;
  color: #e2e8f0 !important;
}

/* 额外的强制样式 */
:deep(.vehicle-detail-dialog.tech-style) {
  background: linear-gradient(145deg, #1e293b 0%, #334155 50%, #1e293b 100%) !important;
}

.vehicle-detail-dialog.tech-style .el-dialog__header {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 50%, rgba(30, 41, 59, 0.95) 100%) !important;
  border-bottom: 2px solid rgba(59, 130, 246, 0.3) !important;
  color: #e2e8f0 !important;
}

.vehicle-detail-dialog.tech-style .el-dialog__body {
  background: transparent !important;
  color: #e2e8f0 !important;
}

/* 性能优化模式下的弹窗样式 */
.vehicle-monitor-fullscreen.performance-mode {
  :deep(.vehicle-detail-dialog.tech-style) {
    background: #1e293b !important; /* 使用纯色背景 */
    border: 1px solid rgba(59, 130, 246, 0.3) !important; /* 简化边框 */
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5) !important; /* 简化阴影 */

    .el-dialog__header {
      background: #0f172a !important; /* 使用纯色背景 */
    }

    .detail-section {
      background: rgba(30, 41, 59, 0.8) !important; /* 简化背景 */
      transition: none !important;

      &:hover {
        transform: none !important;
        box-shadow: none !important;
      }

      &::before {
        display: none !important; /* 移除装饰线 */
      }
    }

    .detail-item {
      transition: none !important;

      &:hover {
        transform: none !important;
        box-shadow: none !important;
      }

      &::before {
        display: none !important; /* 移除装饰线 */
      }
    }

    /* 禁用弹窗内按钮的动画效果 */
    .el-button {
      transition: none !important;

      &:hover,
      &:focus {
        transform: none !important;
        box-shadow: none !important;
      }
    }

    /* 禁用输入框动画 */
    .el-input {
      .el-input__wrapper {
        transition: none !important;
      }

      .el-input__inner {
        transition: none !important;
      }
    }

    /* 禁用标签动画 */
    .el-tag {
      transition: none !important;
    }
  }

  /* 语音消息弹窗优化 */
  :deep(.voice-message-dialog) {
    .el-button {
      transition: none !important;

      &:hover,
      &:focus {
        transform: none !important;
        box-shadow: none !important;
      }
    }

    .el-input {
      .el-input__wrapper {
        transition: none !important;
      }

      .el-input__inner {
        transition: none !important;
      }
    }
  }
}
</style>
