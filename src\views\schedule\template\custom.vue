<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>自定义排班编辑</span>
          <div>
            <el-button type="info" @click="handleImport">导入模板</el-button>
            <el-button type="primary" @click="handleSave">保存设置</el-button>
          </div>
        </div>
      </template>

      <el-form ref="customRef" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="线路" prop="routeId">
              <el-select v-model="form.routeId" placeholder="请选择线路" @change="handleRouteChange">
                <el-option v-for="route in routeOptions" :key="route.routeId" :label="route.routeName" :value="route.routeId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板名称" prop="templateName">
              <el-input v-model="form.templateName" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left">发车时刻设置</el-divider>

        <div class="schedule-editor">
          <div class="editor-toolbar">
            <el-button type="primary" size="small" @click="addDepartureTime">添加发车时刻</el-button>
            <el-button type="success" size="small" @click="batchAdd">批量添加</el-button>
            <el-button type="warning" size="small" @click="sortSchedule">时间排序</el-button>
            <el-button type="danger" size="small" @click="clearAll">清空全部</el-button>
          </div>

          <div class="time-list">
            <div v-for="(time, index) in form.departureSchedule" :key="index" class="time-item">
              <el-time-picker v-model="time.departureTime" placeholder="发车时间" value-format="HH:mm" size="small" />
              <el-input v-model="time.remark" placeholder="备注" size="small" style="margin-left: 10px; width: 150px" />
              <el-button type="danger" size="small" icon="Delete" @click="removeDepartureTime(index)" style="margin-left: 10px">删除</el-button>
            </div>
          </div>

          <div class="schedule-summary">
            <el-alert :title="`共设置 ${form.departureSchedule.length} 个发车时刻`" type="info" :closable="false" />
          </div>
        </div>

        <el-divider content-position="left">时刻表预览</el-divider>

        <div class="schedule-preview">
          <div class="preview-toolbar">
            <el-button type="info" size="small" @click="previewSchedule">刷新预览</el-button>
            <el-button type="success" size="small" @click="exportSchedule">导出时刻表</el-button>
          </div>

          <div class="time-grid" v-if="sortedSchedule.length > 0">
            <div v-for="(time, index) in sortedSchedule" :key="index" class="time-cell" :class="{ 'time-conflict': time.conflict }">
              <div class="time-value">{{ time.departureTime }}</div>
              <div class="time-remark" v-if="time.remark">{{ time.remark }}</div>
              <div class="conflict-warning" v-if="time.conflict">
                <el-icon color="#f56c6c"><Warning /></el-icon>
                间隔过短
              </div>
            </div>
          </div>
        </div>

        <el-divider content-position="left">高级设置</el-divider>

        <el-row>
          <el-col :span="8">
            <el-form-item label="单程时间(分钟)" prop="singleTripTime">
              <el-input-number v-model="form.singleTripTime" :min="10" :max="300" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最小发车间隔(分钟)" prop="minInterval">
              <el-input-number v-model="form.minInterval" :min="1" :max="30" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否启用" prop="status">
              <el-switch v-model="form.status" active-value="1" inactive-value="0" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="模板描述" prop="templateDescription">
          <el-input v-model="form.templateDescription" type="textarea" :rows="3" placeholder="请输入模板描述" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 批量添加对话框 -->
    <el-dialog title="批量添加发车时刻" v-model="batchOpen" width="600px" append-to-body>
      <el-form ref="batchRef" :model="batchForm" label-width="120px">
        <el-form-item label="开始时间">
          <el-time-picker v-model="batchForm.startTime" placeholder="开始时间" value-format="HH:mm" />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-time-picker v-model="batchForm.endTime" placeholder="结束时间" value-format="HH:mm" />
        </el-form-item>
        <el-form-item label="时间间隔(分钟)">
          <el-input-number v-model="batchForm.interval" :min="1" :max="60" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="batchForm.remark" placeholder="统一备注（可选）" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmBatchAdd">确 定</el-button>
          <el-button @click="batchOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CustomTemplate">
const { proxy } = getCurrentInstance();

const routeOptions = ref([]);
const sortedSchedule = ref([]);
const batchOpen = ref(false);

const data = reactive({
  form: {
    templateId: null,
    templateName: null,
    routeId: null,
    routeName: null,
    templateType: 'custom',
    singleTripTime: 45,
    minInterval: 3,
    status: '1',
    templateDescription: null,
    remark: null,
    departureSchedule: [
      { departureTime: '06:00', remark: '首班车' },
      { departureTime: '22:00', remark: '末班车' }
    ]
  },
  batchForm: {
    startTime: '07:00',
    endTime: '19:00',
    interval: 10,
    remark: ''
  },
  rules: {
    routeId: [{ required: true, message: '请选择线路', trigger: 'change' }],
    templateName: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }]
  }
});

const { form, batchForm, rules } = toRefs(data);

/** 获取线路选项 */
function getRouteOptions() {
  routeOptions.value = [
    { routeId: 1, routeName: '1路' },
    { routeId: 2, routeName: '2路' },
    { routeId: 3, routeName: '3路' }
  ];
}

/** 添加发车时刻 */
function addDepartureTime() {
  form.value.departureSchedule.push({
    departureTime: '08:00',
    remark: ''
  });
}

/** 删除发车时刻 */
function removeDepartureTime(index) {
  form.value.departureSchedule.splice(index, 1);
}

/** 批量添加 */
function batchAdd() {
  batchOpen.value = true;
}

/** 确认批量添加 */
function confirmBatchAdd() {
  if (!batchForm.value.startTime || !batchForm.value.endTime) {
    proxy.$modal.msgError('请设置开始和结束时间');
    return;
  }

  const startTime = parseTime(batchForm.value.startTime);
  const endTime = parseTime(batchForm.value.endTime);
  const interval = batchForm.value.interval;

  if (startTime >= endTime) {
    proxy.$modal.msgError('结束时间必须晚于开始时间');
    return;
  }

  let currentTime = startTime;
  const newSchedule = [];

  while (currentTime <= endTime) {
    newSchedule.push({
      departureTime: formatTime(currentTime),
      remark: batchForm.value.remark
    });
    currentTime += interval;
  }

  form.value.departureSchedule.push(...newSchedule);
  batchOpen.value = false;
  proxy.$modal.msgSuccess(`批量添加了 ${newSchedule.length} 个发车时刻`);
}

/** 时间排序 */
function sortSchedule() {
  form.value.departureSchedule.sort((a, b) => {
    return parseTime(a.departureTime) - parseTime(b.departureTime);
  });
  proxy.$modal.msgSuccess('时间排序完成');
}

/** 清空全部 */
function clearAll() {
  proxy.$modal
    .confirm('是否确认清空所有发车时刻？')
    .then(() => {
      form.value.departureSchedule = [];
      proxy.$modal.msgSuccess('清空成功');
    })
    .catch(() => {});
}

/** 预览时刻表 */
function previewSchedule() {
  const schedule = [...form.value.departureSchedule]
    .filter((item) => item.departureTime)
    .sort((a, b) => parseTime(a.departureTime) - parseTime(b.departureTime));

  // 检查时间冲突
  for (let i = 0; i < schedule.length; i++) {
    schedule[i].conflict = false;
    if (i > 0) {
      const prevTime = parseTime(schedule[i - 1].departureTime);
      const currentTime = parseTime(schedule[i].departureTime);
      if (currentTime - prevTime < form.value.minInterval) {
        schedule[i].conflict = true;
      }
    }
  }

  sortedSchedule.value = schedule;
}

/** 导出时刻表 */
function exportSchedule() {
  proxy.$modal.msgSuccess('时刻表导出功能开发中');
}

/** 导入模板 */
function handleImport() {
  proxy.$modal.msgSuccess('模板导入功能开发中');
}

/** 解析时间字符串为分钟数 */
function parseTime(timeStr) {
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
}

/** 格式化分钟数为时间字符串 */
function formatTime(minutes) {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}

function handleRouteChange(routeId) {
  const route = routeOptions.value.find((r) => r.routeId === routeId);
  if (route) {
    form.value.routeName = route.routeName;
    if (!form.value.templateName) {
      form.value.templateName = `${route.routeName}自定义模板`;
    }
  }
}

function handleSave() {
  proxy.$refs['customRef'].validate((valid) => {
    if (valid) {
      if (form.value.departureSchedule.length === 0) {
        proxy.$modal.msgError('请至少设置一个发车时刻');
        return;
      }

      // 检查是否有冲突
      previewSchedule();
      const hasConflict = sortedSchedule.value.some((item) => item.conflict);
      if (hasConflict) {
        proxy.$modal
          .confirm('检测到发车时间间隔过短，是否继续保存？')
          .then(() => {
            proxy.$modal.msgSuccess('自定义排班模板保存成功');
          })
          .catch(() => {});
      } else {
        proxy.$modal.msgSuccess('自定义排班模板保存成功');
      }
    }
  });
}

onMounted(() => {
  getRouteOptions();
  previewSchedule();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.schedule-editor {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.editor-toolbar {
  margin-bottom: 15px;
}

.time-list {
  max-height: 300px;
  overflow-y: auto;
}

.time-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 10px;
  background: white;
  border-radius: 4px;
}

.schedule-summary {
  margin-top: 15px;
}

.schedule-preview {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.preview-toolbar {
  margin-bottom: 15px;
}

.time-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
  max-height: 400px;
  overflow-y: auto;
}

.time-cell {
  background: white;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
  border: 1px solid #e4e7ed;
}

.time-cell.time-conflict {
  border-color: #f56c6c;
  background: #fef0f0;
}

.time-value {
  font-weight: bold;
  font-size: 14px;
  color: #409eff;
}

.time-remark {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.conflict-warning {
  font-size: 10px;
  color: #f56c6c;
  margin-top: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
