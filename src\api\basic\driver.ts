import request from '@/utils/request';
import { AxiosPromise } from 'axios';

// 司机信息接口类型定义
export interface DriverVO {
  driverId?: number;
  employeeId: string;
  driverName: string;
  gender?: string;
  age?: number;
  phone?: string;
  idCard?: string;
  licenseNumber?: string;
  licenseType?: string;
  deptId?: number | string;
  deptName?: string;
  hireDate?: string;
  status?: string;
  remark?: string;
}

export interface DriverForm extends DriverVO {}

export interface DriverQuery {
  pageNum?: number;
  pageSize?: number;
  driverName?: string;
  employeeId?: string;
  status?: string;
  deptId?: number | string;
}

// 查询司机列表
export function listDriver(query: DriverQuery): AxiosPromise<DriverVO[]> {
  return request({
    url: '/basic/driver/list',
    method: 'get',
    params: query
  });
}

// 查询司机详细
export function getDriver(driverId: string | number): AxiosPromise<DriverVO> {
  return request({
    url: '/basic/driver/' + driverId,
    method: 'get'
  });
}

// 新增司机
export function addDriver(data: DriverForm): AxiosPromise<void> {
  return request({
    url: '/basic/driver',
    method: 'post',
    data: data
  });
}

// 修改司机
export function updateDriver(data: DriverForm): AxiosPromise<void> {
  return request({
    url: '/basic/driver',
    method: 'put',
    data: data
  });
}

// 删除司机
export function delDriver(driverId: string | number | Array<string | number>): AxiosPromise<void> {
  return request({
    url: '/basic/driver/' + driverId,
    method: 'delete'
  });
}
