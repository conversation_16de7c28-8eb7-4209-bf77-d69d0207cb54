<template>
  <div class="announcement-test-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>
          <el-icon><Operation /></el-icon>
          报站系统测试平台
        </h1>
        <p>测试报站文件生成接口和配置功能</p>
      </div>
      <div class="header-right">
        <el-tag :type="statsData.configValid ? 'success' : 'warning'" size="large">
          {{ statsData.configValid ? '配置完整' : '配置不完整' }}
        </el-tag>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 左侧控制面板 -->
      <el-col :span="8">
        <el-card header="控制面板" class="control-panel">
          <!-- 版本选择 -->
          <div class="control-section">
            <h3>文件版本</h3>
            <el-select v-model="selectedVersion" placeholder="选择文件版本" style="width: 100%">
              <el-option
                v-for="(desc, version) in versionOptions"
                :key="version"
                :label="'版本 ' + version + ' - ' + desc"
                :value="Number(version)"
              />
            </el-select>
            <p class="version-desc" v-if="selectedVersionDesc">{{ selectedVersionDesc }}</p>
          </div>

          <!-- 操作按钮 -->
          <div class="control-section">
            <h3>文件操作</h3>
            <div class="button-group">
              <el-button 
                type="primary" 
                @click="generateFiles" 
                :loading="generating"
                icon="Download"
                style="width: 100%; margin-bottom: 10px;"
              >
                生成配置文件包
              </el-button>
              
              <el-button 
                @click="previewFiles" 
                :loading="previewing"
                icon="View"
                style="width: 100%; margin-bottom: 10px;"
              >
                预览文件结构
              </el-button>
              
              <el-button 
                @click="validateConfig" 
                :loading="validating"
                icon="CircleCheck"
                style="width: 100%; margin-bottom: 10px;"
              >
                验证配置
              </el-button>
            </div>
          </div>

          <!-- 单独下载 -->
          <div class="control-section">
            <h3>单独下载</h3>
            <div class="button-group">
              <el-button 
                @click="downloadInfoLibrary" 
                :loading="downloadingLibrary"
                icon="DataBoard"
                size="small"
                style="width: 48%; margin-right: 4%;"
              >
                信息库文件
              </el-button>
              
              <el-button 
                @click="downloadSystemFiles" 
                :loading="downloadingSystem"
                icon="Setting"
                size="small"
                style="width: 48%;"
              >
                系统配置
              </el-button>
            </div>
          </div>

          <!-- 系统操作 -->
          <div class="control-section">
            <h3>系统操作</h3>
            <div class="button-group">
              <el-button 
                @click="refreshStats" 
                :loading="loadingStats"
                icon="Refresh"
                size="small"
                style="width: 48%; margin-right: 4%;"
              >
                刷新状态
              </el-button>
              
              <el-button 
                @click="getConfigWizard" 
                :loading="loadingWizard"
                icon="Guide"
                size="small" 
                style="width: 48%;"
              >
                配置向导
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- 统计信息 -->
        <el-card header="系统状态" class="stats-card">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ statsData.stationCount }}</div>
              <div class="stat-label">站点数量</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ statsData.lineCount }}</div>
              <div class="stat-label">线路数量</div>
            </div>
          </div>

          <!-- 配置向导进度 -->
          <div v-if="wizardData" class="wizard-progress">
            <h4>配置进度</h4>
            <el-progress 
              :percentage="wizardData.completionRate" 
              :color="getProgressColor(wizardData.completionRate)"
            />
            <p class="next-step" v-if="wizardData.nextStep != 'completed'">
              <el-icon><InfoFilled /></el-icon>
              {{ wizardData.nextStepDescription }}
            </p>
            <el-alert
              v-else
              title="配置已完成！"
              type="success"
              :closable="false"
            />
          </div>
        </el-card>
      </el-col>

      <!-- 右侧结果展示 -->
      <el-col :span="16">
        <el-tabs v-model="activeTab" type="card">
          <!-- 文件预览 -->
          <el-tab-pane label="文件结构" name="preview">
            <el-card>
              <div v-if="previewData">
                <div class="preview-header">
                  <h3>文件结构预览 (版本: {{ previewData.version }})</h3>
                  <el-tag>{{ previewData.totalStations }} 个站点</el-tag>
                  <el-tag>{{ previewData.totalLines }} 条线路</el-tag>
                </div>
                
                <el-tree
                  :data="treeData"
                  :props="{ label: 'label', children: 'children' }"
                  :default-expand-all="true"
                  node-key="id"
                  class="file-tree"
                >
                  <template #default="{ data }">
                    <span class="tree-node">
                      <el-icon v-if="data.isFolder"><Folder /></el-icon>
                      <el-icon v-else><Document /></el-icon>
                      {{ data.label }}
                      <el-tag v-if="data.description" size="small" type="info">
                        {{ data.description }}
                      </el-tag>
                    </span>
                  </template>
                </el-tree>
              </div>
              <el-empty v-else description="请先点击预览文件结构" />
            </el-card>
          </el-tab-pane>

          <!-- 配置验证 -->
          <el-tab-pane label="配置验证" name="validation">
            <el-card>
              <div v-if="validationData">
                <div class="validation-header">
                  <h3>配置验证结果</h3>
                  <el-tag :type="validationData.valid ? 'success' : 'danger'" size="large">
                    {{ validationData.valid ? '验证通过' : '验证失败' }}
                  </el-tag>
                </div>

                <!-- 错误信息 -->
                <div v-if="validationData.errors.length > 0" class="validation-section">
                  <h4>错误信息</h4>
                  <el-alert
                    v-for="(error, index) in validationData.errors"
                    :key="index"
                    :title="error"
                    type="error"
                    :closable="false"
                    style="margin-bottom: 10px;"
                  />
                </div>

                <!-- 警告信息 -->
                <div v-if="validationData.warnings.length > 0" class="validation-section">
                  <h4>警告信息</h4>
                  <el-alert
                    v-for="(warning, index) in validationData.warnings"
                    :key="index"
                    :title="warning"
                    type="warning"
                    :closable="false"
                    style="margin-bottom: 10px;"
                  />
                </div>

                <!-- 统计摘要 -->
                <div class="validation-summary">
                  <h4>配置摘要</h4>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="站点数量">{{ validationData.stationCount }}</el-descriptions-item>
                    <el-descriptions-item label="线路数量">{{ validationData.lineCount }}</el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
              <el-empty v-else description="请先点击验证配置" />
            </el-card>
          </el-tab-pane>

          <!-- 系统配置 -->
          <el-tab-pane label="系统配置" name="config">
            <el-card>
              <div v-if="systemConfig">
                <h3>报站系统配置</h3>
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="系统版本">{{ systemConfig.reportSystemVersion }}</el-descriptions-item>
                  <el-descriptions-item label="当前版本">{{ systemConfig.currentVersion }}</el-descriptions-item>
                  <el-descriptions-item label="编码格式">{{ systemConfig.codeFormat === 1 ? 'UTF-8' : 'GB2312' }}</el-descriptions-item>
                  <el-descriptions-item label="语言数量">{{ systemConfig.languageCount }}</el-descriptions-item>
                  <el-descriptions-item label="报站方式">{{ systemConfig.reportType === 1 ? '手动' : '自动' }}</el-descriptions-item>
                  <el-descriptions-item label="车内音量">{{ systemConfig.reportVolumeIn }}%</el-descriptions-item>
                  <el-descriptions-item label="车外音量">{{ systemConfig.reportVolumeOut }}%</el-descriptions-item>
                  <el-descriptions-item label="报站半径">{{ systemConfig.reportRadiusMeter }}米</el-descriptions-item>
                </el-descriptions>
              </div>
              <el-empty v-else description="正在加载系统配置..." />
            </el-card>
          </el-tab-pane>

          <!-- 操作日志 -->
          <el-tab-pane label="操作日志" name="logs">
            <el-card>
              <div class="log-section">
                <div class="log-header">
                  <h3>操作日志</h3>
                  <el-button @click="clearLogs" size="small" type="danger">清空日志</el-button>
                </div>
                <div class="log-container" ref="logContainer">
                  <div 
                    v-for="(log, index) in operationLogs.slice(0, 20)" 
                    :key="`${log.time}-${index}`"
                    :class="['log-item', log.type]"
                  >
                    <span class="log-time">{{ log.time }}</span>
                    <span class="log-message">{{ log.message }}</span>
                  </div>
                  <div v-if="operationLogs.length > 20" class="log-item info">
                    <span class="log-time">--</span>
                    <span class="log-message">还有 {{ operationLogs.length - 20 }} 条更早的日志...</span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Operation,
  Download,
  View,
  CircleCheck,
  DataBoard,
  Setting,
  Refresh,
  Guide,
  InfoFilled,
  Folder,
  Document
} from '@element-plus/icons-vue';

// API 导入
import {
  generateAnnouncementFiles,
  previewFileStructure,
  validateAnnouncementConfig,
  downloadInfoLibrary,
  downloadSystemFiles,
  getSupportedVersions,
  getSystemConfig,
  getAnnouncementStats,
  getConfigWizard
} from '@/api/announcement';

import type {
  AnnouncementStats,
  ConfigValidation,
  FilePreview,
  ConfigWizard,
  SupportedVersions,
  AnnouncementSystemConfig
} from '@/api/announcement/types';

// 响应式数据
const selectedVersion = ref<number>(20231221);
const activeTab = ref('preview');

// 加载状态
const generating = ref(false);
const previewing = ref(false);
const validating = ref(false);
const downloadingLibrary = ref(false);
const downloadingSystem = ref(false);
const loadingStats = ref(false);
const loadingWizard = ref(false);

// 数据状态
const statsData = ref<AnnouncementStats>({
  stationCount: 0,
  lineCount: 0,
  tenantId: '',
  configValid: false,
  validationSummary: {
    valid: false,
    errors: [],
    warnings: [],
    stationCount: 0,
    lineCount: 0
  }
});

const previewData = ref<FilePreview | null>(null);
const validationData = ref<ConfigValidation | null>(null);
const wizardData = ref<ConfigWizard | null>(null);
const systemConfig = ref<AnnouncementSystemConfig | null>(null);
const versionOptions = ref<Record<string, string>>({});

// 操作日志
const operationLogs = ref<Array<{
  time: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
}>>([]);

// 计算属性
const selectedVersionDesc = computed(() => {
  const versions = versionOptions.value;
  return versions[selectedVersion.value.toString()] || '';
});

const treeData = computed(() => {
  if (!previewData.value?.structure) return [];
  
  try {
    return Object.entries(previewData.value.structure).map(([key, value]) => ({
      id: key,
      label: key,
      isFolder: true,
      children: typeof value === 'object' && value !== null ? 
        Object.entries(value as Record<string, any>).map(([subKey, subValue]) => ({
          id: `${key}-${subKey}`,
          label: subKey,
          isFolder: false,
          description: typeof subValue === 'string' ? subValue : undefined
        })) : []
    }));
  } catch (error) {
    console.warn('Error processing tree data:', error);
    return [];
  }
});

// 方法
const logContainer = ref<HTMLElement>();

const addLog = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
  // 简化日志操作，避免性能问题
  const log = {
    time: new Date().toLocaleTimeString(),
    message,
    type
  };
  
  // 限制日志数量，避免内存泄漏
  if (operationLogs.value.length >= 20) {
    operationLogs.value.splice(10); // 保留最新10条
  }
  
  operationLogs.value.unshift(log);
  
  // 同时在控制台输出，方便调试
  console.log(`[${type.toUpperCase()}] ${message}`);
};

const clearLogs = () => {
  operationLogs.value = [];
};

const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c';
  if (percentage < 70) return '#e6a23c';
  return '#67c23a';
};

// API 调用方法
const generateFiles = async () => {
  generating.value = true;
  addLog(`开始生成版本 ${selectedVersion.value} 的配置文件...`);
  
  try {
    const response = await generateAnnouncementFiles(selectedVersion.value);
    
    // 下载文件
    const blob = new Blob([response], { type: 'application/zip' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `announcement_config_v${selectedVersion.value}.zip`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    addLog('配置文件包生成成功！', 'success');
    ElMessage.success('配置文件包生成成功！');
  } catch (error) {
    const errorMsg = `生成配置文件包失败: ${error}`;
    addLog(errorMsg, 'error');
    ElMessage.error('生成配置文件包失败');
  } finally {
    generating.value = false;
  }
};

const previewFiles = async () => {
  previewing.value = true;
  addLog('正在预览文件结构...');
  
  try {
    const response = await previewFileStructure(selectedVersion.value);
    previewData.value = response.data;
    activeTab.value = 'preview';
    
    addLog('文件结构预览成功', 'success');
    ElMessage.success('文件结构预览成功');
  } catch (error) {
    addLog(`预览文件结构失败: ${error}`, 'error');
    ElMessage.error('预览文件结构失败');
  } finally {
    previewing.value = false;
  }
};

const validateConfig = async () => {
  validating.value = true;
  addLog('正在验证配置完整性...');
  
  try {
    const response = await validateAnnouncementConfig();
    validationData.value = response.data;
    activeTab.value = 'validation';
    
    const resultMsg = response.data.valid ? '配置验证通过' : '配置验证失败';
    addLog(resultMsg, response.data.valid ? 'success' : 'warning');
    ElMessage({
      message: resultMsg,
      type: response.data.valid ? 'success' : 'warning'
    });
  } catch (error) {
    addLog(`验证配置失败: ${error}`, 'error');
    ElMessage.error('验证配置失败');
  } finally {
    validating.value = false;
  }
};

const downloadInfoLibrary = async () => {
  downloadingLibrary.value = true;
  addLog('正在下载信息库文件...');
  
  try {
    const response = await downloadInfoLibrary(selectedVersion.value);
    
    const blob = new Blob([response], { type: 'application/octet-stream' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `infoLibrary_v${selectedVersion.value}.dat`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    addLog('信息库文件下载成功', 'success');
    ElMessage.success('信息库文件下载成功');
  } catch (error) {
    addLog(`下载信息库文件失败: ${error}`, 'error');
    ElMessage.error('下载信息库文件失败');
  } finally {
    downloadingLibrary.value = false;
  }
};

const downloadSystemFiles = async () => {
  downloadingSystem.value = true;
  addLog('正在下载系统配置文件...');
  
  try {
    const response = await downloadSystemFiles();
    
    const blob = new Blob([response], { type: 'application/zip' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `system_config.zip`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    addLog('系统配置文件下载成功', 'success');
    ElMessage.success('系统配置文件下载成功');
  } catch (error) {
    addLog(`下载系统配置文件失败: ${error}`, 'error');
    ElMessage.error('下载系统配置文件失败');
  } finally {
    downloadingSystem.value = false;
  }
};

const refreshStats = async () => {
  loadingStats.value = true;
  addLog('正在刷新系统状态...');
  
  try {
    const response = await getAnnouncementStats();
    statsData.value = response.data;
    
    addLog('系统状态刷新成功', 'success');
  } catch (error) {
    addLog(`刷新系统状态失败: ${error}`, 'error');
    ElMessage.error('刷新系统状态失败');
  } finally {
    loadingStats.value = false;
  }
};

const getConfigWizard = async () => {
  loadingWizard.value = true;
  addLog('正在获取配置向导信息...');
  
  try {
    const response = await getConfigWizard();
    wizardData.value = response.data;
    
    addLog('配置向导信息获取成功', 'success');
  } catch (error) {
    addLog(`获取配置向导失败: ${error}`, 'error');
    ElMessage.error('获取配置向导失败');
  } finally {
    loadingWizard.value = false;
  }
};

// 初始化
onMounted(async () => {
  // 简化初始化，避免卡死
  console.log('初始化报站系统测试平台...');
  
  // 设置默认值，避免API调用失败导致卡死
  versionOptions.value = {
    '1': '基础版本',
    '20200803': '2020年8月版本',
    '20210617': '2021年6月版本', 
    '20231221': '2023年12月版本'
  };
  selectedVersion.value = 20231221;
  
  // 设置默认系统配置
  systemConfig.value = {
    reportSystemVersion: 1,
    codeFormat: 1,
    languageCount: 4,
    reportType: 2,
    reportVolumeIn: 80,
    reportVolumeOut: 85,
    reportRadiusMeter: 100,
    currentVersion: '24121415301234'
  };
  
  // 异步加载真实数据，不阻塞UI
  setTimeout(async () => {
    try {
      const versionsResponse = await getSupportedVersions();
      if (versionsResponse?.data?.description) {
        versionOptions.value = versionsResponse.data.description;
        selectedVersion.value = versionsResponse.data.recommended;
      }
    } catch (error) {
      console.warn('加载版本信息失败:', error);
    }
    
    try {
      const configResponse = await getSystemConfig();
      if (configResponse?.data) {
        systemConfig.value = configResponse.data;
      }
    } catch (error) {
      console.warn('加载系统配置失败:', error);
    }
  }, 100);
});
</script>

<style scoped lang="scss">
.announcement-test-page {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    
    .header-left {
      h1 {
        margin: 0;
        display: flex;
        align-items: center;
        font-size: 24px;
        
        .el-icon {
          margin-right: 10px;
        }
      }
      
      p {
        margin: 5px 0 0 0;
        opacity: 0.8;
      }
    }
  }
  
  .control-panel {
    margin-bottom: 20px;
    
    .control-section {
      margin-bottom: 25px;
      
      h3 {
        margin: 0 0 10px 0;
        font-size: 14px;
        color: #606266;
        font-weight: 600;
      }
      
      .version-desc {
        font-size: 12px;
        color: #909399;
        margin-top: 5px;
      }
      
      .button-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
    }
  }
  
  .stats-card {
    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin-bottom: 20px;
      
      .stat-item {
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 6px;
        
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #409eff;
        }
        
        .stat-label {
          font-size: 12px;
          color: #909399;
          margin-top: 5px;
        }
      }
    }
    
    .wizard-progress {
      h4 {
        margin: 0 0 10px 0;
        font-size: 14px;
      }
      
      .next-step {
        margin: 10px 0 0 0;
        font-size: 12px;
        color: #606266;
        display: flex;
        align-items: center;
        
        .el-icon {
          margin-right: 5px;
        }
      }
    }
  }
  
  .preview-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      flex: 1;
    }
  }
  
  .file-tree {
    .tree-node {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .el-icon {
        color: #409eff;
      }
    }
  }
  
  .validation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
    }
  }
  
  .validation-section {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 10px 0;
      font-size: 14px;
    }
  }
  
  .validation-summary {
    h4 {
      margin: 0 0 10px 0;
      font-size: 14px;
    }
  }
  
  .log-section {
    .log-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      
      h3 {
        margin: 0;
      }
    }
    
    .log-container {
      max-height: 400px;
      overflow-y: auto;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      /* 优化滚动性能 */
      transform: translateZ(0);
      will-change: scroll-position;
      
      .log-item {
        padding: 8px 12px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
        font-size: 12px;
        /* 优化渲染性能 */
        contain: layout;
        
        &:last-child {
          border-bottom: none;
        }
        
        .log-time {
          color: #909399;
          margin-right: 10px;
          min-width: 80px;
        }
        
        .log-message {
          flex: 1;
        }
        
        &.success {
          background-color: #f0f9ff;
          border-left: 3px solid #67c23a;
        }
        
        &.error {
          background-color: #fef0f0;
          border-left: 3px solid #f56c6c;
        }
        
        &.warning {
          background-color: #fdf6ec;
          border-left: 3px solid #e6a23c;
        }
        
        &.info {
          background-color: #f4f4f5;
          border-left: 3px solid #909399;
        }
      }
    }
  }
}
</style>