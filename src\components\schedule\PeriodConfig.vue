<!-- 分时段模式参数配置组件 -->
<template>
  <div class="period-config">
    <el-form ref="formRef" :model="config" label-width="120px" size="large">
      <div class="config-section">
        <h4 class="section-title">
          <el-icon><Clock /></el-icon>
          基本设置
        </h4>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="模板名称" required>
              <el-input v-model="config.templateName" placeholder="请输入模板名称" maxlength="50" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="config-section">
        <h4 class="section-title">
          <el-icon><Timer /></el-icon>
          时段配置
          <el-button type="primary" size="small" @click="addPeriod">
            <el-icon><CirclePlus /></el-icon>
            添加时段
          </el-button>
        </h4>

        <div class="periods-container">
          <div v-for="(period, index) in config.periods" :key="index" class="period-item">
            <div class="period-header">
              <span class="period-title">时段 {{ index + 1 }}</span>
              <el-button type="danger" size="small" text @click="removePeriod(index)" v-if="config.periods.length > 1">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开始时间">
                  <el-time-picker v-model="period.startTime" placeholder="开始时间" value-format="HH:mm" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结束时间">
                  <el-time-picker v-model="period.endTime" placeholder="结束时间" value-format="HH:mm" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="发车间隔(分钟)">
                  <el-input-number v-model="period.interval" :min="3" :max="60" :step="1" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="时段描述">
                  <el-input v-model="period.description" placeholder="如：早高峰" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <div class="quick-presets">
          <h5>快速预设：</h5>
          <el-button-group>
            <el-button @click="applyPreset('standard')">标准模式</el-button>
            <el-button @click="applyPreset('rush')">高峰加密</el-button>
            <el-button @click="applyPreset('weekend')">周末模式</el-button>
          </el-button-group>
        </div>
      </div>

      <div class="preview-section">
        <h4 class="section-title">
          <el-icon><DataAnalysis /></el-icon>
          预计效果
        </h4>
        <div class="preview-stats">
          <div class="stat-card">
            <div class="stat-value">{{ totalTrips }}</div>
            <div class="stat-label">总班次</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ peakInterval }}</div>
            <div class="stat-label">最小间隔</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ operatingHours }}</div>
            <div class="stat-label">运营时长</div>
          </div>
        </div>

        <div class="period-timeline">
          <h5>时段分布：</h5>
          <div class="timeline">
            <div v-for="(period, index) in config.periods" :key="index" class="timeline-item" :style="{ backgroundColor: getColorByIndex(index) }">
              <span>{{ period.description || `时段${index + 1}` }}</span>
              <small>{{ period.startTime }}-{{ period.endTime }} ({{ period.interval }}分钟)</small>
            </div>
          </div>
        </div>
      </div>

      <div class="config-section">
        <h4 class="section-title">
          <el-icon><EditPen /></el-icon>
          其他设置
        </h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="单程时间">
              <el-input-number v-model="config.singleTripTime" :min="10" :max="300" :step="5" style="width: 100%" />
              <div class="form-tip">用于计算车辆配车数量</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="缓冲时间">
              <el-input-number v-model="config.bufferTime" :min="0" :max="30" :step="1" style="width: 100%" />
              <div class="form-tip">时段间的缓冲时间(分钟)</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="模板描述">
          <el-input v-model="config.description" type="textarea" :rows="3" placeholder="请输入模板描述（可选）" maxlength="200" show-word-limit />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Clock, Timer, CirclePlus, Delete, DataAnalysis, EditPen } from '@element-plus/icons-vue';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue']);

// Form reference
const formRef = ref();

const config = ref({
  templateName: '',
  routeId: null,
  periods: [
    { startTime: '06:00', endTime: '09:00', interval: 8, description: '早高峰' },
    { startTime: '09:00', endTime: '17:00', interval: 15, description: '平峰' },
    { startTime: '17:00', endTime: '20:00', interval: 8, description: '晚高峰' },
    { startTime: '20:00', endTime: '22:00', interval: 20, description: '夜间' }
  ],
  singleTripTime: 60,
  bufferTime: 2,
  description: '',
  ...props.modelValue
});

const routeOptions = ref([
  { routeId: 1, routeName: '1路' },
  { routeId: 2, routeName: '2路' },
  { routeId: 3, routeName: '3路' },
  { routeId: 4, routeName: '4路' }
]);

// 添加时段
function addPeriod() {
  config.value.periods.push({
    startTime: '22:00',
    endTime: '23:00',
    interval: 30,
    description: '新时段'
  });
}

// 删除时段
function removePeriod(index) {
  config.value.periods.splice(index, 1);
}

// 应用预设
function applyPreset(type) {
  switch (type) {
    case 'standard':
      config.value.periods = [
        { startTime: '06:00', endTime: '09:00', interval: 10, description: '早高峰' },
        { startTime: '09:00', endTime: '17:00', interval: 15, description: '平峰' },
        { startTime: '17:00', endTime: '20:00', interval: 10, description: '晚高峰' },
        { startTime: '20:00', endTime: '22:00', interval: 20, description: '夜间' }
      ];
      break;
    case 'rush':
      config.value.periods = [
        { startTime: '06:00', endTime: '08:30', interval: 5, description: '早高峰' },
        { startTime: '08:30', endTime: '17:30', interval: 12, description: '平峰' },
        { startTime: '17:30', endTime: '20:00', interval: 5, description: '晚高峰' },
        { startTime: '20:00', endTime: '22:00', interval: 15, description: '夜间' }
      ];
      break;
    case 'weekend':
      config.value.periods = [
        { startTime: '07:00', endTime: '11:00', interval: 15, description: '上午' },
        { startTime: '11:00', endTime: '18:00', interval: 20, description: '下午' },
        { startTime: '18:00', endTime: '21:00', interval: 15, description: '晚间' }
      ];
      break;
  }
}

// 计算总班次
const totalTrips = computed(() => {
  let total = 0;
  config.value.periods.forEach((period) => {
    if (period.startTime && period.endTime && period.interval) {
      const [startHour, startMin] = period.startTime.split(':').map(Number);
      const [endHour, endMin] = period.endTime.split(':').map(Number);
      const startMinutes = startHour * 60 + startMin;
      const endMinutes = endHour * 60 + endMin;
      const duration = endMinutes - startMinutes;
      if (duration > 0) {
        total += Math.floor(duration / period.interval);
      }
    }
  });
  return total || '--';
});

// 计算最小间隔
const peakInterval = computed(() => {
  const intervals = config.value.periods.filter((p) => p.interval).map((p) => p.interval);
  return intervals.length ? Math.min(...intervals) + '分钟' : '--';
});

// 计算运营时长
const operatingHours = computed(() => {
  if (!config.value.periods.length) return '--';

  const periods = config.value.periods.filter((p) => p.startTime && p.endTime).sort((a, b) => a.startTime.localeCompare(b.startTime));

  if (!periods.length) return '--';

  const firstPeriod = periods[0];
  const lastPeriod = periods[periods.length - 1];

  const [firstHour, firstMin] = firstPeriod.startTime.split(':').map(Number);
  const [lastHour, lastMin] = lastPeriod.endTime.split(':').map(Number);

  const firstMinutes = firstHour * 60 + firstMin;
  const lastMinutes = lastHour * 60 + lastMin;
  const totalMinutes = lastMinutes - firstMinutes;

  if (totalMinutes <= 0) return '--';

  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  return `${hours}小时${minutes}分钟`;
});

// 获取时段颜色
function getColorByIndex(index) {
  const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
  return colors[index % colors.length];
}

// 监听配置变化
watch(
  config,
  (newValue) => {
    emit('update:modelValue', newValue);
  },
  { deep: true }
);
</script>

<style scoped>
.period-config {
  padding: 20px 0;
}

.config-section {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.6) 0%, rgba(30, 41, 59, 0.4) 100%);
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
}

.section-title .el-button {
  margin-left: auto;
}

.periods-container {
  margin-bottom: 20px;
}

.period-item {
  background: linear-gradient(135deg, rgba(51, 65, 85, 0.6) 0%, rgba(71, 85, 105, 0.4) 100%);
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid rgba(147, 197, 253, 0.2);
}

.period-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.period-title {
  font-weight: 600;
}

.quick-presets {
  background: linear-gradient(135deg, rgba(51, 65, 85, 0.4) 0%, rgba(71, 85, 105, 0.2) 100%);
  padding: 16px;
  border-radius: 6px;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

.quick-presets h5 {
  margin: 0 0 12px 0;
  color: #e2e8f0;
  font-size: 14px;
}

.preview-section {
  background: transparent;
  color: #e2e8f0;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.preview-section .section-title {
  color: #e2e8f0;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
}

.preview-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px 16px;
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.3);
  transition: all 0.3s ease;
}

.stat-card:hover {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(15, 23, 42, 0.5);
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  color: #60a5fa;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #cbd5e1;
  font-weight: 500;
}

.stat-card {
  background: rgba(255, 255, 255, 0.15);
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  backdrop-filter: blur(10px);
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.period-timeline h5 {
  margin: 0 0 12px 0;
  color: white;
  font-size: 14px;
}

.timeline {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.timeline-item {
  padding: 8px 12px;
  border-radius: 6px;
  color: white;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.timeline-item span {
  font-weight: 600;
  margin-bottom: 4px;
}

.form-tip {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

/* Element Plus 深色主题样式覆盖 */
:deep(.el-input .el-input__count .el-input__count-inner) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #94a3b8 !important;
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-form-item__label) {
  color: #e2e8f0 !important;
}

:deep(.el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  box-shadow: none !important;
}

:deep(.el-input__inner) {
  color: #e2e8f0 !important;
}

:deep(.el-input__inner::placeholder) {
  color: #94a3b8 !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

:deep(.el-time-picker .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

/* 时间选择器面板样式修复 */
:deep(.el-time-picker__popper),
:deep(.el-picker-panel) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(147, 197, 253, 0.3) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
}

:deep(.el-time-panel) {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(147, 197, 253, 0.3) !important;
  color: #e2e8f0 !important;
}

:deep(.el-time-panel__header) {
  border-bottom-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-time-panel__content) {
  background: transparent !important;
}

:deep(.el-time-spinner) {
  background: transparent !important;
}

:deep(.el-time-spinner__wrapper) {
  border-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-time-spinner__list) {
  background: transparent !important;
}

:deep(.el-time-spinner__item) {
  color: #e2e8f0 !important;
  background: transparent !important;
  height: 32px !important;
  line-height: 32px !important;
  font-size: 14px !important;
}

:deep(.el-time-spinner__item:hover) {
  background: rgba(59, 130, 246, 0.2) !important;
  color: #ffffff !important;
}

:deep(.el-time-spinner__item.active) {
  background: rgba(59, 130, 246, 0.3) !important;
  color: #ffffff !important;
  font-weight: 600 !important;
}

:deep(.el-time-spinner__item.disabled) {
  color: #64748b !important;
  background: transparent !important;
  cursor: not-allowed !important;
}

/* 确保时间面板显示在正确的层级 */
:deep(.el-picker-panel) {
  z-index: 9999 !important;
}

:deep(.el-time-panel__footer) {
  border-top-color: rgba(147, 197, 253, 0.2) !important;
  background: rgba(30, 41, 59, 0.5) !important;
}

:deep(.el-time-panel__btn) {
  color: #e2e8f0 !important;
}

:deep(.el-time-panel__btn:hover) {
  color: #3b82f6 !important;
}

:deep(.el-time-panel__btn.confirm) {
  color: #3b82f6 !important;
  font-weight: 600 !important;
}

:deep(.el-input-number .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  background: rgba(30, 41, 59, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e2e8f0 !important;
}

:deep(.el-textarea__inner) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e2e8f0 !important;
}
</style>
