<template>
  <div class='vehicle-anomaly-analysis'>
    <el-row :gutter='20'>
      <!-- 左侧组织机构树 -->
      <el-col :lg='4' :xs='24'>
        <div class='left-panel'>
          <div class='panel-header'>
            <div class='header-left'>
              <el-icon>
                <OfficeBuilding />
              </el-icon>
              <span>组织机构筛选</span>
            </div>
          </div>
          <div class='panel-content'>
            <div class='search-box'>
              <el-input v-model='deptName' placeholder='请输入组织机构' prefix-icon='Search' clearable />
            </div>

            <el-tree
              ref='deptTreeRef'
              class='dept-tree'
              node-key='id'
              :data='deptOptions'
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node='false'
              :filter-node-method='filterNode'
              highlight-current
              default-expand-all
              @node-click='handleNodeClick'
            >
              <template #default="{ node, data }">
                <span class="tree-node">
                  <el-icon class="dept-icon">
                    <OfficeBuilding />
                  </el-icon>
                  <span class="node-label dept">{{ data.label }}</span>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
      </el-col>

      <el-col :lg='20' :xs='24'>
        <div class='right-content'>
          <!-- 筛选条件区域 -->
          <div class='filter-section'>
            <el-card class='filter-card'>
              <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="90px">
                <el-form-item label="时间范围" prop="dateRange">
                  <el-date-picker
                    v-model="queryParams.dateRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    size="default"
                    style="width: 320px"
                  />
                </el-form-item>
                <el-form-item label="车牌号" prop="plateNumber">
                  <el-input
                    v-model="queryParams.plateNumber"
                    placeholder="请输入车牌号"
                    clearable
                    style="width: 160px"
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="车辆编号" prop="vehicleNumber">
                  <el-input
                    v-model="queryParams.vehicleNumber"
                    placeholder="请输入车辆编号"
                    clearable
                    style="width: 120px"
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="线路" prop="routeId">
                  <el-select v-model="queryParams.routeId" placeholder="请选择线路" clearable style="width: 120px">
                    <el-option
                      v-for="route in routeOptions"
                      :key="route.value"
                      :label="route.label"
                      :value="route.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="异常类型" prop="anomalyType">
                  <el-select v-model="queryParams.anomalyType" placeholder="请选择异常类型" clearable style="width: 140px">
                    <el-option label="全部" value="" />
                    <el-option label="通信中断" value="communication" />
                    <el-option label="GPS信号丢失" value="gps_lost" />
                    <el-option label="设备故障" value="device_fault" />
                    <el-option label="电源异常" value="power_abnormal" />
                    <el-option label="网络异常" value="network_abnormal" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
                  <el-button @click="resetQuery" icon="Refresh">重置</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>

          <!-- 统计卡片区域 -->
          <div class='stats-section'>
            <el-row :gutter='16'>
              <el-col :span='8'>
                <div class='stat-card total'>
                  <div class='stat-icon'>
                    <el-icon><Warning /></el-icon>
                  </div>
                  <div class='stat-info'>
                    <div class='stat-value'>{{ statsData.totalAnomalies }}</div>
                    <div class='stat-label'>异常总数</div>
                  </div>
                </div>
              </el-col>
              <el-col :span='8'>
                <div class='stat-card processed'>
                  <div class='stat-icon'>
                    <el-icon><CircleCheck /></el-icon>
                  </div>
                  <div class='stat-info'>
                    <div class='stat-value'>{{ statsData.processedCount }}</div>
                    <div class='stat-label'>已处理</div>
                  </div>
                </div>
              </el-col>
              <el-col :span='8'>
                <div class='stat-card unprocessed'>
                  <div class='stat-icon'>
                    <el-icon><Clock /></el-icon>
                  </div>
                  <div class='stat-info'>
                    <div class='stat-value'>{{ statsData.unprocessedCount }}</div>
                    <div class='stat-label'>未处理</div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- Tab切换区域 -->
          <div class='tabs-section'>
            <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
              <!-- 图表分析 -->
              <el-tab-pane label="图表分析" name="charts">
                <div class="tab-content">
                  <!-- 图表区域 -->
                  <div class='charts-section'>
                    <el-row :gutter='16'>
                      <el-col :span='8'>
                        <div class='chart-card'>
                          <div class='chart-header'>
                            <h3>异常类型分布</h3>
                          </div>
                          <div ref="pieChartRef" class='chart-container'></div>
                        </div>
                      </el-col>
                      <el-col :span='16'>
                        <div class='chart-card'>
                          <div class='chart-header'>
                            <h3>异常数量趋势图</h3>
                          </div>
                          <div ref="trendChartRef" class='chart-container'></div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 明细表格 -->
              <el-tab-pane label="明细表格" name="table">
                <div class="tab-content">
                  <!-- 数据表格 -->
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <h3>车辆异常统计表</h3>
                    </div>
            <el-table :data="vehicleAnomalyList" @selection-change="handleSelectionChange" height="520" max-height="720">
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="序号" type="index" width="80" align="center" />
              <el-table-column label="车牌号" align="center" prop="plateNumber" width="120" />
              <el-table-column label="车辆编号" align="center" prop="vehicleNumber" width="120" />
              <el-table-column label="所属线路" align="center" prop="routeName" width="100" />
              <el-table-column label="所属组织机构" align="center" prop="deptName" width="120" />
              <el-table-column label="在线状态" align="center" prop="onlineStatus" width="100">
                <template #default="scope">
                  <el-tag
                    :type="scope.row.onlineStatus === 'online' ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ scope.row.onlineStatus === 'online' ? '在线' : '离线' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="最后在线时间" align="center" prop="lastOnlineTime" width="160">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.lastOnlineTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
              <el-table-column label="离线时长" align="center" prop="offlineDuration" width="120">
                <template #default="scope">
                  <span :class="getOfflineDurationClass(scope.row.offlineDuration)">
                    {{ formatDuration(scope.row.offlineDuration) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="GPS位置" align="center" prop="lastLocation" width="200" show-overflow-tooltip />
              <el-table-column label="设备状态" align="center" prop="deviceStatus" width="100">
                <template #default="scope">
                  <el-tag
                    :type="getDeviceStatusType(scope.row.deviceStatus)"
                    size="small"
                  >
                    {{ getDeviceStatusText(scope.row.deviceStatus) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="异常类型" align="center" prop="anomalyType" width="120">
                <template #default="scope">
                  <el-tag
                    :type="getAnomalyTypeStyle(scope.row.anomalyType)"
                    size="small"
                  >
                    {{ scope.row.anomalyType }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="备注" align="center" prop="remark" min-width="150" show-overflow-tooltip />
              <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width" fixed="right">
                <template #default="scope">
                  <el-button link type="success" icon="Check" @click="handleProcess(scope.row)">处理</el-button>
                  <el-button link type="warning" icon="Position" @click="handleLocate(scope.row)">定位</el-button>
                </template>
              </el-table-column>
            </el-table>

                    <pagination
                      v-show="total > 0"
                      :total="total"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getList"
                    />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
    </el-row>


  </div>
</template>

<script setup name="VehicleAnomalyAnalysis">
import { ref, reactive, onMounted, nextTick, watchEffect } from 'vue';
import { TrendCharts, Van, OfficeBuilding, Warning, CircleCheck, Clock, Check } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';

const loading = ref(true);
const total = ref(0);
const vehicleAnomalyList = ref([]);
const activeTab = ref('charts');

// 树形相关
const deptName = ref('');
const deptTreeRef = ref();

// 图表引用
const pieChartRef = ref(null);
const trendChartRef = ref(null);
let pieChart = null;
let trendChart = null;

// 表单引用
const queryRef = ref();

// 统计数据
const statsData = ref({
  totalAnomalies: '128',
  processedCount: '95',
  unprocessedCount: '33'
});

// 组织机构和车辆树数据
const deptOptions = ref([]);

// 线路选项
const routeOptions = ref([
  { label: '115路', value: '115' },
  { label: '135路', value: '135' },
  { label: '201路', value: '201' },
  { label: '202路', value: '202' },
  { label: '301路', value: '301' }
]);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  dateRange: null,
  plateNumber: null,
  vehicleNumber: null,
  deptId: null,
  routeId: null,
  anomalyType: null
});

onMounted(() => {
  getTreeSelect();
  handleTabChange(activeTab.value);
  nextTick(() => {
    initCharts();
  });
});

/** 组织机构树筛选 */
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选组织机构树 */
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

/** Tab切换事件 */
function handleTabChange(tabName) {
  activeTab.value = tabName;
  loading.value = true;

  switch (tabName) {
    case 'charts':
      // 图表分析不需要加载表格数据，只更新图表
      loading.value = false;
      break;
    case 'table':
      getList();
      break;
  }
}

/** 查询组织机构下拉树结构 */
async function getTreeSelect() {
  try {
    deptOptions.value = [
      {
        id: 1,
        label: '总公司',
        children: [
          {
            id: 2,
            label: '运营部',
            children: [
              { id: 3, label: '第一车队' },
              { id: 4, label: '第二车队' },
              { id: 5, label: '第三车队' }
            ]
          },
          {
            id: 6,
            label: '维修部',
            children: [
              { id: 7, label: '机修组' },
              { id: 8, label: '电修组' }
            ]
          }
        ]
      }
    ];
  } catch (error) {
    console.error('获取组织机构树失败:', error);
  }
}

/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.deptId = data.id;
  queryParams.plateNumber = null;
  queryParams.vehicleNumber = null;
  handleQuery();
}

function getList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateMockData();
    vehicleAnomalyList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    total.value = mockData.length;
    loading.value = false;
  }, 500);
}

function generateMockData() {
  const data = [];
  const vehicles = [
    { plateNumber: '京A12345', vehicleNumber: 'V001', routeName: '115路', deptName: '第一车队', deptId: 3 },
    { plateNumber: '京A12346', vehicleNumber: 'V002', routeName: '135路', deptName: '第二车队', deptId: 4 },
    { plateNumber: '京A12347', vehicleNumber: 'V003', routeName: '201路', deptName: '机修组', deptId: 6 },
    { plateNumber: '京A12348', vehicleNumber: 'V004', routeName: '115路', deptName: '第一车队', deptId: 3 },
    { plateNumber: '京A12349', vehicleNumber: 'V005', routeName: '301路', deptName: '第二车队', deptId: 4 },
    { plateNumber: '京A12351', vehicleNumber: 'V007', routeName: '135路', deptName: '第二车队', deptId: 4 },
    { plateNumber: '京A12352', vehicleNumber: 'V008', routeName: '301路', deptName: '机修组', deptId: 6 },
    { plateNumber: '京A12353', vehicleNumber: 'V009', routeName: '115路', deptName: '第一车队', deptId: 3 },
    { plateNumber: '京A12354', vehicleNumber: 'V010', routeName: '202路', deptName: '第二车队', deptId: 4 },
    { plateNumber: '京A12355', vehicleNumber: 'V011', routeName: '135路', deptName: '电修组', deptId: 7 }
  ];

  for (let i = 0; i < 50; i++) {
    const vehicle = vehicles[i % vehicles.length];
    const baseDate = new Date();
    baseDate.setHours(baseDate.getHours() - Math.random() * 72);

    let shouldInclude = true;

    if (queryParams.plateNumber && !vehicle.plateNumber.includes(queryParams.plateNumber)) {
      shouldInclude = false;
    }

    if (queryParams.deptId) {
      const deptIds = getDeptIdsRecursive(queryParams.deptId);
      if (!deptIds.includes(vehicle.deptId)) {
        shouldInclude = false;
      }
    }

    const onlineStatus = Math.random() > 0.3 ? 'online' : 'offline';

    if (queryParams.onlineStatus && queryParams.onlineStatus !== onlineStatus) {
      shouldInclude = false;
    }

    if (!shouldInclude) continue;

    const offlineDurationMinutes = onlineStatus === 'offline' ? Math.floor(Math.random() * 2880) + 30 : 0;

    data.push({
      id: i + 1,
      ...vehicle,
      onlineStatus,
      lastOnlineTime: baseDate.toISOString(),
      offlineDuration: offlineDurationMinutes,
      lastLocation: `${(116.3 + Math.random() * 0.1).toFixed(6)}, ${(39.9 + Math.random() * 0.1).toFixed(6)}`,
      deviceStatus: ['normal', 'fault', 'maintenance'][Math.floor(Math.random() * 3)],
      anomalyType: onlineStatus === 'offline' ?
        ['通信中断', 'GPS信号丢失', '设备故障', '电源异常', '网络异常'][Math.floor(Math.random() * 5)] :
        '正常',
      simNumber: `1380138${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`,
      imeiNumber: `86012345${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      signalStrength: Math.floor(Math.random() * 5) + 1,
      anomalyDescription: onlineStatus === 'offline' ? '车辆设备失联，无法获取实时位置信息' : '',
      remark: Math.random() > 0.5 ? '已安排技术人员处理' : null
    });
  }

  return data;
}

// 递归获取组织机构及其子组织机构的ID
function getDeptIdsRecursive(deptId) {
  const ids = [deptId];

  function findChildren(nodes, parentId) {
    nodes.forEach(node => {
      if (node.id === parentId && node.children) {
        node.children.forEach(child => {
          if (child.type === 'dept') {
            ids.push(child.id);
            findChildren(node.children, child.id);
          }
        });
      } else if (node.children) {
        findChildren(node.children, parentId);
      }
    });
  }

  findChildren(deptOptions.value, deptId);
  return ids;
}

// 初始化图表
function initCharts() {
  initPieChart();
  initTrendChart();
}

// 初始化饼图
function initPieChart() {
  if (!pieChartRef.value) return;

  pieChart = echarts.init(pieChartRef.value);
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' }
    },
    legend: {
      orient: 'vertical',
      left: 'right',
      textStyle: { color: '#e5e7eb' },
      top: 'center'
    },
    series: [
      {
        name: '异常类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#0f172a',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold',
            color: '#e5e7eb'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 35, name: '通信中断', itemStyle: { color: '#F56C6C' } },
          { value: 28, name: 'GPS信号丢失', itemStyle: { color: '#E6A23C' } },
          { value: 22, name: '设备故障', itemStyle: { color: '#409EFF' } },
          { value: 18, name: '电源异常', itemStyle: { color: '#909399' } },
          { value: 25, name: '网络异常', itemStyle: { color: '#67C23A' } }
        ]
      }
    ]
  };
  pieChart.setOption(option);
}

// 初始化趋势图表
function initTrendChart() {
  if (!trendChartRef.value) return;

  trendChart = echarts.init(trendChartRef.value);
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' }
    },
    legend: {
      data: ['异常总数', '已处理', '未处理'],
      textStyle: { color: '#e5e7eb' },
      top: '5%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      axisLabel: { color: '#94a3b8' },
      axisLine: { lineStyle: { color: '#374151' } },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      name: '异常数量',
      nameTextStyle: { color: '#94a3b8' },
      axisLabel: { color: '#94a3b8' },
      axisLine: { lineStyle: { color: '#374151' } },
      splitLine: { lineStyle: { color: '#374151', type: 'dashed' } }
    },
    series: [
      {
        name: '异常总数',
        type: 'line',
        data: [45, 52, 38, 42, 56, 48, 35, 41, 47, 53, 39, 44],
        smooth: true,
        itemStyle: { color: '#F56C6C' },
        lineStyle: { color: '#F56C6C', width: 3 },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(245, 108, 108, 0.3)' },
            { offset: 1, color: 'rgba(245, 108, 108, 0.05)' }
          ])
        },
        symbol: 'circle',
        symbolSize: 6
      },
      {
        name: '已处理',
        type: 'line',
        data: [35, 42, 28, 32, 46, 38, 25, 31, 37, 43, 29, 34],
        smooth: true,
        itemStyle: { color: '#67C23A' },
        lineStyle: { color: '#67C23A', width: 3 },
        symbol: 'circle',
        symbolSize: 6
      },
      {
        name: '未处理',
        type: 'line',
        data: [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10],
        smooth: true,
        itemStyle: { color: '#E6A23C' },
        lineStyle: { color: '#E6A23C', width: 3 },
        symbol: 'circle',
        symbolSize: 6
      }
    ]
  };
  trendChart.setOption(option);
}

function handleQuery() {
  queryParams.pageNum = 1;
  getList();
}

function resetQuery() {
  queryRef.value?.resetFields();
  queryParams.deptId = null;
  queryParams.plateNumber = null;
  queryParams.vehicleNumber = null;
  queryParams.routeId = null;
  queryParams.anomalyType = null;
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

function handleSelectionChange(selection) {
  // 处理表格选择
}

function handleProcess(row) {
  ElMessage.success(`正在处理车辆 ${row.plateNumber} 的异常问题`);
  // 这里可以添加具体的处理逻辑
  // 比如打开处理弹窗、更新异常状态等
}

function handleLocate(row) {
  ElMessage.info(`正在定位车辆：${row.plateNumber}`);
}

function getOfflineDurationClass(duration) {
  if (duration > 1440) return 'duration-critical';
  if (duration > 720) return 'duration-warning';
  return 'duration-normal';
}

function formatDuration(minutes) {
  if (minutes === 0) return '-';
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  if (hours > 24) {
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days}天${remainingHours}小时`;
  }
  return `${hours}小时${mins}分钟`;
}

function getDeviceStatusType(status) {
  const typeMap = {
    'normal': 'success',
    'fault': 'danger',
    'maintenance': 'warning'
  };
  return typeMap[status] || 'info';
}

function getDeviceStatusText(status) {
  const textMap = {
    'normal': '正常',
    'fault': '故障',
    'maintenance': '维修'
  };
  return textMap[status] || '未知';
}

function getAnomalyTypeStyle(type) {
  if (type === '正常') return 'success';
  if (type.includes('故障') || type.includes('异常')) return 'danger';
  return 'warning';
}

function parseTime(time, pattern) {
  if (!time) return '';
  const date = new Date(time);
  const format = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds()
  };
  return pattern.replace(/{(y|m|d|h|i|s)+}/g, (result, key) => {
    const value = format[key];
    return key === 'y' ? value : value.toString().padStart(2, '0');
  });
}
</script>

<style scoped>
.vehicle-anomaly-analysis {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 左侧面板样式 */
.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 24px;
}

.search-box {
  margin-bottom: 16px;
}

/* 右侧内容区域 */
.right-content {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  backdrop-filter: blur(10px);
}

/* 筛选条件区域 */
.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  background: rgba(15, 23, 42, 0.98) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

/* 树形组件样式 */
.dept-tree {
  background: transparent;
  color: #e5e7eb;
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
}

.tree-node {
  display: flex;
  align-items: center;
  font-size: 14px;
  flex: 1;
  max-width: 100%;
  overflow: hidden;
}

.dept-icon {
  margin-right: 8px;
  color: #60a5fa;
  font-size: 16px;
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

.node-label.dept {
  font-weight: 500;
  color: #f1f5f9;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(41, 52, 70, 0.6) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
  border-color: rgba(96, 165, 250, 0.4);
}

.stat-card.total {
  border-left: 4px solid #F56C6C;
}

.stat-card.processed {
  border-left: 4px solid #67C23A;
}

.stat-card.unprocessed {
  border-left: 4px solid #E6A23C;
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #60a5fa;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #f8fafc;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #94a3b8;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  height: 400px;
}

.chart-card .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.chart-card .chart-header h3 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.chart-container {
  height: 320px;
  width: 100%;
}

/* 表格样式 */
.table-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  min-height: 620px;
  height: calc(100vh - 480px);
  display: flex;
  flex-direction: column;
}

.table-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.table-header h3 {
  margin: 0;
  color: #f8fafc;
  font-size: 18px;
  font-weight: 600;
}

.duration-normal {
  color: #67C23A;
}

.duration-warning {
  color: #E6A23C;
  font-weight: 600;
}

.duration-critical {
  color: #F56C6C;
  font-weight: 600;
}



/* Element UI组件覆盖 */
:deep(.el-card) {
  background: rgba(15, 23, 42, 0.98) !important;
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  color: #e5e7eb !important;
}

:deep(.el-card__body) {
  padding: 24px !important;
}

:deep(.el-form-item__label) {
  color: #e5e7eb !important;
}

:deep(.el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e5e7eb !important;
}

:deep(.el-input__inner) {
  color: #e5e7eb !important;
}

:deep(.el-input__inner::placeholder) {
  color: #64748b !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
}

:deep(.el-table) {
  background: transparent !important;
  color: #e5e7eb !important;
  height: 100% !important;
  flex: 1 !important;
}

:deep(.el-table tr) {
  background: rgba(15, 23, 42, 0.6) !important;
}

:deep(.el-table tr:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
}

:deep(.el-table th.el-table__cell) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #f8fafc !important;
  border-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-table td.el-table__cell) {
  border-color: rgba(147, 197, 253, 0.1) !important;
  color: #e5e7eb !important;
}

/* Element Tree组件覆盖样式 */
:deep(.el-tree-node) {
  background: transparent;
  width: 100%;
}

:deep(.el-tree-node:hover > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 6px;
}

:deep(.el-tree-node__content) {
  background: transparent !important;
  border: none !important;
  color: #e5e7eb !important;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

:deep(.el-tree-node__content:hover) {
  background-color: rgba(59, 130, 246, 0.15) !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.2) !important;
  color: #93c5fd !important;
}

:deep(.el-tree-node__expand-icon) {
  color: #64748b !important;
  flex-shrink: 0;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-tree .el-tree-node__children) {
  overflow: visible;
}

:deep(.el-tree-node__label) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vehicle-anomaly-analysis {
    padding: 10px;
  }

  .stats-section .el-col {
    margin-bottom: 16px;
  }

  .chart-card {
    height: 300px;
  }

  .chart-container {
    height: 220px;
  }
}

/* Tab区域 */
.tabs-section {
  margin-bottom: 20px;
}

.tab-content {
  padding-top: 20px;
}

/* Element UI Tab组件覆盖样式 */
:deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.8);
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  background: transparent;
  border: none;
  color: #94a3b8;
  transition: all 0.3s ease;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item:hover) {
  background: rgba(59, 130, 246, 0.1);
  color: #93c5fd;
}
</style>
