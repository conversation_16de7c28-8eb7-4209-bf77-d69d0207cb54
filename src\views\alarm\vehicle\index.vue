<template>
  <div class='vehicle-alarm-management'>
    <!-- 页面标题区域 -->
    <div class='page-header'>
      <div class='header-left'>
        <el-icon class="header-icon">
          <Warning />
        </el-icon>
        <h1 class='page-title'>车辆报警记录管理</h1>
      </div>
      <div class='header-right'>
        <el-button type="danger" icon="Bell" @click="handleEmergencyAlert">紧急报警</el-button>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class='stats-section'>
      <el-row :gutter='20'>
        <el-col :span='6'>
          <div class='stat-card total'>
            <div class='stat-icon'>
              <el-icon><Warning /></el-icon>
            </div>
            <div class='stat-info'>
              <div class='stat-value'>{{ statsData.totalAlarms }}</div>
              <div class='stat-label'>总报警数</div>
            </div>
          </div>
        </el-col>
        <el-col :span='6'>
          <div class='stat-card pending'>
            <div class='stat-icon'>
              <el-icon><Clock /></el-icon>
            </div>
            <div class='stat-info'>
              <div class='stat-value'>{{ statsData.pendingAlarms }}</div>
              <div class='stat-label'>待处理</div>
            </div>
          </div>
        </el-col>
        <el-col :span='6'>
          <div class='stat-card processing'>
            <div class='stat-icon'>
              <el-icon><Tools /></el-icon>
            </div>
            <div class='stat-info'>
              <div class='stat-value'>{{ statsData.processingAlarms }}</div>
              <div class='stat-label'>处理中</div>
            </div>
          </div>
        </el-col>
        <el-col :span='6'>
          <div class='stat-card resolved'>
            <div class='stat-icon'>
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class='stat-info'>
              <div class='stat-value'>{{ statsData.resolvedAlarms }}</div>
              <div class='stat-label'>已解决</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选条件区域 -->
    <div class='filter-section'>
      <el-card class='filter-card'>
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
          <el-form-item label="时间范围" prop="dateRange">
            <el-date-picker
              v-model="queryParams.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              size="default"
              style="width: 300px"
            />
          </el-form-item>
          <el-form-item label="车牌号" prop="vehicleNumber">
            <el-input
              v-model="queryParams.vehicleNumber"
              placeholder="请输入车牌号"
              clearable
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="报警类型" prop="alarmType">
            <el-select v-model="queryParams.alarmType" placeholder="请选择类型" clearable style="width: 130px">
              <el-option label="全部" value="" />
              <el-option label="超速报警" value="overspeed" />
              <el-option label="故障报警" value="malfunction" />
              <el-option label="偏离路线" value="deviation" />
              <el-option label="紧急制动" value="emergency_brake" />
              <el-option label="燃油不足" value="low_fuel" />
              <el-option label="发动机故障" value="engine_fault" />
              <el-option label="轮胎异常" value="tire_fault" />
            </el-select>
          </el-form-item>
          <el-form-item label="报警级别" prop="alarmLevel">
            <el-select v-model="queryParams.alarmLevel" placeholder="请选择级别" clearable style="width: 120px">
              <el-option label="全部" value="" />
              <el-option label="低级" value="low" />
              <el-option label="中级" value="medium" />
              <el-option label="高级" value="high" />
              <el-option label="紧急" value="critical" />
            </el-select>
          </el-form-item>
          <el-form-item label="处理状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 120px">
              <el-option label="全部" value="" />
              <el-option label="待处理" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已解决" value="resolved" />
              <el-option label="已忽略" value="ignored" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
            <el-button @click="resetQuery" icon="Refresh">重置</el-button>
            <el-button type="success" @click="handleExport" icon="Download">导出</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 操作工具栏 -->
    <div class='toolbar-section'>
      <div class='toolbar-left'>
        <el-button type="primary" @click="handleBatchProcess" :disabled="selectedRecords.length === 0" icon="Operation">
          批量处理
        </el-button>
        <el-button type="warning" @click="handleBatchIgnore" :disabled="selectedRecords.length === 0" icon="Hide">
          批量忽略
        </el-button>
        <el-dropdown @command="handleBatchCommand" v-if="selectedRecords.length > 0">
          <el-button type="info">
            更多操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="assign">指派处理人</el-dropdown-item>
              <el-dropdown-item command="priority">修改优先级</el-dropdown-item>
              <el-dropdown-item command="delete" divided>删除记录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class='toolbar-right'>
        <el-tag v-if="selectedRecords.length > 0" type="info">
          已选择 {{ selectedRecords.length }} 条记录
        </el-tag>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class='table-container' v-loading='loading'>
      <el-table
        :data="alarmList"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        row-key="id"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" width="80" align="center" />

        <el-table-column label="报警时间" align="center" prop="alarmTime" width="160">
          <template #default="scope">
            <span>{{ formatTime(scope.row.alarmTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="车牌号" align="center" prop="vehicleNumber" width="120">
          <template #default="scope">
            <el-button link type="primary" @click="handleVehicleDetail(scope.row)">
              {{ scope.row.vehicleNumber }}
            </el-button>
          </template>
        </el-table-column>

        <el-table-column label="司机" align="center" prop="driverName" width="100" />

        <el-table-column label="报警类型" align="center" prop="alarmType" width="120">
          <template #default="scope">
            <el-tag :type="getAlarmTypeStyle(scope.row.alarmType)" size="small">
              {{ getAlarmTypeText(scope.row.alarmType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="报警级别" align="center" prop="alarmLevel" width="100">
          <template #default="scope">
            <el-tag :type="getAlarmLevelStyle(scope.row.alarmLevel)" size="small">
              {{ getAlarmLevelText(scope.row.alarmLevel) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="报警内容" align="center" prop="alarmContent" min-width="200" show-overflow-tooltip />

        <el-table-column label="位置信息" align="center" prop="location" width="150" show-overflow-tooltip />

        <el-table-column label="处理状态" align="center" prop="status" width="100">
          <template #default="scope">
            <el-tag :type="getStatusStyle(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="处理人" align="center" prop="processor" width="100" />

        <el-table-column label="处理时间" align="center" prop="processTime" width="160">
          <template #default="scope">
            <span>{{ scope.row.processTime ? formatTime(scope.row.processTime) : '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width" fixed="right">
          <template #default="scope">
            <div class="operation-buttons">
              <el-button
                link
                type="primary"
                icon="View"
                @click="handleDetail(scope.row)"
                size="small"
              >
                详情
              </el-button>

              <el-button
                v-if="scope.row.status === 'pending'"
                link
                type="success"
                icon="Tools"
                @click="handleProcess(scope.row)"
                size="small"
              >
                处理
              </el-button>

              <el-button
                v-if="scope.row.status === 'processing'"
                link
                type="warning"
                icon="CircleCheck"
                @click="handleResolve(scope.row)"
                size="small"
              >
                解决
              </el-button>

              <el-dropdown
                @command="(command) => handleCommand(command, scope.row)"
                v-if="scope.row.status !== 'resolved'"
                trigger="hover"
              >
                <el-button link type="info" size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="assign">指派他人</el-dropdown-item>
                    <el-dropdown-item command="ignore">忽略报警</el-dropdown-item>
                    <el-dropdown-item command="priority">修改优先级</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 报警详情弹窗 -->
    <el-dialog
      :title="`报警详情 - ${detailData?.vehicleNumber}`"
      v-model="showDetailDialog"
      width="900px"
      append-to-body
    >
      <div v-if="detailData" class="detail-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="detail-card">
              <template #header>
                <span class="card-title">基本信息</span>
              </template>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="车牌号">{{ detailData.vehicleNumber }}</el-descriptions-item>
                <el-descriptions-item label="司机姓名">{{ detailData.driverName }}</el-descriptions-item>
                <el-descriptions-item label="联系电话">{{ detailData.driverPhone }}</el-descriptions-item>
                <el-descriptions-item label="报警时间">{{ formatTime(detailData.alarmTime) }}</el-descriptions-item>
                <el-descriptions-item label="报警类型">
                  <el-tag :type="getAlarmTypeStyle(detailData.alarmType)" size="small">
                    {{ getAlarmTypeText(detailData.alarmType) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="报警级别">
                  <el-tag :type="getAlarmLevelStyle(detailData.alarmLevel)" size="small">
                    {{ getAlarmLevelText(detailData.alarmLevel) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="处理状态">
                  <el-tag :type="getStatusStyle(detailData.status)" size="small">
                    {{ getStatusText(detailData.status) }}
                  </el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>

          <el-col :span="12">
            <el-card class="detail-card">
              <template #header>
                <span class="card-title">位置与参数</span>
              </template>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="当前位置">{{ detailData.location }}</el-descriptions-item>
                <el-descriptions-item label="经纬度">{{ detailData.coordinates }}</el-descriptions-item>
                <el-descriptions-item label="行驶速度">{{ detailData.speed }}km/h</el-descriptions-item>
                <el-descriptions-item label="引擎状态">{{ detailData.engineStatus }}</el-descriptions-item>
                <el-descriptions-item label="燃油余量">{{ detailData.fuelLevel }}%</el-descriptions-item>
                <el-descriptions-item label="里程数">{{ detailData.mileage }}km</el-descriptions-item>
                <el-descriptions-item label="轮胎压力">{{ detailData.tirePressure }}</el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
        </el-row>

        <el-card class="detail-card" style="margin-top: 20px;">
          <template #header>
            <span class="card-title">报警详情</span>
          </template>
          <div class="alarm-detail">
            <p><strong>报警内容：</strong>{{ detailData.alarmContent }}</p>
            <p><strong>详细描述：</strong>{{ detailData.description || '暂无详细描述' }}</p>
            <p v-if="detailData.processor"><strong>处理人：</strong>{{ detailData.processor }}</p>
            <p v-if="detailData.processTime"><strong>处理时间：</strong>{{ formatTime(detailData.processTime) }}</p>
            <p v-if="detailData.processNote"><strong>处理备注：</strong>{{ detailData.processNote }}</p>
          </div>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关 闭</el-button>
          <el-button
            v-if="detailData?.status === 'pending'"
            type="primary"
            @click="handleProcessFromDetail"
          >
            立即处理
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 处理报警弹窗 -->
    <el-dialog
      title="处理报警"
      v-model="showProcessDialog"
      width="600px"
      append-to-body
    >
      <el-form :model="processForm" ref="processFormRef" :rules="processRules" label-width="100px">
        <el-form-item label="处理方式" prop="processType">
          <el-radio-group v-model="processForm.processType">
            <el-radio value="immediate">立即处理</el-radio>
            <el-radio value="assign">指派他人</el-radio>
            <el-radio value="schedule">预约处理</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="处理人" prop="processor" v-if="processForm.processType === 'assign'">
          <el-select v-model="processForm.processor" placeholder="请选择处理人" style="width: 100%">
            <el-option label="张维修" value="张维修" />
            <el-option label="李技师" value="李技师" />
            <el-option label="王师傅" value="王师傅" />
            <el-option label="陈主管" value="陈主管" />
          </el-select>
        </el-form-item>

        <el-form-item label="预计时间" prop="scheduledTime" v-if="processForm.processType === 'schedule'">
          <el-date-picker
            v-model="processForm.scheduledTime"
            type="datetime"
            placeholder="选择处理时间"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="优先级" prop="priority">
          <el-select v-model="processForm.priority" placeholder="请选择优先级">
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="critical" />
          </el-select>
        </el-form-item>

        <el-form-item label="处理备注" prop="note">
          <el-input
            v-model="processForm.note"
            type="textarea"
            :rows="4"
            placeholder="请输入处理备注或说明"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showProcessDialog = false">取 消</el-button>
          <el-button type="primary" @click="confirmProcess">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="VehicleAlarmManagement">
import { ref, reactive, onMounted } from 'vue';
import {
  Warning,
  Clock,
  Tools,
  CircleCheck,
  Bell,
  ArrowDown
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const loading = ref(true);
const total = ref(0);
const selectedRecords = ref([]);
const showDetailDialog = ref(false);
const showProcessDialog = ref(false);
const detailData = ref(null);
const alarmList = ref([]);

const queryRef = ref();
const processFormRef = ref();

// 统计数据
const statsData = ref({
  totalAlarms: '156',
  pendingAlarms: '23',
  processingAlarms: '8',
  resolvedAlarms: '125'
});

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  dateRange: null,
  vehicleNumber: null,
  alarmType: null,
  alarmLevel: null,
  status: null
});

// 处理表单
const processForm = reactive({
  processType: 'immediate',
  processor: '',
  scheduledTime: null,
  priority: 'medium',
  note: ''
});

// 处理表单验证规则
const processRules = {
  processor: [
    { required: true, message: '请选择处理人', trigger: 'change' }
  ],
  scheduledTime: [
    { required: true, message: '请选择处理时间', trigger: 'change' }
  ]
};

onMounted(() => {
  getList();
});

function getList() {
  loading.value = true;

  setTimeout(() => {
    alarmList.value = generateMockData();
    total.value = alarmList.value.length;
    loading.value = false;
  }, 500);
}

// 生成模拟数据
function generateMockData() {
  const types = ['overspeed', 'malfunction', 'deviation', 'emergency_brake', 'low_fuel', 'engine_fault', 'tire_fault'];
  const levels = ['low', 'medium', 'high', 'critical'];
  const statuses = ['pending', 'processing', 'resolved', 'ignored'];
  const vehicles = ['京A12345', '京A12346', '京A12347', '京A12348', '京A12349'];
  const drivers = ['张志明', '李华强', '王建国', '陈美丽', '刘德华'];
  const locations = ['北京市朝阳区建国门外大街', '北京市海淀区中关村大街', '北京市东城区王府井大街', '北京市西城区西单大街'];

  const data = [];
  for (let i = 0; i < 50; i++) {
    const alarmTime = new Date();
    alarmTime.setHours(alarmTime.getHours() - Math.floor(Math.random() * 72));

    const type = types[Math.floor(Math.random() * types.length)];
    const level = levels[Math.floor(Math.random() * levels.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const vehicle = vehicles[Math.floor(Math.random() * vehicles.length)];
    const driver = drivers[Math.floor(Math.random() * drivers.length)];
    const location = locations[Math.floor(Math.random() * locations.length)];

    const processTime = status !== 'pending' ? new Date(alarmTime.getTime() + Math.random() * 3600000) : null;

    data.push({
      id: i + 1,
      alarmTime: alarmTime.toISOString(),
      vehicleNumber: vehicle,
      driverName: driver,
      driverPhone: `138${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
      alarmType: type,
      alarmLevel: level,
      alarmContent: getAlarmContent(type),
      location: location,
      coordinates: `${(116.4 + Math.random() * 0.1).toFixed(6)}, ${(39.9 + Math.random() * 0.1).toFixed(6)}`,
      status: status,
      processor: status !== 'pending' ? '张维修' : null,
      processTime: processTime?.toISOString(),
      speed: Math.floor(Math.random() * 80 + 20),
      engineStatus: Math.random() > 0.8 ? '异常' : '正常',
      fuelLevel: Math.floor(Math.random() * 100),
      mileage: Math.floor(Math.random() * 100000 + 50000),
      tirePressure: '正常',
      description: `${getAlarmContent(type)}的详细描述信息`,
      processNote: status === 'resolved' ? '已完成维修处理' : null
    });
  }

  return data.sort((a, b) => new Date(b.alarmTime) - new Date(a.alarmTime));
}

function getAlarmContent(type) {
  const contentMap = {
    'overspeed': '车辆超速行驶，当前速度85km/h',
    'malfunction': '发动机故障，需要立即检修',
    'deviation': '车辆偏离预定路线',
    'emergency_brake': '紧急制动触发',
    'low_fuel': '燃油不足，剩余15%',
    'engine_fault': '发动机温度过高',
    'tire_fault': '轮胎压力异常'
  };
  return contentMap[type] || '未知报警';
}

function handleQuery() {
  queryParams.pageNum = 1;
  getList();
}

function resetQuery() {
  queryRef.value?.resetFields();
  handleQuery();
}

function handleSelectionChange(selection) {
  selectedRecords.value = selection;
}

function handleDetail(row) {
  detailData.value = row;
  showDetailDialog.value = true;
}

function handleProcess(row) {
  detailData.value = row;
  resetProcessForm();
  showProcessDialog.value = true;
}

function handleProcessFromDetail() {
  resetProcessForm();
  showDetailDialog.value = false;
  showProcessDialog.value = true;
}

function resetProcessForm() {
  processForm.processType = 'immediate';
  processForm.processor = '';
  processForm.scheduledTime = null;
  processForm.priority = 'medium';
  processForm.note = '';
}

function confirmProcess() {
  processFormRef.value?.validate((valid) => {
    if (valid) {
      ElMessage.success('报警处理成功');
      showProcessDialog.value = false;
      getList();
    }
  });
}

function handleResolve(row) {
  ElMessageBox.confirm('确认标记此报警为已解决？', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'success'
  }).then(() => {
    ElMessage.success('报警已标记为解决');
    getList();
  });
}

function handleCommand(command, row) {
  switch (command) {
    case 'assign':
      handleAssign(row);
      break;
    case 'ignore':
      handleIgnore(row);
      break;
    case 'priority':
      handlePriority(row);
      break;
  }
}

function handleAssign(row) {
  ElMessage.info('指派功能开发中');
}

function handleIgnore(row) {
  ElMessageBox.confirm('确认忽略此报警？', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('报警已忽略');
    getList();
  });
}

function handlePriority(row) {
  ElMessage.info('优先级修改功能开发中');
}

function handleBatchProcess() {
  ElMessage.info('批量处理功能开发中');
}

function handleBatchIgnore() {
  ElMessageBox.confirm(`确认忽略选中的 ${selectedRecords.value.length} 条报警？`, '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('批量忽略成功');
    getList();
  });
}

function handleBatchCommand(command) {
  ElMessage.info(`批量${command}功能开发中`);
}

function handleVehicleDetail(row) {
  ElMessage.info('车辆详情功能开发中');
}

function handleEmergencyAlert() {
  ElMessage.warning('紧急报警功能开发中');
}

function handleExport() {
  ElMessage.success('导出成功');
}

function formatTime(time) {
  if (!time) return '';
  return new Date(time).toLocaleString('zh-CN');
}

function getAlarmTypeText(type) {
  const typeMap = {
    'overspeed': '超速报警',
    'malfunction': '故障报警',
    'deviation': '偏离路线',
    'emergency_brake': '紧急制动',
    'low_fuel': '燃油不足',
    'engine_fault': '发动机故障',
    'tire_fault': '轮胎异常'
  };
  return typeMap[type] || '未知';
}

function getAlarmTypeStyle(type) {
  const styleMap = {
    'overspeed': 'danger',
    'malfunction': 'danger',
    'deviation': 'warning',
    'emergency_brake': 'danger',
    'low_fuel': 'warning',
    'engine_fault': 'danger',
    'tire_fault': 'warning'
  };
  return styleMap[type] || 'info';
}

function getAlarmLevelText(level) {
  const levelMap = {
    'low': '低级',
    'medium': '中级',
    'high': '高级',
    'critical': '紧急'
  };
  return levelMap[level] || '未知';
}

function getAlarmLevelStyle(level) {
  const styleMap = {
    'low': 'info',
    'medium': 'warning',
    'high': 'danger',
    'critical': 'danger'
  };
  return styleMap[level] || 'info';
}

function getStatusText(status) {
  const statusMap = {
    'pending': '待处理',
    'processing': '处理中',
    'resolved': '已解决',
    'ignored': '已忽略'
  };
  return statusMap[status] || '未知';
}

function getStatusStyle(status) {
  const styleMap = {
    'pending': 'warning',
    'processing': 'primary',
    'resolved': 'success',
    'ignored': 'info'
  };
  return styleMap[status] || 'info';
}
</script>

<style scoped>
.vehicle-alarm-management {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 24px;
  color: #f59e0b;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #f8fafc;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(41, 52, 70, 0.6) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
  border-color: rgba(96, 165, 250, 0.4);
}

.stat-card.total {
  border-left: 4px solid #f59e0b;
}

.stat-card.pending {
  border-left: 4px solid #ef4444;
}

.stat-card.processing {
  border-left: 4px solid #3b82f6;
}

.stat-card.resolved {
  border-left: 4px solid #10b981;
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #60a5fa;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #f8fafc;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #94a3b8;
}

/* 筛选条件区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  background: rgba(15, 23, 42, 0.98) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

/* 工具栏 */
.toolbar-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: rgba(15, 23, 42, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

/* 表格容器 */
.table-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  min-height: 400px;
}

/* 操作按钮布局 */
.operation-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
  padding: 4px 0;
}

.operation-buttons .el-button {
  margin: 0 !important;
  min-width: auto;
  padding: 4px 8px;
}

/* 详情弹窗 */
.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 16px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.card-title {
  font-weight: 600;
  color: #374151;
}

.alarm-detail p {
  margin: 8px 0;
  line-height: 1.6;
}

/* Element UI组件覆盖 */
:deep(.el-form-item__label) {
  color: #e5e7eb !important;
}

:deep(.el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e5e7eb !important;
}

:deep(.el-input__inner) {
  color: #e5e7eb !important;
}

:deep(.el-input__inner::placeholder) {
  color: #64748b !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
}

:deep(.el-table) {
  background: transparent !important;
  color: #e5e7eb !important;
}

:deep(.el-table tr) {
  background: rgba(15, 23, 42, 0.6) !important;
}

:deep(.el-table tr:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
}

:deep(.el-table th.el-table__cell) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #f8fafc !important;
  border-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-table td.el-table__cell) {
  border-color: rgba(147, 197, 253, 0.1) !important;
  color: #e5e7eb !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vehicle-alarm-management {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .toolbar-section {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .stats-section .el-col {
    margin-bottom: 16px;
  }
}
</style>
