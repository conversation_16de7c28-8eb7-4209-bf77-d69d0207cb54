<template>
  <div class="top-menu-layout">
    <!-- 顶部主导航 -->
    <div class="header-container">
      <div class="header-content">
        <!-- Logo区域 -->
        <div class="logo-section">
          <img src="@/assets/logo/logo.png" alt="TransitSync" />
          <span class="logo-text">智慧公交</span>
        </div>

        <!-- 主菜单区域 -->
        <div class="main-menu">
          <el-menu
            v-if="topMenus.length > 0"
            :key="activeMainMenu"
            mode="horizontal"
            class="top-menu"
            :default-active="activeMainMenu"
            :active-text-color="'#1890ff'"
            :router="false"
            @select="handleMainMenuSelect"
          >
            <template v-for="menu in topMenus" :key="menu.path">
              <!-- 没有子菜单的普通菜单项 -->
              <el-menu-item v-if="!menu.children || menu.children.length === 0" :index="menu.path">
                <svg-icon v-if="menu.meta && menu.meta.icon && menu.meta.icon !== '#'" :icon-class="menu.meta.icon" />
                <span>{{ menu.meta?.title }}</span>
              </el-menu-item>

              <!-- 有子菜单的下拉菜单 -->
              <el-sub-menu v-else :index="menu.path" popper-class="top-menu-submenu">
                <template #title>
                  <svg-icon v-if="menu.meta && menu.meta.icon && menu.meta.icon !== '#'" :icon-class="menu.meta.icon" class="menu-icon" />
                  <span class="menu-title">{{ menu.meta?.title }}</span>
                </template>

                <el-menu-item
                  v-for="subItem in getChildrenForMenu(menu)"
                  :key="subItem.path"
                  :index="subItem.path"
                  :class="['sub-menu-item', { 'is-active': route.path === subItem.path }]"
                >
                  <svg-icon
                    v-if="subItem.meta && subItem.meta.icon && subItem.meta.icon !== '#'"
                    :icon-class="subItem.meta.icon"
                    class="sub-menu-icon"
                  />
                  &nbsp;&nbsp;<span class="sub-menu-title">{{ subItem.meta?.title }}</span>
                  <el-tag v-if="subItem.meta?.badge" size="small" type="danger">{{ subItem.meta.badge }}</el-tag>
                  <!-- 选中标识 -->
                  <el-icon v-if="route.path === subItem.path" class="selected-indicator">
                    <Check />
                  </el-icon>
                </el-menu-item>
              </el-sub-menu>
            </template>
          </el-menu>

          <!-- 菜单加载状态 -->
          <div v-else class="loading-menu">
            <el-skeleton animated>
              <template #template>
                <div class="menu-skeleton">
                  <el-skeleton-item variant="text" style="width: 80px; height: 20px; margin-right: 20px" />
                  <el-skeleton-item variant="text" style="width: 100px; height: 20px; margin-right: 20px" />
                  <el-skeleton-item variant="text" style="width: 90px; height: 20px; margin-right: 20px" />
                  <el-skeleton-item variant="text" style="width: 70px; height: 20px" />
                </div>
              </template>
            </el-skeleton>
          </div>
        </div>

        <!-- 右侧功能区 -->
        <div class="header-actions">
          <!-- 工作台按钮 -->
          <div class="header-action-item" title="工作台" @click="goToWorkspace">
            <svg-icon icon-class="dashboard" />
          </div>

          <!-- 租户选择 -->
          <el-select
            v-if="userId === 1 && tenantEnabled"
            v-model="companyName"
            class="tenant-select"
            clearable
            filterable
            reserve-keyword
            :placeholder="$t('navbar.selectTenant')"
            @change="dynamicTenantEvent"
            @clear="dynamicClearEvent"
          >
            <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.companyName" :value="item.tenantId" />
            <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
          </el-select>

          <!-- 全屏 -->
          <div class="header-action-item" :title="$t('navbar.full')">
            <screenfull />
          </div>

          <!-- 语言选择 -->
          <div class="header-action-item" :title="$t('navbar.language')">
            <lang-select />
          </div>

          <!-- 尺寸选择 -->
          <div class="header-action-item" :title="$t('navbar.layoutSize')">
            <size-select />
          </div>

          <!-- 消息通知 -->
          <el-popover placement="bottom" trigger="click" transition="el-zoom-in-top" :width="300" :persistent="false" :title="$t('navbar.message')">
            <template #reference>
              <div class="header-action-item" :title="$t('navbar.message')">
                <el-badge :value="newNotice > 0 ? newNotice : ''" :max="99">
                  <svg-icon icon-class="message" />
                </el-badge>
              </div>
            </template>
            <template #default>
              <notice />
            </template>
          </el-popover>

          <!-- 用户菜单 - 移除头像显示 -->
          <el-dropdown @command="handleUserCommand" class="user-dropdown">
            <div class="user-section">
              <svg-icon icon-class="user" class="user-icon" />
              <span>{{ userStore.name }}</span>
              <el-icon><caret-bottom /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <router-link v-if="!dynamic" to="/user/profile">
                  <el-dropdown-item>{{ $t('navbar.personalCenter') }}</el-dropdown-item>
                </router-link>
                <el-dropdown-item v-if="settingsStore.showSettings" command="setLayout">
                  <span>{{ $t('navbar.layoutSetting') }}</span>
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <span>{{ $t('navbar.logout') }}</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 面包屑 + 标签页区域 - 已隐藏以为内容留取更多空间 -->
    <div class="breadcrumb-container" style="display: none">
      <div class="breadcrumb-content">
        <el-breadcrumb separator=">">
          <el-breadcrumb-item v-for="item in breadcrumbList" :key="item.path" :to="item.isDirectory ? null : item.path">
            <span :class="{ 'breadcrumb-directory': item.isDirectory, 'breadcrumb-link': !item.isDirectory }">
              <svg-icon v-if="item.icon" :icon-class="item.icon" />
              {{ item.title }}
            </span>
          </el-breadcrumb-item>
        </el-breadcrumb>

        <!-- 右侧工具栏区域 -->
        <div class="breadcrumb-actions">
          <!-- 页面标签 -->
          <div class="page-tags" v-if="settingsStore.tagsView">
            <el-tag
              v-for="tag in visitedViews"
              :key="tag.path"
              :closable="!isAffix(tag)"
              @close="closeTag(tag)"
              @click="switchToPage(tag)"
              :type="isActive(tag) ? 'primary' : 'info'"
              class="page-tag"
            >
              {{ tag.title }}
            </el-tag>
          </div>

          <!-- 页面工具栏插槽 -->
          <div id="page-toolbar-container" class="page-toolbar-container">
            <!-- 这里会被页面中的right-toolbar组件填充 -->
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content-wrapper">
      <!-- 主要内容 -->
      <div ref="contentAreaRef" class="content-area full-width">
        <router-view v-slot="{ Component, route }">
          <transition :enter-active-class="proxy?.animate.pageTransitionAnimate" mode="out-in">
            <keep-alive :include="tagsViewStore.cachedViews">
              <component :is="Component" v-if="!route.meta.link" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
        <iframe-toggle />
      </div>
    </div>

    <!-- 设置面板 -->
    <settings ref="settingsRef" />
  </div>
</template>

<script setup lang="ts">
import { Check } from '@element-plus/icons-vue';
import { useUserStore } from '@/store/modules/user';
import { usePermissionStore } from '@/store/modules/permission';
import { useTagsViewStore } from '@/store/modules/tagsView';
import { useSettingsStore } from '@/store/modules/settings';
import { useNoticeStore } from '@/store/modules/notice';
import { getTenantList } from '@/api/login';
import { dynamicClear, dynamicTenant } from '@/api/system/tenant';
import { TenantVO } from '@/api/types';
import { constantRoutes } from '@/router';
import Notice from './components/notice/index.vue';
import IframeToggle from './components/IframeToggle/index.vue';
import Settings from './components/Settings/index.vue';
import { ElMessageBox, ElMessageBoxOptions } from 'element-plus';
import router from '@/router';
import { useScrollbar } from '@/composables/useScrollbar';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const route = useRoute();
const userStore = useUserStore();
const permissionStore = usePermissionStore();
const tagsViewStore = useTagsViewStore();
const settingsStore = useSettingsStore();
const noticeStore = storeToRefs(useNoticeStore());

// 租户相关
const userId = ref(userStore.userId);
const companyName = ref(undefined);
const tenantList = ref<TenantVO[]>([]);
const dynamic = ref(false);
const tenantEnabled = ref(true);
const newNotice = ref(0);

// Settings 组件引用
const settingsRef = ref<InstanceType<typeof Settings>>();

// 内容区域引用和滚动条
const contentAreaRef = ref<HTMLElement>();
const { initializeScrollbar, scrollToTop } = useScrollbar(contentAreaRef);

// 所有的路由信息
const routers = computed(() => permissionStore.getTopbarRoutes());

// 从权限存储获取顶部菜单（这些是从接口动态加载的）
const topMenus = computed(() => {
  const topMenus: any[] = [];
  routers.value.forEach((menu) => {
    if (menu.hidden !== true) {
      // 兼容顶部栏一级菜单内部跳转
      if (menu.path === '/' && menu.children) {
        topMenus.push(menu.children ? menu.children[0] : menu);
      } else {
        topMenus.push(menu);
      }
    }
  });

  return topMenus;
});

// 设置子路由
const childrenMenus = computed(() => {
  const childrenMenus: any[] = [];
  routers.value.forEach((router) => {
    if (router.children) {
      router.children.forEach((item) => {
        // 创建子路由的副本，避免修改原始数据
        const childRoute = { ...item };

        if (childRoute.parentPath === undefined) {
          if (router.path === '/') {
            childRoute.path = '/' + childRoute.path;
          } else {
            if (!isHttp(childRoute.path)) {
              // 确保路径正确拼接
              if (childRoute.path.startsWith('/')) {
                // 如果子路径已经是绝对路径，直接使用
                childRoute.path = childRoute.path;
              } else {
                // 如果是相对路径，拼接父路径
                childRoute.path = router.path + '/' + childRoute.path;
              }
            }
          }
          childRoute.parentPath = router.path;
        }
        childrenMenus.push(childRoute);
      });
    }
  });
  return constantRoutes.concat(childrenMenus);
});

// 当前激活的主菜单
const activeMainMenu = computed(() => {
  const path = route.path;


  // 如果是首页，激活首页
  if (path === '/' || path === '/index') {
    return '/index';
  }

  // 先查找是否有主菜单直接匹配
  const directMatch = topMenus.value.find((menu) => menu.path === path);
  if (directMatch) {
    console.log('直接匹配到主菜单:', directMatch.path);
    return path;
  }

  // 查找包含当前路径的主菜单
  for (const menu of topMenus.value) {
    if (menu.children && menu.children.length > 0) {
      // 查找子菜单中是否有匹配的路径
      const hasActiveChild = childrenMenus.value.some((child) => child.parentPath === menu.path && child.path === path);
      if (hasActiveChild) {
        console.log('通过子菜单匹配到主菜单:', menu.path, '当前页面:', path);
        return menu.path;
      }
    }
  }

  // 如果没有找到精确匹配，尝试按路径前缀匹配
  for (const menu of topMenus.value) {
    if (path.startsWith(menu.path + '/') || path === menu.path) {
      console.log('通过前缀匹配到主菜单:', menu.path);
      return menu.path;
    }
  }

  console.log('未找到匹配的主菜单');
  return '';
});

// 面包屑导航
const breadcrumbList = computed(() => {
  const matched = route.matched.filter((item) => item.meta && item.meta.title);
  return matched.map((item, index) => {
    // 检查是否为目录项（最后一项总是可点击的，其他项需要检查是否为纯目录）
    const isDirectory = index < matched.length - 1 && (!item.component || item.redirect === 'noRedirect');

    return {
      path: item.path,
      title: item.meta?.title,
      icon: item.meta?.icon,
      isDirectory: isDirectory
    };
  });
});

// 访问过的页面
const visitedViews = computed(() => tagsViewStore.visitedViews);

// 路由数据是否已加载完成
const isRoutesLoaded = computed(() => {
  return topMenus.value && topMenus.value.length > 0 && childrenMenus.value && childrenMenus.value.length > 0;
});

// 主菜单选择处理 - 修复路径匹配问题
const handleMainMenuSelect = async (index: string) => {
  console.log('点击菜单:', index);

  // 如果路由数据还没加载完成，等待一下
  if (!isRoutesLoaded.value) {
    console.warn('菜单数据尚未加载完成，等待加载...');
    let retryCount = 0;
    while (!isRoutesLoaded.value && retryCount < 30) {
      await new Promise((resolve) => setTimeout(resolve, 100));
      retryCount++;
    }

    if (!isRoutesLoaded.value) {
      console.error('路由数据加载超时');
      return;
    }
  }

  if (isHttp(index)) {
    // http(s):// 路径新窗口打开
    window.open(index, '_blank');
    return;
  }

  // 优先在childrenMenus中查找完整路径匹配
  const childRoute = childrenMenus.value.find((item) => item.path === index);
  if (childRoute) {
    console.log('找到子路由，直接跳转:', index);
    await router.push({ path: index });
    // 使用nextTick确保路由更新后再触发菜单重新计算
    await nextTick();
    return;
  }

  // 然后查找主菜单
  const selectedRoute = topMenus.value.find((item) => item.path === index);
  if (selectedRoute) {
    if (selectedRoute.children && selectedRoute.children.length > 0) {
      // 有子路由，跳转到第一个可用的子路由
      const activeRoutes = getActiveRoutes(index);
      if (activeRoutes.length > 0) {
        const firstRoute = activeRoutes[0];
        console.log('跳转到第一个子路由:', firstRoute.path);
        await router.push({ path: firstRoute.path });
        await nextTick();
      } else {
        console.warn('未找到子路由，尝试直接跳转到:', index);
        await router.push({ path: index });
        await nextTick();
      }
    } else {
      // 没有子路由，直接跳转
      console.log('主菜单直接跳转:', index);
      await router.push({ path: index });
      await nextTick();
    }
    return;
  }

  // 最后尝试直接跳转
  console.warn('未找到匹配的路由，尝试直接跳转到:', index);
  await router.push({ path: index });
  await nextTick();
};

// 获取指定主菜单的子菜单列表（使用处理后的完整路径）
const getChildrenForMenu = (menu: any) => {
  return childrenMenus.value.filter((child) => child.parentPath === menu.path && !child.hidden);
};

// 获取激活的子路由
const getActiveRoutes = (key: string) => {
  const routes: any[] = [];
  if (childrenMenus.value && childrenMenus.value.length > 0) {
    childrenMenus.value.forEach((item) => {
      if (key === item.parentPath || (key === 'index' && '' === item.path)) {
        routes.push(item);
      }
    });
  }
  return routes;
};

// 判断是否为HTTP链接
const isHttp = (url: string) => {
  return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1;
};

// 打开设置面板
const openSettingsPanel = () => {
  settingsRef.value?.openSetting();
};

// 用户命令处理
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'logout':
      logout();
      break;
    case 'setLayout':
      // 打开设置面板
      openSettingsPanel();
      break;
  }
};

// 退出登录
const logout = async () => {
  await ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  } as ElMessageBoxOptions);

  userStore.logout().then(() => {
    router.replace({
      path: '/login',
      query: {
        redirect: encodeURIComponent(route.fullPath || '/')
      }
    });
    proxy?.$tab.closeAllPage();
  });
};

// 租户相关方法
const dynamicTenantEvent = async (tenantId: string) => {
  if (companyName.value != null && companyName.value !== '') {
    await dynamicTenant(tenantId);
    dynamic.value = true;
    await router.push('/');
    await proxy?.$tab.closeAllPage();
    await proxy?.$tab.refreshPage();
  }
};

const dynamicClearEvent = async () => {
  await dynamicClear();
  dynamic.value = false;
  await router.push('/');
  await proxy?.$tab.closeAllPage();
  await proxy?.$tab.refreshPage();
};

// 跳转到工作台页面
const goToWorkspace = () => {
  router.push('/');
};

const initTenantList = async () => {
  const { data } = await getTenantList(true);
  tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled;
  if (tenantEnabled.value) {
    tenantList.value = data.voList;
  }
};

// 标签相关方法
const isActive = (tag: any) => {
  return tag.path === route.path;
};

const isAffix = (tag: any) => {
  return tag?.meta && tag?.meta?.affix;
};

const closeTag = (tag: any) => {
  proxy?.$tab.closePage(tag);
};

const switchToPage = (tag: any) => {
  if (tag.path !== route.path) {
    router.push(tag.path);
  }
};

// 监听消息通知
watch(
  () => noticeStore.state.value.notices,
  (newVal) => {
    newVal && (newNotice.value = newVal.filter((item: any) => !item.read).length);
  },
  { deep: true }
);

onMounted(() => {
  initTenantList();
  // 初始化内容区域滚动条
  nextTick(() => {
    if (contentAreaRef.value) {
      initializeScrollbar();
    }
  });
});

defineExpose({
  initTenantList
});
</script>

<style lang="scss" scoped>
/**
 * 现代化CSS变量系统 - 公交调度系统专业配色
 *
 * 设计原则：
 * 1. 现代化渐变背景，提升视觉层次
 * 2. 高对比度文字，确保可读性
 * 3. 统一的颜色命名规范
 * 4. 支持运行时动态修改
 * 5. 优雅的交互动效
 */
.top-menu-layout {
  // {{ AURA-X: Modify - 顶部导航切换为高级低调黑色主题。Confirmed via 产品需求 }}
  --transit-header-bg: linear-gradient(135deg, #0a1629 0%, #1e293b 50%, #334155 100%);
  --transit-header-bg-solid: #1e293b;
  --transit-header-border: rgba(59, 130, 246, 0.3);
  // 蓝色系强调色，用于细节分隔线/下划线
  --transit-header-accent: rgba(59, 130, 246, 0.6);
  --transit-icon-default: rgba(255, 255, 255, 0.92);
  --transit-icon-hover: #60a5fa;
  --transit-icon-disabled: rgba(255, 255, 255, 0.4);
  --transit-text-primary: rgba(255, 255, 255, 0.96);
  --transit-text-secondary: rgba(255, 255, 255, 0.85);
  --transit-hover-bg: rgba(59, 130, 246, 0.12);
  --transit-active-bg: rgba(59, 130, 246, 0.18);
}

html.dark .top-menu-layout {
  // 深色模式保持相同的科技蓝主题
  --transit-header-bg: linear-gradient(135deg, #081420 0%, #1a252f 50%, #2d3748 100%);
  --transit-header-bg-solid: #1a252f;
  --transit-header-border: rgba(59, 130, 246, 0.25);
  --transit-header-accent: rgba(59, 130, 246, 0.5);
  --transit-icon-default: rgba(255, 255, 255, 0.88);
  --transit-icon-hover: #60a5fa;
  --transit-icon-disabled: rgba(255, 255, 255, 0.3);
  --transit-text-primary: rgba(255, 255, 255, 0.92);
  --transit-text-secondary: rgba(255, 255, 255, 0.75);
  --transit-hover-bg: rgba(59, 130, 246, 0.1);
  --transit-active-bg: rgba(59, 130, 246, 0.15);
}

.top-menu-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header-container {
  // 科技蓝渐变背景 - 与车辆监控页面呼应
  background: var(--transit-header-bg) !important;
  background-color: var(--transit-header-bg-solid) !important; // 回退色
  border-bottom: 1px solid var(--transit-header-border) !important;
  box-shadow:
    0 4px 20px rgba(59, 130, 246, 0.15),
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.04) !important;
  z-index: 1000;
  position: relative;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);

  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--transit-header-accent) 20%, var(--transit-header-accent) 80%, transparent 100%);
    opacity: 0.6;
  }

  .header-content {
    display: flex;
    align-items: center;
    height: 64px;
    padding: 0 24px;

    .logo-section {
      display: flex;
      align-items: center;
      margin-right: 48px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
      }

      img {
        height: 36px;
        margin-right: 12px;
        filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.1));
      }

      .logo-text {
        font-size: 20px;
        font-weight: 700;
        // {{ AURA: Modify - 金属质感Logo文字样式，兼容中文显示 }}
        color: transparent !important; /* 设为透明，让渐变背景显示 */
        letter-spacing: 0.5px; /* 中文适配：减少字间距 */
        white-space: nowrap; /* 防止换行 */
        display: inline-block; /* 确保水平排列 */

        /* 金属质感渐变背景 */
        background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 30%, #c7d2fe 60%, #a5b4fc 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .main-menu {
      flex: 1;
      min-width: 0; /* 防止flex子元素溢出 */
      overflow: hidden; /* 隐藏溢出内容 */

      .loading-menu {
        display: flex;
        align-items: center;
        height: 64px;

        .menu-skeleton {
          display: flex;
          align-items: center;
        }
      }

      :deep(.top-menu) {
        border-bottom: none;
        background: transparent;

        .el-menu-item,
        .el-sub-menu > .el-sub-menu__title {
          height: 64px;
          line-height: 64px;
          margin: 0 6px;
          border-radius: 10px;
          position: relative;
          font-weight: 500;
          font-size: 14px;
          letter-spacing: 0.3px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          // {{ AURA: Modify - 现代化菜单文字样式，确保高对比度 }}
          color: rgba(255, 255, 255, 0.96) !important;

          &::before {
            content: '';
            position: absolute;
            bottom: 6px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 3px;
            background: var(--transit-header-accent);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(75, 85, 99, 0.35);
          }

          &:hover {
            background: rgba(255, 255, 255, 0.12) !important;
            color: rgba(255, 255, 255, 0.96) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

            &::before {
              width: 70%;
            }
          }

          &.is-active {
            background: rgba(255, 255, 255, 0.18) !important;
            color: rgba(255, 255, 255, 0.96) !important;
            font-weight: 600;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);

            &::before {
              width: 90%;
            }
          }

          .svg-icon {
            margin-right: 10px;
            font-size: 16px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            // {{ AURA: Modify - 现代化图标样式，确保高对比度 }}
            color: rgba(255, 255, 255, 0.92) !important;
            fill: rgba(255, 255, 255, 0.92) !important;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
          }

          &:hover .svg-icon {
            transform: scale(1.08) translateY(-1px);
            color: #ffffff !important;
            fill: #ffffff !important;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
          }

          &.is-active .svg-icon {
            color: #ffffff !important;
            fill: #ffffff !important;
            transform: scale(1.05);
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
          }
        }

        .el-sub-menu {
          .el-sub-menu__title {
            .svg-icon {
              margin-right: 12px;
              font-size: 16px;
              transition: all 0.3s ease;
              // {{ AURA: Modify - 子菜单图标样式优化 }}
              color: rgba(255, 255, 255, 0.92) !important;
              fill: rgba(255, 255, 255, 0.92) !important;
            }

            &:hover {
              background: rgba(255, 255, 255, 0.12) !important;
              color: rgba(255, 255, 255, 0.96) !important;

              .svg-icon {
                color: #ffffff !important;
                fill: #ffffff !important;
              }
            }

            .el-sub-menu__icon-arrow {
              transition: all 0.3s ease;
              color: rgba(255, 255, 255, 0.85) !important;
            }
          }

          &.is-opened > .el-sub-menu__title .el-sub-menu__icon-arrow {
            transform: rotateZ(180deg);
          }
        }
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;
      flex-shrink: 0; /* 防止被挤压 */
      min-width: 200px; /* 确保最小宽度 */

      .tenant-select {
        width: 200px;

        :deep(.el-input__wrapper) {
          border-radius: 8px;
          transition: all 0.3s ease;
          background: rgba(255, 255, 255, 0.12) !important;
          border-color: rgba(255, 255, 255, 0.08) !important;

          &:hover {
            box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
          }

          .el-input__inner {
            color: rgba(255, 255, 255, 0.96) !important;
          }
        }
      }

      .header-action-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        cursor: pointer;
        border-radius: 8px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        // {{ AURA: Modify - 现代化功能区图标样式 }}
        color: rgba(255, 255, 255, 0.92) !important;
        position: relative;
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 0.12) !important;
          color: #ffffff !important;
          transform: translateY(-2px);
          box-shadow:
            0 6px 20px rgba(0, 0, 0, 0.15),
            0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &:active {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .svg-icon {
          font-size: 16px;
          // {{ AURA: Modify - 确保功能区图标正确显示 }}
          color: var(--transit-icon-default) !important;
          fill: var(--transit-icon-default) !important;
          transition: all 0.3s ease;
        }

        &:hover .svg-icon {
          color: var(--transit-icon-hover) !important;
          fill: var(--transit-icon-hover) !important;
          transform: scale(1.1);
        }

        // 确保内部组件样式正确
        > * {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .user-dropdown {
        .user-section {
          display: flex;
          align-items: center;
          gap: 10px;
          cursor: pointer;
          padding: 8px 16px;
          border-radius: 8px;
          transition: all 0.3s ease;
          font-weight: 500;
          // {{ AURA: Modify - 用户区域样式优化 }}
          color: var(--transit-text-primary);

          &:hover {
            background: var(--transit-hover-bg);
            color: var(--transit-text-primary);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          .user-icon {
            font-size: 18px;
            color: var(--transit-icon-default);
            transition: all 0.3s ease;
          }

          &:hover .user-icon {
            color: var(--transit-icon-hover);
            transform: scale(1.1);
          }
        }
      }
    }
  }

  // {{ AURA: Modify - 全局图标颜色统一管理 }}
  .header-container {
    // 所有 svg 图标
    svg,
    .svg-icon {
      color: var(--transit-icon-default) !important;
      fill: var(--transit-icon-default) !important;
    }

    // Element Plus 图标
    .el-icon {
      color: var(--transit-icon-default) !important;
    }

    // 特别针对功能组件的图标
    .header-action-item,
    .header-actions > * {
      svg,
      .svg-icon,
      .el-icon {
        color: var(--transit-icon-default) !important;
        fill: var(--transit-icon-default) !important;
      }

      // 深层嵌套的图标
      * {
        svg,
        .svg-icon,
        .el-icon {
          color: var(--transit-icon-default) !important;
          fill: var(--transit-icon-default) !important;
        }
      }
    }

    // 悬停状态
    .el-menu-item:hover,
    .el-sub-menu__title:hover,
    .header-action-item:hover,
    .user-section:hover {
      svg,
      .svg-icon,
      .el-icon {
        color: var(--transit-icon-hover) !important;
        fill: var(--transit-icon-hover) !important;
      }
    }

    // 激活状态
    .el-menu-item.is-active {
      svg,
      .svg-icon {
        color: var(--transit-icon-hover) !important;
        fill: var(--transit-icon-hover) !important;
      }
    }
  }
}

/* 通用深色背景图标修复类 - 使用CSS变量，便于主题切换 */
.dark-bg-icon-fix {
  svg,
  .svg-icon,
  .el-icon {
    color: var(--dark-icon-default) !important;
    fill: var(--dark-icon-default) !important;
    transition: all 0.3s ease !important;
  }

  &:hover {
    svg,
    .svg-icon,
    .el-icon {
      color: var(--dark-icon-hover) !important;
      fill: var(--dark-icon-hover) !important;
      transform: scale(1.05) !important;
    }
  }

  // 处理禁用状态
  &:disabled,
  &.is-disabled {
    svg,
    .svg-icon,
    .el-icon {
      color: var(--dark-icon-disabled) !important;
      fill: var(--dark-icon-disabled) !important;
      cursor: not-allowed !important;
    }
  }

  // 处理加载状态
  &.is-loading {
    svg,
    .svg-icon,
    .el-icon {
      animation: icon-spin 1s linear infinite !important;
    }
  }
}

/* 图标旋转动画 */
@keyframes icon-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 全局下拉菜单样式 - 与顶部导航保持一致的深色主题
:deep(.top-menu-submenu) {
  margin-top: 8px !important;
  border-radius: 12px !important;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.5) !important,
    0 4px 16px rgba(59, 130, 246, 0.2) !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  // 与顶部导航相同的深色背景
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 100%) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;

  .el-menu {
    background: transparent !important;
    border: none !important;
    padding: 8px 0 !important;
  }

  .el-menu-item {
    height: 48px !important;
    line-height: 1 !important;
    padding: 12px 24px !important;
    margin: 2px 8px !important;
    border-radius: 8px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    font-size: 14px !important;
    // 使用与顶部导航相同的文字颜色
    color: rgba(255, 255, 255, 0.96) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    position: relative !important;
    font-weight: 500 !important;
    letter-spacing: 0.3px !important;

    &:hover {
      // 与顶部导航相同的悬停效果
      background: rgba(255, 255, 255, 0.12) !important;
      color: rgba(255, 255, 255, 0.96) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
    }

    &.is-active {
      // 与顶部导航相同的激活效果
      background: rgba(255, 255, 255, 0.18) !important;
      color: rgba(255, 255, 255, 0.96) !important;
      font-weight: 600 !important;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
    }

    // 重新定义图标样式 - 与顶部导航保持一致
    .svg-icon,
    .sub-menu-icon {
      font-size: 16px !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      flex-shrink: 0 !important;
      width: 16px !important;
      height: 16px !important;
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      min-width: 16px !important;
      margin: 0 !important;
      // 与顶部导航相同的图标颜色
      color: rgba(255, 255, 255, 0.92) !important;
      fill: rgba(255, 255, 255, 0.92) !important;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) !important;
    }

    // 重新定义文本样式
    .sub-menu-title {
      font-size: 14px !important;
      line-height: 1.4 !important;
      margin: 0 0 0 16px !important;
      flex: 1 !important;
      text-align: left !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      font-weight: inherit !important;
    }

    &:hover .svg-icon,
    &:hover .sub-menu-icon {
      transform: scale(1.08) translateY(-1px) !important;
      color: #ffffff !important;
      fill: #ffffff !important;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2)) !important;
    }

    &.is-active .svg-icon,
    &.is-active .sub-menu-icon {
      color: #ffffff !important;
      fill: #ffffff !important;
      transform: scale(1.05) !important;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2)) !important;
    }

    .el-tag {
      margin-left: 12px !important;
      font-size: 10px !important;
      height: 18px !important;
      line-height: 16px !important;
      padding: 0 6px !important;
      border-radius: 9px !important;
      flex-shrink: 0 !important;
      // 深色主题下的标签样式
      background: rgba(59, 130, 246, 0.2) !important;
      color: #60a5fa !important;
      border: 1px solid rgba(59, 130, 246, 0.3) !important;
    }

    .selected-indicator {
      margin-left: auto !important;
      color: #60a5fa !important;
      font-size: 14px !important;
      flex-shrink: 0 !important;
    }
  }

  // 分割线样式 - 适应深色主题
  .el-menu-item + .el-menu-item {
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 20px;
      right: 20px;
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, rgba(147, 197, 253, 0.2) 50%, transparent 100%);
      opacity: 0.6;
    }
  }
}

// 强制覆盖 Element Plus 默认样式
:deep(.el-popper.top-menu-submenu) {
  .el-menu-item {
    .svg-icon,
    .sub-menu-icon {
      margin-right: 16px !important;
      margin-left: 0 !important;
    }

    .sub-menu-title {
      margin-left: 0 !important;
    }

    // 确保图标后面的文本有间距
    .svg-icon + span,
    .sub-menu-icon + span {
      margin-left: 16px !important;
    }
  }
}

.breadcrumb-container {
  /* 完全隐藏面包屑容器以为内容留取更多空间 */
  display: none !important;
  height: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  background: none !important;
  position: relative;

  &::before {
    display: none;
  }

  .breadcrumb-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 48px;
    padding: 0 24px;

    .breadcrumb-actions {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    :deep(.el-breadcrumb) {
      .el-breadcrumb__item {
        .el-breadcrumb__inner {
          color: #666;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            color: #1890ff;
            transform: translateX(2px);
          }
        }

        &:last-child .el-breadcrumb__inner {
          color: #3b82f6;
          font-weight: 600;
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .el-breadcrumb__separator {
          color: #ccc;
          font-weight: 600;
          transition: all 0.3s ease;
        }

        &:hover + .el-breadcrumb__item .el-breadcrumb__separator {
          color: #1890ff;
        }
      }
    }

    .breadcrumb-directory {
      color: #999 !important;
      cursor: default !important;

      .svg-icon {
        opacity: 0.6;
      }
    }

    .breadcrumb-link {
      color: #666;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        color: #1890ff;
        transform: translateX(2px);
      }

      .svg-icon {
        margin-right: 6px;
      }
    }

    .page-tags {
      display: flex;
      gap: 8px;
      align-items: center;

      .page-tag {
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 16px;
        font-size: 12px;
        height: 28px;
        line-height: 26px;
        padding: 0 12px;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
        }

        &.el-tag--primary {
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          border: none;
          color: white;
        }

        &.el-tag--info {
          background: rgba(24, 144, 255, 0.1);
          border: 1px solid rgba(24, 144, 255, 0.2);
          color: #1890ff;
        }
      }
    }

    .page-toolbar-container {
      display: flex;
      align-items: center;

      // 确保工具栏组件样式正确
      :deep(.right-toolbar) {
        display: flex;
        align-items: center;
        gap: 8px;

        .el-button {
          height: 32px;
          padding: 0 12px;
          font-size: 12px;
          border-radius: 6px;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }
}

.main-content-wrapper {
  flex: 1;
  display: flex;
  overflow: hidden;

  .content-area {
    flex: 1;
    padding: 0;
    position: relative;
    // {{ AURA-X: Modify - 移除原生滚动条，使用 OverlayScrollbars }}
    // overflow: auto; // 由 OverlayScrollbars 接管
    height: 100%;

    &.full-width {
      padding: 0;
    }
  }
}

// 动画样式
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
