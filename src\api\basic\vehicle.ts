import request from '@/utils/request';
import { AxiosPromise } from 'axios';

// 车辆信息接口类型定义
export interface VehicleVO {
  vehicleId?: number;
  plateNumber: string;
  vehicleNumber: string;
  brandModel?: string;
  vehicleType?: string;
  seatCount?: number;
  engineNumber?: string;
  vinNumber?: string;
  purchaseDate?: string;
  totalMileage?: number;
  deptId?: number | string;
  deptName?: string;
  status?: string;
  remark?: string;
}

export interface VehicleForm extends VehicleVO {}

export interface VehicleQuery {
  pageNum?: number;
  pageSize?: number;
  plateNumber?: string;
  vehicleNumber?: string;
  status?: string;
  deptId?: number | string;
}

// 查询车辆列表
export function listVehicle(query: VehicleQuery): AxiosPromise<VehicleVO[]> {
  return request({
    url: '/basic/vehicle/list',
    method: 'get',
    params: query
  });
}

// 查询车辆详细
export function getVehicle(vehicleId: string | number): AxiosPromise<VehicleVO> {
  return request({
    url: '/basic/vehicle/' + vehicleId,
    method: 'get'
  });
}

// 新增车辆
export function addVehicle(data: VehicleForm): AxiosPromise<void> {
  return request({
    url: '/basic/vehicle',
    method: 'post',
    data: data
  });
}

// 修改车辆
export function updateVehicle(data: VehicleForm): AxiosPromise<void> {
  return request({
    url: '/basic/vehicle',
    method: 'put',
    data: data
  });
}

// 删除车辆
export function delVehicle(vehicleId: string | number | Array<string | number>): AxiosPromise<void> {
  return request({
    url: '/basic/vehicle/' + vehicleId,
    method: 'delete'
  });
}
