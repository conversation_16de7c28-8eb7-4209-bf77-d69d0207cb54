<template>
  <div class="station-management-container">
    <el-row :gutter="20" style="height: 100vh;">
      <!-- 左侧线路导航 -->
      <el-col :span="6" class="left-panel">
        <div class="route-navigation">
          <div class="nav-header">
            <el-icon><MapLocation /></el-icon>
            <span>线路管理</span>
            <el-button type="primary" size="small" icon="Plus" @click="handleAddRoute">新增线路</el-button>
          </div>

          <div class="route-search">
            <el-input
              v-model="searchRoute"
              placeholder="搜索线路"
              prefix-icon="Search"
              clearable
            />
          </div>

          <div class="route-list">
            <div
              v-for="route in filteredRoutes"
              :key="route.routeId"
              class="route-item"
              :class="{ active: selectedRoute?.routeId === route.routeId }"
              @click="selectRoute(route)"
            >
              <div class="route-info">
                <div class="route-name">
                  <el-icon><Star /></el-icon>
                  {{ route.routeName }}
                </div>
                <div class="route-stats">
                  <span class="station-count">{{ route.stationCount }}站</span>
                  <span class="route-length">{{ route.totalLength }}km</span>
                </div>
                <div class="route-desc">{{ route.description }}</div>
              </div>
              <div class="route-actions">
                <el-button type="text" icon="Edit" @click.stop="handleEditRoute(route)" />
                <el-button type="text" icon="Delete" @click.stop="handleDeleteRoute(route)" />
              </div>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 右侧图形化编辑区域 -->
      <el-col :span="18" class="right-panel">
        <div class="station-editor" v-if="selectedRoute">
          <!-- 顶部工具栏 -->
          <div class="editor-header">
            <div class="route-title">
              <h2>{{ selectedRoute.routeName }} - 站点编辑</h2>
              <div class="route-meta">
                <el-tag type="info">{{ selectedRoute.routeType }}</el-tag>
                <span>共 {{ selectedRoute.stations.length }} 个站点</span>
              </div>
            </div>
            <div class="editor-actions">
              <el-button type="primary" icon="Plus" @click="handleAddStation">添加站点</el-button>
              <el-button type="success" icon="Check" @click="handleSaveRoute">保存路线</el-button>
              <el-button
                type="info"
                :icon="currentView === 'map' ? 'List' : 'MapLocation'"
                @click="toggleView"
              >
                {{ currentView === 'map' ? '图形化视图' : '地图视图' }}
              </el-button>
            </div>
          </div>

          <!-- 站点连线图区域 -->
          <div v-if="currentView === 'graphic'" class="station-timeline-container">
            <div class="timeline-header">
              <div class="direction-indicator">
                <el-icon><Right /></el-icon>
                <span>行驶方向</span>
              </div>
              <div class="legend">
                <div class="legend-item">
                  <div class="station-dot normal"></div>
                  <span>正常</span>
                </div>
                <div class="legend-item">
                  <div class="station-dot maintenance"></div>
                  <span>维护中</span>
                </div>
                <div class="legend-item">
                  <div class="station-dot disabled"></div>
                  <span>停用</span>
                </div>
              </div>
            </div>

            <!-- 可拖拽的站点网格 -->
            <div class="station-grid" ref="timelineRef">
              <div
                v-for="(station, index) in sortedStations"
                :key="station.stationId"
                class="station-node"
                :class="{
                  active: selectedStation?.stationId === station.stationId,
                  [`status-${station.status}`]: true
                }"
                @click="selectStation(station)"
                @contextmenu.prevent="showStationMenu($event, station)"
                draggable="true"
                @dragstart="handleDragStart($event, index)"
                @dragover.prevent
                @drop="handleDrop($event, index)"
              >
                <!-- 站点图标 -->
                <div class="station-icon">
                  <span class="station-number">{{ station.stationOrder }}</span>
                </div>

                <!-- 站点信息 -->
                <div class="station-info">
                  <div class="station-name">{{ station.stationName }}</div>
                  <div class="station-order">第{{ station.stationOrder }}站</div>
                  <div class="station-distance" v-if="station.stationOrder > 1">
                    {{ getDistanceFromPrev(station, index) }}m
                  </div>
                </div>

                <!-- 站点状态指示器 -->
                <div class="status-indicator" :class="station.status">
                  <el-icon v-if="station.status === 'maintenance'"><Tools /></el-icon>
                  <el-icon v-else-if="station.status === 'disabled'"><Close /></el-icon>
                  <el-icon v-else><Check /></el-icon>
                </div>
              </div>

              <!-- 添加站点的占位符 -->
              <div
                class="add-station-placeholder"
                @click="handleInsertStation(selectedRoute.stations.length)"
              >
                <el-icon><Plus /></el-icon>
                <span>添加站点</span>
              </div>
            </div>
          </div>

          <!-- 地图视图 -->
          <div v-if="currentView === 'map'" class="map-view-container">
            <div class="map-header">
              <h3>站点地图视图</h3>
              <div class="map-controls">
                <el-button size="small" @click="centerMapToRoute">居中显示</el-button>
                <el-button size="small" @click="refreshStationMap">刷新地图</el-button>
                <el-button
                  size="small"
                  :type="editingStationMode ? 'warning' : 'info'"
                  :class="{ 'active-mode': editingStationMode }"
                  @click="toggleEditingMode"
                >
                  {{ editingStationMode ? '完成编辑' : '编辑站点' }}
                </el-button>
                <div class="map-tip" v-if="editingStationMode">
                  <el-icon><InfoFilled /></el-icon>
                  <span>可以拖拽移动站点位置或点击空白处添加站点</span>
                </div>
              </div>
            </div>
            <div class="map-content" ref="mapRef">
              <div id="station-map" class="station-map-view"></div>
              <!-- 地图加载层 -->
              <div v-if="mapLoading" class="map-loading-overlay">
                <div class="loading-content">
                  <el-icon class="is-loading" size="32"><Loading /></el-icon>
                  <p>地图加载中...</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 未选择线路时的提示 -->
        <div class="empty-state" v-else>
          <el-empty description="请选择一条线路开始编辑站点" />
        </div>

        <!-- 地图最大化时的背景遮罩 -->
        <div v-if="mapMaximized" class="map-overlay" @click="toggleMapSize"></div>
      </el-col>
    </el-row>

    <!-- 新增路线弹窗 -->
    <el-dialog
      v-model="addRouteDialogVisible"
      title="新增路线"
      width="800px"
      append-to-body
    >
      <div class="station-detail-form" v-if="newRouteForm">
        <el-form :model="newRouteForm" label-width="100px" :label-position="'left'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="路线名称">
                <el-input v-model="newRouteForm.routeName" placeholder="请输入路线名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路线类型">
                <el-select v-model="newRouteForm.routeType" placeholder="请选择路线类型" style="width: 100%">
                  <el-option label="市内线路" value="市内线路" />
                  <el-option label="郊区线路" value="郊区线路" />
                  <el-option label="快速线路" value="快速线路" />
                  <el-option label="夜班线路" value="夜班线路" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="起点站">
                <el-input v-model="newRouteForm.startStation" placeholder="请输入起点站" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="终点站">
                <el-input v-model="newRouteForm.endStation" placeholder="请输入终点站" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="总里程">
                <el-input-number
                  v-model="newRouteForm.totalLength"
                  :precision="1"
                  :min="0"
                  placeholder="公里"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="运行时间">
                <el-input v-model="newRouteForm.operationTime" placeholder="如：6:00-22:00" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="发车间隔">
                <el-input-number
                  v-model="newRouteForm.interval"
                  :min="1"
                  placeholder="分钟"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="票价">
                <el-input-number
                  v-model="newRouteForm.ticketPrice"
                  :precision="1"
                  :min="0"
                  placeholder="元"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="路线描述">
            <el-input v-model="newRouteForm.description" type="textarea" :rows="2" placeholder="请输入路线描述" />
          </el-form-item>

          <el-form-item label="备注">
            <el-input v-model="newRouteForm.remark" type="textarea" :rows="2" placeholder="请输入备注信息" />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmAddRoute">确定</el-button>
          <el-button @click="addRouteDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑路线弹窗 -->
    <el-dialog
      v-model="editRouteDialogVisible"
      title="编辑路线"
      width="800px"
      append-to-body
    >
      <div class="station-detail-form" v-if="editRouteForm">
        <el-form :model="editRouteForm" label-width="100px" :label-position="'left'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="路线名称">
                <el-input v-model="editRouteForm.routeName" placeholder="请输入路线名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="路线类型">
                <el-select v-model="editRouteForm.routeType" placeholder="请选择路线类型" style="width: 100%">
                  <el-option label="市内线路" value="市内线路" />
                  <el-option label="郊区线路" value="郊区线路" />
                  <el-option label="快速线路" value="快速线路" />
                  <el-option label="夜班线路" value="夜班线路" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="总里程">
                <el-input-number
                  v-model="editRouteForm.totalLength"
                  :precision="1"
                  :min="0"
                  placeholder="公里"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="运行时间">
                <el-input v-model="editRouteForm.operationTime" placeholder="如：6:00-22:00" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="发车间隔">
                <el-input-number
                  v-model="editRouteForm.interval"
                  :min="1"
                  placeholder="分钟"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="票价">
                <el-input-number
                  v-model="editRouteForm.ticketPrice"
                  :precision="1"
                  :min="0"
                  placeholder="元"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="路线描述">
            <el-input v-model="editRouteForm.description" type="textarea" :rows="2" placeholder="请输入路线描述" />
          </el-form-item>

          <el-form-item label="备注">
            <el-input v-model="editRouteForm.remark" type="textarea" :rows="2" placeholder="请输入备注信息" />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmEditRoute">确定</el-button>
          <el-button @click="editRouteDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增站点弹窗 -->
    <el-dialog
      v-model="addStationDialogVisible"
      title="新增站点"
      width="800px"
      append-to-body
    >
      <div class="station-detail-form" v-if="newStationForm">
        <el-form :model="newStationForm" label-width="100px" :label-position="'left'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="站点名称">
                <el-input v-model="newStationForm.stationName" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="站点序号">
                <el-input-number v-model="newStationForm.stationOrder" :min="1" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="经纬度">
                <el-row :gutter="10">
                  <el-col :span="12">
                    <el-input-number
                      v-model="newStationForm.longitude"
                      :precision="6"
                      placeholder="经度"
                      style="width: 100%"
                    />
                  </el-col>
                  <el-col :span="12">
                    <el-input-number
                      v-model="newStationForm.latitude"
                      :precision="6"
                      placeholder="纬度"
                      style="width: 100%"
                    />
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="站台类型">
                <el-select v-model="newStationForm.platformType" style="width: 100%">
                  <el-option label="岛式站台" value="island" />
                  <el-option label="侧式站台" value="side" />
                  <el-option label="港湾式站台" value="bay" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="候车亭">
                <el-switch v-model="newStationForm.hasShelter" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="站点状态">
            <el-radio-group v-model="newStationForm.status">
              <el-radio value="normal">正常</el-radio>
              <el-radio value="maintenance">维护中</el-radio>
              <el-radio value="disabled">停用</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="站点地址">
            <el-input v-model="newStationForm.address" type="textarea" :rows="2" />
          </el-form-item>

          <el-form-item label="周边设施">
            <el-input v-model="newStationForm.facilities" type="textarea" :rows="3" />
          </el-form-item>

          <el-form-item label="备注">
            <el-input v-model="newStationForm.remark" type="textarea" :rows="2" />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmAddStation">确定</el-button>
          <el-button @click="addStationDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>


    <!-- 站点详情编辑弹窗 -->
    <el-dialog
      v-model="stationDialogVisible"
      title="站点详情"
      width="800px"
      append-to-body
    >
      <div class="station-detail-form" v-if="selectedStation">
        <el-form :model="selectedStation" label-width="100px" :label-position="'left'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="站点名称">
                <el-input v-model="selectedStation.stationName" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="站点序号">
                <el-input-number v-model="selectedStation.stationOrder" :min="1" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="经纬度">
                <el-row :gutter="10">
                  <el-col :span="12">
                    <el-input-number
                      v-model="selectedStation.longitude"
                      :precision="6"
                      placeholder="经度"
                      style="width: 100%"
                    />
                  </el-col>
                  <el-col :span="12">
                    <el-input-number
                      v-model="selectedStation.latitude"
                      :precision="6"
                      placeholder="纬度"
                      style="width: 100%"
                    />
                  </el-col>
                </el-row>
                <div style="margin-top: 8px; color: #666; font-size: 12px;">
                  💡 提示：在地图上直接拖拽站点标记即可修改位置
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="站台类型">
                <el-select v-model="selectedStation.platformType" style="width: 100%">
                  <el-option label="岛式站台" value="island" />
                  <el-option label="侧式站台" value="side" />
                  <el-option label="港湾式站台" value="bay" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="候车亭">
                <el-switch v-model="selectedStation.hasShelter" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="站点状态">
            <el-radio-group v-model="selectedStation.status">
              <el-radio value="normal">正常</el-radio>
              <el-radio value="maintenance">维护中</el-radio>
              <el-radio value="disabled">停用</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="站点地址">
            <el-input v-model="selectedStation.address" type="textarea" :rows="2" />
          </el-form-item>

          <el-form-item label="周边设施">
            <el-input v-model="selectedStation.facilities" type="textarea" :rows="3" />
          </el-form-item>

          <el-form-item label="备注">
            <el-input v-model="selectedStation.remark" type="textarea" :rows="2" />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="saveStationDetails">保存</el-button>
          <el-button @click="stationDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="deleteStation">删除站点</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 右键菜单 -->
    <div
      class="context-menu"
      v-show="contextMenuVisible"
      :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }"
      @blur="hideContextMenu"
      tabindex="-1"
    >
      <div class="menu-item" @click="editStationInPlace">编辑站点</div>
      <div class="menu-item" @click="insertStationBefore">前面插入站点</div>
      <div class="menu-item" @click="insertStationAfter">后面插入站点</div>
      <div class="menu-divider"></div>
      <div class="menu-item" @click="locateStationOnMap">定位到地图</div>
      <div class="menu-divider"></div>
      <div class="menu-item danger" @click="deleteStationFromMenu">删除站点</div>
    </div>
  </div>
</template>

<script setup name="Station">

import {
  createOptimizedMap,
  addOptimizedControls,
  suppressMapWarnings
} from '@/utils/mapConfig';

const { proxy } = getCurrentInstance();

// 数据状态
const routes = ref([]);
const selectedRoute = ref(null);
const selectedStation = ref(null);
const searchRoute = ref('');
const currentView = ref('graphic'); // 'graphic' | 'map'
const stationDialogVisible = ref(false);
const addStationDialogVisible = ref(false);
const addRouteDialogVisible = ref(false);
const editRouteDialogVisible = ref(false);
const newStationForm = ref(null);
const newRouteForm = ref(null);
const editRouteForm = ref(null);
const mapMaximized = ref(false);
const mapLoading = ref(false);
const editingStationMode = ref(false); // 编辑站点位置模式
const pendingChanges = ref(new Map()); // 存储待确认的位置变更

// 初始化window对象存储地图相关数据
if (!window.stationMapData) {
  window.stationMapData = {
    mapInstance: null,
    markers: []
  };
}

// 辅助函数，简化对地图对象的访问
const getMapInstance = () => window.stationMapData.mapInstance;
const setMapInstance = (map) => { window.stationMapData.mapInstance = map; };
const getMarkers = () => window.stationMapData.markers;
const setMarkers = (markers) => { window.stationMapData.markers = markers; };

// 右键菜单
const contextMenuVisible = ref(false);
const contextMenuX = ref(0);
const contextMenuY = ref(0);
const contextMenuStation = ref(null);

// 拖拽相关
const draggedIndex = ref(-1);

// 插入模式相关
const insertMode = ref(''); // 'before' | 'after' | ''
const insertTargetIndex = ref(-1);

// 模拟数据
const mockRoutes = [
  {
    routeId: 1,
    routeName: '1路',
    routeType: '市内线路',
    stationCount: 25,
    totalLength: 18.5,
    description: '火车站 - 体育中心',
    stations: [
      {
        stationId: 1,
        stationName: '火车站',
        stationOrder: 1,
        longitude: 116.407526,
        latitude: 39.904030,
        platformType: 'island',
        hasShelter: true,
        status: 'normal',
        address: '北京市东城区火车站广场',
        facilities: '地铁换乘、商场、餐厅',
        remark: '起点站'
      },
      {
        stationId: 2,
        stationName: '市政府',
        stationOrder: 2,
        longitude: 116.408526,
        latitude: 39.905030,
        platformType: 'side',
        hasShelter: true,
        status: 'normal',
        address: '北京市东城区市政府门前',
        facilities: '政务大厅、银行',
        remark: '重要站点'
      },
      {
        stationId: 3,
        stationName: '购物中心',
        stationOrder: 3,
        longitude: 116.409026,
        latitude: 39.905530,
        platformType: 'bay',
        hasShelter: false,
        status: 'maintenance',
        address: '北京市朝阳区购物中心',
        facilities: '商场、停车场',
        remark: '维护中'
      },
      {
        stationId: 4,
        stationName: '体育中心',
        stationOrder: 4,
        longitude: 116.409526,
        latitude: 39.906030,
        platformType: 'island',
        hasShelter: true,
        status: 'normal',
        address: '北京市朝阳区体育中心',
        facilities: '体育场馆、停车场',
        remark: '终点站'
      }
    ]
  },
  {
    routeId: 2,
    routeName: '2路',
    routeType: '市内线路',
    stationCount: 18,
    totalLength: 12.3,
    description: '汽车站 - 科技园',
    stations: [
      {
        stationId: 5,
        stationName: '汽车站',
        stationOrder: 1,
        longitude: 116.407526,
        latitude: 39.904030,
        platformType: 'side',
        hasShelter: true,
        status: 'normal',
        address: '北京市西城区汽车站',
        facilities: '长途客运、餐厅',
        remark: '起点站'
      },
      {
        stationId: 6,
        stationName: '科技园',
        stationOrder: 2,
        longitude: 116.408526,
        latitude: 39.905030,
        platformType: 'island',
        hasShelter: true,
        status: 'disabled',
        address: '北京市海淀区科技园',
        facilities: '写字楼、科技公司',
        remark: '终点站'
      }
    ]
  }
];

// 计算属性
const filteredRoutes = computed(() => {
  if (!searchRoute.value) return routes.value;
  return routes.value.filter(route =>
    route.routeName.includes(searchRoute.value) ||
    route.description.includes(searchRoute.value)
  );
});

// 按站点顺序排序的站点列表
const sortedStations = computed(() => {
  if (!selectedRoute.value || !selectedRoute.value.stations) return [];
  return [...selectedRoute.value.stations].sort((a, b) => a.stationOrder - b.stationOrder);
});

// 方法实现
function selectRoute(route) {
  selectedRoute.value = route;
  selectedStation.value = null;

  // 如果地图已显示，更新地图内容
  if (currentView.value === 'map') {
    nextTick(() => {
      updateMapContent();
    });
  }
}

function selectStation(station) {
  selectedStation.value = station;
  stationDialogVisible.value = true;
}

function getDistanceFromPrev(station, index) {
  if (index === 0) return 0;
  const prevStation = selectedRoute.value.stations[index - 1];
  // 简单计算两点间距离（实际应用中需要更精确的地理计算）
  const distance = Math.sqrt(
    Math.pow((station.longitude - prevStation.longitude) * 111000, 2) +
    Math.pow((station.latitude - prevStation.latitude) * 111000, 2)
  );
  return Math.round(distance);
}

function showStationMenu(event, station) {
  contextMenuVisible.value = false;
  contextMenuStation.value = station;
  contextMenuX.value = event.clientX;
  contextMenuY.value = event.clientY;
  contextMenuVisible.value = true;

  nextTick(() => {
    const menu = document.querySelector('.context-menu');
    if (menu) {
      menu.focus();
    }
  });
}

function hideContextMenu() {
  contextMenuVisible.value = false;
}

function handleDragStart(event, index) {
  draggedIndex.value = index;
}

function handleDrop(event, targetIndex) {
  if (draggedIndex.value !== -1 && draggedIndex.value !== targetIndex && selectedRoute.value) {
    const stations = selectedRoute.value.stations;
    
    // 移动站点：从原位置移除并插入到目标位置
    const draggedStation = stations.splice(draggedIndex.value, 1)[0];
    stations.splice(targetIndex, 0, draggedStation);
    
    // 重新分配所有站点的stationOrder（从1开始）
    stations.forEach((station, newIndex) => {
      station.stationOrder = newIndex + 1;
    });

    // 自动更新路线描述（基于新的stationOrder排序）
    updateRouteDescription(selectedRoute.value);

    // 同步更新routes数组中的对应路线
    const routeIndex = routes.value.findIndex(r => r.routeId === selectedRoute.value.routeId);
    if (routeIndex > -1) {
      routes.value[routeIndex] = { ...selectedRoute.value };
    }

    // 刷新地图显示
    if (currentView.value === 'map' && window.stationMapData.mapInstance) {
      updateMapContent();
    }

    proxy?.$modal.msgSuccess(`${draggedStation.stationName} 已移动到第${targetIndex + 1}位`);
  }
  draggedIndex.value = -1;
}

function toggleView() {
  currentView.value = currentView.value === 'graphic' ? 'map' : 'graphic';

  if (currentView.value === 'map' && selectedRoute.value) {
    // 确保地图实例存在，如果不存在则创建
    nextTick(() => {
      ensureMapInstance();
    });
  }
}

function handleAddRoute() {
  // 初始化新路线表单
  newRouteForm.value = {
    routeId: Date.now(),
    routeName: '',
    routeType: '市内线路',
    startStation: '',
    endStation: '',
    totalLength: 0,
    operationTime: '6:00-22:00',
    interval: 10,
    ticketPrice: 2.0,
    description: '',
    remark: '',
    stationCount: 0,
    stations: []
  };

  addRouteDialogVisible.value = true;
}

// 确认添加路线
function confirmAddRoute() {
  if (!newRouteForm.value) return;

  // 生成路线描述
  if (newRouteForm.value.startStation && newRouteForm.value.endStation) {
    newRouteForm.value.description = `${newRouteForm.value.startStation} - ${newRouteForm.value.endStation}`;
  }

  // 添加到路线列表
  routes.value.push({ ...newRouteForm.value });
  addRouteDialogVisible.value = false;
  newRouteForm.value = null;
  proxy?.$modal.msgSuccess('路线添加成功');
}

// 确认修改路线
function confirmEditRoute() {
  if (!editRouteForm.value) return;

  const index = routes.value.findIndex(r => r.routeId === editRouteForm.value.routeId);
  if (index > -1) {
    // 更新路线信息
    routes.value[index] = { ...editRouteForm.value };

    // 如果当前选中的路线是被编辑的路线，同步更新
    if (selectedRoute.value?.routeId === editRouteForm.value.routeId) {
      selectedRoute.value = { ...editRouteForm.value };
    }
  }

  editRouteDialogVisible.value = false;
  editRouteForm.value = null;
  proxy?.$modal.msgSuccess('路线修改成功');
}

function handleEditRoute(route) {
  // 初始化编辑表单
  editRouteForm.value = {
    routeId: route.routeId,
    routeName: route.routeName,
    routeType: route.routeType,
    totalLength: route.totalLength,
    operationTime: route.operationTime,
    interval: route.interval,
    ticketPrice: route.ticketPrice,
    description: route.description,
    remark: route.remark,
    stationCount: route.stationCount,
    stations: route.stations
  };

  editRouteDialogVisible.value = true;
}


// 切换编辑模式（整合移动和新增功能）
function toggleEditingMode() {
  editingStationMode.value = !editingStationMode.value;

  if (editingStationMode.value) {
    // 进入编辑模式时，同时启用添加站点功能
    addingStationMode.value = true;
    // 清空之前的待确认变更
    pendingChanges.value.clear();
    proxy?.$modal.msgSuccess('已进入编辑模式，可以拖拽移动站点或点击空白处添加站点');
  } else {
    // 退出编辑模式，同时关闭添加站点功能，自动确认所有变更
    addingStationMode.value = false;
    if (pendingChanges.value.size > 0) {
      confirmAllChanges();
    } else {
      proxy?.$modal.msgSuccess('已退出编辑模式');
    }
  }

  // 刷新地图以更新拖拽状态
  if (currentView.value === 'map' && window.stationMapData.mapInstance) {
    updateMapContent();
  }
}

// 确认所有变更
function confirmAllChanges() {
  if (pendingChanges.value.size === 0) {
    proxy?.$modal.msgInfo('没有待确认的变更');
    return;
  }

  let updatedCount = 0;
  const promises = [];

  // 批量处理所有变更
  pendingChanges.value.forEach((change, stationId) => {
    const { station, newPosition } = change;

    // 更新站点坐标
    station.longitude = newPosition.longitude;
    station.latitude = newPosition.latitude;
    updatedCount++;

    // 异步获取地址信息
    const addressPromise = getAddressForStation(newPosition.longitude, newPosition.latitude, station);
    promises.push(addressPromise);
  });

  // 同步更新routes数组中的对应路线
  if (selectedRoute.value) {
    const routeIndex = routes.value.findIndex(r => r.routeId === selectedRoute.value.routeId);
    if (routeIndex > -1) {
      routes.value[routeIndex] = { ...selectedRoute.value };
    }
  }

  // 清空待确认变更
  pendingChanges.value.clear();

  // 等待所有地址信息获取完成
  Promise.allSettled(promises).then(() => {
    proxy?.$modal.msgSuccess(`已确认 ${updatedCount} 个站点的位置变更`);
  });
}

// 取消所有变更
function cancelAllChanges() {
  if (pendingChanges.value.size === 0) {
    proxy?.$modal.msgInfo('没有待取消的变更');
    return;
  }

  // 将所有标记恢复到原始位置
  pendingChanges.value.forEach((change, stationId) => {
    const marker = window.stationMapData.markers.find(m =>
      m.getExtData() && m.getExtData().stationId === stationId
    );
    if (marker) {
      const { originalPosition } = change;
      marker.setPosition([originalPosition.longitude, originalPosition.latitude]);
    }
  });

  const cancelledCount = pendingChanges.value.size;
  pendingChanges.value.clear();

  proxy?.$modal.msgSuccess(`已取消 ${cancelledCount} 个站点的位置变更`);
}

// 确认修改路线

// 获取地址信息（优化版本，返回Promise）
function getAddressFromCoordinates(lng, lat) {
  return new Promise((resolve) => {
    // 检查AMap是否已加载
    if (typeof AMap === 'undefined') {
      console.warn('AMap未加载，跳过地址解析');
      resolve('');
      return;
    }

    // 使用高德地图逆地理编码服务
    AMap.plugin('AMap.Geocoder', () => {
      const geocoder = new AMap.Geocoder({
        radius: 1000,
        extensions: 'all'
      });

      geocoder.getAddress([lng, lat], (status, result) => {
        if (status === 'complete' && result.regeocode) {
          resolve(result.regeocode.formattedAddress);
        } else {
          resolve('');
        }
      });
    });
  });
}

// 为站点获取地址信息（兼容旧版本）
function getAddressForStation(lng, lat, station) {
  getAddressFromCoordinates(lng, lat).then(address => {
    if (address) {
      station.address = address;
      // 同步更新routes数组
      const routeIndex = routes.value.findIndex(r => r.routeId === selectedRoute.value.routeId);
      if (routeIndex > -1) {
        routes.value[routeIndex] = { ...selectedRoute.value };
      }
    }
  });
}

function handleDeleteRoute(route) {
  proxy?.$modal.confirm(`确认删除线路: ${route.routeName}?`).then(() => {
    const index = routes.value.findIndex(r => r.routeId === route.routeId);
    if (index > -1) {
      routes.value.splice(index, 1);
      if (selectedRoute.value?.routeId === route.routeId) {
        selectedRoute.value = null;
      }
    }
    proxy?.$modal.msgSuccess('删除成功');
  });
}

function handleAddStation() {
  if (!selectedRoute.value) return;

  // 直接切换到地图视图
  currentView.value = 'map';

  // 确保地图加载完成后显示提示
  nextTick(() => {
    ensureMapInstance();
    // 不再自动进入添加模式，需要用户手动点击"新增站点"按钮
    proxy?.$modal.msgSuccess('请点击地图上方的"新增站点"按钮进入添加模式');
  });
}

// 更新路线描述（根据站点自动识别起终点）
function updateRouteDescription(route) {
  if (!route || !route.stations || route.stations.length === 0) {
    route.description = '';
    route.stationCount = 0;
    return;
  }

  // 按站点序号排序
  const sortedStations = [...route.stations].sort((a, b) => a.stationOrder - b.stationOrder);

  // 更新站点数量
  route.stationCount = sortedStations.length;

  // 生成起终点描述
  if (sortedStations.length === 1) {
    route.description = sortedStations[0].stationName;
  } else if (sortedStations.length >= 2) {
    const startStation = sortedStations[0].stationName;
    const endStation = sortedStations[sortedStations.length - 1].stationName;
    route.description = `${startStation} - ${endStation}`;
  }
}

function confirmAddStation() {
  if (!selectedRoute.value || !newStationForm.value) return;

  if (insertMode.value === 'before' || insertMode.value === 'after') {
    // 插入模式处理
    const targetIndex = insertTargetIndex.value;
    const insertIndex = insertMode.value === 'before' ? targetIndex : targetIndex + 1;

    // 在指定位置插入站点
    selectedRoute.value.stations.splice(insertIndex, 0, { ...newStationForm.value });

    // 重新编号所有站点
    selectedRoute.value.stations.forEach((station, index) => {
      station.stationOrder = index + 1;
    });

    // 重置插入模式标记
    insertMode.value = '';
    insertTargetIndex.value = -1;
  } else {
    // 正常添加模式，在末尾添加
    selectedRoute.value.stations.push({ ...newStationForm.value });
  }

  // 自动更新路线描述
  updateRouteDescription(selectedRoute.value);

  // 同步更新routes数组中的对应路线
  const routeIndex = routes.value.findIndex(r => r.routeId === selectedRoute.value.routeId);
  if (routeIndex > -1) {
    routes.value[routeIndex] = { ...selectedRoute.value };
  }

  // 刷新地图显示
  if (currentView.value === 'map' && window.stationMapData.mapInstance) {
    updateMapContent();
  }

  addStationDialogVisible.value = false;
  newStationForm.value = null;
  proxy?.$modal.msgSuccess('站点添加成功');
}

function handleSaveRoute() {
  proxy?.$modal.msgSuccess('路线保存成功');
}

function handleInsertStation(position) {
  if (!selectedRoute.value) return;

  // 直接切换到地图视图
  currentView.value = 'map';

  // 确保地图加载完成后显示提示
  nextTick(() => {
    ensureMapInstance();
    // 不再自动进入添加模式，需要用户手动点击"新增站点"按钮
    proxy?.$modal.msgSuccess('请点击地图上方的"新增站点"按钮进入添加模式');
  });
}

function saveStationDetails() {
  // 自动更新路线描述
  if (selectedRoute.value) {
    updateRouteDescription(selectedRoute.value);

    // 同步更新routes数组中的对应路线
    const routeIndex = routes.value.findIndex(r => r.routeId === selectedRoute.value.routeId);
    if (routeIndex > -1) {
      routes.value[routeIndex] = { ...selectedRoute.value };
    }

    // 刷新地图显示
    if (currentView.value === 'map' && window.stationMapData.mapInstance) {
      updateMapContent();
    }
  }

  proxy?.$modal.msgSuccess('站点信息保存成功');
  stationDialogVisible.value = false;
}

function deleteStation() {
  if (!selectedStation.value || !selectedRoute.value) return;

  proxy?.$modal.confirm(`确认删除站点: ${selectedStation.value.stationName}?`).then(() => {
    const index = selectedRoute.value.stations.findIndex(
      s => s.stationId === selectedStation.value.stationId
    );
    if (index > -1) {
      selectedRoute.value.stations.splice(index, 1);

      // 重新编号
      selectedRoute.value.stations.forEach((station, idx) => {
        station.stationOrder = idx + 1;
      });

      // 自动更新路线描述
      updateRouteDescription(selectedRoute.value);

      // 同步更新routes数组中的对应路线
      const routeIndex = routes.value.findIndex(r => r.routeId === selectedRoute.value.routeId);
      if (routeIndex > -1) {
        routes.value[routeIndex] = { ...selectedRoute.value };
      }
    }

    stationDialogVisible.value = false;
    selectedStation.value = null;
    proxy?.$modal.msgSuccess('删除成功');
  });
}

function editStationInPlace() {
  if (contextMenuStation.value) {
    selectedStation.value = contextMenuStation.value;
    stationDialogVisible.value = true;
  }
  hideContextMenu();
}

function insertStationBefore() {
  if (!contextMenuStation.value || !selectedRoute.value) return;

  const targetIndex = selectedRoute.value.stations.findIndex(
    s => s.stationId === contextMenuStation.value.stationId
  );

  // 初始化新站点表单，序号为目标站点序号
  newStationForm.value = {
    stationId: Date.now(),
    stationName: `新站点${Date.now()}`,
    stationOrder: contextMenuStation.value.stationOrder,
    longitude: 116.407526,
    latitude: 39.904030,
    platformType: 'side',
    hasShelter: false,
    status: 'normal',
    address: '',
    facilities: '',
    remark: ''
  };

  // 设置插入模式标记
  insertMode.value = 'before';
  insertTargetIndex.value = targetIndex;

  addStationDialogVisible.value = true;
  hideContextMenu();
}

function insertStationAfter() {
  if (!contextMenuStation.value || !selectedRoute.value) return;

  const targetIndex = selectedRoute.value.stations.findIndex(
    s => s.stationId === contextMenuStation.value.stationId
  );

  // 初始化新站点表单，序号为目标站点序号+1
  newStationForm.value = {
    stationId: Date.now(),
    stationName: `新站点${Date.now()}`,
    stationOrder: contextMenuStation.value.stationOrder + 1,
    longitude: 116.407526,
    latitude: 39.904030,
    platformType: 'side',
    hasShelter: false,
    status: 'normal',
    address: '',
    facilities: '',
    remark: ''
  };

  // 设置插入模式标记
  insertMode.value = 'after';
  insertTargetIndex.value = targetIndex;

  addStationDialogVisible.value = true;
  hideContextMenu();
}

function deleteStationFromMenu() {
  if (!contextMenuStation.value || !selectedRoute.value) return;

  proxy?.$modal.confirm(`确认删除站点: ${contextMenuStation.value.stationName}?`).then(() => {
    const index = selectedRoute.value.stations.findIndex(
      s => s.stationId === contextMenuStation.value.stationId
    );
    if (index > -1) {
      selectedRoute.value.stations.splice(index, 1);

      // 重新编号
      selectedRoute.value.stations.forEach((station, idx) => {
        station.stationOrder = idx + 1;
      });

      // 自动更新路线描述
      updateRouteDescription(selectedRoute.value);

      // 同步更新routes数组中的对应路线
      const routeIndex = routes.value.findIndex(r => r.routeId === selectedRoute.value.routeId);
      if (routeIndex > -1) {
        routes.value[routeIndex] = { ...selectedRoute.value };
      }
    }

    proxy?.$modal.msgSuccess('删除成功');
  });

  hideContextMenu();
}

function locateStationOnMap() {
  if (!contextMenuStation.value) return;

  // 确保地图已显示并初始化
  if (currentView.value !== 'map') {
    currentView.value = 'map';
    // 使用setTimeout确保DOM更新后再初始化地图，然后等待地图初始化完成
    setTimeout(() => {
      if (selectedRoute.value) {
        ensureMapInstance();
        waitForMapComplete().then(() => {
          centerMapOnStation();
        }).catch(error => {
          console.error('地图初始化失败:', error);
          proxy?.$modal.msgError('地图初始化失败，请重试');
        });
      }
    }, 100);
  } else if (!window.stationMapData.mapInstance) {
    // 地图已显示但没有初始化，重新初始化
    // 直接定位
    centerMapOnStation();
  } else {
    // 地图已准备就绪，直接定位
    centerMapOnStation();
  }

  function waitForMapComplete() {
    return new Promise((resolve, reject) => {
      let attempts = 0;
      const maxAttempts = 120; // 12秒超时，比AMap API加载超时长一些

      const checkMap = setInterval(() => {
        attempts++;

        if (window.stationMapData.mapInstance && !mapLoading.value) {
          clearInterval(checkMap);
          resolve();
        } else if (attempts >= maxAttempts) {
          clearInterval(checkMap);
          reject(new Error('地图初始化超时'));
        }
      }, 100);
    });
  }

  function centerMapOnStation() {
    if (window.stationMapData.mapInstance && contextMenuStation.value) {
      const position = [contextMenuStation.value.longitude, contextMenuStation.value.latitude];

      // 使用panTo方法快速定位，设置较短的动画时间
      window.stationMapData.mapInstance.panTo(position, 200); // 200ms动画时间
      window.stationMapData.mapInstance.setZoom(16, true, 200); // 200ms缩放动画

      // 高亮显示目标站点
      const targetMarker = window.stationMapData.markers.find(marker =>
        marker.getExtData() && marker.getExtData().stationId === contextMenuStation.value.stationId
      );

      if (targetMarker) {
        // 让标记闪烁几次以突出显示，缩短闪烁间隔
        let blinkCount = 0;
        const blinkInterval = setInterval(() => {
          if (blinkCount >= 6) {
            clearInterval(blinkInterval);
            return;
          }
          targetMarker.setVisible(blinkCount % 2 === 0);
          blinkCount++;
        }, 150); // 从300ms改为150ms
      }

      proxy?.$modal.msgSuccess(`已定位到站点: ${contextMenuStation.value.stationName}`);
    }
  }

  hideContextMenu();
}

// 确保地图实例存在
function ensureMapInstance() {
  if (!selectedRoute.value || !selectedRoute.value.stations.length) return;

  // 检查地图容器是否存在
  const mapContainer = document.getElementById('station-map');
  if (!mapContainer) {
    console.warn('地图容器不存在，延迟重试');
    setTimeout(() => {
      ensureMapInstance();
    }, 200);
    return;
  }

  // 检查地图实例是否有效
  if (window.stationMapData.mapInstance && isMapInstanceValid()) {
    console.log('地图实例有效，更新地图内容');
    updateMapContent();
    return;
  }

  // 地图实例无效或不存在，重新创建
  console.log('地图实例无效或不存在，重新创建');
  if (window.stationMapData.mapInstance) {
    // 清理无效的实例
    try {
      window.stationMapData.mapInstance.destroy();
    } catch (error) {
      console.warn('清理无效地图实例时出错:', error);
    }
    window.stationMapData.mapInstance = null;
    window.stationMapData.markers = [];
  }

  // 创建新的地图实例
  createMapInstance();
}

// 检查地图实例是否有效
function isMapInstanceValid() {
  if (!window.stationMapData.mapInstance) return false;

  try {
    // 检查地图容器是否还在DOM中
    const container = window.stationMapData.mapInstance.getContainer();
    if (!container || !document.contains(container)) {
      console.log('地图容器已被移除从 DOM');
      return false;
    }

    // 检查地图实例是否正常
    const mapStatus = window.stationMapData.mapInstance.getStatus();
    if (!mapStatus || mapStatus.destroyed) {
      console.log('地图实例已被销毁');
      return false;
    }

    return true;
  } catch (error) {
    console.warn('检查地图实例有效性时出错:', error);
    return false;
  }
}

// 创建地图实例（只在第一次或实例被销毁后调用）
function createMapInstance() {
  // 检查AMap是否已加载
  if (typeof AMap === 'undefined') {
    waitForAMap().then(() => {
      createMapInstanceInternal();
    }).catch(error => {
      console.error('AMap API加载失败:', error);
      proxy?.$modal.msgError('地图API加载失败，请刷新页面重试');
    });
    return;
  }

  createMapInstanceInternal();
}

// 内部创建地图实例方法
function createMapInstanceInternal() {
  try {
    mapLoading.value = true;

    // 抑制地图警告
    suppressMapWarnings();

    // 计算地图中心点
    const stations = selectedRoute.value.stations;
    const centerLng = stations.reduce((sum, station) => sum + station.longitude, 0) / stations.length;
    const centerLat = stations.reduce((sum, station) => sum + station.latitude, 0) / stations.length;

    // 创建地图实例
    const map = createOptimizedMap('station-map', {
      center: [centerLng, centerLat],
      zoom: 14
    });

    // 添加控件
    addOptimizedControls(map);

    window.stationMapData.mapInstance = map;

    // 地图加载完成后添加内容
    map.on('complete', () => {
      updateMapContent();
      addMapClickHandler(map); // 添加地图点击事件
      mapLoading.value = false;
      proxy?.$modal.msgSuccess('地图加载完成');
    });

    // 监听地图错误
    map.on('error', (error) => {
      console.error('地图错误:', error);
      mapLoading.value = false;
      proxy?.$modal.msgError('地图加载失败');
    });

  } catch (error) {
    console.error('地图实例创建失败:', error);
    proxy?.$modal.msgError('地图实例创建失败');
    mapLoading.value = false;
  }
}

// 更新地图内容（标记和路线）
function updateMapContent() {
  if (!window.stationMapData.mapInstance || !selectedRoute.value) return;

  // 再次检查地图实例有效性
  if (!isMapInstanceValid()) {
    console.log('地图实例无效，重新创建');
    ensureMapInstance();
    return;
  }

  try {
    // 清理并重新添加站点标记
    clearMapMarkers();
    addStationMarkers(window.stationMapData.mapInstance);

    // 确保地图点击事件在地图视图中生效
    if (currentView.value === 'map') {
      addMapClickHandler(window.stationMapData.mapInstance);
    }

    // 调整视野适应所有站点
    setTimeout(() => {
      centerMapToRoute();
    }, 100);

  } catch (error) {
    console.error('更新地图内容失败:', error);
    // 如果更新失败，尝试重新创建地图
    window.stationMapData.mapInstance = null;
    ensureMapInstance();
  }
}

// 添加地图点击事件处理器
function addMapClickHandler(map) {
  if (!map || currentView.value !== 'map') return;

  // 移除之前的监听器（如果有）
  map.off('click', handleMapClick);

  // 添加新的左键点击监听器
  map.on('click', handleMapClick);
}

// 处理地图左键点击事件
function handleMapClick(e) {
  // 只在编辑模式且地图视图且有选中路线时才处理添加站点
  if (!editingStationMode.value || currentView.value !== 'map' || !selectedRoute.value) return;

  const { lng, lat } = e.lnglat;

  // 创建新站点表单，预填经纬度
  createNewStationFromMap(lng, lat);
}

// 从地图点击创建新站点
function createNewStationFromMap(lng, lat) {
  // 初始化新站点表单
  newStationForm.value = {
    stationId: Date.now(),
    stationName: '新站点',
    stationOrder: selectedRoute.value.stations.length + 1,
    longitude: Number(lng.toFixed(6)),
    latitude: Number(lat.toFixed(6)),
    platformType: 'side',
    hasShelter: false,
    status: 'normal',
    address: '',
    facilities: '',
    remark: '通过地图点击添加'
  };

  // 获取地址信息（异步）
  getAddressFromCoordinates(lng, lat).then(address => {
    if (address && newStationForm.value) {
      newStationForm.value.address = address;
    }
  });

  // 显示添加站点对话框
  addStationDialogVisible.value = true;

  // 给用户提示
  proxy?.$modal.msgSuccess(`已获取坐标: ${lng.toFixed(6)}, ${lat.toFixed(6)}`);

  // 显示成功提示，但保持编辑模式继续
}

// 清理地图标记
function clearMapMarkers() {
  if (window.stationMapData.mapInstance && window.stationMapData.markers.length > 0) {
    try {
      // 清理标记及其关联的文本标记
      window.stationMapData.markers.forEach(marker => {
        if (marker.textMarker) {
          window.stationMapData.mapInstance.remove(marker.textMarker);
        }
      });
      window.stationMapData.mapInstance.remove(window.stationMapData.markers);
    } catch (error) {
      console.warn('清理地图标记时出错:', error);
    }
    window.stationMapData.markers = [];
  }
}

// 等待AMap API加载
function waitForAMap() {
  return new Promise((resolve, reject) => {
    if (typeof AMap !== 'undefined') {
      resolve(AMap);
      return;
    }

    // 显示加载提示
    proxy?.$modal.msgInfo('正在加载地图API...');

    let attempts = 0;
    const maxAttempts = 50; // 减少到5秒超时，提升响应速度

    const checkAMap = setInterval(() => {
      attempts++;

      if (typeof AMap !== 'undefined') {
        clearInterval(checkAMap);
        resolve(AMap);
      } else if (attempts >= maxAttempts) {
        clearInterval(checkAMap);
        reject(new Error('AMap API加载超时'));
      }
    }, 100);
  });
}

// 内部地图初始化方法
function initStationMapInternal() {
  try {
    // 再次检查地图容器
    const mapContainer = document.getElementById('station-map');
    if (!mapContainer) {
      console.error('地图容器不存在，无法初始化地图');
      mapLoading.value = false;
      return;
    }

    // 清理旧地图实例和相关资源
    if (window.stationMapData.mapInstance) {
      // 清理站点标记
      if (window.stationMapData.markers.length > 0) {
        try {
          window.stationMapData.mapInstance.remove(window.stationMapData.markers);
        } catch (error) {
          console.warn('清理旧站点标记时出错:', error);
        }
        window.stationMapData.markers = [];
      }

      // 销毁地图实例
      try {
        window.stationMapData.mapInstance.destroy();
      } catch (error) {
        console.warn('销毁地图实例时出错:', error);
      }
      window.stationMapData.mapInstance = null;
    }

    // 清理容器内容
    mapContainer.innerHTML = '';

    // 抑制地图警告
    suppressMapWarnings();

    // 计算地图中心点
    const stations = selectedRoute.value.stations;
    const centerLng = stations.reduce((sum, station) => sum + station.longitude, 0) / stations.length;
    const centerLat = stations.reduce((sum, station) => sum + station.latitude, 0) / stations.length;

    // 创建地图实例
    const map = createOptimizedMap('station-map', {
      center: [centerLng, centerLat],
      zoom: 14
    });

    // 添加控件
    addOptimizedControls(map);

    window.stationMapData.mapInstance = map;

    // 地图加载完成后批量处理所有内容
    map.on('complete', () => {
      try {
        // 批量添加站点标记
        addStationMarkers(map);

        // 调整视野适应所有站点
        setTimeout(() => {
          centerMapToRoute();
        }, 100);

        mapLoading.value = false;
        proxy?.$modal.msgSuccess('地图加载完成');
      } catch (error) {
        console.error('地图内容初始化失败:', error);
        mapLoading.value = false;
      }
    });

    // 监听地图错误
    map.on('error', (error) => {
      console.error('地图错误:', error);
      mapLoading.value = false;
      proxy?.$modal.msgError('地图加载失败');
    });

    // 确保loading状态在合理时间内结束
    setTimeout(() => {
      if (mapLoading.value) {
        mapLoading.value = false;
        console.warn('地图加载超时，强制结束loading状态');
      }
    }, 5000); // 增加超时时间到5秒

  } catch (error) {
    console.error('站点地图初始化失败:', error);
    proxy?.$modal.msgError('站点地图初始化失败');
    mapLoading.value = false;
  }
}

// 添加站点标记
function addStationMarkers(map) {
  // 检查AMap是否可用
  if (typeof AMap === 'undefined') {
    console.warn('AMap未加载，无法添加站点标记');
    return;
  }

  if (!selectedRoute.value || !selectedRoute.value.stations.length) return;

  // 按照stationOrder排序站点
  const sortedStations = [...selectedRoute.value.stations].sort((a, b) => a.stationOrder - b.stationOrder);

  // 检查是否需要重新创建标记（站点数量或顺序改变）
  const needRecreate = window.stationMapData.markers.length !== sortedStations.length ||
    window.stationMapData.markers.some((marker, index) => {
      const markerData = marker.getExtData();
      return !markerData || markerData.stationId !== sortedStations[index]?.stationId;
    });

  if (!needRecreate) {
    console.log('站点标记无需重新创建，跳过');
    return;
  }

  // 批量清理旧标记
  if (window.stationMapData.markers.length > 0) {
    map.remove(window.stationMapData.markers);
    window.stationMapData.markers = [];
  }

  // 批量创建新标记
  const markersToAdd = [];
  sortedStations.forEach((station, index) => {
    // 创建自定义标记内容
    const markerContent = createStationMarkerContent(station, index, sortedStations.length);

    const marker = new AMap.Marker({
      position: [station.longitude, station.latitude],
      content: markerContent,
      anchor: 'bottom-center',
      offset: new AMap.Pixel(0, 0),
      draggable: editingStationMode.value, // 只在编辑模式下可拖拽
      extData: { stationId: station.stationId, stationOrder: station.stationOrder }
    });

    // 拖拽开始事件
    marker.on('dragstart', () => {
      if (!editingStationMode.value) return;
      proxy?.$modal.msgInfo(`正在移动 ${station.stationName}`);
    });

    // 拖拽过程中更新文本标记位置
    marker.on('dragging', (e) => {
      if (marker.textMarker) {
        marker.textMarker.setPosition(e.lnglat);
      }
    });

    // 拖拽结束事件 - 暂存变更，不立即更新数据
    marker.on('dragend', (e) => {
      if (!editingStationMode.value) return;

      const { lng, lat } = e.lnglat;
      const newLng = Number(lng.toFixed(6));
      const newLat = Number(lat.toFixed(6));

      // 更新文本标记位置
      if (marker.textMarker) {
        marker.textMarker.setPosition([newLng, newLat]);
      }

      // 将变更存储到待确认列表中
      pendingChanges.value.set(station.stationId, {
        station: station,
        originalPosition: { longitude: station.longitude, latitude: station.latitude },
        newPosition: { longitude: newLng, latitude: newLat }
      });

      proxy?.$modal.msgInfo(`${station.stationName} 位置变更已暂存，点击完成编辑自动保存`);
    });

    // 添加站点名称文本标记
    const textMarker = new AMap.Text({
      text: station.stationName,
      position: [station.longitude, station.latitude],
      offset: new AMap.Pixel(-40, -80),
      style: {
        'background-color': 'rgba(0,0,0,0.85)',
        'border': '1px solid rgba(255,255,255,0.2)',
        'border-radius': '6px',
        'color': '#60a5fa',
        'font-size': '11px',
        'font-weight': 'bold',
        'padding': '6px 10px',
        'text-align': 'center',
        'white-space': 'nowrap',
        'box-shadow': '0 2px 8px rgba(0,0,0,0.3)',
        'min-width': '80px'
      }
    });

    // 添加文本标记到地图
    map.add(textMarker);

    // 存储文本标记引用
    marker.textMarker = textMarker;

    // 左键点击事件 - 弹出编辑对话框
    marker.on('click', () => {
      selectedStation.value = station;
      stationDialogVisible.value = true;
    });

    markersToAdd.push(marker);
  });

  // 批量添加到地图
  if (markersToAdd.length > 0) {
    map.add(markersToAdd);
    window.stationMapData.markers = markersToAdd;
  }

  console.log('站点标记已批量添加到地图，数量:', markersToAdd.length);
}

// 创建站点标记内容
function createStationMarkerContent(station, index, totalLength) {
  const stationNumber = index + 1;

  let bgColor = '#60a5fa';
  let shadowColor = 'rgba(96, 165, 250, 0.6)';

  if (station.status === 'maintenance') {
    bgColor = '#f59e0b';
    shadowColor = 'rgba(245, 158, 11, 0.6)';
  } else if (station.status === 'disabled') {
    bgColor = '#ef4444';
    shadowColor = 'rgba(239, 68, 68, 0.6)';
  }

  // 根据编辑状态动态设置鼠标样式
  const cursorStyle = editingStationMode.value ? 'move' : 'pointer';

  return `
    <div class="custom-station-marker" style="
      background: linear-gradient(135deg, ${bgColor} 0%, ${adjustBrightness(bgColor, -20)} 100%);
      border: 3px solid rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      width: 44px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 6px 20px ${shadowColor}, 0 0 30px ${shadowColor};
      cursor: ${cursorStyle};
      font-size: 18px;
      font-weight: bold;
      color: #ffffff;
      backdrop-filter: blur(5px);
      transition: all 0.3s ease;
      position: relative;
    ">
      ${stationNumber}
    </div>
  `;
}

// 调整颜色亮度的辅助函数
function adjustBrightness(color, amount) {
  const usePound = color[0] === '#';
  const col = usePound ? color.slice(1) : color;
  const num = parseInt(col, 16);
  const r = Math.max(0, Math.min(255, (num >> 16) + amount));
  const g = Math.max(0, Math.min(255, (num >> 8 & 0x00FF) + amount));
  const b = Math.max(0, Math.min(255, (num & 0x0000FF) + amount));
  return (usePound ? '#' : '') + (g | (b << 8) | (r << 16)).toString(16);
}

// 刷新站点地图
function refreshStationMap() {
  if (currentView.value === 'map' && selectedRoute.value) {
    updateMapContent();
    proxy?.$modal.msgSuccess('地图已刷新');
  }
}

// 切换地图大小
function toggleMapSize() {
  mapMaximized.value = !mapMaximized.value;

  // 提供用户反馈
  proxy?.$modal.msgSuccess(mapMaximized.value ? '地图已展开' : '地图已收起');

  // 延迟调整地图大小以等待CSS动画完成
  nextTick(() => {
    setTimeout(() => {
      if (window.stationMapData.mapInstance) {
        // 触发地图重新计算大小
        window.stationMapData.mapInstance.getSize();
        window.stationMapData.mapInstance.setFitView();

        // 重新居中显示
        if (selectedRoute.value && selectedRoute.value.stations.length > 0) {
          centerMapToRoute();
        }

        // 刷新地图标记以更新悬停信息状态
        addStationMarkers(window.stationMapData.mapInstance);
      }
    }, 350); // 稍微增加延迟以确保动画完成
  });
}

function centerMapToRoute() {
  if (!window.stationMapData.mapInstance || !selectedRoute.value || !selectedRoute.value.stations.length) {
    proxy?.$modal.msgInfo('没有可显示的站点');
    return;
  }

  try {
    const stations = selectedRoute.value.stations;

    if (stations.length === 1) {
      // 单个站点，直接定位，缩短动画时间
      window.stationMapData.mapInstance.panTo([stations[0].longitude, stations[0].latitude], 100);
      window.stationMapData.mapInstance.setZoom(16, true, 100);
    } else {
      // 多个站点，自动调整视野包含所有站点，缩短动画时间
      if (window.stationMapData.markers.length > 0) {
        window.stationMapData.mapInstance.setFitView(window.stationMapData.markers, false, [50, 50, 50, 50], 100);
      } else {
        // 备用方案：直接计算边界
        const lngs = stations.map(s => s.longitude);
        const lats = stations.map(s => s.latitude);
        const bounds = new AMap.Bounds(
          [Math.min(...lngs), Math.min(...lats)],
          [Math.max(...lngs), Math.max(...lats)]
        );
        window.stationMapData.mapInstance.setBounds(bounds, false, [50, 50, 50, 50], 100);
      }
    }

    proxy?.$modal.msgSuccess('地图已居中到当前路线');
  } catch (error) {
    console.error('地图居中失败:', error);
    proxy?.$modal.msgError('地图居中失败');
  }
}

// 初始化数据
onMounted(() => {
  routes.value = mockRoutes;
});

onUnmounted(() => {
  // 清理待确认的变更
  pendingChanges.value.clear();

  // 清理站点地图
  if (window.stationMapData.mapInstance) {
    try {
      // 清理站点标记
      if (window.stationMapData.markers.length > 0) {
        window.stationMapData.mapInstance.remove(window.stationMapData.markers);
        window.stationMapData.markers = [];
      }

      // 销毁地图实例
      window.stationMapData.mapInstance.destroy();
    } catch (error) {
      console.warn('清理站点地图时出错:', error);
    }
    window.stationMapData.mapInstance = null;
  }
});
</script>

<style scoped>
.station-management-container {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  overflow: hidden;
}

/* 左侧导航面板 */
.left-panel {
  height: 100vh;
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border-right: 1px solid rgba(147, 197, 253, 0.2);
}

.route-navigation {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.nav-header {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-header span {
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
  flex: 1;
}

.route-search {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
}

.route-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  background: transparent;
}

.route-item {
  padding: 15px;
  margin-bottom: 10px;
  background: linear-gradient(145deg,
    rgba(30, 41, 59, 0.9) 0%,
    rgba(51, 65, 85, 0.8) 100%);
  border: 2px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.route-item:hover {
  transform: translateY(-2px);
  border-color: rgba(96, 165, 250, 0.4);
  box-shadow: 0 8px 32px rgba(96, 165, 250, 0.2),
    0 0 20px rgba(96, 165, 250, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.route-item.active {
  border-color: #22c55e;
  background: linear-gradient(145deg,
    rgba(34, 197, 94, 0.15) 0%,
    rgba(30, 41, 59, 0.9) 50%,
    rgba(51, 65, 85, 0.8) 100%);
  box-shadow: 0 0 30px rgba(34, 197, 94, 0.3),
    0 8px 25px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.route-info {
  flex: 1;
}

.route-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 8px;
  color: #f8fafc;
  text-shadow: 0 0 10px rgba(96, 165, 250, 0.3);
}

.route-stats {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #94a3b8;
  margin-bottom: 5px;
}

.route-desc {
  font-size: 12px;
  color: #64748b;
}

.route-actions {
  display: flex;
  gap: 5px;
}

/* 右侧编辑面板 */
.right-panel {
  height: 100vh;
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(10px);
}

.station-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-header {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.route-title h2 {
  margin: 0 0 10px 0;
  color: #f8fafc;
  font-weight: 700;
  text-shadow: 0 0 10px rgba(96, 165, 250, 0.3);
}

.route-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
  color: #94a3b8;
}

.editor-actions {
  display: flex;
  gap: 10px;
}

/* 站点时间线 */
.station-timeline-container {
  flex: 1;
  padding: 24px;
  overflow: auto;
  background: rgba(10, 22, 48, 0.4);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 16px 20px;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.direction-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #f8fafc;
}

.legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #cbd5e1;
}

.station-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
}

.station-dot.normal {
  background: #22c55e;
  box-shadow: 0 0 15px rgba(34, 197, 94, 0.4);
}

.station-dot.maintenance {
  background: #f59e0b;
  box-shadow: 0 0 15px rgba(245, 158, 11, 0.4);
}

.station-dot.disabled {
  background: #ef4444;
  box-shadow: 0 0 15px rgba(239, 68, 68, 0.4);
}

.station-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
  padding: 30px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%);
  border: 2px dashed rgba(96, 165, 250, 0.3);
  border-radius: 16px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  box-shadow: inset 0 2px 20px rgba(0, 0, 0, 0.3);
  min-height: 200px;
  justify-items: center;
  align-items: start;
}

.station-node {
  position: relative;
  width: 140px;
  cursor: pointer;
  text-align: center;
  transition: all 0.3s ease;
  margin-bottom: 10px;
}

.station-node.active {
  z-index: 10;
}

.station-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(145deg, rgba(30, 41, 59, 0.9) 0%, rgba(51, 65, 85, 0.8) 100%);
  border: 3px solid #60a5fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 10px;
  font-size: 20px;
  color: #60a5fa;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(96, 165, 250, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.station-number {
  font-weight: bold;
  font-size: 18px;
  color: #e5e7eb;
}

.station-node:hover .station-icon {
  transform: scale(1.15);
  box-shadow: 0 8px 32px rgba(96, 165, 250, 0.4),
    0 0 30px rgba(96, 165, 250, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.station-node.active .station-icon {
  border-color: #22c55e;
  color: #22c55e;
  box-shadow: 0 8px 32px rgba(34, 197, 94, 0.4),
    0 0 30px rgba(34, 197, 94, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.station-node.status-maintenance .station-icon {
  border-color: #f59e0b;
  color: #f59e0b;
  box-shadow: 0 8px 32px rgba(245, 158, 11, 0.4),
    0 0 30px rgba(245, 158, 11, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.station-node.status-disabled .station-icon {
  border-color: #ef4444;
  color: #ef4444;
  box-shadow: 0 8px 32px rgba(239, 68, 68, 0.4),
    0 0 30px rgba(239, 68, 68, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.station-info {
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.9) 0%,
    rgba(30, 41, 59, 0.8) 100%);
  border-radius: 8px;
  padding: 8px;
  font-size: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.station-name {
  font-weight: 600;
  margin-bottom: 4px;
  color: #f8fafc;
  text-shadow: 0 0 8px rgba(96, 165, 250, 0.3);
}

.station-order {
  color: #94a3b8;
  margin-bottom: 2px;
}

.station-distance {
  color: #64748b;
  font-size: 10px;
}

.status-indicator {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(145deg, rgba(34, 197, 94, 0.9) 0%, rgba(34, 197, 94, 0.7) 100%);
  border: 2px solid #22c55e;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #22c55e;
  backdrop-filter: blur(5px);
  box-shadow: 0 0 15px rgba(34, 197, 94, 0.4);
}

.status-indicator.maintenance {
  background: linear-gradient(145deg, rgba(245, 158, 11, 0.9) 0%, rgba(245, 158, 11, 0.7) 100%);
  border-color: #f59e0b;
  color: #f59e0b;
  box-shadow: 0 0 15px rgba(245, 158, 11, 0.4);
}

.status-indicator.disabled {
  background: linear-gradient(145deg, rgba(239, 68, 68, 0.9) 0%, rgba(239, 68, 68, 0.7) 100%);
  border-color: #ef4444;
  color: #ef4444;
  box-shadow: 0 0 15px rgba(239, 68, 68, 0.4);
}

.add-station-placeholder {
  width: 140px;
  height: 120px;
  border: 2px dashed #60a5fa;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #60a5fa;
  font-size: 12px;
  transition: all 0.3s ease;
  background: rgba(96, 165, 250, 0.05);
  backdrop-filter: blur(5px);
  margin-top: 10px;
}

.add-station-placeholder:hover {
  background: rgba(96, 165, 250, 0.15);
  border-color: #22c55e;
  color: #22c55e;
  box-shadow: 0 0 20px rgba(96, 165, 250, 0.3);
}

/* 地图视图容器 */
.map-view-container {
  flex: 1;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(147, 197, 253, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.map-header {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(30, 41, 59, 0.9);
  border-radius: 12px 12px 0 0;
}

.map-header h3 {
  margin: 0;
  color: #f8fafc;
  text-shadow: 0 0 10px rgba(96, 165, 250, 0.3);
}

.map-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.map-tip {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  background: rgba(96, 165, 250, 0.1);
  border: 1px solid rgba(96, 165, 250, 0.3);
  border-radius: 6px;
  color: #60a5fa;
  font-size: 12px;
  margin-left: auto;
}

.map-tip .el-icon {
  font-size: 14px;
}

/* 新增站点按钮激活状态 */
.map-controls .el-button.active-mode {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%) !important;
  border-color: #22c55e !important;
  color: white !important;
  box-shadow: 0 0 15px rgba(34, 197, 94, 0.4) !important;
  animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
  0% {
    box-shadow: 0 0 15px rgba(34, 197, 94, 0.4);
  }
  50% {
    box-shadow: 0 0 25px rgba(34, 197, 94, 0.6);
  }
  100% {
    box-shadow: 0 0 15px rgba(34, 197, 94, 0.4);
  }
}

.map-content {
  height: calc(100% - 70px);
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.8) 100%);
}

.station-map-view {
  width: 100%;
  height: 100%;
  border-radius: 0 0 12px 12px;
}

/* 地图加载层 */
.map-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 23, 42, 0.9);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 0 12px 12px;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  color: #f8fafc;
}

.loading-content .el-icon {
  color: #60a5fa;
  margin-bottom: 12px;
}

.loading-content p {
  margin: 0;
  font-size: 14px;
  color: #cbd5e1;
  text-shadow: 0 0 8px rgba(96, 165, 250, 0.3);
}

/* 地图最大化遮罩层 */
.map-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 9;
  backdrop-filter: blur(2px);
  cursor: pointer;
}

/* 弹窗样式 */
.station-detail-form {
  padding: 20px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%);
  color: #e5e7eb;
}

.dialog-footer {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* 右键菜单 */
.context-menu {
  position: fixed;
  background: linear-gradient(145deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.9) 100%);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(96, 165, 250, 0.1);
  padding: 8px 0;
  min-width: 120px;
  z-index: 9999;
  backdrop-filter: blur(15px);
}

.menu-item {
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  color: #e5e7eb;
}

.menu-item:hover {
  background: rgba(96, 165, 250, 0.15);
  color: #60a5fa;
}

.menu-item.danger {
  color: #ef4444;
}

.menu-item.danger:hover {
  background: rgba(239, 68, 68, 0.15);
  color: #ef4444;
}

.menu-divider {
  height: 1px;
  background: rgba(147, 197, 253, 0.2);
  margin: 8px 0;
}

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(15, 23, 42, 0.6);
  color: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .station-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .station-management-container {
    padding: 10px;
  }

  .station-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    padding: 20px;
  }

  .station-node {
    width: 120px;
  }

  .station-icon {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .add-station-placeholder {
    width: 120px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .station-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}


/* 移除弹窗背景模糊效果 */
:global(.el-dialog__body) {
  backdrop-filter: none !important;
}

/* 覆盖AMap InfoWindow默认样式 */
:global(.amap-info) {
  border: none !important;
  background: transparent !important;
}

:global(.amap-info-content) {
  background: transparent !important;
  padding: 0 !important;
}
</style>
