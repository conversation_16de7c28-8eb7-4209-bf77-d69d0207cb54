<template>
  <div class="app-container">
    <div class="plan-create-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <h1>图形化排班计划制定</h1>
          <p>通过拖拽方式快速制定排班计划</p>
        </div>
        <div class="header-actions">
          <el-button @click="resetAll">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="previewPlan">
            <el-icon><View /></el-icon>
            预览
          </el-button>
          <el-button @click="$router.go(-1)">返回</el-button>
        </div>
      </div>

      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" align-center class="steps-container">
        <el-step title="选择时间点" description="设置发车时间" />
        <el-step title="分配车辆" description="拖拽车辆到时间线" />
        <el-step title="确认保存" description="检查并保存计划" />
      </el-steps>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧：时间选择和车辆列表 -->
        <div class="left-panel">
          <!-- 时间选择器 -->
          <TimeSelector ref="timeSelectorRef" v-model="selectedTimes" @times-changed="handleTimesChanged" @time-selected="handleTimeSelected" />

          <!-- 车辆列表 -->
          <VehicleList
            :vehicles="vehicles"
            :loading="vehiclesLoading"
            @vehicle-drag-start="handleVehicleDragStart"
            @vehicle-drag-end="handleVehicleDragEnd"
            @refresh="loadVehicles"
          />
        </div>

        <!-- 右侧：时间线 -->
        <div class="right-panel">
          <ScheduleTimeline
            ref="timelineRef"
            :time-points="selectedTimes"
            :vehicles="vehicles"
            @vehicle-assigned="handleVehicleAssigned"
            @vehicle-unassigned="handleVehicleUnassigned"
            @schedule-changed="handleScheduleChanged"
            @save="handleSaveSchedule"
          />
        </div>
      </div>

      <!-- 计划预览对话框 -->
      <el-dialog v-model="showPreviewDialog" title="排班计划预览" width="80%" :before-close="handleClosePreview">
        <div class="preview-content">
          <div class="preview-summary">
            <h3>计划概要</h3>
            <div class="summary-stats">
              <div class="stat-card">
                <div class="stat-number">{{ selectedTimes.length }}</div>
                <div class="stat-label">发车时间点</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ assignedVehicles.length }}</div>
                <div class="stat-label">分配车辆</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ scheduleItems.length }}</div>
                <div class="stat-label">总班次</div>
              </div>
            </div>
          </div>

          <div class="preview-table">
            <h3>详细安排</h3>
            <el-table :data="scheduleItems" stripe>
              <el-table-column prop="start" label="发车时间" width="120">
                <template #default="{ row }">
                  {{ formatTime(row.start) }}
                </template>
              </el-table-column>
              <el-table-column prop="plateNumber" label="车牌号" width="120" />
              <el-table-column prop="vehicleId" label="车辆编号" width="100" />
              <el-table-column label="车辆状态" width="100">
                <template #default="{ row }">
                  <el-tag type="success">正常</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="{ row }">
                  <el-button type="danger" size="small" @click="removeScheduleItem(row)"> 移除 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <template #footer>
          <el-button @click="showPreviewDialog = false">关闭</el-button>
          <el-button type="primary" @click="confirmSave">确认保存</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts" name="PlanCreate">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Refresh, View } from '@element-plus/icons-vue';
import TimeSelector from '@/components/schedule/TimeSelector.vue';
import VehicleList from '@/components/schedule/VehicleList.vue';
import ScheduleTimeline from '@/components/schedule/ScheduleTimeline.vue';

// 接口定义
interface Vehicle {
  vehicleId: number | string;
  plateNumber: string;
  vehicleNumber: string;
  vehicleType: string;
  brandModel?: string;
  seatCount?: number;
  status: string;
  isAssigned?: boolean;
  assignedTime?: string;
  totalMileage?: number;
  remark?: string;
}

interface ScheduleItem {
  id: string | number;
  content: string;
  start: Date;
  className?: string;
  vehicleId: number | string;
  plateNumber: string;
}

// 响应式数据
const timeSelectorRef = ref();
const timelineRef = ref();
const currentStep = ref(0);
const selectedTimes = ref<string[]>([]);
const vehicles = ref<Vehicle[]>([]);
const vehiclesLoading = ref(false);
const assignedVehicles = ref<Vehicle[]>([]);
const scheduleItems = ref<ScheduleItem[]>([]);
const showPreviewDialog = ref(false);

// 计算属性
const canProceedToStep2 = computed(() => selectedTimes.value.length > 0);
const canProceedToStep3 = computed(() => scheduleItems.value.length > 0);

// 方法
const loadVehicles = async () => {
  vehiclesLoading.value = true;
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 模拟车辆数据
    vehicles.value = [
      {
        vehicleId: 1,
        plateNumber: '京A12345',
        vehicleNumber: 'V001',
        vehicleType: 'bus',
        brandModel: '宇通ZK6105HNG',
        seatCount: 35,
        status: '1',
        totalMileage: 125000,
        remark: '1路公交车'
      },
      {
        vehicleId: 2,
        plateNumber: '京A12346',
        vehicleNumber: 'V002',
        vehicleType: 'electric_bus',
        brandModel: '比亚迪K9',
        seatCount: 30,
        status: '1',
        totalMileage: 85000,
        remark: '2路电动公交车'
      },
      {
        vehicleId: 3,
        plateNumber: '京A12347',
        vehicleNumber: 'V003',
        vehicleType: 'bus',
        brandModel: '金龙XMQ6127G',
        seatCount: 40,
        status: '1',
        totalMileage: 95000,
        remark: '3路公交车'
      },
      {
        vehicleId: 4,
        plateNumber: '京A12348',
        vehicleNumber: 'V004',
        vehicleType: 'hybrid_bus',
        brandModel: '福田BJ6105C7BHB',
        seatCount: 32,
        status: '2', // 维修中
        totalMileage: 78000,
        remark: '4路混动公交车'
      },
      {
        vehicleId: 5,
        plateNumber: '京A12349',
        vehicleNumber: 'V005',
        vehicleType: 'electric_bus',
        brandModel: '比亚迪K8',
        seatCount: 28,
        status: '1',
        totalMileage: 45000,
        remark: '5路电动公交车'
      }
    ];
  } catch (error) {
    ElMessage.error('加载车辆数据失败');
    console.error('Load vehicles error:', error);
  } finally {
    vehiclesLoading.value = false;
  }
};

const handleTimesChanged = (times: string[]) => {
  // 不需要再次设置selectedTimes，因为v-model已经处理了双向绑定
  // selectedTimes.value = times
  if (times.length > 0 && currentStep.value === 0) {
    currentStep.value = 1;
  }
};

const handleTimeSelected = (index: number, time: string) => {
  console.log('Selected time:', time);
};

const handleVehicleDragStart = (vehicle: Vehicle, event: DragEvent) => {
  console.log('Vehicle drag start:', vehicle.plateNumber);
};

const handleVehicleDragEnd = (vehicle: Vehicle, event: DragEvent) => {
  console.log('Vehicle drag end:', vehicle.plateNumber);
};

const handleVehicleAssigned = (vehicle: Vehicle, time: string) => {
  // 更新车辆状态
  const vehicleIndex = vehicles.value.findIndex((v) => v.vehicleId === vehicle.vehicleId);
  if (vehicleIndex !== -1) {
    vehicles.value[vehicleIndex].isAssigned = true;
    vehicles.value[vehicleIndex].assignedTime = time;
  }

  // 添加到已分配列表
  if (!assignedVehicles.value.find((v) => v.vehicleId === vehicle.vehicleId)) {
    assignedVehicles.value.push({ ...vehicle, isAssigned: true, assignedTime: time });
  }

  if (currentStep.value === 1) {
    currentStep.value = 2;
  }
};

const handleVehicleUnassigned = (vehicle: Vehicle) => {
  // 更新车辆状态
  const vehicleIndex = vehicles.value.findIndex((v) => v.vehicleId === vehicle.vehicleId);
  if (vehicleIndex !== -1) {
    vehicles.value[vehicleIndex].isAssigned = false;
    vehicles.value[vehicleIndex].assignedTime = undefined;
  }

  // 从已分配列表移除
  const assignedIndex = assignedVehicles.value.findIndex((v) => v.vehicleId === vehicle.vehicleId);
  if (assignedIndex !== -1) {
    assignedVehicles.value.splice(assignedIndex, 1);
  }
};

const handleScheduleChanged = (items: ScheduleItem[]) => {
  scheduleItems.value = items;
};

const handleSaveSchedule = (items: ScheduleItem[]) => {
  scheduleItems.value = items;
  previewPlan();
};

const previewPlan = () => {
  if (scheduleItems.value.length === 0) {
    ElMessage.warning('请先分配车辆到时间线');
    return;
  }
  showPreviewDialog.value = true;
};

const handleClosePreview = () => {
  showPreviewDialog.value = false;
};

const removeScheduleItem = (item: ScheduleItem) => {
  const vehicle = vehicles.value.find((v) => v.vehicleId === item.vehicleId);
  if (vehicle) {
    handleVehicleUnassigned(vehicle);
  }

  // 从时间线移除
  if (timelineRef.value) {
    const timelineItems = timelineRef.value.getScheduleItems();
    const filteredItems = timelineItems.filter((i: ScheduleItem) => i.id !== item.id);
    scheduleItems.value = filteredItems;
  }
};

const confirmSave = async () => {
  try {
    // 模拟保存API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));

    ElMessage.success('排班计划保存成功');
    showPreviewDialog.value = false;

    // 可以跳转到计划列表页面
    // this.$router.push('/schedule/plan')
  } catch (error) {
    ElMessage.error('保存失败，请重试');
    console.error('Save schedule error:', error);
  }
};

const resetAll = async () => {
  try {
    await ElMessageBox.confirm('确定要重置所有设置吗？', '确认重置', {
      type: 'warning'
    });

    // 重置所有数据
    selectedTimes.value = [];
    assignedVehicles.value = [];
    scheduleItems.value = [];
    currentStep.value = 0;

    // 重置组件状态
    if (timeSelectorRef.value) {
      timeSelectorRef.value.clearTimes();
    }
    if (timelineRef.value) {
      timelineRef.value.clearSchedule();
    }

    // 重新加载车辆数据
    await loadVehicles();

    ElMessage.success('已重置所有设置');
  } catch {
    // 用户取消
  }
};

const formatTime = (date: Date): string => {
  return date.toTimeString().slice(0, 5);
};

// 生命周期
onMounted(() => {
  loadVehicles();
});
</script>

<style scoped>
.app-container {
  padding: 0;
}

.plan-create-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px;
}

.page-header {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.steps-container {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.main-content {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 20px;
  min-height: 800px;
}

.left-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.right-panel {
  display: flex;
  flex-direction: column;
}

.preview-content {
  max-height: 70vh;
  overflow-y: auto;
}

.preview-summary {
  margin-bottom: 24px;
}

.preview-summary h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  border: 1px solid #e4e7ed;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.preview-table {
  margin-top: 24px;
}

.preview-table h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .main-content {
    grid-template-columns: 350px 1fr;
  }
}

@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .left-panel {
    order: 2;
  }

  .right-panel {
    order: 1;
  }
}

@media (max-width: 768px) {
  .plan-create-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    text-align: center;
  }

  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .steps-container {
    padding: 16px;
  }

  .summary-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-card {
    padding: 16px;
  }

  .stat-number {
    font-size: 24px;
  }
}

/* 动画效果 */
.plan-create-container * {
  transition: all 0.3s ease;
}

.page-header:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.steps-container:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* 拖拽相关样式 */
.main-content.dragging .left-panel {
  opacity: 0.8;
}

.main-content.dragging .right-panel {
  background: rgba(64, 158, 255, 0.05);
  border-radius: 12px;
}
</style>
