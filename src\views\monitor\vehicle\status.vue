<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>运行状态监控</span>
          <el-button @click="$router.go(-1)">返回</el-button>
        </div>
      </template>

      <div class="status-content">
        <el-alert
          title="运行状态监控"
          description="监控车辆速度、转速、水温等运行状态信息。"
          type="info"
          :closable="false"
          style="margin-bottom: 20px"
        />

        <el-empty description="运行状态监控功能开发中" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="VehicleStatus"></script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-content {
  padding: 20px;
}
</style>
