<template>
  <div class="demo-data-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>演示数据管理</span>
          <el-button class="button" type="primary" @click="generateDemoData">生成演示数据</el-button>
        </div>
      </template>
      
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="站点数据" name="stations">
          <el-table :data="demoStations" style="width: 100%">
            <el-table-column prop="indexCode" label="编号" width="80" />
            <el-table-column prop="stationName" label="站点名称" width="150" />
            <el-table-column prop="infoType" label="类型" width="100">
              <template #default="scope">
                {{ getStationTypeText(scope.row.infoType) }}
              </template>
            </el-table-column>
            <el-table-column prop="reportRadiusMeter" label="报站半径(米)" width="120" />
            <el-table-column prop="staySecond" label="停站时间(秒)" width="120" />
            <el-table-column prop="stationShowName1" label="中文名称" width="150" />
            <el-table-column prop="stationShowName2" label="英文名称" width="150" />
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="线路数据" name="lines">
          <el-table :data="demoLines" style="width: 100%">
            <el-table-column prop="lineIndexCode" label="线路号" width="80" />
            <el-table-column prop="lineName" label="线路名称" width="150" />
            <el-table-column prop="firstStationName" label="起点站" width="150" />
            <el-table-column prop="lastStationName" label="终点站" width="150" />
            <el-table-column prop="stationCount" label="站点数" width="100" />
            <el-table-column prop="languageCount" label="语言数" width="100" />
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="系统配置" name="config">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="系统版本">{{ demoConfig.reportSystemVersion }}</el-descriptions-item>
            <el-descriptions-item label="编码格式">{{ demoConfig.codeFormat === 1 ? 'UTF-8' : 'GB2312' }}</el-descriptions-item>
            <el-descriptions-item label="语言数量">{{ demoConfig.languageCount }}</el-descriptions-item>
            <el-descriptions-item label="报站方式">{{ demoConfig.reportType === 1 ? '手动' : '自动' }}</el-descriptions-item>
            <el-descriptions-item label="车内音量">{{ demoConfig.reportVolumeIn }}%</el-descriptions-item>
            <el-descriptions-item label="车外音量">{{ demoConfig.reportVolumeOut }}%</el-descriptions-item>
            <el-descriptions-item label="报站半径">{{ demoConfig.reportRadiusMeter }}米</el-descriptions-item>
            <el-descriptions-item label="当前版本">{{ demoConfig.currentVersion }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
      </el-tabs>
      
      <div class="demo-actions">
        <el-alert
          title="演示说明"
          description="这些是模拟的演示数据，用于测试报站文件生成功能。实际使用时需要通过管理界面配置真实的站点和线路信息。"
          type="info"
          :closable="false"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { AnnouncementStation, AnnouncementLineConfig, AnnouncementSystemConfig } from '@/api/announcement/types';

const activeTab = ref('stations');

// 演示数据
const demoStations = ref<AnnouncementStation[]>([
  {
    indexCode: 1,
    infoType: 1,
    stationName: '火车站',
    stationShowName1: '火车站',
    stationShowName2: 'Train Station',
    stationShowName3: '火車站',
    stationShowName4: 'Bahnhof',
    reportRadiusMeter: 100,
    staySecond: 30,
    latitudeDegree: 22,
    latitudeCent: 32,
    longitudeDegree: 114,
    longitudeCent: 6,
    latitudeSec: 12000,
    longitudeSec: 35000,
    gpsAngle: 90,
    directionLatitude: 'N',
    directionLongitude: 'E'
  },
  {
    indexCode: 2,
    infoType: 1,
    stationName: '市政府',
    stationShowName1: '市政府',
    stationShowName2: 'City Hall',
    stationShowName3: '市政府',
    stationShowName4: 'Rathaus',
    reportRadiusMeter: 100,
    staySecond: 30,
    latitudeDegree: 22,
    latitudeCent: 33,
    longitudeDegree: 114,
    longitudeCent: 7,
    latitudeSec: 15000,
    longitudeSec: 42000,
    gpsAngle: 90,
    directionLatitude: 'N',
    directionLongitude: 'E'
  },
  {
    indexCode: 3,
    infoType: 1,
    stationName: '购物中心',
    stationShowName1: '购物中心',
    stationShowName2: 'Shopping Mall',
    stationShowName3: '購物中心',
    stationShowName4: 'Einkaufszentrum',
    reportRadiusMeter: 120,
    staySecond: 45,
    latitudeDegree: 22,
    latitudeCent: 34,
    longitudeDegree: 114,
    longitudeCent: 8,
    latitudeSec: 18000,
    longitudeSec: 48000,
    gpsAngle: 90,
    directionLatitude: 'N',
    directionLongitude: 'E'
  },
  {
    indexCode: 4,
    infoType: 3,
    stationName: '总站',
    stationShowName1: '总站',
    stationShowName2: 'Main Terminal',
    stationShowName3: '總站',
    stationShowName4: 'Hauptbahnhof',
    reportRadiusMeter: 150,
    staySecond: 60,
    latitudeDegree: 22,
    latitudeCent: 35,
    longitudeDegree: 114,
    longitudeCent: 9,
    latitudeSec: 21000,
    longitudeSec: 54000,
    gpsAngle: 90,
    directionLatitude: 'N',
    directionLongitude: 'E'
  },
  {
    indexCode: 5,
    infoType: 9,
    stationName: '拐弯点1',
    stationShowName1: '拐弯点1',
    stationShowName2: 'Turn Point 1',
    stationShowName3: '拐彎點1',
    stationShowName4: 'Abbiegepunkt 1',
    reportRadiusMeter: 50,
    staySecond: 0,
    latitudeDegree: 22,
    latitudeCent: 33,
    longitudeDegree: 114,
    longitudeCent: 7,
    latitudeSec: 30000,
    longitudeSec: 25000,
    gpsAngle: 45,
    directionLatitude: 'N',
    directionLongitude: 'E'
  }
]);

const demoLines = ref<AnnouncementLineConfig[]>([
  {
    lineIndexCode: 1,
    lineName: '1路公交',
    lineFileName: 'line001',
    firstStationName: '火车站',
    lastStationName: '总站',
    stationCount: 15,
    languageCount: 4,
    lineRun: '1',
    directPolicy: 1,
    middleVoiceInA1: 'welcome_chinese.mp3',
    middleVoiceInA2: 'welcome_english.mp3',
    middleVoiceInA3: 'welcome_traditional.mp3',
    middleVoiceInA4: 'welcome_german.mp3',
    middleVoiceInC1: 'next_stop_chinese.mp3',
    middleVoiceInC2: 'next_stop_english.mp3',
    middleVoiceInC3: 'next_stop_traditional.mp3',
    middleVoiceInC4: 'next_stop_german.mp3'
  },
  {
    lineIndexCode: 2,
    lineName: '2路公交',
    lineFileName: 'line002',
    firstStationName: '市政府',
    lastStationName: '购物中心',
    stationCount: 12,
    languageCount: 4,
    lineRun: '1',
    directPolicy: 1,
    middleVoiceInA1: 'welcome_chinese.mp3',
    middleVoiceInA2: 'welcome_english.mp3',
    middleVoiceInA3: 'welcome_traditional.mp3',
    middleVoiceInA4: 'welcome_german.mp3'
  },
  {
    lineIndexCode: 99,
    lineName: '夜班专线',
    lineFileName: 'line_night',
    firstStationName: '火车站',
    lastStationName: '机场',
    stationCount: 8,
    languageCount: 2,
    lineRun: '2',
    directPolicy: 2,
    middleVoiceInA1: 'night_welcome_chinese.mp3',
    middleVoiceInA2: 'night_welcome_english.mp3'
  }
]);

const demoConfig = ref<AnnouncementSystemConfig>({
  reportSystemVersion: 1,
  codeFormat: 1,
  languageCount: 4,
  reportType: 2,
  reportVolumeIn: 80,
  reportVolumeOut: 85,
  reportRadiusMeter: 100,
  codeType: 1,
  currentVersion: '24121415301234',
  cityLatitudeDegree: 22,
  cityLatitudeCent: 33,
  cityLongitudeDegree: 114,
  cityLongitudeCent: 7,
  cityLatitudeSec: 0,
  cityLongitudeSec: 0,
  cityGpsAngle: 0,
  cityDirectionLatitude: 'N',
  cityDirectionLongitude: 'E',
  status: '0'
});

// 获取站点类型文本
const getStationTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '常规站点',
    2: '管理站点',
    3: '主站',
    4: '副站',
    5: '停车场',
    6: '加油站',
    7: '维修点',
    9: '拐弯点',
    10: '安全黑点',
    11: '字符串发声站点',
    13: '站点间信息',
    14: '删除站点',
    15: '标示点',
    18: '路口',
    19: '停用站',
    20: '途经点'
  };
  return typeMap[type] || '未知类型';
};

// 生成演示数据
const generateDemoData = () => {
  // 重新生成当前版本号
  const now = new Date();
  const timeStr = now.getFullYear().toString().slice(-2) +
                  String(now.getMonth() + 1).padStart(2, '0') +
                  String(now.getDate()).padStart(2, '0') +
                  String(now.getHours()).padStart(2, '0') +
                  String(now.getMinutes()).padStart(2, '0') +
                  String(now.getSeconds()).padStart(2, '0');
  const randomStr = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  
  demoConfig.value.currentVersion = timeStr + randomStr;
  
  ElMessage.success('演示数据已更新');
};
</script>

<style scoped lang="scss">
.demo-data-page {
  padding: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .demo-actions {
    margin-top: 20px;
  }
}
</style>