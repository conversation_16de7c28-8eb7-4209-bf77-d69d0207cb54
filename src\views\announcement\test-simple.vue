<template>
  <div class="announcement-test-simple">
    <el-card>
      <template #header>
        <h3>报站系统测试</h3>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card header="控制面板">
            <div style="margin-bottom: 20px;">
              <h4>版本选择</h4>
              <el-select v-model="selectedVersion" style="width: 100%;">
                <el-option 
                  v-for="(desc, version) in versionOptions"
                  :key="version"
                  :label="`版本 ${version} - ${desc}`"
                  :value="Number(version)"
                />
              </el-select>
            </div>
            
            <div style="margin-bottom: 20px;">
              <h4>文件操作</h4>
              <el-button 
                type="primary" 
                @click="generateFiles"
                :loading="generating"
                icon="Download"
                style="width: 100%; margin-bottom: 10px;"
              >
                生成配置文件包
              </el-button>
              
              <el-button 
                @click="previewFiles"
                :loading="previewing"
                icon="View"
                style="width: 100%; margin-bottom: 10px;"
              >
                预览文件结构
              </el-button>
              
              <el-button 
                @click="validateConfig"
                :loading="validating"
                icon="CircleCheck"
                style="width: 100%; margin-bottom: 10px;"
              >
                验证配置
              </el-button>
            </div>
            
            <div>
              <h4>系统操作</h4>
              <el-button 
                @click="refreshStats"
                :loading="loadingStats"
                icon="Refresh"
                style="width: 100%; margin-bottom: 10px;"
              >
                刷新状态
              </el-button>
              
              <el-button 
                @click="clearLog"
                style="width: 100%;"
              >
                清空日志
              </el-button>
            </div>
          </el-card>
          
          <!-- 统计信息 -->
          <el-card header="系统状态" style="margin-top: 20px;">
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ statsData.stationCount }}</div>
                <div class="stat-label">站点数量</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ statsData.lineCount }}</div>
                <div class="stat-label">线路数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="16">
          <el-tabs v-model="activeTab" type="card">
            <!-- 操作日志 -->
            <el-tab-pane label="操作日志" name="logs">
              <el-card>
                <div class="log-area">
                  <div 
                    v-for="(log, index) in logs.slice(0, 15)" 
                    :key="index"
                    :class="['log-item', log.type]"
                  >
                    <span class="log-time">{{ log.time }}</span>
                    <span>{{ log.message }}</span>
                  </div>
                  <div v-if="logs.length === 0" class="no-logs">
                    暂无操作日志
                  </div>
                </div>
              </el-card>
            </el-tab-pane>
            
            <!-- 文件预览 -->
            <el-tab-pane label="文件预览" name="preview">
              <el-card>
                <div v-if="previewData">
                  <h4>文件结构预览 (版本: {{ previewData.version }})</h4>
                  <el-tag>{{ previewData.totalStations }} 个站点</el-tag>
                  <el-tag style="margin-left: 10px;">{{ previewData.totalLines }} 条线路</el-tag>
                  
                  <div class="preview-content" style="margin-top: 15px;">
                    <pre>{{ JSON.stringify(previewData.structure, null, 2) }}</pre>
                  </div>
                </div>
                <div v-else class="no-data">
                  请先点击预览文件结构
                </div>
              </el-card>
            </el-tab-pane>
            
            <!-- 验证结果 -->
            <el-tab-pane label="验证结果" name="validation">
              <el-card>
                <div v-if="validationData">
                  <div class="validation-header">
                    <h4>配置验证结果</h4>
                    <el-tag :type="validationData.valid ? 'success' : 'danger'">
                      {{ validationData.valid ? '验证通过' : '验证失败' }}
                    </el-tag>
                  </div>
                  
                  <div v-if="validationData.errors.length > 0" style="margin-top: 15px;">
                    <h5>错误信息:</h5>
                    <el-alert
                      v-for="(error, index) in validationData.errors"
                      :key="index"
                      :title="error"
                      type="error"
                      :closable="false"
                      style="margin-bottom: 10px;"
                    />
                  </div>
                  
                  <div v-if="validationData.warnings.length > 0" style="margin-top: 15px;">
                    <h5>警告信息:</h5>
                    <el-alert
                      v-for="(warning, index) in validationData.warnings"
                      :key="index"
                      :title="warning"
                      type="warning"
                      :closable="false"
                      style="margin-bottom: 10px;"
                    />
                  </div>
                  
                  <div style="margin-top: 15px;">
                    <h5>统计信息:</h5>
                    <p>站点数量: {{ validationData.stationCount }}</p>
                    <p>线路数量: {{ validationData.lineCount }}</p>
                  </div>
                </div>
                <div v-else class="no-data">
                  请先点击验证配置
                </div>
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

// API 导入
import {
  generateAnnouncementFiles,
  previewFileStructure,
  validateAnnouncementConfig,
  getSupportedVersions,
  getSystemConfig,
  getAnnouncementStats
} from '@/api/announcement';

import type {
  AnnouncementStats,
  ConfigValidation,
  FilePreview,
  SupportedVersions,
  AnnouncementSystemConfig
} from '@/api/announcement/types';

// 响应式数据
const selectedVersion = ref(20231221);
const activeTab = ref('logs');

// 加载状态
const generating = ref(false);
const previewing = ref(false);
const validating = ref(false);
const loadingStats = ref(false);

// 数据
const versionOptions = ref<Record<string, string>>({});
const statsData = ref<AnnouncementStats>({
  stationCount: 0,
  lineCount: 0,
  tenantId: '',
  configValid: false,
  validationSummary: {
    valid: false,
    errors: [],
    warnings: [],
    stationCount: 0,
    lineCount: 0
  }
});
const previewData = ref<FilePreview | null>(null);
const validationData = ref<ConfigValidation | null>(null);

// 日志
const logs = ref<Array<{
  time: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
}>>([]);

// 添加日志
const addLog = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
  const log = {
    time: new Date().toLocaleTimeString(),
    message,
    type
  };
  logs.value.unshift(log);
  
  // 限制日志数量
  if (logs.value.length > 30) {
    logs.value.splice(20);
  }
  
  console.log(`[${type.toUpperCase()}] ${message}`);
};

// 清空日志
const clearLog = () => {
  logs.value = [];
  addLog('日志已清空');
};

// API调用方法
const generateFiles = async () => {
  generating.value = true;
  addLog(`开始生成版本 ${selectedVersion.value} 的配置文件...`);
  
  try {
    const response = await generateAnnouncementFiles(selectedVersion.value);
    
    // 下载文件
    const blob = new Blob([response], { type: 'application/zip' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `announcement_config_v${selectedVersion.value}.zip`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    addLog('配置文件包生成成功！', 'success');
    ElMessage.success('配置文件包生成成功！');
  } catch (error) {
    const errorMsg = `生成配置文件包失败: ${error}`;
    addLog(errorMsg, 'error');
    ElMessage.error('生成配置文件包失败');
  } finally {
    generating.value = false;
  }
};

const previewFiles = async () => {
  previewing.value = true;
  addLog('正在预览文件结构...');
  
  try {
    const response = await previewFileStructure(selectedVersion.value);
    previewData.value = response.data;
    activeTab.value = 'preview';
    
    addLog('文件结构预览成功', 'success');
    ElMessage.success('文件结构预览成功');
  } catch (error) {
    addLog(`预览文件结构失败: ${error}`, 'error');
    ElMessage.error('预览文件结构失败');
  } finally {
    previewing.value = false;
  }
};

const validateConfig = async () => {
  validating.value = true;
  addLog('正在验证配置完整性...');
  
  try {
    const response = await validateAnnouncementConfig();
    validationData.value = response.data;
    activeTab.value = 'validation';
    
    const resultMsg = response.data.valid ? '配置验证通过' : '配置验证失败';
    addLog(resultMsg, response.data.valid ? 'success' : 'warning');
    ElMessage({
      message: resultMsg,
      type: response.data.valid ? 'success' : 'warning'
    });
  } catch (error) {
    addLog(`验证配置失败: ${error}`, 'error');
    ElMessage.error('验证配置失败');
  } finally {
    validating.value = false;
  }
};

const refreshStats = async () => {
  loadingStats.value = true;
  addLog('正在刷新系统状态...');
  
  try {
    const response = await getAnnouncementStats();
    statsData.value = response.data;
    
    addLog('系统状态刷新成功', 'success');
    ElMessage.success('状态刷新成功');
  } catch (error) {
    addLog(`刷新系统状态失败: ${error}`, 'error');
    ElMessage.error('刷新系统状态失败');
  } finally {
    loadingStats.value = false;
  }
};

// 初始化
onMounted(async () => {
  addLog('初始化报站系统测试平台...');
  
  try {
    // 加载支持的版本
    const versionsResponse = await getSupportedVersions();
    versionOptions.value = versionsResponse.data.description;
    selectedVersion.value = versionsResponse.data.recommended;
    addLog('版本信息加载成功', 'success');
    
    // 加载统计信息
    await refreshStats();
    
    addLog('初始化完成', 'success');
  } catch (error) {
    addLog('初始化失败，请检查后端服务是否启动', 'error');
  }
});
</script>

<style scoped>
.announcement-test-simple {
  padding: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.log-area {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
}

.log-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.log-item:last-child {
  border-bottom: none;
}

.log-item.success {
  background-color: #f0f9ff;
  border-left: 3px solid #67c23a;
  padding-left: 8px;
}

.log-item.error {
  background-color: #fef0f0;
  border-left: 3px solid #f56c6c;
  padding-left: 8px;
}

.log-item.warning {
  background-color: #fdf6ec;
  border-left: 3px solid #e6a23c;
  padding-left: 8px;
}

.log-item.info {
  background-color: #f4f4f5;
  border-left: 3px solid #909399;
  padding-left: 8px;
}

.log-time {
  color: #909399;
  margin-right: 10px;
  min-width: 80px;
}

.no-logs, .no-data {
  color: #909399;
  text-align: center;
  padding: 40px 20px;
  font-size: 14px;
}

.validation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.preview-content {
  max-height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
}

h4, h5 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #606266;
}

h5 {
  font-size: 13px;
  margin-top: 15px;
}
</style>