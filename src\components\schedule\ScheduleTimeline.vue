<template>
  <div class="schedule-timeline">
    <div class="timeline-header">
      <h3>排班时间线</h3>
      <div class="timeline-controls">
        <el-button-group size="small">
          <el-button @click="zoomIn">
            <el-icon><ZoomIn /></el-icon>
            放大
          </el-button>
          <el-button @click="zoomOut">
            <el-icon><ZoomOut /></el-icon>
            缩小
          </el-button>
          <el-button @click="fitWindow">
            <el-icon><FullScreen /></el-icon>
            适应窗口
          </el-button>
        </el-button-group>
        <el-button type="primary" size="small" @click="saveSchedule">
          <el-icon><Check /></el-icon>
          保存计划
        </el-button>
      </div>
    </div>

    <div class="timeline-info">
      <div class="info-item">
        <span class="info-label">时间范围:</span>
        <span class="info-value">{{ timeRangeText }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">已分配车辆:</span>
        <span class="info-value">{{ assignedVehiclesCount }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">发车班次:</span>
        <span class="info-value">{{ totalTrips }}</span>
      </div>
    </div>

    <!-- 时间线容器 -->
    <div
      ref="timelineContainer"
      class="timeline-container"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
    >
      <div v-if="!hasTimePoints" class="timeline-placeholder">
        <el-empty description="请先选择发车时间点" />
      </div>
    </div>

    <!-- 拖拽提示层 -->
    <div v-if="isDragOver" class="drop-zone-overlay">
      <div class="drop-hint">
        <el-icon><Download /></el-icon>
        <span>释放以分配车辆到此时间点</span>
      </div>
    </div>

    <!-- 冲突提示对话框 -->
    <el-dialog v-model="showConflictDialog" title="时间冲突提示" width="400px">
      <div class="conflict-message">
        <el-icon class="conflict-icon"><Warning /></el-icon>
        <p>{{ conflictMessage }}</p>
      </div>
      <template #footer>
        <el-button @click="showConflictDialog = false">取消</el-button>
        <el-button type="primary" @click="resolveConflict">确定分配</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { ZoomIn, ZoomOut, FullScreen, Check, Download, Warning } from '@element-plus/icons-vue';
import { Timeline } from 'vis-timeline/standalone';
import { DataSet } from 'vis-data/standalone';
import type { IdType } from 'vis-data';

// 接口定义
interface Vehicle {
  vehicleId: number | string;
  plateNumber: string;
  vehicleNumber: string;
  vehicleType: string;
  status: string;
  isAssigned?: boolean;
  assignedTime?: string;
}

interface ScheduleItem {
  id: IdType;
  content: string;
  start: Date;
  group?: number;
  className?: string;
  vehicleId: number | string;
  plateNumber: string;
}

// Props
interface Props {
  timePoints: string[];
  vehicles: Vehicle[];
}

const props = defineProps<Props>();

// Emits
interface Emits {
  (e: 'vehicleAssigned', vehicle: Vehicle, time: string): void;
  (e: 'vehicleUnassigned', vehicle: Vehicle): void;
  (e: 'scheduleChanged', items: ScheduleItem[]): void;
  (e: 'save', schedule: ScheduleItem[]): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const timelineContainer = ref<HTMLElement>();
const timeline = ref<Timeline>();
const items = ref(new DataSet<ScheduleItem>());
const isDragOver = ref(false);
const showConflictDialog = ref(false);
const conflictMessage = ref('');
const pendingAssignment = ref<{ vehicle: Vehicle; time: string } | null>(null);

// 计算属性
const hasTimePoints = computed(() => props.timePoints.length > 0);

const timeRangeText = computed(() => {
  if (props.timePoints.length === 0) return '未设置';
  const sorted = [...props.timePoints].sort();
  return `${sorted[0]} - ${sorted[sorted.length - 1]}`;
});

const assignedVehiclesCount = computed(() => {
  return items.value.length;
});

const totalTrips = computed(() => {
  return items.value.length;
});

// 方法
const initTimeline = () => {
  if (!timelineContainer.value || !hasTimePoints.value) return;

  const options = {
    height: '400px',
    stack: false,
    editable: {
      add: false,
      updateTime: true,
      updateGroup: false,
      remove: true
    },
    orientation: 'top',
    showCurrentTime: true,
    zoomMin: 1000 * 60 * 30, // 30分钟
    zoomMax: 1000 * 60 * 60 * 24, // 24小时
    format: {
      minorLabels: {
        hour: 'HH:mm',
        day: 'DD'
      },
      majorLabels: {
        hour: 'HH:mm',
        day: 'MMMM DD'
      }
    },
    locale: 'zh-CN',
    onMove: (item: any, callback: Function) => {
      // 移动项目时的回调
      callback(item);
      handleItemMoved(item);
    },
    onRemove: (item: any, callback: Function) => {
      // 删除项目时的回调
      handleItemRemoved(item);
      callback(item);
    }
  };

  timeline.value = new Timeline(timelineContainer.value, items.value, options);

  // 设置时间窗口
  setTimeWindow();
};

const setTimeWindow = () => {
  if (!timeline.value || props.timePoints.length === 0) return;

  const sorted = [...props.timePoints].sort();
  const startTime = new Date();
  const [startHour, startMinute] = sorted[0].split(':').map(Number);
  startTime.setHours(startHour, startMinute, 0, 0);

  const endTime = new Date();
  const [endHour, endMinute] = sorted[sorted.length - 1].split(':').map(Number);
  endTime.setHours(endHour + 2, endMinute, 0, 0); // 结束时间延后2小时

  timeline.value.setWindow(startTime, endTime);
};

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  event.dataTransfer!.dropEffect = 'move';
};

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault();
  isDragOver.value = true;
};

const handleDragLeave = (event: DragEvent) => {
  // 只有当离开整个容器时才隐藏提示
  if (!timelineContainer.value?.contains(event.relatedTarget as Node)) {
    isDragOver.value = false;
  }
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  isDragOver.value = false;

  try {
    const dragData = JSON.parse(event.dataTransfer!.getData('text/plain'));

    if (dragData.type === 'vehicle') {
      const vehicle = dragData.vehicle as Vehicle;

      // 获取拖拽位置对应的时间
      const rect = timelineContainer.value!.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const time = timeline.value!.getEventProperties(event).time;

      if (time) {
        assignVehicleToTime(vehicle, time);
      }
    }
  } catch (error) {
    console.error('处理拖拽数据失败:', error);
    ElMessage.error('拖拽操作失败');
  }
};

const assignVehicleToTime = (vehicle: Vehicle, time: Date) => {
  // 检查时间冲突
  const timeStr = formatTime(time);
  const existingItem = items.value.get().find(
    (item) => Math.abs(item.start.getTime() - time.getTime()) < 5 * 60 * 1000 // 5分钟内算冲突
  );

  if (existingItem) {
    conflictMessage.value = `时间 ${timeStr} 附近已有车辆 ${existingItem.plateNumber} 分配，是否继续？`;
    pendingAssignment.value = { vehicle, time: timeStr };
    showConflictDialog.value = true;
    return;
  }

  // 直接分配
  doAssignVehicle(vehicle, time);
};

const doAssignVehicle = (vehicle: Vehicle, time: Date) => {
  const newItem: ScheduleItem = {
    id: `${vehicle.vehicleId}_${time.getTime()}`,
    content: `🚌 ${vehicle.plateNumber}`,
    start: time,
    className: 'vehicle-item',
    vehicleId: vehicle.vehicleId,
    plateNumber: vehicle.plateNumber
  };

  items.value.add(newItem);
  emit('vehicleAssigned', vehicle, formatTime(time));
  emit('scheduleChanged', items.value.get() as ScheduleItem[]);

  ElMessage.success(`车辆 ${vehicle.plateNumber} 已分配到 ${formatTime(time)}`);
};

const handleItemMoved = (item: any) => {
  // 处理项目移动
  emit('scheduleChanged', items.value.get() as ScheduleItem[]);
};

const handleItemRemoved = (item: any) => {
  // 处理项目删除
  const vehicle = props.vehicles.find((v) => v.vehicleId === item.vehicleId);
  if (vehicle) {
    emit('vehicleUnassigned', vehicle);
  }
  emit('scheduleChanged', items.value.get() as ScheduleItem[]);
};

const resolveConflict = () => {
  if (pendingAssignment.value) {
    const { vehicle, time } = pendingAssignment.value;
    doAssignVehicle(vehicle, new Date(`2024-01-01 ${time}`));
    pendingAssignment.value = null;
  }
  showConflictDialog.value = false;
};

const formatTime = (date: Date): string => {
  return date.toTimeString().slice(0, 5);
};

const zoomIn = () => {
  timeline.value?.zoomIn(0.5);
};

const zoomOut = () => {
  timeline.value?.zoomOut(0.5);
};

const fitWindow = () => {
  timeline.value?.fit();
};

const saveSchedule = () => {
  const scheduleItems = items.value.get() as ScheduleItem[];
  emit('save', scheduleItems);
};

// 监听器
watch(
  () => props.timePoints,
  () => {
    nextTick(() => {
      if (timeline.value) {
        timeline.value.destroy();
      }
      initTimeline();
    });
  },
  { deep: true }
);

// 生命周期
onMounted(() => {
  nextTick(() => {
    initTimeline();
  });
});

onUnmounted(() => {
  if (timeline.value) {
    timeline.value.destroy();
  }
});

// 暴露方法给父组件
defineExpose({
  clearSchedule: () => {
    items.value.clear();
  },
  getScheduleItems: () => {
    return items.value.get() as ScheduleItem[];
  }
});
</script>

<style scoped>
.schedule-timeline {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.timeline-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.timeline-info {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-label {
  font-size: 14px;
  color: #606266;
}

.info-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.timeline-container {
  min-height: 400px;
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  position: relative;
  background: #fafafa;
}

.timeline-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.drop-zone-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(64, 158, 255, 0.1);
  border: 2px dashed #409eff;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.drop-hint {
  background: #409eff;
  color: white;
  padding: 16px 24px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.conflict-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #fff7e6;
  border-radius: 6px;
  border-left: 4px solid #e6a23c;
}

.conflict-icon {
  color: #e6a23c;
  font-size: 20px;
  margin-top: 2px;
}

.conflict-message p {
  margin: 0;
  color: #606266;
  line-height: 1.5;
}

/* 时间线样式覆盖 */
:deep(.vis-timeline) {
  border: none;
  font-family: inherit;
}

:deep(.vis-item.vehicle-item) {
  background: linear-gradient(135deg, #409eff, #67c23a);
  border: none;
  border-radius: 16px;
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

:deep(.vis-item.vehicle-item:hover) {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
}

:deep(.vis-item.vehicle-item.vis-selected) {
  background: linear-gradient(135deg, #67c23a, #409eff);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .schedule-timeline {
    padding: 16px;
  }

  .timeline-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .timeline-info {
    flex-direction: column;
    gap: 8px;
  }

  .timeline-container {
    min-height: 300px;
  }
}
</style>
