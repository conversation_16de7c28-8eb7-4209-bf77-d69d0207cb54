// 报站系统相关的TypeScript类型定义

// 报站系统配置
export interface AnnouncementSystemConfig {
  configId?: number;
  reportSystemVersion?: number;
  currentVersion?: string;
  codeFormat?: number;
  updateVersion?: number;
  reportRadiusLongitude?: number;
  reportRadiusLatitude?: number;
  reportRadiusMeter?: number;
  languageCount?: number;
  reportType?: number;
  reportVolumeIn?: number;
  reportVolumeOut?: number;
  codeType?: number;
  cityLatitudeDegree?: number;
  cityLatitudeCent?: number;
  cityLongitudeDegree?: number;
  cityLongitudeCent?: number;
  cityLatitudeSec?: number;
  cityLongitudeSec?: number;
  cityGpsAngle?: number;
  cityDirectionLatitude?: string;
  cityDirectionLongitude?: string;
  status?: string;
  tenantId?: string;
}

// 站点信息
export interface AnnouncementStation {
  stationId?: number;
  indexCode?: number;
  infoType?: number;
  stationName?: string;
  memo?: string;
  reportRadiusMeter?: number;
  staySecond?: number;
  reportRadiusLongitude?: number;
  reportRadiusLatitude?: number;
  latitudeDegree?: number;
  latitudeCent?: number;
  longitudeDegree?: number;
  longitudeCent?: number;
  latitudeSec?: number;
  longitudeSec?: number;
  gpsAngle?: number;
  directionLatitude?: string;
  directionLongitude?: string;
  stationShowName1?: string;
  stationShowName2?: string;
  stationShowName3?: string;
  stationShowName4?: string;
  stationVoiceFile1?: string;
  stationVoiceFile2?: string;
  stationVoiceFile3?: string;
  stationVoiceFile4?: string;
  adVoiceIn1?: string;
  adVoiceIn2?: string;
  adVoiceIn3?: string;
  adVoiceIn4?: string;
  adVoiceOut1?: string;
  adVoiceOut2?: string;
  adVoiceOut3?: string;
  adVoiceOut4?: string;
  version?: number;
  status?: string;
  tenantId?: string;
}

// 线路配置
export interface AnnouncementLineConfig {
  lineConfigId?: number;
  lineFileName?: string;
  lineName?: string;
  firstStationName?: string;
  lastStationName?: string;
  lineIndexCode?: number;
  lineIndexCode3?: number;
  lineIndexCode4?: number;
  stationCount?: number;
  languageCount?: number;
  lineRun?: string;
  directPolicy?: number;
  middleVoiceInA1?: string;
  middleVoiceInA2?: string;
  middleVoiceInA3?: string;
  middleVoiceInA4?: string;
  middleVoiceInC1?: string;
  middleVoiceInC2?: string;
  middleVoiceInC3?: string;
  middleVoiceInC4?: string;
  middleVoiceOutA1?: string;
  middleVoiceOutA2?: string;
  middleVoiceOutA3?: string;
  middleVoiceOutA4?: string;
  middleVoiceOutC1?: string;
  middleVoiceOutC2?: string;
  middleVoiceOutC3?: string;
  middleVoiceOutC4?: string;
  adVoiceInE1?: string;
  adVoiceInE2?: string;
  adVoiceInE3?: string;
  adVoiceInE4?: string;
  adVoiceOutE1?: string;
  adVoiceOutE2?: string;
  adVoiceOutE3?: string;
  adVoiceOutE4?: string;
  version?: number;
  updateVersion?: number;
  status?: string;
  tenantId?: string;
}

// 文件预览结构
export interface FilePreview {
  structure: Record<string, any>;
  totalStations: number;
  totalLines: number;
  version: number;
  tenantId: string;
}

// 配置验证结果
export interface ConfigValidation {
  valid: boolean;
  errors: string[];
  warnings: string[];
  stationCount: number;
  lineCount: number;
}

// 统计信息
export interface AnnouncementStats {
  stationCount: number;
  lineCount: number;
  tenantId: string;
  configValid: boolean;
  validationSummary: ConfigValidation;
}

// 配置向导步骤
export interface ConfigWizard {
  steps: {
    systemConfig: boolean;
    stationConfig: boolean;
    lineConfig: boolean;
    voiceConfig: boolean;
  };
  completionRate: number;
  nextStep: string;
  nextStepDescription: string;
}

// 支持的版本信息
export interface SupportedVersions {
  VERSION_1: number;
  VERSION_20200803: number;
  VERSION_20210617: number;
  VERSION_20231221: number;
  recommended: number;
  description: Record<number, string>;
}

// 文件生成选项
export interface FileGenerateOptions {
  version: number;
  includeVoiceFiles?: boolean;
  compressionLevel?: number;
}