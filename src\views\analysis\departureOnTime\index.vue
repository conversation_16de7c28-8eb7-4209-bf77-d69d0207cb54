<template>
  <div class='departure-ontime-analysis'>
    <el-row :gutter='20'>
      <!-- 左侧线路和车辆树 -->
      <el-col :lg='4' :xs='24'>
        <div class='left-panel'>
          <div class='panel-header'>
            <div class='header-left'>
              <el-icon>
                <Operation />
              </el-icon>
              <span>线路车辆</span>
            </div>
          </div>
          <div class='panel-content'>
            <div class='search-box'>
              <el-input v-model='deptName' placeholder='请输入线路或车辆' prefix-icon='Search' clearable />
            </div>

            <el-tree
              ref='deptTreeRef'
              class='dept-tree'
              node-key='id'
              :data='deptOptions'
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node='false'
              :filter-node-method='filterNode'
              highlight-current
              default-expand-all
              @node-click='handleNodeClick'
            >
              <template #default="{ node, data }">
                <span class="tree-node">
                  <el-icon v-if="data.type === 'route'" class="route-icon">
                    <Connection />
                  </el-icon>
                  <el-icon v-else-if="data.type === 'vehicle'" class="vehicle-icon">
                    <Van />
                  </el-icon>
                  <span class="node-label" :class="data.type">{{ data.label }}</span>
                  <el-tag
                    v-if="data.type === 'route' && data.onTimeRate"
                    :type="getOnTimeRateType(data.onTimeRate)"
                    size="small"
                    style="margin-left: 8px;"
                  >
                    {{ data.onTimeRate }}%
                  </el-tag>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
      </el-col>

      <el-col :lg='20' :xs='24'>
        <div class='right-content'>
          <!-- 筛选条件区域 -->
          <div class='filter-section'>
            <el-card class='filter-card'>
              <!-- 时间分析选择器 -->
              <TimeAnalysisSelector
                :initialAnalysisType="queryParams.analysisType"
                @params-change="handleTimeAnalysisChange"
                ref="timeAnalysisSelectorRef"
              >
                <template #actions>
                  <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
                  <el-button @click="resetQuery" icon="Refresh">重置</el-button>
                  <el-button type="success" plain @click="handleExport" icon="Download">导出</el-button>
                  <el-button type="primary" plain @click="handlePrint" icon="Printer">打印</el-button>
                </template>
              </TimeAnalysisSelector>

              <!-- 其他筛选条件 -->
              <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px" style="margin-top: 16px;">
                <el-form-item label="线路编号" prop="routeNumber">
                  <el-input
                    v-model="queryParams.routeNumber"
                    placeholder="请输入线路编号"
                    clearable
                    style="width: 120px"
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="车牌号" prop="plateNumber">
                  <el-input
                    v-model="queryParams.plateNumber"
                    placeholder="请输入车牌号"
                    clearable
                    style="width: 160px"
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="准点状态" prop="onTimeStatus">
                  <el-select v-model="queryParams.onTimeStatus" placeholder="请选择状态" clearable style="width: 120px">
                    <el-option label="全部" value="" />
                    <el-option label="准点" value="ontime" />
                    <el-option label="早点" value="early" />
                    <el-option label="晚点" value="late" />
                  </el-select>
                </el-form-item>
              </el-form>
            </el-card>
          </div>

          <!-- Tab切换区域 -->
          <div class='tabs-section'>
            <!-- 统一的时间范围显示 -->
            <div class="time-range-info" v-if="queryParams.startTime && queryParams.endTime">
              <el-alert
                :title="`统计时间范围：${getTimeRangeText()} (${getAnalysisTypeText(queryParams.analysisType)})`"
                type="info"
                :closable="false"
                show-icon
                style="margin-bottom: 16px;"
              />
            </div>
            <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
              <!-- 线路准点率汇总 -->
              <el-tab-pane label="线路准点率汇总" name="routeSummary">
                <div class="tab-content">
                  <!-- 统计卡片区域 -->
                  <div class='stats-section'>
                    <el-row :gutter='16'>
                      <el-col :span='6'>
                        <div class='stat-card total'>
                          <div class='stat-icon'>
                            <el-icon><Connection /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.totalRoutes }}</div>
                            <div class='stat-label'>总线路数</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card average'>
                          <div class='stat-icon'>
                            <el-icon><TrendCharts /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.avgOnTimeRate }}%</div>
                            <div class='stat-label'>平均准点率</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card highest'>
                          <div class='stat-icon'>
                            <el-icon><TopRight /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.bestOnTimeRate }}%</div>
                            <div class='stat-label'>最高准点率</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card departures'>
                          <div class='stat-icon'>
                            <el-icon><Operation /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.totalDepartures }}</div>
                            <div class='stat-label'>总发车次数</div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 图表区域 -->
                  <div class='charts-section'>
                    <el-row :gutter='16'>
                      <el-col :span='24'>
                        <div class='chart-card'>
                          <div class='chart-header'>
                            <h3>线路准点率对比图 - {{ getAnalysisTypeText(queryParams.analysisType) }}</h3>
                            <span v-if="queryParams.startTime && queryParams.endTime" class="chart-subtitle">
                              {{ getTimeRangeText() }}
                            </span>
                          </div>
                          <div ref="routeSummaryChartRef" class='chart-container'></div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 汇总表格 -->
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>线路发车准点率汇总表</h4>
                      </div>
                    </div>
                    <el-table :data="routeSummaryList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="线路编号" align="center" prop="routeNumber" width="100" />
                      <el-table-column label="线路名称" align="center" prop="routeName" min-width="150" />
                      <el-table-column label="起点站" align="center" prop="startStation" min-width="120" />
                      <el-table-column label="终点站" align="center" prop="endStation" min-width="120" />
                      <el-table-column label="计划发车" align="center" prop="plannedDepartures" width="100" />
                      <el-table-column label="实际发车" align="center" prop="actualDepartures" width="100" />
                      <el-table-column label="准点发车" align="center" prop="onTimeDepartures" width="100">
                        <template #default="scope">
                          <span class="ontime-count">{{ scope.row.onTimeDepartures }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="准点率" align="center" prop="onTimeRate" width="100">
                        <template #default="scope">
                          <el-tag :type="getOnTimeRateType(scope.row.onTimeRate)" size="small">
                            {{ scope.row.onTimeRate }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="平均晚点" align="center" prop="avgDelay" width="100">
                        <template #default="scope">
                          <span class="delay-time">{{ scope.row.avgDelay }}分钟</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="routeSummaryTotal > 0"
                      :total="routeSummaryTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getRouteSummaryList"
                    />
                  </div>
                </div>
              </el-tab-pane>

              <!-- 线路发车日统计 -->
              <el-tab-pane label="线路发车日统计" name="routeDaily">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>线路发车日统计报表</h4>
                      </div>
                    </div>
                    <el-table :data="routeDailyList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="统计日期" align="center" prop="statisticDate" width="120" />
                      <el-table-column label="线路编号" align="center" prop="routeNumber" width="100" />
                      <el-table-column label="线路名称" align="center" prop="routeName" min-width="150" />
                      <el-table-column label="计划班次" align="center" prop="plannedTrips" width="100" />
                      <el-table-column label="实际班次" align="center" prop="actualTrips" width="100" />
                      <el-table-column label="准点班次" align="center" prop="onTimeTrips" width="100">
                        <template #default="scope">
                          <span class="ontime-count">{{ scope.row.onTimeTrips }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="早点班次" align="center" prop="earlyTrips" width="100">
                        <template #default="scope">
                          <span class="early-count">{{ scope.row.earlyTrips }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="晚点班次" align="center" prop="lateTrips" width="100">
                        <template #default="scope">
                          <span class="late-count">{{ scope.row.lateTrips }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="准点率" align="center" prop="onTimeRate" width="100">
                        <template #default="scope">
                          <el-tag :type="getOnTimeRateType(scope.row.onTimeRate)" size="small">
                            {{ scope.row.onTimeRate }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="routeDailyTotal > 0"
                      :total="routeDailyTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getRouteDailyList"
                    />
                  </div>
                </div>
              </el-tab-pane>

              <!-- 车辆准点率统计 -->
              <el-tab-pane label="车辆准点率统计" name="vehicleSummary">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>车辆发车准点率统计表</h4>
                      </div>
                    </div>
                    <el-table :data="vehicleSummaryList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="车牌号" align="center" prop="plateNumber" width="120" />
                      <el-table-column label="车辆编号" align="center" prop="vehicleNumber" width="100" />
                      <el-table-column label="所属线路" align="center" prop="routeName" min-width="120" />
                      <el-table-column label="驾驶员" align="center" prop="driverName" min-width="100" />
                      <el-table-column label="计划发车" align="center" prop="plannedDepartures" width="100" />
                      <el-table-column label="实际发车" align="center" prop="actualDepartures" width="100" />
                      <el-table-column label="准点发车" align="center" prop="onTimeDepartures" width="100">
                        <template #default="scope">
                          <span class="ontime-count">{{ scope.row.onTimeDepartures }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="准点率" align="center" prop="onTimeRate" width="100">
                        <template #default="scope">
                          <el-tag :type="getOnTimeRateType(scope.row.onTimeRate)" size="small">
                            {{ scope.row.onTimeRate }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="平均晚点" align="center" prop="avgDelay" width="100">
                        <template #default="scope">
                          <span class="delay-time">{{ scope.row.avgDelay }}分钟</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="vehicleSummaryTotal > 0"
                      :total="vehicleSummaryTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getVehicleSummaryList"
                    />
                  </div>
                </div>
              </el-tab-pane>

              <!-- 车辆发车日统计 -->
              <el-tab-pane label="车辆发车日统计" name="vehicleDaily">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>车辆发车日统计报表</h4>
                      </div>
                    </div>
                    <el-table :data="vehicleDailyList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="统计日期" align="center" prop="statisticDate" width="120" />
                      <el-table-column label="车牌号" align="center" prop="plateNumber" width="120" />
                      <el-table-column label="车辆编号" align="center" prop="vehicleNumber" width="100" />
                      <el-table-column label="所属线路" align="center" prop="routeName" min-width="120" />
                      <el-table-column label="驾驶员" align="center" prop="driverName" min-width="100" />
                      <el-table-column label="计划班次" align="center" prop="plannedTrips" width="100" />
                      <el-table-column label="实际班次" align="center" prop="actualTrips" width="100" />
                      <el-table-column label="准点班次" align="center" prop="onTimeTrips" width="100">
                        <template #default="scope">
                          <span class="ontime-count">{{ scope.row.onTimeTrips }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="准点率" align="center" prop="onTimeRate" width="100">
                        <template #default="scope">
                          <el-tag :type="getOnTimeRateType(scope.row.onTimeRate)" size="small">
                            {{ scope.row.onTimeRate }}%
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="vehicleDailyTotal > 0"
                      :total="vehicleDailyTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getVehicleDailyList"
                    />
                  </div>
                </div>
              </el-tab-pane>

              <!-- 车辆发车明细 -->
              <el-tab-pane label="车辆发车明细" name="departureDetails">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <div>
                        <h4>车辆发车明细表</h4>
                      </div>
                    </div>
                    <el-table :data="departureDetailsList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="发车日期" align="center" prop="departureDate" width="120" />
                      <el-table-column label="线路" align="center" prop="routeName" min-width="100" />
                      <el-table-column label="车牌号" align="center" prop="plateNumber" width="120" />
                      <el-table-column label="班次编号" align="center" prop="tripNumber" width="100" />
                      <el-table-column label="起点站" align="center" prop="startStation" min-width="120" />
                      <el-table-column label="计划发车" align="center" prop="scheduledTime" width="120" />
                      <el-table-column label="实际发车" align="center" prop="actualTime" width="120" />
                      <el-table-column label="时差" align="center" prop="timeDifference" width="100">
                        <template #default="scope">
                          <span :class="getTimeDifferenceClass(scope.row.timeDifference)">
                            {{ scope.row.timeDifference }}分钟
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column label="状态" align="center" prop="status" width="100">
                        <template #default="scope">
                          <el-tag :type="getDepartureStatusType(scope.row.status)" size="small">
                            {{ getDepartureStatusText(scope.row.status) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="驾驶员" align="center" prop="driverName" width="100" />
                      <el-table-column label="备注" align="center" prop="remark" min-width="120" show-overflow-tooltip />
                      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleDepartureDetail(scope.row)">详情</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="departureDetailsTotal > 0"
                      :total="departureDetailsTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getDepartureDetailsList"
                    />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 详情弹窗 -->
    <el-dialog :title="dialogTitle" v-model="showDetailDialog" width="800px" append-to-body>
      <div v-if="detailData" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item v-if="detailData.routeName" label="线路名称">{{ detailData.routeName }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.routeNumber" label="线路编号">{{ detailData.routeNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.plateNumber" label="车牌号">{{ detailData.plateNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.vehicleNumber" label="车辆编号">{{ detailData.vehicleNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.driverName" label="驾驶员">{{ detailData.driverName }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.statisticDate" label="统计日期">{{ detailData.statisticDate }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.plannedDepartures || detailData.plannedTrips" label="计划发车/班次">{{ detailData.plannedDepartures || detailData.plannedTrips }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.actualDepartures || detailData.actualTrips" label="实际发车/班次">{{ detailData.actualDepartures || detailData.actualTrips }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.onTimeDepartures || detailData.onTimeTrips" label="准点发车/班次">{{ detailData.onTimeDepartures || detailData.onTimeTrips }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.onTimeRate" label="准点率">
            <el-tag :type="getOnTimeRateType(detailData.onTimeRate)" size="small">
              {{ detailData.onTimeRate }}%
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item v-if="detailData.avgDelay" label="平均晚点">{{ detailData.avgDelay }}分钟</el-descriptions-item>
          <el-descriptions-item v-if="detailData.startStation" label="起点站">{{ detailData.startStation }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.endStation" label="终点站">{{ detailData.endStation }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.earlyTrips" label="早点班次">{{ detailData.earlyTrips }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.lateTrips" label="晚点班次">{{ detailData.lateTrips }}</el-descriptions-item>
          <el-descriptions-item label="备注" span="2">{{ detailData.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DepartureOnTimeAnalysis">
import { ref, reactive, onMounted, nextTick, watchEffect, computed } from 'vue';
import { Operation, Connection, Van, TrendCharts, TopRight } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import TimeAnalysisSelector from '@/components/TimeAnalysisSelector/index.vue';

const loading = ref(true);
const showDetailDialog = ref(false);
const detailData = ref(null);
const activeTab = ref('routeSummary');

// 树形相关
const deptName = ref('');
const deptTreeRef = ref();

// 图表引用
const routeSummaryChartRef = ref(null);
let routeSummaryChart = null;

// 表单引用
const queryRef = ref();
const timeAnalysisSelectorRef = ref();

// 分页数据
const routeSummaryTotal = ref(0);
const routeDailyTotal = ref(0);
const vehicleSummaryTotal = ref(0);
const vehicleDailyTotal = ref(0);
const departureDetailsTotal = ref(0);

// 各个Tab的数据列表
const routeSummaryList = ref([]);
const routeDailyList = ref([]);
const vehicleSummaryList = ref([]);
const vehicleDailyList = ref([]);
const departureDetailsList = ref([]);

// 统计数据
const summaryStats = ref({
  totalRoutes: '28',
  avgOnTimeRate: '92.5',
  bestOnTimeRate: '98.7',
  totalDepartures: '8,640'
});

// 线路和车辆树数据
const deptOptions = ref([]);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  analysisType: '3', // 默认日分析
  startTime: undefined,
  endTime: undefined,
  dateRange: null,
  routeNumber: null,
  plateNumber: null,
  onTimeStatus: null,
  routeId: null,
  vehicleId: null
});

// 计算弹窗标题
const dialogTitle = computed(() => {
  if (!detailData.value) return '详情';
  if (detailData.value.routeName && !detailData.value.plateNumber) {
    return `${detailData.value.routeName} - 线路准点率详情`;
  }
  if (detailData.value.plateNumber) {
    return `${detailData.value.plateNumber} - 车辆准点率详情`;
  }
  return '发车准点率详情';
});

onMounted(() => {
  getTreeSelect();
  handleTabChange(activeTab.value);
  nextTick(() => {
    initCharts();
  });
});

/** 组织机构树筛选 */
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选组织机构树 */
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

/** 查询线路车辆下拉树结构 */
async function getTreeSelect() {
  try {
    deptOptions.value = [
      {
        id: 'route_115',
        label: '115路',
        type: 'route',
        routeNumber: '115',
        onTimeRate: 94.2,
        children: [
          { id: 'vehicle_1', label: '京A12345 (V001)', type: 'vehicle', plateNumber: '京A12345', vehicleNumber: 'V001', routeId: 'route_115' },
          { id: 'vehicle_2', label: '京A12346 (V002)', type: 'vehicle', plateNumber: '京A12346', vehicleNumber: 'V002', routeId: 'route_115' },
          { id: 'vehicle_3', label: '京A12347 (V003)', type: 'vehicle', plateNumber: '京A12347', vehicleNumber: 'V003', routeId: 'route_115' }
        ]
      },
      {
        id: 'route_135',
        label: '135路',
        type: 'route',
        routeNumber: '135',
        onTimeRate: 88.6,
        children: [
          { id: 'vehicle_4', label: '京A12348 (V004)', type: 'vehicle', plateNumber: '京A12348', vehicleNumber: 'V004', routeId: 'route_135' },
          { id: 'vehicle_5', label: '京A12349 (V005)', type: 'vehicle', plateNumber: '京A12349', vehicleNumber: 'V005', routeId: 'route_135' }
        ]
      },
      {
        id: 'route_201',
        label: '201路',
        type: 'route',
        routeNumber: '201',
        onTimeRate: 96.8,
        children: [
          { id: 'vehicle_6', label: '京A12350 (V006)', type: 'vehicle', plateNumber: '京A12350', vehicleNumber: 'V006', routeId: 'route_201' },
          { id: 'vehicle_7', label: '京A12351 (V007)', type: 'vehicle', plateNumber: '京A12351', vehicleNumber: 'V007', routeId: 'route_201' }
        ]
      },
      {
        id: 'route_301',
        label: '301路',
        type: 'route',
        routeNumber: '301',
        onTimeRate: 90.3,
        children: [
          { id: 'vehicle_8', label: '京A12352 (V008)', type: 'vehicle', plateNumber: '京A12352', vehicleNumber: 'V008', routeId: 'route_301' },
          { id: 'vehicle_9', label: '京A12353 (V009)', type: 'vehicle', plateNumber: '京A12353', vehicleNumber: 'V009', routeId: 'route_301' }
        ]
      }
    ];
  } catch (error) {
    console.error('获取线路车辆树失败:', error);
  }
}

/** 时间分析参数变化处理 */
function handleTimeAnalysisChange(params) {
  queryParams.analysisType = params.analysisType;
  queryParams.startTime = params.startTime;
  queryParams.endTime = params.endTime;

  // 更新统计数据和图表
  updateStatsData();
  if (activeTab.value === 'routeSummary') {
    nextTick(() => {
      updateRouteSummaryChart();
    });
  }
}

/** 更新统计数据 */
function updateStatsData() {
  // 根据选中的时间范围和类型更新统计数据
  const analysisText = getAnalysisTypeText(queryParams.analysisType);
  const timeRange = getTimeRangeText();

  // 更新统计卡片数据
  summaryStats.value = {
    totalRoutes: '28',
    avgOnTimeRate: '92.5',
    bestOnTimeRate: '98.7',
    totalDepartures: '8,640'
  };
}

/** 获取分析类型文本 */
function getAnalysisTypeText(type) {
  const typeMap = {
    '0': '年度统计',
    '1': '周统计',
    '2': '月度统计',
    '3': '日统计',
    '4': '小时统计'
  };
  return typeMap[type] || '日统计';
}

/** 获取时间范围文本 */
function getTimeRangeText() {
  if (!queryParams.startTime || !queryParams.endTime) {
    return '未选择时间范围';
  }

  const startTime = new Date(queryParams.startTime);
  const endTime = new Date(queryParams.endTime);

  switch(queryParams.analysisType) {
    case '0': // 年度
      return `${startTime.getFullYear()}年 - ${endTime.getFullYear()}年`;
    case '1': // 周
      return `${startTime.toISOString().split('T')[0]} 至 ${endTime.toISOString().split('T')[0]} (周统计)`;
    case '2': // 月度
      return `${startTime.getFullYear()}-${(startTime.getMonth() + 1).toString().padStart(2, '0')} 至 ${endTime.getFullYear()}-${(endTime.getMonth() + 1).toString().padStart(2, '0')}`;
    case '3': // 日
      return `${startTime.toISOString().split('T')[0]} 至 ${endTime.toISOString().split('T')[0]}`;
    case '4': // 小时
      return `${startTime.toISOString().split('T')[0]} ${startTime.toTimeString().split(' ')[0]} 至 ${endTime.toISOString().split('T')[0]} ${endTime.toTimeString().split(' ')[0]}`;
    default:
      return '未知时间范围';
  }
}

/** 节点单击事件 */
function handleNodeClick(data) {
  if (data.type === 'route') {
    queryParams.routeId = data.id;
    queryParams.routeNumber = data.routeNumber;
    queryParams.vehicleId = null;
    queryParams.plateNumber = null;
  } else if (data.type === 'vehicle') {
    queryParams.vehicleId = data.id;
    queryParams.plateNumber = data.plateNumber;
    queryParams.routeId = null;
    queryParams.routeNumber = null;
  }
  handleQuery();
}

/** Tab切换事件 */
function handleTabChange(tabName) {
  queryParams.pageNum = 1;
  activeTab.value = tabName;
  loading.value = true;

  switch (tabName) {
    case 'routeSummary':
      getRouteSummaryList();
      break;
    case 'routeDaily':
      getRouteDailyList();
      break;
    case 'vehicleSummary':
      getVehicleSummaryList();
      break;
    case 'vehicleDaily':
      getVehicleDailyList();
      break;
    case 'departureDetails':
      getDepartureDetailsList();
      break;
  }
}

// 获取线路准点率汇总数据
function getRouteSummaryList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateRouteSummaryData();
    routeSummaryList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    routeSummaryTotal.value = mockData.length;
    loading.value = false;

    // 更新图表
    nextTick(() => {
      updateRouteSummaryChart();
    });
  }, 500);
}

// 获取线路发车日统计数据
function getRouteDailyList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateRouteDailyData();
    routeDailyList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    routeDailyTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 获取车辆准点率统计数据
function getVehicleSummaryList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateVehicleSummaryData();
    vehicleSummaryList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    vehicleSummaryTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 获取车辆发车日统计数据
function getVehicleDailyList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateVehicleDailyData();
    vehicleDailyList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    vehicleDailyTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 获取车辆发车明细数据
function getDepartureDetailsList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateDepartureDetailsData();
    departureDetailsList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    departureDetailsTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 生成线路准点率汇总模拟数据
function generateRouteSummaryData() {
  const routes = [
    { routeNumber: '115', routeName: '115路', startStation: '火车站', endStation: '高新区' },
    { routeNumber: '135', routeName: '135路', startStation: '汽车站', endStation: '科技园' },
    { routeNumber: '201', routeName: '201路', startStation: '机场', endStation: '医院' },
    { routeNumber: '301', routeName: '301路', startStation: '东站', endStation: '北站' },
    { routeNumber: '202', routeName: '202路', startStation: '码头', endStation: '工业园' }
  ];

  return routes.map((route, index) => {
    const plannedDepartures = Math.floor(Math.random() * 50 + 150);
    const actualDepartures = Math.floor(plannedDepartures * (0.95 + Math.random() * 0.05));
    const onTimeDepartures = Math.floor(actualDepartures * (0.85 + Math.random() * 0.15));

    return {
      id: index + 1,
      ...route,
      plannedDepartures,
      actualDepartures,
      onTimeDepartures,
      onTimeRate: ((onTimeDepartures / actualDepartures) * 100).toFixed(1),
      avgDelay: (Math.random() * 8 + 2).toFixed(1),
      remark: Math.random() > 0.7 ? '运营正常' : null
    };
  });
}

// 生成线路发车日统计模拟数据
function generateRouteDailyData() {
  const data = [];
  const routes = ['115路', '135路', '201路', '301路'];

  for (let i = 0; i < 30; i++) {
    routes.forEach((routeName, routeIndex) => {
      const baseDate = new Date();
      baseDate.setDate(baseDate.getDate() - i);

      const plannedTrips = Math.floor(Math.random() * 20 + 80);
      const actualTrips = Math.floor(plannedTrips * (0.95 + Math.random() * 0.05));
      const onTimeTrips = Math.floor(actualTrips * (0.85 + Math.random() * 0.15));
      const earlyTrips = Math.floor(actualTrips * (0.05 + Math.random() * 0.05));
      const lateTrips = actualTrips - onTimeTrips - earlyTrips;

      data.push({
        id: i * routes.length + routeIndex + 1,
        statisticDate: baseDate.toISOString().split('T')[0],
        routeNumber: routeName.replace('路', ''),
        routeName,
        plannedTrips,
        actualTrips,
        onTimeTrips,
        earlyTrips: Math.max(0, earlyTrips),
        lateTrips: Math.max(0, lateTrips),
        onTimeRate: ((onTimeTrips / actualTrips) * 100).toFixed(1),
        remark: Math.random() > 0.8 ? '天气影响' : null
      });
    });
  }

  return data.sort((a, b) => new Date(b.statisticDate) - new Date(a.statisticDate));
}

// 生成车辆准点率统计模拟数据
function generateVehicleSummaryData() {
  const vehicles = [
    { plateNumber: '京A12345', vehicleNumber: 'V001', routeName: '115路', driverName: '张志明' },
    { plateNumber: '京A12346', vehicleNumber: 'V002', routeName: '135路', driverName: '李华强' },
    { plateNumber: '京A12347', vehicleNumber: 'V003', routeName: '201路', driverName: '王建国' },
    { plateNumber: '京A12348', vehicleNumber: 'V004', routeName: '301路', driverName: '陈美丽' },
    { plateNumber: '京A12349', vehicleNumber: 'V005', routeName: '115路', driverName: '刘德华' },
    { plateNumber: '京A12350', vehicleNumber: 'V006', routeName: '202路', driverName: '赵雅芝' }
  ];

  return vehicles.map((vehicle, index) => {
    const plannedDepartures = Math.floor(Math.random() * 30 + 70);
    const actualDepartures = Math.floor(plannedDepartures * (0.95 + Math.random() * 0.05));
    const onTimeDepartures = Math.floor(actualDepartures * (0.80 + Math.random() * 0.20));

    return {
      id: index + 1,
      ...vehicle,
      plannedDepartures,
      actualDepartures,
      onTimeDepartures,
      onTimeRate: ((onTimeDepartures / actualDepartures) * 100).toFixed(1),
      avgDelay: (Math.random() * 10 + 2).toFixed(1),
      remark: Math.random() > 0.7 ? '表现良好' : null
    };
  });
}

// 生成车辆发车日统计模拟数据
function generateVehicleDailyData() {
  const data = [];
  const vehicles = [
    { plateNumber: '京A12345', vehicleNumber: 'V001', routeName: '115路', driverName: '张志明' },
    { plateNumber: '京A12346', vehicleNumber: 'V002', routeName: '135路', driverName: '李华强' },
    { plateNumber: '京A12347', vehicleNumber: 'V003', routeName: '201路', driverName: '王建国' }
  ];

  for (let i = 0; i < 15; i++) {
    vehicles.forEach((vehicle, vehicleIndex) => {
      const baseDate = new Date();
      baseDate.setDate(baseDate.getDate() - i);

      const plannedTrips = Math.floor(Math.random() * 10 + 30);
      const actualTrips = Math.floor(plannedTrips * (0.95 + Math.random() * 0.05));
      const onTimeTrips = Math.floor(actualTrips * (0.80 + Math.random() * 0.20));

      data.push({
        id: i * vehicles.length + vehicleIndex + 1,
        statisticDate: baseDate.toISOString().split('T')[0],
        ...vehicle,
        plannedTrips,
        actualTrips,
        onTimeTrips,
        onTimeRate: ((onTimeTrips / actualTrips) * 100).toFixed(1),
        remark: Math.random() > 0.8 ? '正常运营' : null
      });
    });
  }

  return data.sort((a, b) => new Date(b.statisticDate) - new Date(a.statisticDate));
}

// 生成车辆发车明细模拟数据
function generateDepartureDetailsData() {
  const data = [];
  const routes = [
    { routeName: '115路', startStation: '火车站' },
    { routeName: '135路', startStation: '汽车站' },
    { routeName: '201路', startStation: '机场' },
    { routeName: '301路', startStation: '东站' }
  ];
  const vehicles = ['京A12345', '京A12346', '京A12347', '京A12348'];
  const drivers = ['张志明', '李华强', '王建国', '陈美丽'];

  for (let i = 0; i < 50; i++) {
    const routeIndex = Math.floor(Math.random() * routes.length);
    const vehicleIndex = Math.floor(Math.random() * vehicles.length);

    const baseDate = new Date();
    baseDate.setDate(baseDate.getDate() - Math.floor(i / 10));

    const scheduledHour = 6 + Math.floor(Math.random() * 14);
    const scheduledMinute = Math.floor(Math.random() * 60);

    const scheduledTime = `${scheduledHour.toString().padStart(2, '0')}:${scheduledMinute.toString().padStart(2, '0')}`;

    const timeDifference = Math.round((Math.random() - 0.7) * 15); // -10.5 to 4.5 minutes
    const actualMinute = scheduledMinute + timeDifference;
    const actualHour = scheduledHour + Math.floor(actualMinute / 60);
    const normalizedMinute = ((actualMinute % 60) + 60) % 60;

    const actualTime = `${actualHour.toString().padStart(2, '0')}:${normalizedMinute.toString().padStart(2, '0')}`;

    let status;
    if (timeDifference <= -3) status = 'early';
    else if (timeDifference >= 5) status = 'late';
    else status = 'ontime';

    data.push({
      id: i + 1,
      departureDate: baseDate.toISOString().split('T')[0],
      routeName: routes[routeIndex].routeName,
      plateNumber: vehicles[vehicleIndex],
      tripNumber: `T${(1000 + i).toString()}`,
      startStation: routes[routeIndex].startStation,
      scheduledTime,
      actualTime,
      timeDifference,
      status,
      driverName: drivers[vehicleIndex],
      remark: Math.random() > 0.8 ? (status === 'late' ? '交通拥堵' : '正常') : null
    });
  }

  return data.sort((a, b) => new Date(b.departureDate) - new Date(a.departureDate));
}

// 初始化图表
function initCharts() {
  initRouteSummaryChart();
}

// 初始化线路准点率对比图表
function initRouteSummaryChart() {
  if (!routeSummaryChartRef.value) return;

  routeSummaryChart = echarts.init(routeSummaryChartRef.value);
  updateRouteSummaryChart();
}

// 更新线路准点率对比图表
function updateRouteSummaryChart() {
  if (!routeSummaryChart) return;

  const routes = routeSummaryList.value.slice(0, 10); // 取前10条数据
  const routeNames = routes.map(r => r.routeName);
  const onTimeRates = routes.map(r => parseFloat(r.onTimeRate));
  const avgDelays = routes.map(r => parseFloat(r.avgDelay));

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' },
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' }
    },
    legend: {
      data: ['准点率', '平均晚点'],
      textStyle: { color: '#e5e7eb' },
      top: '5%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: routeNames,
      axisLabel: { color: '#94a3b8', rotate: 30 },
      axisLine: { lineStyle: { color: '#374151' } }
    },
    yAxis: [
      {
        type: 'value',
        name: '准点率(%)',
        position: 'left',
        axisLabel: { color: '#94a3b8' },
        axisLine: { lineStyle: { color: '#374151' } },
        splitLine: { lineStyle: { color: '#374151' } }
      },
      {
        type: 'value',
        name: '平均晚点(分钟)',
        position: 'right',
        axisLabel: { color: '#94a3b8' },
        axisLine: { lineStyle: { color: '#374151' } },
        splitLine: { show: false }
      }
    ],
    series: [
      {
        name: '准点率',
        type: 'bar',
        data: onTimeRates,
        itemStyle: { color: '#67C23A' },
        yAxisIndex: 0
      },
      {
        name: '平均晚点',
        type: 'line',
        data: avgDelays,
        itemStyle: { color: '#E6A23C' },
        lineStyle: { color: '#E6A23C' },
        yAxisIndex: 1
      }
    ]
  };

  routeSummaryChart.setOption(option);
}

// 查询
function handleQuery() {
  queryParams.pageNum = 1;
  // 更新统计数据
  updateStatsData();
  handleTabChange(activeTab.value);
}

// 重置
function resetQuery() {
  queryRef.value?.resetFields();
  queryParams.routeId = null;
  queryParams.vehicleId = null;
  timeAnalysisSelectorRef.value?.reset();
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

// 表格选择
function handleSelectionChange(selection) {
  // 处理表格选择
}

// 查看详情
function handleDetail(row) {
  detailData.value = row;
  showDetailDialog.value = true;
}

// 查看发车详情
function handleDepartureDetail(row) {
  detailData.value = {
    ...row,
    routeName: row.routeName,
    plateNumber: row.plateNumber
  };
  showDetailDialog.value = true;
}

// 导出数据
function handleExport() {
  ElMessage.success('导出成功');
}

// 打印报表
function handlePrint() {
  window.print();
}

// 获取准点率类型
function getOnTimeRateType(rate) {
  const numRate = parseFloat(rate);
  if (numRate >= 95) return 'success';
  if (numRate >= 90) return '';
  if (numRate >= 80) return 'warning';
  return 'danger';
}

// 获取时差样式类
function getTimeDifferenceClass(timeDiff) {
  if (timeDiff <= -3) return 'early-time';
  if (timeDiff >= 5) return 'late-time';
  return 'ontime-time';
}

// 获取发车状态类型
function getDepartureStatusType(status) {
  const typeMap = {
    'ontime': 'success',
    'early': 'warning',
    'late': 'danger'
  };
  return typeMap[status] || 'info';
}

// 获取发车状态文本
function getDepartureStatusText(status) {
  const textMap = {
    'ontime': '准点',
    'early': '早点',
    'late': '晚点'
  };
  return textMap[status] || '未知';
}

// 格式化时间
function formatTime(timeStr) {
  if (!timeStr) return '';
  return timeStr;
}
</script>

<style scoped>
.departure-ontime-analysis {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 左侧面板样式 */
.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 24px;
}

.search-box {
  margin-bottom: 16px;
}

/* 右侧内容区域 */
.right-content {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  backdrop-filter: blur(10px);
}

/* 筛选条件区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  background: rgba(15, 23, 42, 0.98) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

/* Tab区域 */
.tabs-section {
  margin-bottom: 20px;
}

.tab-content {
  padding-top: 20px;
}

/* 时间范围信息 */
.time-range-info {
  margin-bottom: 16px;
}

.chart-subtitle {
  font-size: 12px;
  color: #94a3b8;
  font-weight: normal;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(41, 52, 70, 0.6) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
  border-color: rgba(96, 165, 250, 0.4);
}

.stat-card.total {
  border-left: 4px solid #409EFF;
}

.stat-card.average {
  border-left: 4px solid #67C23A;
}

.stat-card.highest {
  border-left: 4px solid #E6A23C;
}

.stat-card.departures {
  border-left: 4px solid #F56C6C;
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #60a5fa;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #f8fafc;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #94a3b8;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  height: 400px;
}

.chart-card .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.chart-card .chart-header h3 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.chart-container {
  height: 320px;
  width: 100%;
}

/* 表格样式 */
.table-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  min-height: 620px;
  height: calc(100vh - 480px);
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.table-header h4 {
  margin: 0 0 4px 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.table-subtitle {
  font-size: 12px;
  color: #94a3b8;
  font-weight: normal;
}

/* 数据样式 */
.ontime-count {
  color: #67C23A;
  font-weight: 600;
}

.early-count {
  color: #E6A23C;
  font-weight: 600;
}

.late-count {
  color: #F56C6C;
  font-weight: 600;
}

.delay-time {
  color: #F56C6C;
  font-weight: 600;
}

.early-time {
  color: #E6A23C;
  font-weight: 600;
}

.ontime-time {
  color: #67C23A;
  font-weight: 600;
}

.late-time {
  color: #F56C6C;
  font-weight: 600;
}

/* 详情弹窗 */
.detail-content {
  max-height: 500px;
  overflow-y: auto;
}

/* 树形组件样式 */
.dept-tree {
  background: transparent;
  color: #e5e7eb;
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
}

.tree-node {
  display: flex;
  align-items: center;
  font-size: 14px;
  flex: 1;
}

.route-icon {
  margin-right: 8px;
  color: #60a5fa;
  font-size: 16px;
  flex-shrink: 0;
}

.vehicle-icon {
  margin-right: 8px;
  color: #34d399;
  font-size: 16px;
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  min-width: 0;
  white-space: nowrap;
}

.node-label.route {
  font-weight: 500;
  color: #f1f5f9;
}

.node-label.vehicle {
  color: #94a3b8;
}

/* Element UI组件覆盖 */
:deep(.el-form-item__label) {
  color: #e5e7eb !important;
}

:deep(.el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e5e7eb !important;
}

:deep(.el-input__inner) {
  color: #e5e7eb !important;
}

:deep(.el-input__inner::placeholder) {
  color: #64748b !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
}

:deep(.el-table) {
  background: transparent !important;
  color: #e5e7eb !important;
  height: 100% !important;
  flex: 1 !important;
}

:deep(.el-table tr) {
  background: rgba(15, 23, 42, 0.6) !important;
}

:deep(.el-table tr:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
}

:deep(.el-table th.el-table__cell) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #f8fafc !important;
  border-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-table td.el-table__cell) {
  border-color: rgba(147, 197, 253, 0.1) !important;
  color: #e5e7eb !important;
}

:deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.8);
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  background: transparent;
  border: none;
  color: #94a3b8;
  transition: all 0.3s ease;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item:hover) {
  background: rgba(59, 130, 246, 0.1);
  color: #93c5fd;
}

/* Element Tree组件覆盖样式 */
:deep(.el-tree-node) {
  background: transparent;
}

:deep(.el-tree-node:hover > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 6px;
}

:deep(.el-tree-node__content) {
  background: transparent !important;
  border: none !important;
  color: #e5e7eb !important;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

:deep(.el-tree-node__content:hover) {
  background-color: rgba(59, 130, 246, 0.15) !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.2) !important;
  color: #93c5fd !important;
}

:deep(.el-tree-node__expand-icon) {
  color: #64748b !important;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-tree .el-tree-node__children) {
  overflow: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .departure-ontime-analysis {
    padding: 10px;
  }

  .stats-section .el-col {
    margin-bottom: 16px;
  }

  .chart-card {
    height: 300px;
  }

  .chart-container {
    height: 220px;
  }
}
</style>
