import { createApp } from 'vue';

// 全局警告抑制器 - 必须最先执行
import { initGlobalWarningSuppress } from '@/utils/suppressWarnings';
initGlobalWarningSuppress();

// global css
import 'virtual:uno.css';
import '@/assets/styles/index.scss';
import 'element-plus/theme-chalk/dark/css-vars.css';
import '@/assets/styles/vis-timeline.css';

// App、router、store
import App from './App.vue';
import store from './store';
import router from './router';

// 自定义指令
import directive from './directive';

// 注册插件
import plugins from './plugins/index'; // plugins

// 高亮组件
// import 'highlight.js/styles/a11y-light.css';
import 'highlight.js/styles/atom-one-dark.css';
import 'highlight.js/lib/common';
import HighLight from '@highlightjs/vue-plugin';

// svg图标
import 'virtual:svg-icons-register';
import ElementIcons from '@/plugins/svgicon';

// permission control
import './permission';

// 国际化
import i18n from '@/lang/index';

// 使用官方推荐的AMap加载器
import AMapLoader from '@amap/amap-jsapi-loader';

// 全局初始化高德地图API和UI组件
const initAMapAPI = async () => {
  try {
    // 设置安全密钥
    window._AMapSecurityConfig = {
      securityJsCode: import.meta.env.VITE_APP_AMAP_SECURITY_CODE
    };

    // 全局加载高德地图API
    window.AMap = await AMapLoader.load({
      key: import.meta.env.VITE_APP_AMAP_KEY,
      version: '2.0',
      plugins: [
        'AMap.Scale', 
        'AMap.ToolBar', 
        'AMap.Marker', 
        'AMap.Polyline', 
        'AMap.InfoWindow', 
        'AMap.GeometryUtil',
        'AMap.MoveAnimation'  // 添加轨迹动画插件
      ]
    });

    console.log('高德地图API全局加载成功');
  } catch (error) {
    console.error('高德地图API加载失败:', error);
  }
};

// 初始化AMap API
initAMapAPI();

/**
 * 应用 Canvas 性能优化补丁
 */
export function applyCanvasPerformancePatch() {
  // 保存原始方法
  const originalGetContext = HTMLCanvasElement.prototype.getContext

  // 使用类型安全的实现
  HTMLCanvasElement.prototype.getContext = function <T extends RenderingContext | null>(
    contextId: string,
    options?: any
  ): T {
    if (contextId === "2d") {
      // 处理 2D 上下文
      const context = originalGetContext.call(this, contextId, {
        willReadFrequently: true,
        ...(options || {}),
      }) as CanvasRenderingContext2D | null

      return context as T
    }

    // 处理其他上下文类型
    return originalGetContext.call(this, contextId, options) as T
  }
}
// 在创建 Vue 应用前应用补丁
applyCanvasPerformancePatch()

// vxeTable
import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css';
VXETable.setConfig({
  zIndex: 999999
});

// OverlayScrollbars 全局配置
import { OverlayScrollbars } from 'overlayscrollbars';
// 全局初始化 OverlayScrollbars
OverlayScrollbars.env().setDefaultInitialization({
  cancel: {
    nativeScrollbarsOverlaid: false,
    body: null
  }
});

// 修改 el-dialog 默认点击遮照为不关闭
import { ElDialog } from 'element-plus';
ElDialog.props.closeOnClickModal.default = false;


const app = createApp(App);

app.use(HighLight);
app.use(ElementIcons);
app.use(router);
app.use(store);
app.use(i18n);
app.use(VXETable);
app.use(plugins);
// 自定义指令
directive(app);

app.mount('#app');
