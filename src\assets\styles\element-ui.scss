/* Element UI 全局科技感深色主题样式 */

/* 全局变量定义 */
:root {
  // 主色调 - 科技蓝
  --tech-primary: #3b82f6;
  --tech-primary-light: #60a5fa;
  --tech-primary-dark: #1d4ed8;

  // 背景色 - 深色科技风
  --tech-bg-primary: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  --tech-bg-secondary: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  --tech-bg-tertiary: rgba(15, 23, 42, 0.8);

  // 边框颜色
  --tech-border: rgba(147, 197, 253, 0.3);
  --tech-border-hover: rgba(96, 165, 250, 0.4);
  --tech-border-focus: #3b82f6;

  // 文字颜色
  --tech-text-primary: #e5e7eb;
  --tech-text-secondary: #94a3b8;
  --tech-text-white: #f8fafc;

  // 阴影效果
  --tech-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  --tech-shadow-hover: 0 8px 32px rgba(59, 130, 246, 0.2);
  --tech-shadow-focus: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* ===================
   基础组件样式
=================== */

/* 输入框 */
.el-input__wrapper {
  background: var(--tech-bg-tertiary) !important;
  border: 1px solid var(--tech-border) !important;
  border-radius: 8px !important;
  box-shadow:
    0 0 8px rgba(59, 130, 246, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.03) !important;
  transition: all 0.3s ease !important;
  backdrop-filter: blur(8px) !important;
}

.el-input__wrapper:hover {
  border-color: var(--tech-border-hover) !important;
  background: rgba(30, 41, 59, 0.9) !important;
  box-shadow:
    0 0 12px rgba(96, 165, 250, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

.el-input__wrapper.is-focus {
  border-color: var(--tech-border-focus) !important;
  box-shadow:
    0 0 16px rgba(59, 130, 246, 0.2),
    0 0 0 1px rgba(59, 130, 246, 0.1) inset !important;
  background: rgba(30, 41, 59, 0.95) !important;
}

.el-input__inner {
  color: var(--tech-text-primary) !important;
  background: transparent !important;
  font-weight: 400;
}

.el-input__inner::placeholder {
  color: var(--tech-text-secondary) !important;
  font-weight: 400;
}

/* 按钮 */
.el-button {
  border-color: var(--tech-border);
  transition: all 0.3s ease;
  border-radius: 8px;
  font-weight: 500;
  backdrop-filter: blur(5px);
}

.el-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--tech-shadow);
}

/* 主要按钮 */
.el-button--primary {
  background: linear-gradient(135deg, var(--tech-primary) 0%, var(--tech-primary-dark) 100%) !important;
  border-color: var(--tech-primary) !important;
  color: #ffffff !important;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.2);
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;
  border-color: #2563eb !important;
  transform: translateY(-2px) !important;
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.4),
    0 0 25px rgba(59, 130, 246, 0.3) !important;
}

.el-button--primary.is-plain {
  background: rgba(59, 130, 246, 0.1) !important;
  border-color: rgba(59, 130, 246, 0.4) !important;
  color: var(--tech-primary-light) !important;
}

/* 成功按钮 */
.el-button--success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  border-color: #10b981 !important;
  color: #ffffff !important;
}

.el-button--success:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow:
    0 6px 20px rgba(16, 185, 129, 0.4),
    0 0 25px rgba(16, 185, 129, 0.3) !important;
}

/* 警告按钮 */
.el-button--warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  border-color: #f59e0b !important;
  color: #ffffff !important;
}

/* 危险按钮 */
.el-button--danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  border-color: #ef4444 !important;
  color: #ffffff !important;
}

/* 信息按钮 */
.el-button--info {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%) !important;
  border-color: #06b6d4 !important;
  color: #ffffff !important;
}

/* 默认按钮 */
.el-button:not([class*='el-button--']) {
  background: linear-gradient(135deg, rgba(71, 85, 105, 0.8) 0%, rgba(51, 65, 85, 0.8) 100%) !important;
  border-color: var(--tech-border) !important;
  color: var(--tech-text-primary) !important;
}

/* 选择框 */
.el-select .el-input .el-input__wrapper {
  background: var(--tech-bg-tertiary) !important;
  border: 1px solid var(--tech-border) !important;
}

.el-select .el-input.is-focus .el-input__wrapper {
  background: rgba(30, 41, 59, 0.95) !important;
  border-color: var(--tech-border-focus) !important;
}

.el-select__popper {
  background: rgba(15, 23, 42, 0.98) !important;
  backdrop-filter: blur(15px) !important;
  border: 2px solid var(--tech-border) !important;
  border-radius: 12px !important;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(59, 130, 246, 0.2) !important;
}

.el-select-dropdown {
  background: transparent !important;
  border: none !important;
}

.el-select-dropdown__item {
  color: var(--tech-text-primary) !important;
  background: transparent !important;
  padding: 0 16px !important;
  border-radius: 8px !important;
  margin: 2px 8px !important;
  transition: all 0.2s ease !important;
  height: 40px !important;
  line-height: 40px !important;
  display: flex;
  align-items: center !important;
  justify-content: flex-start !important;
}

.el-select-dropdown__item:hover {
  background: rgba(59, 130, 246, 0.2) !important;
  color: var(--tech-primary-light) !important;
  transform: translateX(2px) !important;
}

.el-select-dropdown__item.selected {
  background: rgba(34, 197, 94, 0.2) !important;
  color: #22c55e !important;
  font-weight: 600 !important;
}

/* 对话框 */
.el-dialog {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%) !important;
  border: 2px solid var(--tech-border) !important;
  border-radius: 16px !important;
  color: var(--tech-text-primary) !important;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.5),
    0 0 40px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(15px) !important;
}

/* 针对模板快速生成弹窗的特殊样式 */
.el-dialog[aria-label*="快速生成模板"] {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 50%, rgba(15, 23, 42, 0.95) 100%) !important;
  border: 2px solid rgba(59, 130, 246, 0.4) !important;
  box-shadow:
    0 0 30px rgba(59, 130, 246, 0.3),
    0 0 60px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.el-dialog[aria-label*="快速生成模板"] .el-dialog__header {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(59, 130, 246, 0.2) 50%, rgba(30, 41, 59, 0.95) 100%) !important;
  border-bottom: 2px solid rgba(59, 130, 246, 0.4) !important;
}

.el-dialog[aria-label*="快速生成模板"] .el-dialog__title {
  color: #f1f5f9 !important;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.5) !important;
}

.el-dialog__header {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 50%, rgba(51, 65, 85, 0.9) 100%) !important;
  border-bottom: 2px solid var(--tech-border) !important;
  border-radius: 14px 14px 0 0 !important;
  padding: 20px 24px !important;
  box-shadow: var(--tech-shadow);
}

.el-dialog__title {
  color: var(--tech-text-white) !important;
  font-weight: 600 !important;
  font-size: 18px !important;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.3) !important;
}

.el-dialog__body {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.3) 0%, rgba(30, 41, 59, 0.2) 50%, rgba(15, 23, 42, 0.3) 100%) !important;
  color: var(--tech-text-primary) !important;
  padding: 24px !important;
  backdrop-filter: blur(10px) !important;
}

.el-dialog__footer {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 50%, rgba(51, 65, 85, 0.9) 100%) !important;
  border-top: 2px solid var(--tech-border) !important;
  border-radius: 0 0 14px 14px !important;
  padding: 20px 24px !important;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3) !important;
}

.el-dialog__headerbtn .el-dialog__close {
  color: var(--tech-text-secondary) !important;
  font-size: 20px !important;
  transition: all 0.3s ease !important;
}

.el-dialog__headerbtn .el-dialog__close:hover {
  color: var(--tech-primary-light) !important;
  text-shadow: 0 0 8px rgba(96, 165, 250, 0.5) !important;
}

/* 表单 */
.el-form-item__label {
  color: var(--tech-text-primary) !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.el-form-item__error {
  color: #f87171 !important;
  font-size: 12px;
  background: rgba(239, 68, 68, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  border-left: 2px solid #f87171;
}

/* 文本域 */
.el-textarea__inner {
  background: var(--tech-bg-tertiary) !important;
  border: 2px solid var(--tech-border) !important;
  color: var(--tech-text-primary) !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.el-textarea__inner:hover {
  border-color: var(--tech-border-hover) !important;
}

.el-textarea__inner:focus {
  border-color: var(--tech-border-focus) !important;
  box-shadow: var(--tech-shadow-focus) !important;
}

/* 数字输入框 */
.el-input-number .el-input__wrapper {
  background: var(--tech-bg-tertiary) !important;
  border: 1px solid var(--tech-border) !important;
}

.el-input-number__decrease,
.el-input-number__increase {
  background: rgba(30, 41, 59, 0.8) !important;
  border-color: var(--tech-border) !important;
  color: var(--tech-text-secondary) !important;
}

.el-input-number__decrease:hover,
.el-input-number__increase:hover {
  background: rgba(59, 130, 246, 0.2) !important;
  color: var(--tech-primary-light) !important;
}

/* 日期选择器 */
.el-date-editor .el-input__wrapper {
  background: var(--tech-bg-tertiary) !important;
  border-color: var(--tech-border) !important;
}

.el-picker__popper {
  background: rgba(15, 23, 42, 0.98) !important;
  backdrop-filter: blur(15px) !important;
  border: 2px solid var(--tech-border) !important;
  border-radius: 12px !important;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(59, 130, 246, 0.2) !important;
}

/* 树组件样式 */
.el-tree {
  background: transparent !important;
  color: var(--tech-text-primary) !important;
}

.el-tree-node__content {
  height: 40px !important;
  background: transparent !important;
  color: var(--tech-text-primary) !important;
  border-radius: 8px !important;
  margin-bottom: 2px !important;
  transition: all 0.2s ease !important;
  padding: 0 8px !important;
  border: 1px solid transparent !important;
  display: flex !important;
  align-items: center !important;
  line-height: 1 !important;
}

.el-tree-node__content:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(96, 165, 250, 0.1) 100%) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
  color: var(--tech-primary-light) !important;
  transform: translateX(2px) !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1) !important;
}

.el-tree-node__expand-icon {
  color: var(--tech-primary-light) !important;
  font-size: 14px !important;
  transition: all 0.2s ease !important;
}

.el-tree-node__expand-icon:hover {
  color: var(--tech-primary) !important;
  transform: scale(1.1) !important;
}

.el-tree-node__expand-icon.is-leaf {
  color: transparent !important;
}

.el-tree-node__label {
  font-weight: 500 !important;
  color: var(--tech-text-primary) !important;
  transition: color 0.2s ease !important;
}

.el-tree-node__content:hover .el-tree-node__label {
  color: var(--tech-primary-light) !important;
  font-weight: 600 !important;
}

/* 当前选中节点样式 */
.el-tree-node.is-current > .el-tree-node__content {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%) !important;
  border-color: rgba(34, 197, 94, 0.4) !important;
  color: #22c55e !important;
  font-weight: 600 !important;
  box-shadow:
    0 0 15px rgba(34, 197, 94, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.el-tree-node.is-current > .el-tree-node__content .el-tree-node__label {
  color: #22c55e !important;
  font-weight: 600 !important;
  text-shadow: 0 0 8px rgba(34, 197, 94, 0.3) !important;
}

.el-tree-node.is-current > .el-tree-node__content .el-tree-node__expand-icon {
  color: #22c55e !important;
}

/* 树选择器 */
.el-tree-select .el-input__wrapper {
  background: var(--tech-bg-tertiary) !important;
  border-color: var(--tech-border) !important;
}

.el-tree-select__popper {
  background: rgba(15, 23, 42, 0.98) !important;
  backdrop-filter: blur(15px) !important;
  border: 2px solid var(--tech-border) !important;
  border-radius: 12px !important;
}

/* 树选择器下拉面板中的树节点也需要垂直居中 */
.el-tree-select__popper .el-tree-node__content {
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  line-height: 1 !important;
  padding: 0 12px !important;
}

/* 级联选择器和其他下拉组件的选项垂直居中 */
.el-cascader-panel .el-cascader-node {
  height: 40px !important;
  line-height: 40px !important;
  display: flex !important;
  align-items: center !important;
  padding: 0 12px !important;
}

.el-dropdown-menu__item {
  height: 40px !important;
  line-height: 40px !important;
  display: flex !important;
  align-items: center !important;
  padding: 0 16px !important;
}

.el-autocomplete-suggestion li {
  height: 40px !important;
  line-height: 40px !important;
  display: flex !important;
  align-items: center !important;
  padding: 0 16px !important;
}

/* 标签 */
.el-tag {
  border-radius: 6px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 分页 */
.el-pagination {
  --el-pagination-bg-color: var(--tech-bg-tertiary);
  --el-pagination-text-color: var(--tech-text-primary);
  --el-pagination-border-radius: 8px;
}

.el-pagination .el-pager li {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid var(--tech-border);
  color: var(--tech-text-primary);
  border-radius: 6px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

.el-pagination .el-pager li:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: var(--tech-border-hover);
  color: var(--tech-primary-light);
  transform: translateY(-1px);
}

.el-pagination .el-pager li.is-active {
  background: linear-gradient(135deg, var(--tech-primary) 0%, var(--tech-primary-dark) 100%);
  border-color: var(--tech-primary);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Loading */
.el-loading-mask {
  background: rgba(15, 23, 42, 0.8) !important;
  backdrop-filter: blur(8px) !important;
}

.el-loading-spinner {
  color: var(--tech-primary-light) !important;
}

.el-loading-text {
  color: var(--tech-text-primary) !important;
  font-weight: 500;
}

/* 空状态 */
.el-empty {
  --el-empty-description-color: var(--tech-text-secondary);
  --el-empty-image-width: 120px;
}

.el-empty__description p {
  color: var(--tech-text-secondary) !important;
  font-size: 14px;
}

/* ===================
   保持原有样式
=================== */

.el-collapse {
  .collapse__title {
    font-weight: 600;
    padding: 0 8px;
    font-size: 1.2em;
    line-height: 1.1em;
  }
  .el-collapse-item__content {
    padding: 0 8px;
  }
}

.el-divider--horizontal {
  margin-bottom: 10px;
  margin-top: 10px;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

/* Dialog 覆盖层 */
.el-overlay {
  overflow: hidden;
  background: rgba(0, 0, 0, 0.85) !important;
  backdrop-filter: blur(8px) !important;

  .el-overlay-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    .el-dialog {
      margin: 0 auto !important;

      .el-dialog__body {
        padding: 15px !important;
        max-height: calc(90vh - 111px) !important;
        overflow-y: auto;
        overflow-x: hidden;
      }
    }
  }
}

/* 上传容器 */
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

/* 下拉菜单 */
.el-dropdown-menu {
  a {
    display: block;
  }
}

/* 日期范围选择器修复 */
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse > div > .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}

/* 内联表单样式 */
.el-form--inline .el-form-item__label {
  width: 68px;
}

.el-form--inline .el-select {
  width: 240px;
}

.el-form--inline .el-input {
  width: 240px;
}

/* 消息框样式 */
.el-message-box .el-message-box__message {
  word-break: break-word;
}

/* ===================
   时间选择器样式 (从 time-picker-fix.scss 整合)
=================== */

/* 时间选择器面板通用样式 */
.el-time-picker__popper,
.el-picker-panel {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(147, 197, 253, 0.3) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px) !important;
}

.el-time-panel {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(147, 197, 253, 0.3) !important;
  color: #e2e8f0 !important;
}

.el-time-panel__header {
  border-bottom-color: rgba(147, 197, 253, 0.2) !important;
  background: rgba(30, 41, 59, 0.5) !important;
}

.el-time-panel__content {
  background: transparent !important;
}

.el-time-spinner {
  background: transparent !important;
}

.el-time-spinner__wrapper {
  border-color: rgba(147, 197, 253, 0.2) !important;
}

.el-time-spinner__list {
  background: transparent !important;
}

.el-time-spinner__item {
  color: #e2e8f0 !important;
  background: transparent !important;
  height: 32px !important;
  line-height: 32px !important;
  font-size: 14px !important;
  transition: all 0.2s ease !important;
}

.el-time-spinner__item:hover {
  background: rgba(59, 130, 246, 0.2) !important;
  color: #ffffff !important;
}

.el-time-spinner__item.active {
  background: rgba(59, 130, 246, 0.3) !important;
  color: #ffffff !important;
  font-weight: 600 !important;
}

.el-time-spinner__item.disabled {
  color: #64748b !important;
  background: transparent !important;
  cursor: not-allowed !important;
}

/* 确保时间面板显示在正确的层级 */
.el-picker-panel {
  z-index: 9999 !important;
}

.el-time-panel__footer {
  border-top-color: rgba(147, 197, 253, 0.2) !important;
  background: rgba(30, 41, 59, 0.5) !important;
}

.el-time-panel__btn {
  color: #e2e8f0 !important;
  transition: color 0.2s ease !important;
}

.el-time-panel__btn:hover {
  color: #3b82f6 !important;
}

.el-time-panel__btn.confirm {
  color: #3b82f6 !important;
  font-weight: 600 !important;
}

/* 时间输入框样式 */
.el-time-picker .el-input__wrapper {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
}

.el-time-picker .el-input__wrapper:hover {
  border-color: rgba(96, 165, 250, 0.6) !important;
}

.el-time-picker .el-input__wrapper.is-focus {
  border-color: rgba(96, 165, 250, 0.8) !important;
  box-shadow: 0 0 0 1px rgba(96, 165, 250, 0.4) inset !important;
}

/* 日期选择器样式（如果有用到） */
.el-date-picker__popper,
.el-date-table {
  background: rgba(15, 23, 42, 0.95) !important;
  border: 1px solid rgba(147, 197, 253, 0.3) !important;
}

.el-date-table th,
.el-date-table td {
  color: #e2e8f0 !important;
  border-color: rgba(147, 197, 253, 0.1) !important;
}

.el-date-table td.today span {
  background: rgba(59, 130, 246, 0.3) !important;
  color: #ffffff !important;
}

.el-date-table td.current:not(.disabled) span {
  background: rgba(59, 130, 246, 0.5) !important;
  color: #ffffff !important;
}

/* 修复弹出层遮罩问题 */
.el-overlay {
  background: rgba(0, 0, 0, 0.5) !important;
}

/* 确保时间选择器在弹窗中正常显示 */
.el-dialog .el-time-picker__popper,
.el-dialog .el-picker-panel {
  z-index: 10000 !important;
}

/* ===================
   弹窗样式 (从 global-dialog.scss 和 dialog-performance.scss 整合)
=================== */

/* 全局覆盖Element UI弹窗样式 - 最高优先级 */
.el-overlay.tech-modal,
.el-overlay {
  background: rgba(0, 0, 0, 0.85) !important;
}

/* 全局弹窗样式 - 科技感设计 */
.el-dialog,
.el-overlay .el-overlay-dialog .el-dialog {
  background: linear-gradient(145deg, #1e293b 0%, #334155 50%, #1e293b 100%) !important;
  border: 2px solid rgba(59, 130, 246, 0.4) !important;
  border-radius: 16px !important;
  box-shadow:
    0 0 30px rgba(59, 130, 246, 0.3),
    0 0 60px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  margin: 0 !important;
  color: #e2e8f0 !important;
}

/* 弹窗头部样式 */
.el-dialog__header {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 50%, rgba(30, 41, 59, 0.95) 100%) !important;
  border-bottom: 2px solid rgba(59, 130, 246, 0.3) !important;
  color: #e2e8f0 !important;
  padding: 20px 24px !important;
}

.el-dialog__title {
  font-size: 18px !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
  color: #f1f5f9 !important;
}

.el-dialog__body {
  background: transparent !important;
  color: #e2e8f0 !important;
  padding: 24px !important;
}

/* 针对需要高性能的弹窗，可以添加此类名来禁用所有动画效果 */
.el-dialog.performance-mode {
  /* 强制GPU加速，创建复合图层 */
  will-change: transform, opacity;
  transform: translateZ(0);
  /* 简化阴影效果 */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;

  /* 禁用所有过渡效果 */
  * {
    transition: none !important;
    animation: none !important;
  }

  /* 移除所有模糊效果 */
  &,
  &::before,
  &::after {
    backdrop-filter: none !important;
    filter: none !important;
  }

  /* 移除装饰效果 */
  &::before {
    display: none !important;
  }

  /* 简化按钮悬停效果 */
  .el-button:hover {
    transform: none !important;
    box-shadow: none !important;
  }
}

/* 针对表格密集型弹窗的优化 */
.el-dialog.table-mode {
  /* 启用硬件加速 */
  transform: translateZ(0);

  /* 优化表格渲染 */
  .el-table {
    /* 启用复合图层 */
    will-change: scroll-position;
    transform: translateZ(0);

    /* 禁用表格行的悬停效果 */
    tbody tr:hover {
      background-color: transparent !important;
    }
  }
}

/* 针对表单密集型弹窗的优化 */
.el-dialog.form-mode {
  /* 禁用输入框动画 */
  .el-input__wrapper,
  .el-textarea__inner,
  .el-select__wrapper {
    transition: border-color 0.1s ease !important;
  }

  /* 简化聚焦效果 */
  .el-input__wrapper.is-focus,
  .el-textarea__inner:focus {
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.3) !important;
  }
}

.el-dialog:not(.is-fullscreen) {
  margin-top: 6vh !important;
}

.el-dialog.scrollbar .el-dialog__body {
  overflow: auto;
  overflow-x: hidden;
  max-height: 70vh;
  padding: 10px 20px 0;
}

/* ===================
   表格和表单样式 (从 ruoyi.scss 整合)
=================== */

.el-form .el-form-item__label {
  font-weight: 700;
}

.el-table {
  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    th {
      word-break: break-word;
      background-color: var(--table-header-bg, #f5f7fa) !important;
      color: var(--table-header-text-color, #606266);
      height: 40px !important;
    }
  }
  .el-table__body-wrapper {
    .el-button [class*='el-icon-'] + span {
      margin-left: 1px;
    }
  }
}

@media (max-width: 768px) {
  .pagination-container .el-pagination > .el-pagination__jump {
    display: none !important;
  }
  .pagination-container .el-pagination > .el-pagination__sizes {
    display: none !important;
  }
}

.el-table .fixed-width .el-button--small {
  padding-left: 0;
  padding-right: 0;
  width: inherit;
}

/** 表格更多操作下拉样式 */
.el-table .el-dropdown-link {
  cursor: pointer;
  color: #409eff;
  margin-left: 10px;
}

.el-table .el-dropdown,
.el-icon-arrow-down {
  font-size: 12px;
}

.el-tree-node__content > .el-checkbox {
  margin-right: 8px;
}

.el-card__header {
  padding: 14px 15px 7px !important;
  min-height: 40px;
}

.el-card__body {
  padding: 15px 20px 20px 20px !important;
}

/* cyan button styles moved to ruoyi.scss to avoid duplication */

/* ===================
   侧边栏菜单样式 (从 sidebar.scss 整合)
=================== */

.sidebar-container {
  .el-scrollbar__bar.is-vertical {
    right: 0;
  }

  .el-scrollbar {
    height: 100%;
  }

  &.has-logo {
    .el-scrollbar {
      height: calc(100% - 50px);
    }
  }

  .el-menu {
    border: none;
    height: 100%;
    width: 100% !important;
  }

  .el-menu-item,
  .menu-title {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  // 优化菜单项高度和内边距
  .el-menu-item {
    height: 48px !important;
    line-height: 48px !important;
  }

  .el-sub-menu__title {
    height: 50px !important;
    line-height: 50px !important;
    padding-left: 20px !important;
  }

  .el-menu-item .el-menu-tooltip__trigger {
    padding-left: 20px !important;
  }

  .el-sub-menu .el-menu-item {
    height: 50px !important;
    line-height: 50px !important;
    padding-left: 45px !important;
    min-width: 200px;
  }

  &.openSidebar {
    .el-sub-menu__title {
      color: #bfcbd9;
    }

    &.theme-dark .el-sub-menu__title {
      color: #bfcbd9;
    }

    .el-sub-menu__title {
      color: #999093;
    }

    & .theme-dark .is-active > .el-sub-menu__title {
      color: #ffffff !important;
    }

    & .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      min-width: 200px;
      background-color: var(--el-color-primary-light-9) !important;
      color: var(--el-text-color-primary) !important;
    }

    & .theme-dark .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .theme-dark .el-sub-menu .el-menu-item {
      background-color: #001528 !important;
    }

    & .theme-dark .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .theme-dark .el-menu-item {
      color: #bfcbd9;
    }

    & .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .el-menu-item {
      color: var(--el-text-color-primary) !important;
    }

    .hideSidebar {
      .el-tooltip {
        display: none;
      }
    }
  }

  &.hideSidebar {
    .el-sub-menu {
      overflow: hidden;

      & > .el-sub-menu__title {
        padding: 0 !important;
        text-align: center;
        text-overflow: clip;
      }

      .el-sub-menu__icon-arrow {
        display: none;
      }
    }

    .el-menu--collapse {
      .el-sub-menu {
        & > .el-sub-menu__title {
          & > span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }
}

.el-menu--collapse .el-menu .el-sub-menu {
  min-width: 200px !important;
}

.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 16px;
    }
    .el-sub-menu__title {
      .svg-icon {
        margin-right: 12px;
        margin-left: -2px;
      }
    }
  }
}

/* ===================
   模板预览组件深色主题样式
=================== */

/* TemplatePreview 组件的深色主题覆盖 */
.template-preview .schedule-view {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%) !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  color: #e2e8f0 !important;
}

.template-preview .schedule-view h4 {
  color: #e2e8f0 !important;
  text-shadow: 0 0 8px rgba(59, 130, 246, 0.3) !important;
}

.template-preview .config-summary {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%) !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  color: #e2e8f0 !important;
}

.template-preview .config-summary h4 {
  color: #e2e8f0 !important;
  text-shadow: 0 0 8px rgba(59, 130, 246, 0.3) !important;
}

.template-preview .summary-section {
  border-bottom-color: rgba(59, 130, 246, 0.2) !important;
}

.template-preview .period-summary {
  color: #94a3b8 !important;
}

/* 优化 preview-header 背景色匹配弹窗 */
.template-preview .preview-header {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(59, 130, 246, 0.2) 50%, rgba(30, 41, 59, 0.95) 100%) !important;
  border: 1px solid rgba(59, 130, 246, 0.4) !important;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.2) !important;
}

/* 时间轴容器深色主题 - 优化布局避免拥挤 */
.template-preview .timeline-container {
  background: rgba(15, 23, 42, 0.6) !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  height: 400px !important; /* 增加高度 */
  padding: 10px !important;
}

.template-preview .timeline-hour-marks {
  border-bottom-color: rgba(59, 130, 246, 0.2) !important;
  height: 50px !important; /* 增加高度 */
}

.template-preview .hour-mark {
  color: #94a3b8 !important;
  border-left-color: rgba(59, 130, 246, 0.2) !important;
  font-size: 11px !important;
  padding: 10px 2px !important;
  line-height: 1.2 !important;
}

.template-preview .timeline-schedule {
  height: 340px !important; /* 增加高度 */
  padding: 20px 0 !important;
}

.template-preview .schedule-point {
  margin-bottom: 15px !important; /* 增加点之间的间距 */
}

.template-preview .point-time {
  color: #e2e8f0 !important;
  font-size: 11px !important;
  margin-top: 8px !important; /* 增加时间文字与点的间距 */
  background: rgba(30, 41, 59, 0.8) !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  white-space: nowrap !important;
}

.template-preview .point-dot {
  width: 10px !important; /* 稍微增大点的大小 */
  height: 10px !important;
  margin-bottom: 8px !important;
}

/* 优化时间轴视图整体布局 */
.template-preview .timeline-view {
  height: 600px !important; /* 增加整体高度 */
  overflow-y: auto !important;
}

.template-preview .timeline-legend {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.6) 0%, rgba(51, 65, 85, 0.4) 100%) !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  color: #e2e8f0 !important;
  margin-top: 20px !important; /* 增加与时间轴的间距 */
}

.template-preview .timeline-legend h5 {
  color: #e2e8f0 !important;
}

.template-preview .legend-items {
  gap: 20px !important; /* 增加图例项之间的间距 */
}

.template-preview .legend-item {
  font-size: 13px !important;
  gap: 8px !important;
}

.template-preview .legend-dot {
  width: 14px !important; /* 稍微增大图例点 */
  height: 14px !important;
}

/* ===================
   其他组件样式整合
=================== */

/* 从 variables.module.scss 整合 */
.el-tree-node__content {
  height: 40px;
}

.el-button--primary {
  background: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

.el-switch {
  --el-switch-on-color: var(--el-color-primary);
}

.el-tag--primary {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

/* 从 vehicle-monitor-performance.scss 整合 */
.vehicle-monitor-fullscreen.performance-mode {
  .el-select {
    .el-input__wrapper {
      transition: none !important;
    }

    .el-input__inner {
      transition: none !important;
    }
  }
}
