<!-- 智能密度模式参数配置组件 -->
<template>
  <div class="density-config">
    <el-form ref="formRef" :model="config" label-width="120px" size="large">
      <div class="config-section">
        <h4 class="section-title">
          <el-icon><Clock /></el-icon>
          基本设置
        </h4>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="模板名称" required>
              <el-input v-model="config.templateName" placeholder="请输入模板名称" maxlength="50" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="config-section">
        <h4 class="section-title">
          <el-icon><TrendCharts /></el-icon>
          密度等级选择
        </h4>
        <div class="density-levels">
          <div
            v-for="level in densityLevels"
            :key="level.value"
            class="density-card"
            :class="{ 'selected': config.densityLevel === level.value }"
            @click="config.densityLevel = level.value"
          >
            <div class="level-header">
              <div class="level-icon" :style="{ backgroundColor: level.color }">
                <el-icon :size="24"><component :is="level.icon" /></el-icon>
              </div>
              <div class="level-info">
                <h5>{{ level.label }}</h5>
                <p>{{ level.description }}</p>
              </div>
            </div>
            <div class="level-details">
              <div class="detail-item">
                <span class="label">间隔范围：</span>
                <span class="value">{{ level.intervalRange }}</span>
              </div>
              <div class="detail-item">
                <span class="label">适用场景：</span>
                <span class="value">{{ level.scenario }}</span>
              </div>
              <div class="detail-item">
                <span class="label">预计班次：</span>
                <span class="value">{{ level.estimatedTrips }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="config-section" v-if="config.densityLevel">
        <h4 class="section-title">
          <el-icon><Timer /></el-icon>
          运营时间
        </h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="首班车时间" required>
              <el-time-picker v-model="config.firstBusTime" placeholder="选择首班车时间" value-format="HH:mm" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="末班车时间" required>
              <el-time-picker v-model="config.lastBusTime" placeholder="选择末班车时间" value-format="HH:mm" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="config-section" v-if="config.densityLevel">
        <h4 class="section-title">
          <el-icon><Setting /></el-icon>
          智能调优
        </h4>
        <div class="optimization-options">
          <el-checkbox-group v-model="config.optimizations">
            <el-checkbox label="peakOptimization">高峰时段自动加密</el-checkbox>
            <el-checkbox label="weatherAdjustment">天气调整（雨雪天加密）</el-checkbox>
            <el-checkbox label="eventOptimization">节假日自动调整</el-checkbox>
            <el-checkbox label="realTimeAdjustment">实时客流调整</el-checkbox>
          </el-checkbox-group>
        </div>

        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="12">
            <el-form-item label="调整幅度">
              <el-slider v-model="config.adjustmentRange" :min="5" :max="50" :step="5" show-stops show-input :format-tooltip="formatTooltip" />
              <div class="form-tip">智能调整的最大幅度（百分比）</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单程时间">
              <el-input-number v-model="config.singleTripTime" :min="10" :max="300" :step="5" style="width: 100%" />
              <div class="form-tip">用于计算车辆配车数量</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="preview-section" v-if="config.densityLevel">
        <h4 class="section-title">
          <el-icon><DataAnalysis /></el-icon>
          智能预测结果
        </h4>
        <div class="prediction-grid">
          <div class="prediction-card">
            <div class="card-header">
              <h5>基础配置</h5>
              <el-tag type="info">{{ selectedLevel?.label }}</el-tag>
            </div>
            <div class="prediction-stats">
              <div class="stat-item">
                <span class="stat-value">{{ baseTrips }}</span>
                <span class="stat-label">基础班次</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ baseInterval }}</span>
                <span class="stat-label">平均间隔</span>
              </div>
            </div>
          </div>

          <div class="prediction-card">
            <div class="card-header">
              <h5>智能优化后</h5>
              <el-tag type="success">推荐配置</el-tag>
            </div>
            <div class="prediction-stats">
              <div class="stat-item">
                <span class="stat-value">{{ optimizedTrips }}</span>
                <span class="stat-label">优化班次</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ estimatedVehicles }}</span>
                <span class="stat-label">建议配车</span>
              </div>
            </div>
          </div>

          <div class="prediction-card">
            <div class="card-header">
              <h5>预期效果</h5>
              <el-tag type="warning">数据预测</el-tag>
            </div>
            <div class="prediction-stats">
              <div class="stat-item">
                <span class="stat-value">{{ efficiencyGain }}%</span>
                <span class="stat-label">效率提升</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ costReduction }}%</span>
                <span class="stat-label">成本节约</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="config-section">
        <h4 class="section-title">
          <el-icon><EditPen /></el-icon>
          其他设置
        </h4>
        <el-form-item label="模板描述">
          <el-input v-model="config.description" type="textarea" :rows="3" placeholder="请输入模板描述（可选）" maxlength="200" show-word-limit />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Clock, TrendCharts, Timer, Setting, DataAnalysis, EditPen } from '@element-plus/icons-vue';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue']);

// Form reference
const formRef = ref();

const config = ref({
  templateName: '',
  routeId: null,
  densityLevel: '',
  firstBusTime: '06:00',
  lastBusTime: '22:00',
  optimizations: ['peakOptimization'],
  adjustmentRange: 20,
  singleTripTime: 60,
  description: '',
  ...props.modelValue
});

const routeOptions = ref([
  { routeId: 1, routeName: '1路' },
  { routeId: 2, routeName: '2路' },
  { routeId: 3, routeName: '3路' },
  { routeId: 4, routeName: '4路' }
]);

const densityLevels = ref([
  {
    value: 'high',
    label: '高密度',
    description: '适合核心商业区、交通枢纽等客流密集区域',
    color: '#ef4444',
    icon: 'TrendCharts',
    intervalRange: '3-8分钟',
    scenario: '商业区、枢纽站',
    estimatedTrips: '150-200班'
  },
  {
    value: 'medium',
    label: '中密度',
    description: '适合居民区、一般商业区等中等客流区域',
    color: '#f59e0b',
    icon: 'Timer',
    intervalRange: '8-15分钟',
    scenario: '居民区、办公区',
    estimatedTrips: '80-120班'
  },
  {
    value: 'low',
    label: '低密度',
    description: '适合郊区、工业区等客流较少的区域',
    color: '#10b981',
    icon: 'Clock',
    intervalRange: '15-30分钟',
    scenario: '郊区、工业区',
    estimatedTrips: '40-80班'
  }
]);

// 当前选择的密度等级
const selectedLevel = computed(() => {
  return densityLevels.value.find((level) => level.value === config.value.densityLevel);
});

// 基础班次计算
const baseTrips = computed(() => {
  if (!config.value.densityLevel || !config.value.firstBusTime || !config.value.lastBusTime) {
    return '--';
  }

  const level = selectedLevel.value;
  if (!level) return '--';

  const [firstHour, firstMin] = config.value.firstBusTime.split(':').map(Number);
  const [lastHour, lastMin] = config.value.lastBusTime.split(':').map(Number);
  const totalMinutes = lastHour * 60 + lastMin - (firstHour * 60 + firstMin);

  if (totalMinutes <= 0) return '--';

  // 根据密度等级确定基础间隔
  const baseInterval = level.value === 'high' ? 6 : level.value === 'medium' ? 12 : 20;
  return Math.floor(totalMinutes / baseInterval) + 1;
});

// 基础间隔
const baseInterval = computed(() => {
  if (!selectedLevel.value) return '--';
  const level = selectedLevel.value;
  const baseInterval = level.value === 'high' ? 6 : level.value === 'medium' ? 12 : 20;
  return baseInterval + '分钟';
});

// 优化后班次
const optimizedTrips = computed(() => {
  if (!baseTrips.value || baseTrips.value === '--') return '--';

  const base = parseInt(baseTrips.value);
  const adjustmentFactor = 1 + (config.value.adjustmentRange / 100) * 0.5; // 优化通常增加班次
  const optimized = Math.floor(base * adjustmentFactor);

  return optimized;
});

// 建议配车数
const estimatedVehicles = computed(() => {
  if (!config.value.singleTripTime || !selectedLevel.value) return '--';

  const level = selectedLevel.value;
  const baseInterval = level.value === 'high' ? 6 : level.value === 'medium' ? 12 : 20;
  const roundTripTime = config.value.singleTripTime * 2;

  return Math.ceil(roundTripTime / baseInterval);
});

// 效率提升
const efficiencyGain = computed(() => {
  if (!config.value.optimizations.length) return 0;

  let gain = 0;
  config.value.optimizations.forEach((opt) => {
    switch (opt) {
      case 'peakOptimization':
        gain += 8;
        break;
      case 'weatherAdjustment':
        gain += 5;
        break;
      case 'eventOptimization':
        gain += 6;
        break;
      case 'realTimeAdjustment':
        gain += 12;
        break;
    }
  });

  return Math.min(gain, 35); // 最大35%
});

// 成本节约
const costReduction = computed(() => {
  return Math.floor(efficiencyGain.value * 0.6); // 效率提升的60%转化为成本节约
});

// 格式化滑块提示
function formatTooltip(val) {
  return `${val}%`;
}

// 监听配置变化
watch(
  config,
  (newValue) => {
    emit('update:modelValue', newValue);
  },
  { deep: true }
);
</script>

<style scoped>
.density-config {
  padding: 20px 0;
}

.config-section {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.6) 0%, rgba(30, 41, 59, 0.4) 100%);
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
}

.density-levels {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.density-card {
  background: linear-gradient(135deg, rgba(51, 65, 85, 0.6) 0%, rgba(71, 85, 105, 0.4) 100%);
  border: 2px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.density-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.density-card.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.level-header {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.level-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.level-info h5 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #1f2937;
}

.level-info p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.level-details {
  border-top: 1px solid #f3f4f6;
  padding-top: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-item .label {
  color: #6b7280;
}

.detail-item .value {
  color: #1f2937;
  font-weight: 500;
}

.optimization-options {
  background: linear-gradient(135deg, rgba(51, 65, 85, 0.4) 0%, rgba(71, 85, 105, 0.2) 100%);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

.preview-section {
  background: transparent;
  color: #e2e8f0;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.preview-section .section-title {
  color: #e2e8f0;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
}

.prediction-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.prediction-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-header h5 {
  margin: 0;
  color: white;
  font-size: 16px;
}

.prediction-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
}

.form-tip {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

/* Element Plus 深色主题样式覆盖 */
:deep(.el-input .el-input__count .el-input__count-inner) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #94a3b8 !important;
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-form-item__label) {
  color: #e2e8f0 !important;
}

:deep(.el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  box-shadow: none !important;
}

:deep(.el-input__inner) {
  color: #e2e8f0 !important;
}

:deep(.el-input__inner::placeholder) {
  color: #94a3b8 !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

:deep(.el-time-picker .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

:deep(.el-input-number .el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
}

:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  background: rgba(30, 41, 59, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e2e8f0 !important;
}

:deep(.el-textarea__inner) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e2e8f0 !important;
}
</style>
