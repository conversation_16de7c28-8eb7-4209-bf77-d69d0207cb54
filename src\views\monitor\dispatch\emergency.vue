<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>紧急调度</span>
          <el-button @click="$router.go(-1)">返回</el-button>
        </div>
      </template>

      <div class="emergency-content">
        <el-alert
          title="紧急调度"
          description="处理紧急情况的调度指令，如加速、减速等。"
          type="danger"
          :closable="false"
          style="margin-bottom: 20px"
        />

        <el-empty description="紧急调度功能开发中" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="DispatchEmergency"></script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.emergency-content {
  padding: 20px;
}
</style>
