<template>
  <div class="screen-container">
    <div class="screen-left data-content-level">
      <div class="screen-frame">
        <!-- 组件封装插入 -->
        <div class="screen-frame-component">
          <div style="margin: 5px auto">
            <el-date-picker
              style="width: 100%;"
              :clearable="false"
              v-model="dateRange"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
            ></el-date-picker>
          </div>

          <el-input
            placeholder="输入关键字进行过滤"
            v-model="filterText"
            clearable
          >
          </el-input>
          <el-scrollbar wrap-class="scrollbar-wrapper" style="height: 80%;">
            <el-tree
              :highlight-current="true"
              class="filter-tree"
              icon-class=""
              :data="carTree"
              :props="defaultProps"
              :filter-node-method="filterNode"
              ref="tree"
              @node-click="handleNodeClick"
              node-key="id"
            >
            </el-tree>
          </el-scrollbar>
        </div>
      </div>
    </div>
    <!-- 地图层 -->
    <div id="map_container" class="map_container"></div>
    <div class="screen-center">
    </div>

    <!--  悬浮按钮  -->
    <div class="floating_slider" style="left: 65%">
      <el-slider :disabled="restAble" :show-tooltip="false" :min="0.01" :max="99" :step="0.01" style="width: 540px"
                 v-model="sliderValue" @change="sliderHandle"
      ></el-slider>
    </div>
    <!--  悬浮按钮  -->
    <div class="floating_buttons">
      <el-button-group>
        <el-button @click="speed" :disabled="!marker">
          <i class="el-icon-odometer"></i>{{ durationName }}
        </el-button>
        <el-button @click="startAnimation()" :disabled="playAble">
          <i class="el-icon-video-play"></i>开始
        </el-button>
        <el-button @click="pauseAnimation()" :disabled="stopAble">
          <i class="el-icon-video-pause"></i>暂停
        </el-button>
        <el-button @click="resumeAnimation()" :disabled="!marker">
          <i class="el-icon-video-play"></i>继续
        </el-button>
        <el-button @click="stopAnimation()" :disabled="restAble">
          <i class="el-icon-refresh-left"></i>停止
        </el-button>
        <el-button type="warning" plain @click="handleExport" :disabled="restAble">
          <i class="el-icon-download"></i>导出
        </el-button>
        <el-button @click="startRule">
          <i class="el-icon-place"></i>测量工具:{{ toolName }}
        </el-button>
      </el-button-group>
    </div>

    <div class="floating_table">
      <el-table :data="tableData" height="100%">
        <el-table-column align="center" prop="deviceTime" label="时间" width="180"></el-table-column>
        <el-table-column align="center" prop="carNo" label="车牌号" width="180"></el-table-column>
        <el-table-column align="center" prop="speed" label="速度(km/h)"></el-table-column>
        <el-table-column align="center" prop="mileage" label="轨迹行驶里程(km)"></el-table-column>
        <el-table-column align="center" prop="carMileage" label="车辆上报里程(km)"></el-table-column>
        <el-table-column align="center" prop="position" label="经纬度"></el-table-column>
      </el-table>
    </div>
    <!-- 地图弹窗DOM -->
    <div style="display: none">
      <div id="windowContent">

      </div>
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
      width="35%"
    >
      <el-descriptions :title="pointInfo.carNo" :column="2"
                       border
      >
        <el-descriptions-item label="时间">{{ pointInfo.deviceTime }}</el-descriptions-item>
        <el-descriptions-item label="经纬度">{{ pointInfo.position }}</el-descriptions-item>
        <el-descriptions-item label="速度(km/h)">{{ pointInfo.speed }}</el-descriptions-item>
        <el-descriptions-item label="车辆上报里程(km)">{{ pointInfo.carMileage }}</el-descriptions-item>
        <el-descriptions-item label="轨迹行驶里程(km)">{{ pointInfo.mileage }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

  </div>
</template>

<script>


import { getCarTree, getTrack } from '@/api/biz/overview'

export default {
  name: 'index',
  components: {},
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  data() {
    return {
      tableData: [],
      mouseTool: undefined,
      startFlag: false,
      sliderValue: 0.01,

      dialogVisible: false,
      pointInfo: {
        carNo: '',
        deviceTime: '',
        mileage: '',
        speed: '',
        position: undefined,
        carMileage: ''
      },
      // 搜索条件
      queryParams: {
        carId: null,
        carNo: null,
        startTime: '',
        endTime: ''
      },
      playAble: true,
      stopAble: true,
      restAble: true,
      filterText: null,
      marker: null,
      polyline: null,
      passedPolyline: null,
      lineArr: [],
      carTree: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },

      // 位置信息列表
      positionList: [],

      // 日期选择相关
      dateRange: [this.formatDate(new Date()), this.formatDate(new Date())],
      pickerOptions: {
        /*disabledDate(time) {
          // 获取当前日期
          const today = new Date();
          // 计算三天前的日期
          const threeDaysAgo = new Date(today);
          threeDaysAgo.setDate(today.getDate() - 3);

          // 如果选择的日期在三天前之前，或者在未来，则禁用
          return time.getTime() < threeDaysAgo.getTime() || time.getTime() > today.getTime();
        }*/
      },

      baseImgUrl: process.env.VUE_APP_BASE_API,

      passedPathIndex: 0,
      linePath: [],
      passedPath: [],
      duration: 200,
      durations: [
        {
          v: 400,
          k: 'x0.5'
        },
        {
          v: 200,
          k: 'x1.0'
        }, {
          v: 100,
          k: 'x2.0'
        }, {
          v: 50,
          k: 'x4.0'
        }
      ],
      durationIndex: 1,
      durationName: 'x1.0',
    }
  },
  created() {
    this.getCarTree()
  },
  computed: {
    toolName() {
      return this.startFlag ? '开' : '关'
    }
  },
  mounted() {
    document.title = '轨迹回放'
    setTimeout(() => {
      this.createMap()
      if (this.$route.query && this.$route.query.id) {
        this.queryParams.carId = this.$route.query.id
        this.getTrack()
      }
    }, 150)
  },
  methods: {

    speed() {
      this.durationIndex++
      let duration = this.durations[this.durationIndex % this.durations.length]
      this.duration = duration.v
      this.durationName = duration.k

      const newSliderValue = this.sliderValue;
      let index = this.lineArr.length * (newSliderValue / 100)
      if (this.playAble) {
        this.pauseAnimation() // 先暂停车
      }
      //this.marker.setPosition(this.lineArr[index]); // 进度条停止移动后，重新设置marker点位置
      this.passedPath = this.lineArr.filter((item, ind) => index > ind)//已经拖动的部分也要保存，不然已经运动的轨迹无法改变颜色
      this.passedPolyline.setPath(this.passedPath) // 设置已经拖动的路线
      this.linePath = this.lineArr.slice(index) // 拖动进度条后，接下来点位要运动的轨迹就不是整条了，要截取剩余的轨迹
      let _this = this
      setTimeout(()=>{
        _this.startAnimation()
      },500)
    },

    sliderHandle(value) {
      let index = this.lineArr.length * (value / 100)
      if (this.playAble) {
        this.pauseAnimation() // 先暂停车
      }
      //this.marker.setPosition(this.lineArr[index]); // 进度条停止移动后，重新设置marker点位置

      this.passedPath = this.lineArr.filter((item, ind) => index > ind)//已经拖动的部分也要保存，不然已经运动的轨迹无法改变颜色
      this.passedPolyline.setPath(this.passedPath) // 设置已经拖动的路线
      this.linePath = this.lineArr.slice(index) // 拖动进度条后，接下来点位要运动的轨迹就不是整条了，要截取剩余的轨迹
      this.startAnimation()
      this.pauseAnimation()
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('/biz/track-playback/export', {
        ...this.queryParams
      }, `终端定位数据报表${new Date().getTime()}.xlsx`)
    },
    // 车辆树筛选
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    handleNodeClick(data) {
      this.tableData = []
      this.sliderValue = 0
      this.passedPath = []
      this.linePath = this.lineArr
      if (data.flag) {
        if (this.marker) {
          this.marker.stopMove()
        }
        this.map.clearMap()
        this.queryParams.carId = data.id
        this.getTrack()
      }
    },

    // 地图图标点击事件
    markerClick(e) {
      // 获取 Marker 的 extData，这里假设 extData 包含了信息
      this.pointInfo = e.target.getExtData()
      this.dialogVisible = true
    },

    getTrack() {

      if (Array.isArray(this.dateRange) && this.dateRange.length === 0) {
        this.$message.warning('请选择回放时间段！')
        return
      }
      this.queryParams.startTime = this.dateRange[0]
      this.queryParams.endTime = this.dateRange[1]
      getTrack(this.queryParams).then(res => {
        this.pointInfo.carNo = res.data.carNo
        // 获取经纬度数组
        if (!res.data.coordData || res.data.coordData.length === 0) {
          this.$message.warning('该车辆当前时间段暂无轨迹信息！')
          return
        }
        this.lineArr = res.data.coordData.map(item => item.coord)
        this.linePath = res.data.coordData.map(item => item.coord)
        let that = this
        this.marker = new AMap.Marker({
          map: that.map,
          position: that.lineArr[0],
          icon: 'http://111.75.158.241:9033/file/image/sys/5cd24acaf458d00f4b063cef',
          offset: new AMap.Pixel(-13, -26)
        })
        that.map.setCenter(that.lineArr[0], false)
        // 绘制轨迹
        this.polyline = new AMap.Polyline({
          map: that.map,
          path: that.lineArr,
          showDir: true,
          strokeColor: '#28F',  //线颜色
          // strokeOpacity: 1,     //线透明度
          strokeWeight: 6      //线宽
          // strokeStyle: "solid"  //线样式
        })

        this.passedPolyline = new AMap.Polyline({
          map: that.map,
          strokeColor: '#AF5',  //线颜色
          strokeWeight: 6      //线宽
        })

        let timeArr = res.data.coordData.map(item => item.deviceTime)
        let mileage = res.data.coordData.map(item => item.mileage)
        let speeds = res.data.coordData.map(item => item.speed)
        let startOne = mileage[0]
        let num = 10
        for (let i = 0; i < this.lineArr.length; i++) {
          let item = that.lineArr[i]
          if (num === 10) {
            let marker = new AMap.Marker({
              position: item,
              icon:new AMap.Icon({
                //image: 'img/icon/'+item.mark,
                size: new AMap.Size(20, 20),  //图标所处区域大小
                imageSize: new AMap.Size(20,20) //图标大小
              }),
              offset: new AMap.Pixel(-5, -18)
            })
            marker.on('click', that.markerClick)
            let extData = {
              carNo: res.data.carNo,
              speed: speeds[i],
              mileage: mileage[i] - startOne,
              deviceTime: timeArr[i],
              position: item[0] + ',' + item[1],
              carMileage: mileage[i]
            }
            that.tableData.push(extData)
            marker.setExtData(extData) //给标注添加信息，点击时可以获取
            marker.setMap(that.map)
            num = 0
          }
          num++
        }

        let index = 100 / this.lineArr.length
        let lastIndex = 0
        this.marker.on('moving', function(e) {
          that.passedPathIndex = e.passedPath.length - 1
          if (lastIndex !== that.passedPathIndex) {
            that.sliderValue += index
          }
          lastIndex = that.passedPathIndex
          that.passedPolyline.setPath([...that.passedPath, ...e.passedPath])
        })
        this.playAble = false
        this.restAble = false
        this.map.setFitView()
      })
    },

    // 创建地图
    createMap() {
      //填入地图容器div的id
      this.map = new AMap.Map('map_container', {
        zoom: 13,	//设置地图的缩放级别
        zooms: [4, 30],	//设置地图的缩放级别区间
        center: [113.22, 22.93],	//设置地图中心点坐标
        mapStyle: 'amap://styles/blue',		//设置地图的显示样式，更改darkblue为你想要的样式
        features: ['road', 'point']	//设置地图的底图元素，缺少则显示全部元素，bg区域面、point兴趣点、road道路及道路标注、building建筑物
      })
      // 鼠标工具
      this.mouseTool = new AMap.MouseTool(this.map)
    },
    startRule() {
      this.startFlag = !this.startFlag
      if (!this.startFlag) {
        this.mouseTool.close(true)//关闭，并清除覆盖物
      } else {
        this.mouseTool.rule({
          strokeColor: '#80d8ff',
          fillColor: '#80d8ff',
          fillOpacity: 0.3
          //同 Polygon 的 Option 设置
        })
      }
    },

    // 底部按钮点击事件
    selectButton(type) {

    },

    // 重置状态 清空地图标点 选中等
    resetALL() {
    },

    /*lineRelay() {
      let _this = this
      _this.map.clearMap()
      this.marker = new AMap.Marker({
        map: _this.map,
        position: [116.478935, 39.997761],
        icon: 'http://111.75.158.241:9033/file/image/sys/5cb1a751f458d007f41b97a7',
        offset: new AMap.Pixel(-13, -26)
      })

      // 绘制轨迹
      this.polyline = new AMap.Polyline({
        map: _this.map,
        path: _this.lineArr,
        showDir: true,
        strokeColor: '#28F',  //线颜色
        // strokeOpacity: 1,     //线透明度
        strokeWeight: 6      //线宽
        // strokeStyle: "solid"  //线样式
      })

      this.passedPolyline = new AMap.Polyline({
        map: _this.map,
        strokeColor: '#AF5',  //线颜色
        strokeWeight: 6      //线宽
      })

      this.marker.on('moving', function(e) {
        _this.passedPolyline.setPath(e.passedPath)
        _this.map.setCenter(e.target.getPosition(), true)
      })

      this.map.setFitView()
    },*/

    // 获取车辆树
    getCarTree() {
      this.filterText = ''
      getCarTree().then(res => {
        this.carTree = res.data
        setTimeout(() => {
          this.filterText = this.$route.query.carNo
          this.$refs.tree.filter(this.$route.query.carNo)
          this.$refs.tree.setCurrentKey(this.$route.query.id)
        }, 300)
      })
    },

    /* 车辆轨迹控制相关 */
    // 播放
    startAnimation() {
      let _this = this
      this.stopAble = false
      this.playAble = true
      this.marker.moveAlong(_this.linePath, {
        // 每一段的时长
        duration: _this.duration,//可根据实际采集时间间隔设置
        // JSAPI2.0 是否延道路自动设置角度在 moveAlong 里设置
        autoRotation: true
      })
    },
    // 暂停
    pauseAnimation() {
      this.marker.pauseMove()
      this.playAble = true
      this.stopAble = true
    },
    // 继续
    resumeAnimation() {
      this.marker.resumeMove()
      this.stopAble = false
    },
    // 停止
    stopAnimation() {
      this.marker.stopMove()
      this.map.clearMap()
      this.getTrack()
      this.tableData = []
      this.sliderValue = 0
      this.passedPath = []
      this.linePath = this.lineArr
    },

    getStartOfToday() {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      return this.formatDate(today)
    },
    getStartOfTomorrow() {
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      tomorrow.setHours(0, 0, 0, 0)
      return this.formatDate(tomorrow)
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }

  }
}
</script>

<style lang="scss" scoped>
.floating_buttons {
  position: absolute;
  z-index: 999;
  bottom: 28%;
  width: 60%;
  left: 72%; /* 将左边界置于容器的中心位置 */
  transform: translateX(-50%); /* 水平居中 */
  display: flex;
  justify-content: space-between;
  padding: 1%;
}


.floating_slider {
  position: absolute;
  z-index: 999;
  bottom: 32%;
  width: 40%;
  left: 55%; /* 将左边界置于容器的中心位置 */
  transform: translateX(-50%); /* 水平居中 */
  display: flex;
  justify-content: space-between;
  padding: 1%;
}

.floating_table {
  position: absolute;
  z-index: 999;
  bottom: 0px;
  height: 28%;
  width: 80%;
  left: 60%; /* 将左边界置于容器的中心位置 */
  transform: translateX(-50%); /* 水平居中 */
  display: flex;
  justify-content: space-between;
}

.screen-container {
  width: 100%;
  height: 100%;
  // background-color: antiquewhite;
  display: flex;
  justify-content: space-between;
}

.map_container {
  width: 80%;
  min-height: 100vh;
  background: rgba(3, 52, 71);
  position: absolute;
  top: 0;
  left: 20%;
  z-index: 1;
}

.screen-left {
  width: 20%;
  height: 100%;
  // background-color: aqua;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .screen-frame {
    width: 100%;
    display: flex;
    flex-direction: column;

    .img_content {
      display: flex;
      justify-content: center; /* 水平居中 */
      align-items: center; /* 垂直居中 */
      height: 100%; /* 设置高度以充满整个视口 */
      .img_text {
        position: absolute;
        font-size: 16px;
        font-weight: 800;
        color: #30d9f5;
        top: 20%;

      }
    }

    .details_num {
      padding: 10% 2%;
      height: 100%;

      .color-square {
        width: 5px;
        height: 5px;
        margin-right: 10px;
        //border-radius: 2px;
        display: inline-block;
        align-self: center; /* 垂直居中 */
      }

      .details_info {
        width: 100%;
        height: 33%;
        display: flex;
        align-items: center; /* 垂直居中 */
        font-size: 17px;
        color: #FFFFFF;
        text-align: left;
        font-weight: bold;
      }
    }


    &:nth-child(1) {
      height: 99%;

      .status-container {

      }

      .status-block {
        display: flex;
        flex-direction: column;
        align-items: flex-start; /* 左对齐 */
        margin-bottom: 20px;
      }

      .status-block__title {
        font-size: 18px;
        margin-bottom: 10px;
        font-weight: bold;
        color: #FFFFFF;
      }

      .status-block__counts {
        display: flex;

        width: 100%;
      }

      .status-count {
        font-size: 15px;
        flex: 1; /* 平均占据宽度 */
        color: #ececec;
        cursor: pointer;
      }
    }


    .screen-frame-top {
      width: 100%;
      height: 0.46rem;
      position: relative;
      // height: 10%;
      // background-color: #B4E6FF;
      .screen-frame-title {
        width: 100%;
        color: #9FB3C1;
        font-weight: bold;
        text-align: center;
        height: 100%;
        line-height: 0.46rem;
        font-size: 0.136rem;
        position: relative;
      }

      .screen-frame-title::before, .screen-frame-title::after {
        content: "";
        position: absolute;
        width: 10px; /* 小圆点的宽度 */
        height: 10px; /* 小圆点的高度 */
        background-color: #3fe0fe; /* 小圆点的颜色 */
        top: 50%; /* 上边距居中 */
        transform: translateY(-50%); /* 垂直居中 */
      }

      .screen-frame-title::before {
        left: 15px; /* 左边的小圆点 */
        border-radius: 50%; /* 圆形 */
      }

      .screen-frame-title::after {
        right: 15px; /* 右边的小圆点 */
        border-radius: 50%; /* 圆形 */
      }

      .screen-frame-search {
        width: 100%;
        position: relative;

        margin: 7px;

      }
    }

    .screen-frame-top::before {
      content: "";
      position: absolute;
      bottom: 0; /* 将伪元素放置在 div 底部 */
      left: 0;
      right: 0;
      height: 2px; /* 边框高度 */
      background: linear-gradient(to left, transparent 0%, #a3d4fe 50%, transparent 100%);
      /* 线性渐变：透明 -> 白色 -> 透明，从左到右渐变 */
    }

    .screen-frame-component {
      padding: 20px;
      overflow: auto;
      flex: 1;
      // background-color: antiquewhite;
    }
  }

}


.screen-center {
  width: 78%;
  margin: 0 auto;
  height: 100%;
  position: relative;
}

.clicked {
  /* 设置点击后的样式 */
  background-color: #60c73f;
  cursor: pointer;
}


</style>
