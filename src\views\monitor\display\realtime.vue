<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>实时数据展示</span>
          <el-button @click="$router.go(-1)">返回</el-button>
        </div>
      </template>

      <div class="realtime-content">
        <el-alert title="实时数据展示" description="实时展示工作车辆数量等信息。" type="info" :closable="false" style="margin-bottom: 20px" />

        <el-empty description="实时数据展示功能开发中" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="DisplayRealtime"></script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.realtime-content {
  padding: 20px;
}
</style>
