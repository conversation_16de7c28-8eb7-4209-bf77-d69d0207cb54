<template>
  <div class='driver-trips-analysis'>
    <el-row :gutter='20'>
      <!-- 左侧组织机构驾驶员树 -->
      <el-col :lg='4' :xs='24'>
        <div class='left-panel'>
          <div class='panel-header'>
            <div class='header-left'>
              <el-icon>
                <User />
              </el-icon>
              <span>组织机构筛选</span>
            </div>
          </div>
          <div class='panel-content'>
            <div class='search-box'>
              <el-input v-model='deptName' placeholder='请输入组织机构' prefix-icon='Search' clearable />
            </div>

            <el-tree
              ref='deptTreeRef'
              class='dept-tree'
              node-key='id'
              :data='deptOptions'
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node='false'
              :filter-node-method='filterNode'
              highlight-current
              default-expand-all
              @node-click='handleNodeClick'
            >
              <template #default="{ node, data }">
                <span class="tree-node">
                  <el-icon class="dept-icon">
                    <OfficeBuilding />
                  </el-icon>
                  <span class="node-label dept">{{ data.label }}</span>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
      </el-col>

      <el-col :lg='20' :xs='24'>
        <div class='right-content'>
          <!-- 筛选条件区域 -->
          <div class='filter-section'>
            <el-card class='filter-card'>
              <!-- 时间分析选择器 -->
              <TimeAnalysisSelector
                :initialAnalysisType="queryParams.analysisType"
                @params-change="handleTimeAnalysisChange"
                ref="timeAnalysisSelectorRef"
              >
                <template #actions>
                  <el-form-item label="司机姓名" prop="driverName">
                    <el-input
                      v-model="queryParams.driverName"
                      placeholder="请输入司机姓名"
                      clearable
                      style="width: 160px"
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="工号" prop="workNumber">
                    <el-input
                      v-model="queryParams.workNumber"
                      placeholder="请输入工号"
                      clearable
                      style="width: 120px"
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="线路" prop="routeId">
                    <el-select v-model="queryParams.routeId" placeholder="请选择线路" clearable style="width: 120px">
                      <el-option
                        v-for="route in routeOptions"
                        :key="route.value"
                        :label="route.label"
                        :value="route.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
                    <el-button @click="resetQuery" icon="Refresh">重置</el-button>
                  </el-form-item>
                </template>
              </TimeAnalysisSelector>
            </el-card>
          </div>

          <!-- Tab切换区域 -->
          <div class='tabs-section'>
            <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
              <!-- 图表分析 -->
              <el-tab-pane label="图表分析" name="charts">
                <div class="tab-content">
                  <!-- 统计卡片区域 -->
                  <div class='stats-section'>
                    <el-row :gutter='16'>
                      <el-col :span='6'>
                        <div class='stat-card total'>
                          <div class='stat-icon'>
                            <el-icon><TrendCharts /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.totalTrips }}</div>
                            <div class='stat-label'>总趟次</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card completed'>
                          <div class='stat-icon'>
                            <el-icon><DataAnalysis /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.completedTrips }}</div>
                            <div class='stat-label'>完成趟次</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card rate'>
                          <div class='stat-icon'>
                            <el-icon><TopRight /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.completionRate }}%</div>
                            <div class='stat-label'>完成率</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card drivers'>
                          <div class='stat-icon'>
                            <el-icon><User /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.driverCount }}</div>
                            <div class='stat-label'>司机数量</div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 图表区域 -->
                  <div class='charts-section'>
                    <el-row :gutter='16'>
                      <el-col :span='24'>
                        <div class='chart-card'>
                          <div class='chart-header'>
                            <h3>趟次趋势图 - {{ getAnalysisTypeText(queryParams.analysisType) }}</h3>
                          </div>
                          <div ref="trendChartRef" class='chart-container'></div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 明细表格 -->
              <el-tab-pane label="明细表格" name="table">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <h4>驾驶员趟次明细表</h4>
                      <div class="table-legend">
                        <div class="legend-item">
                          <span class="legend-color planned"></span>
                          <span class="legend-text">计划趟次</span>
                        </div>
                        <div class="legend-item">
                          <span class="legend-color completed"></span>
                          <span class="legend-text">完成趟次</span>
                        </div>
                        <div class="legend-item">
                          <span class="legend-color rate"></span>
                          <span class="legend-text">完成率</span>
                        </div>
                      </div>
                      <div class="table-toolbar">
                        <el-button type="success" size="small" icon="Download" @click="handleExportTable">导出表格</el-button>
                      </div>
                    </div>
                    <el-table :data="detailTableData" @selection-change="handleSelectionChange" style="width: 100%" height="580" border>
                      <el-table-column type="selection" width="55" align="center" fixed="left" />
                      <el-table-column label="司机姓名" align="center" prop="driverName" width="120" fixed="left" />
                      <el-table-column label="工号" align="center" prop="workNumber" width="100" fixed="left" />
                      <el-table-column label="组织机构" align="center" prop="deptName" width="120" fixed="left" />
                      <el-table-column label="所属线路" align="center" prop="routeName" width="100" fixed="left" />
                      <el-table-column :label="getEfficiencyLabel()" align="center" prop="efficiency" width="120" fixed="left" />
                      <el-table-column label="车辆数" align="center" prop="vehicleCount" width="100" fixed="left">
                        <template #default="scope">
                          <el-button link type="primary" @click="handleVehicleDetail(scope.row)" size="small">
                            {{ scope.row.vehicleCount }}
                          </el-button>
                        </template>
                      </el-table-column>

                      <!-- 动态生成的日期列 -->
                      <el-table-column
                        v-for="dateCol in dynamicDateColumns"
                        :key="dateCol.prop"
                        :label="dateCol.label"
                        align="center"
                        :prop="dateCol.prop"
                      >
                        <template #default="scope">
                          <div class="trips-cell">
                            <div class="planned-trips">{{ scope.row[dateCol.plannedProp] || '-' }}</div>
                            <div class="completed-trips">{{ scope.row[dateCol.completedProp] || '-' }}</div>
                            <div class="completion-rate">{{ scope.row[dateCol.rateProp] || '-' }}%</div>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="total > 0"
                      :total="total"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getList"
                    />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 详情弹窗 -->
    <el-dialog :title="dialogTitle" v-model="showDetailDialog" width="800px" append-to-body>
      <div v-if="detailData" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="司机姓名">{{ detailData.driverName }}</el-descriptions-item>
          <el-descriptions-item label="工号">{{ detailData.workNumber }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ detailData.phoneNumber || '暂无' }}</el-descriptions-item>
          <el-descriptions-item label="所属线路">{{ detailData.routeName }}</el-descriptions-item>
          <el-descriptions-item label="所属组织机构">{{ detailData.deptName }}</el-descriptions-item>
          <el-descriptions-item label="车牌号">{{ detailData.plateNumber || '暂无' }}</el-descriptions-item>
          <el-descriptions-item label="总趟次">{{ detailData.totalTrips || detailData.completedTrips }}趟</el-descriptions-item>
          <el-descriptions-item label="完成率">{{ detailData.completionRate }}%</el-descriptions-item>
          <el-descriptions-item v-if="detailData.totalMileage" label="总里程">{{ detailData.totalMileage }}km</el-descriptions-item>
          <el-descriptions-item v-if="detailData.averageTime" label="平均用时">{{ detailData.averageTime }}分钟</el-descriptions-item>
          <el-descriptions-item v-if="detailData.vehicleUtilization" label="车辆利用率">{{ detailData.vehicleUtilization }}%</el-descriptions-item>
          <el-descriptions-item v-if="detailData.pairDays" label="配对天数">{{ detailData.pairDays }}天</el-descriptions-item>
          <el-descriptions-item label="备注" span="2">{{ detailData.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 车辆详情弹窗 -->
    <el-dialog :title="`${vehicleDetailData?.driverName} - 车辆列表`" v-model="showVehicleDetailDialog" width="600px" append-to-body>
      <div v-if="vehicleDetailData" class="vehicle-detail-content">
        <el-table :data="vehicleDetailData.vehicles" border>
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column label="车牌号" prop="plateNumber" align="center" />
          <el-table-column label="车辆编号" prop="vehicleNumber" align="center" />
          <el-table-column label="状态" prop="status" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.status === '在用' ? 'success' : 'warning'">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showVehicleDetailDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DriverTripsAnalysis">
import { ref, reactive, onMounted, nextTick, watchEffect, computed } from 'vue';
import { TrendCharts, DataAnalysis, TopRight, User, OfficeBuilding } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import TimeAnalysisSelector from '@/components/TimeAnalysisSelector/index.vue';

const loading = ref(true);
const showDetailDialog = ref(false);
const detailData = ref(null);
const activeTab = ref('charts');
const showVehicleDetailDialog = ref(false);
const vehicleDetailData = ref(null);

// 树形相关
const deptName = ref('');
const deptTreeRef = ref();

// 图表引用
const trendChartRef = ref(null);
let trendChart = null;

// 表单引用
const queryRef = ref();
const timeAnalysisSelectorRef = ref();

// 分页数据
const total = ref(0);

// 表格数据
const detailTableData = ref([]);
const dynamicDateColumns = ref([]);

// 统计数据
const summaryStats = ref({
  totalTrips: '15,624',
  completedTrips: '14,328',
  completionRate: '91.7',
  driverCount: '365'
});

// 路线选项
const routeOptions = ref([
  { label: '115路', value: '115' },
  { label: '135路', value: '135' },
  { label: '201路', value: '201' },
  { label: '202路', value: '202' },
  { label: '301路', value: '301' }
]);

// 组织机构树数据
const deptOptions = ref([]);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  analysisType: '3', // 默认日分析
  startTime: undefined,
  endTime: undefined,
  dateRange: null,
  driverName: null,
  workNumber: null,
  routeId: null,
  deptId: null
});

// 计算弹窗标题
const dialogTitle = computed(() => {
  if (!detailData.value) return '详情';
  return `${detailData.value.driverName} (${detailData.value.workNumber}) - 详情信息`;
});

onMounted(() => {
  getTreeSelect();
  handleTabChange(activeTab.value);
  nextTick(() => {
    initCharts();
  });
});

/** 组织机构树筛选 */
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选组织机构树 */
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

/** 查询组织机构下拉树结构 */
async function getTreeSelect() {
  try {
    deptOptions.value = [
      {
        id: 1,
        label: '总公司',
        children: [
          {
            id: 2,
            label: '运营部',
            children: [
              { id: 3, label: '第一车队' },
              { id: 4, label: '第二车队' },
              { id: 5, label: '第三车队' }
            ]
          },
          {
            id: 6,
            label: '维修部',
            children: []
          }
        ]
      }
    ];
  } catch (error) {
    console.error('获取组织机构树失败:', error);
  }
}

/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.deptId = data.id;
  queryParams.driverName = null;
  queryParams.workNumber = null;
  handleQuery();
}

/** Tab切换事件 */
function handleTabChange(tabName) {
  queryParams.pageNum = 1;
  activeTab.value = tabName;
  loading.value = true;

  switch (tabName) {
    case 'charts':
      // 图表分析不再需要加载表格数据，只更新图表
      loading.value = false;
      break;
    case 'table':
      getList();
      break;
  }
}

// 获取总体统计数据
function getSummaryList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateSummaryData();
    summaryList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    summaryTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 获取详细统计数据
function getDetailList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateDetailData();
    detailList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    detailTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 获取汇总表数据
function getAggregateList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateAggregateData();
    aggregateList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    aggregateTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 获取趟次明细数据
function getTripsList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateTripsData();
    tripsList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    tripsTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 生成总体统计模拟数据
function generateSummaryData() {
  const drivers = [
    { driverName: '张志明', workNumber: 'D001', routeName: '115路', deptName: '第一车队' },
    { driverName: '李华强', workNumber: 'D002', routeName: '135路', deptName: '第二车队' },
    { driverName: '王建国', workNumber: 'D003', routeName: '201路', deptName: '第三车队' },
    { driverName: '陈美丽', workNumber: 'D004', routeName: '115路', deptName: '第一车队' },
    { driverName: '刘德华', workNumber: 'D005', routeName: '301路', deptName: '维修部' },
    { driverName: '赵雅芝', workNumber: 'D006', routeName: '202路', deptName: '第二车队' },
    { driverName: '孙悟空', workNumber: 'D007', routeName: '135路', deptName: '第三车队' },
    { driverName: '朱小明', workNumber: 'D008', routeName: '301路', deptName: '第一车队' }
  ];

  return drivers.map((driver, index) => ({
    id: index + 1,
    ...driver,
    totalTrips: Math.floor(Math.random() * 200 + 300),
    averageTrips: (Math.random() * 20 + 30).toFixed(1),
    completionRate: (Math.random() * 15 + 85).toFixed(1),
    phoneNumber: '138****' + (1000 + Math.floor(Math.random() * 9000)),
    remark: Math.random() > 0.7 ? '表现优秀，准时完成任务' : null
  }));
}

// 生成详细统计模拟数据
function generateDetailData() {
  const data = [];
  const drivers = ['张志明', '李华强', '王建国', '陈美丽', '刘德华', '赵雅芝', '孙悟空', '朱小明'];
  const workNumbers = ['D001', 'D002', 'D003', 'D004', 'D005', 'D006', 'D007', 'D008'];

  for (let i = 0; i < 50; i++) {
    const driverIndex = i % drivers.length;
    const baseDate = new Date();
    baseDate.setDate(baseDate.getDate() - i);
    const statisticPeriod = baseDate.toISOString().split('T')[0];

    const plannedTrips = Math.floor(Math.random() * 20 + 40);
    const completedTrips = Math.floor(plannedTrips * (0.8 + Math.random() * 0.2));

    data.push({
      id: i + 1,
      driverName: drivers[driverIndex],
      workNumber: workNumbers[driverIndex],
      statisticPeriod,
      plannedTrips,
      completedTrips,
      completionRate: ((completedTrips / plannedTrips) * 100).toFixed(1),
      totalMileage: (completedTrips * (Math.random() * 5 + 12)).toFixed(1),
      averageTime: Math.floor(Math.random() * 20 + 45),
      remark: Math.random() > 0.8 ? '表现良好' : null
    });
  }

  return data;
}

// 生成汇总表模拟数据
function generateAggregateData() {
  const data = [];
  const drivers = ['张志明', '李华强', '王建国', '陈美丽', '刘德华', '赵雅芝', '孙悟空', '朱小明'];
  const workNumbers = ['D001', 'D002', 'D003', 'D004', 'D005', 'D006', 'D007', 'D008'];
  const vehicles = ['京A12345', '京A12346', '京A12347', '京A12348', '京A12349', '京A12350', '京A12351', '京A12352'];
  const vehicleNumbers = ['V001', 'V002', 'V003', 'V004', 'V005', 'V006', 'V007', 'V008'];

  for (let i = 0; i < 40; i++) {
    const driverIndex = i % drivers.length;
    const vehicleIndex = Math.floor(Math.random() * vehicles.length);

    data.push({
      id: i + 1,
      driverName: drivers[driverIndex],
      workNumber: workNumbers[driverIndex],
      plateNumber: vehicles[vehicleIndex],
      vehicleNumber: vehicleNumbers[vehicleIndex],
      pairTrips: Math.floor(Math.random() * 100 + 150),
      pairDays: Math.floor(Math.random() * 10 + 20),
      vehicleUtilization: (Math.random() * 20 + 75).toFixed(1),
      totalMileage: (Math.random() * 2000 + 3000).toFixed(1),
      remark: Math.random() > 0.7 ? '配对效果良好' : null
    });
  }

  return data;
}

// 生成趟次明细模拟数据
function generateTripsData() {
  const data = [];
  const drivers = ['张志明', '李华强', '王建国', '陈美丽', '刘德华', '赵雅芝', '孙悟空', '朱小明'];
  const routes = ['115路', '135路', '201路', '202路', '301路'];
  const shifts = ['早班', '中班', '晚班'];
  const stations = {
    '115路': ['火车站', '市中心', '开发区', '高新区'],
    '135路': ['汽车站', '商业街', '大学城', '科技园'],
    '201路': ['机场', '市政府', '体育馆', '医院'],
    '202路': ['码头', '老城区', '新区', '工业园'],
    '301路': ['东站', '南站', '西站', '北站']
  };

  for (let i = 0; i < 100; i++) {
    const driverName = drivers[Math.floor(Math.random() * drivers.length)];
    const routeName = routes[Math.floor(Math.random() * routes.length)];
    const routeStations = stations[routeName];
    const startStation = routeStations[0];
    const endStation = routeStations[routeStations.length - 1];

    const departureTime = new Date();
    departureTime.setHours(6 + Math.floor(Math.random() * 16), Math.floor(Math.random() * 60));
    const arrivalTime = new Date(departureTime);
    arrivalTime.setMinutes(arrivalTime.getMinutes() + Math.floor(Math.random() * 60 + 30));

    data.push({
      id: i + 1,
      driverName,
      tripNumber: `T${(1000 + i).toString()}`,
      routeName,
      shift: shifts[Math.floor(Math.random() * shifts.length)],
      departureTime: departureTime.toISOString(),
      arrivalTime: arrivalTime.toISOString(),
      startStation,
      endStation,
      mileage: (Math.random() * 15 + 10).toFixed(1),
      status: ['completed', 'in_progress', 'cancelled'][Math.floor(Math.random() * 3)],
      remark: Math.random() > 0.8 ? '正常运行' : null
    });
  }

  return data;
}

// 初始化图表
function initCharts() {
  initTrendChart();
}

// 初始化趋势图表
function initTrendChart() {
  if (!trendChartRef.value) return;

  trendChart = echarts.init(trendChartRef.value);
  updateTrendChart();
}

// 更新趋势图表
function updateTrendChart() {
  if (!trendChart) return;

  const xAxisData = generateXAxisData();
  const seriesData = generateSeriesData(xAxisData.length);

  function generateXAxisData() {
    const analysisType = queryParams.analysisType;
    const startTime = queryParams.startTime;
    const endTime = queryParams.endTime;

    if (!startTime || !endTime) {
      // 如果没有时间参数，使用默认数据
      return ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    }

    const xData = [];
    const start = new Date(startTime);
    const end = new Date(endTime);

    switch(analysisType) {
      case '0': // 年度
        for (let year = start.getFullYear(); year <= end.getFullYear(); year++) {
          xData.push(`${year}年`);
        }
        break;
      case '1': // 周
        let weekStart = new Date(start);
        let weekNum = 1;
        while (weekStart <= end) {
          xData.push(`第${weekNum}周`);
          weekStart.setDate(weekStart.getDate() + 7);
          weekNum++;
        }
        break;
      case '2': // 月度
        for (let d = new Date(start.getFullYear(), start.getMonth(), 1); d <= end; d.setMonth(d.getMonth() + 1)) {
          xData.push(`${d.getMonth() + 1}月`);
        }
        break;
      case '3': // 日
        for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
          xData.push(`${d.getMonth() + 1}/${d.getDate()}`);
        }
        break;
      case '4': // 小时
        for (let h = start.getHours(); h <= 23; h++) {
          xData.push(`${h}:00`);
        }
        break;
      default:
        return ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    }

    return xData.length > 0 ? xData : ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  }

  function generateSeriesData(length) {
    const data = [];
    for (let i = 0; i < length; i++) {
      data.push(Math.floor(Math.random() * 50 + 20));
    }
    return data;
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' }
    },
    legend: {
      data: ['趟次统计'],
      textStyle: { color: '#e5e7eb' },
      top: '5%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: { color: '#94a3b8' },
      axisLine: { lineStyle: { color: '#374151' } }
    },
    yAxis: {
      type: 'value',
      name: '趟次',
      nameTextStyle: { color: '#94a3b8' },
      axisLabel: { color: '#94a3b8' },
      axisLine: { lineStyle: { color: '#374151' } },
      splitLine: { lineStyle: { color: '#374151' } }
    },
    series: [
      {
        name: '趟次统计',
        type: 'line',
        data: seriesData,
        smooth: true,
        itemStyle: { color: '#409EFF' },
        lineStyle: { color: '#409EFF' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ])
        }
      }
    ]
  };

  trendChart.setOption(option);
}


// 获取分析类型文本
function getAnalysisTypeText(type) {
  const typeMap = {
    '0': '年度统计',
    '1': '周统计',
    '2': '月度统计',
    '3': '日统计',
    '4': '小时统计'
  };
  return typeMap[type] || '日统计';
}

// 查询
function handleQuery() {
  queryParams.pageNum = 1;
  // 如果在图表分析Tab，更新图表
  if (activeTab.value === 'charts') {
    updateTrendChart();
  } else {
    handleTabChange(activeTab.value);
  }
}

// 时间分析参数变化处理
function handleTimeAnalysisChange(params) {
  queryParams.analysisType = params.analysisType;
  queryParams.startTime = params.startTime;
  queryParams.endTime = params.endTime;

  // 更新趋势图
  if (activeTab.value === 'charts') {
    updateTrendChart();
  }
}

// 重置
function resetQuery() {
  queryRef.value?.resetFields();
  queryParams.deptId = null;
  timeAnalysisSelectorRef.value?.reset();
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

// 获取表格数据
function getList() {
  loading.value = true;

  setTimeout(() => {
    // 生成动态日期列
    generateDynamicColumns();
    
    // 生成表格数据
    const mockData = generateTableData();
    detailTableData.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    total.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 生成动态日期列
function generateDynamicColumns() {
  const analysisType = queryParams.analysisType;
  const startTime = queryParams.startTime;
  const endTime = queryParams.endTime;
  
  dynamicDateColumns.value = [];
  
  if (!startTime || !endTime) {
    // 使用默认日期列
    for (let i = 1; i <= 7; i++) {
      const date = new Date();
      date.setDate(date.getDate() - (7 - i));
      const dateStr = `${date.getMonth() + 1}/${date.getDate()}`;
      
      dynamicDateColumns.value.push({
        prop: `date${i}`,
        label: dateStr,
        plannedProp: `planned_${i}`,
        completedProp: `completed_${i}`,
        rateProp: `rate_${i}`
      });
    }
    return;
  }
  
  const start = new Date(startTime);
  const end = new Date(endTime);
  let index = 1;
  
  switch(analysisType) {
    case '0': // 年度
      for (let year = start.getFullYear(); year <= end.getFullYear(); year++) {
        dynamicDateColumns.value.push({
          prop: `date${index}`,
          label: `${year}年`,
          plannedProp: `planned_${index}`,
          completedProp: `completed_${index}`,
          rateProp: `rate_${index}`
        });
        index++;
      }
      break;
    case '2': // 月度
      for (let d = new Date(start.getFullYear(), start.getMonth(), 1); d <= end; d.setMonth(d.getMonth() + 1)) {
        dynamicDateColumns.value.push({
          prop: `date${index}`,
          label: `${d.getMonth() + 1}月`,
          plannedProp: `planned_${index}`,
          completedProp: `completed_${index}`,
          rateProp: `rate_${index}`
        });
        index++;
      }
      break;
    case '3': // 日
      for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
        dynamicDateColumns.value.push({
          prop: `date${index}`,
          label: `${d.getMonth() + 1}/${d.getDate()}`,
          plannedProp: `planned_${index}`,
          completedProp: `completed_${index}`,
          rateProp: `rate_${index}`
        });
        index++;
      }
      break;
    case '4': // 小时
      for (let h = start.getHours(); h <= end.getHours() && index <= 24; h++) {
        dynamicDateColumns.value.push({
          prop: `date${index}`,
          label: `${h}:00`,
          plannedProp: `planned_${index}`,
          completedProp: `completed_${index}`,
          rateProp: `rate_${index}`
        });
        index++;
      }
      break;
  }
}

// 生成表格模拟数据
function generateTableData() {
  const drivers = [
    { driverName: '张志明', workNumber: 'D001', routeName: '115路', deptName: '第一车队' },
    { driverName: '李华强', workNumber: 'D002', routeName: '135路', deptName: '第二车队' },
    { driverName: '王建国', workNumber: 'D003', routeName: '201路', deptName: '第三车队' },
    { driverName: '陈美丽', workNumber: 'D004', routeName: '115路', deptName: '第一车队' },
    { driverName: '刘德华', workNumber: 'D005', routeName: '301路', deptName: '维修部' },
    { driverName: '赵雅芝', workNumber: 'D006', routeName: '202路', deptName: '第二车队' },
    { driverName: '孙悟空', workNumber: 'D007', routeName: '135路', deptName: '第三车队' },
    { driverName: '朱小明', workNumber: 'D008', routeName: '301路', deptName: '第一车队' }
  ];

  return drivers.map((driver, index) => {
    const rowData = {
      id: index + 1,
      ...driver,
      efficiency: (Math.random() * 20 + 80).toFixed(1) + '%',
      vehicleCount: Math.floor(Math.random() * 3 + 1),
      phoneNumber: '138****' + (1000 + Math.floor(Math.random() * 9000)),
      remark: Math.random() > 0.7 ? '表现优秀' : null
    };
    
    // 为每个动态列生成数据
    dynamicDateColumns.value.forEach((col, colIndex) => {
      const planned = Math.floor(Math.random() * 20 + 40);
      const completed = Math.floor(planned * (0.8 + Math.random() * 0.2));
      const rate = ((completed / planned) * 100).toFixed(1);
      
      rowData[col.plannedProp] = planned;
      rowData[col.completedProp] = completed;
      rowData[col.rateProp] = rate;
    });
    
    return rowData;
  });
}

// 获取效率标签
function getEfficiencyLabel() {
  const analysisType = queryParams.analysisType;
  switch(analysisType) {
    case '0': return '年度效率';
    case '2': return '月度效率';
    case '3': return '日效率';
    case '4': return '小时效率';
    default: return '日效率';
  }
}

// 处理车辆详情
function handleVehicleDetail(row) {
  vehicleDetailData.value = {
    driverName: row.driverName,
    vehicles: [
      { plateNumber: '京A12345', vehicleNumber: 'V001', status: '在用' },
      { plateNumber: '京A12346', vehicleNumber: 'V002', status: '维修中' }
    ]
  };
  showVehicleDetailDialog.value = true;
}

// 导出表格
function handleExportTable() {
  ElMessage.success('导出成功');
}

// 查看详情
function handleDetail(row) {
  detailData.value = row;
  showDetailDialog.value = true;
}

// 查看趟次详情
function handleTripDetail(row) {
  detailData.value = {
    ...row,
    totalTrips: '1',
    completionRate: row.status === 'completed' ? '100' : (row.status === 'in_progress' ? '进行中' : '0')
  };
  showDetailDialog.value = true;
}

// 导出数据
function handleExport() {
  ElMessage.success('导出成功');
}

// 打印报表
function handlePrint() {
  window.print();
}

// 获取完成率类型
function getCompletionRateType(rate) {
  const numRate = parseFloat(rate);
  if (numRate >= 95) return 'success';
  if (numRate >= 85) return '';
  if (numRate >= 75) return 'warning';
  return 'danger';
}

// 获取利用率类型
function getUtilizationType(rate) {
  const numRate = parseFloat(rate);
  if (numRate >= 90) return 'success';
  if (numRate >= 80) return '';
  if (numRate >= 70) return 'warning';
  return 'danger';
}

// 获取趟次状态类型
function getTripStatusType(status) {
  const typeMap = {
    'completed': 'success',
    'in_progress': 'warning',
    'cancelled': 'danger'
  };
  return typeMap[status] || 'info';
}

// 获取趟次状态文本
function getTripStatusText(status) {
  const textMap = {
    'completed': '已完成',
    'in_progress': '进行中',
    'cancelled': '已取消'
  };
  return textMap[status] || '未知';
}

// 格式化时间
function formatTime(timeStr) {
  if (!timeStr) return '';
  const date = new Date(timeStr);
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
}
</script>

<style scoped>
.driver-trips-analysis {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 左侧面板样式 */
.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 24px;
}

.search-box {
  margin-bottom: 16px;
}

/* 右侧内容区域 */
.right-content {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  backdrop-filter: blur(10px);
}

/* 筛选条件区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  background: rgba(15, 23, 42, 0.98) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

/* Tab区域 */
.tabs-section {
  margin-bottom: 20px;
}

.tab-content {
  padding-top: 20px;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(41, 52, 70, 0.6) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
  border-color: rgba(96, 165, 250, 0.4);
}

.stat-card.total {
  border-left: 4px solid #409EFF;
}

.stat-card.completed {
  border-left: 4px solid #67C23A;
}

.stat-card.rate {
  border-left: 4px solid #E6A23C;
}

.stat-card.drivers {
  border-left: 4px solid #F56C6C;
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #60a5fa;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #f8fafc;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #94a3b8;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  height: 400px;
}

.chart-card .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.chart-card .chart-header h3 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.chart-container {
  height: 320px;
  width: 100%;
}

/* 表格样式 */
.table-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  min-height: 400px;
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.table-header h4 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.dimension-selector {
  display: flex;
  gap: 8px;
}

/* 表格图例样式 */
.table-legend {
  display: flex;
  gap: 16px;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #94a3b8;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.planned {
  background-color: #409EFF;
}

.legend-color.completed {
  background-color: #67C23A;
}

.legend-color.rate {
  background-color: #E6A23C;
}

.table-toolbar {
  display: flex;
  gap: 8px;
}

/* 趟次单元格样式 */
.trips-cell {
  text-align: center;
  font-size: 12px;
  line-height: 1.2;
}

.planned-trips {
  color: #409EFF;
  font-weight: 600;
}

.completed-trips {
  color: #67C23A;
  font-weight: 600;
}

.completion-rate {
  color: #E6A23C;
  font-weight: 600;
}

/* 详情弹窗 */
.detail-content {
  max-height: 500px;
  overflow-y: auto;
}

/* 树形组件样式 */
.dept-tree {
  background: transparent;
  color: #e5e7eb;
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
}

.tree-node {
  display: flex;
  align-items: center;
  font-size: 14px;
  flex: 1;
}

.dept-icon {
  margin-right: 8px;
  color: #60a5fa;
  font-size: 16px;
  flex-shrink: 0;
}

.driver-icon {
  margin-right: 8px;
  color: #34d399;
  font-size: 16px;
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  min-width: 0;
  white-space: nowrap;
}

.node-label.dept {
  font-weight: 500;
  color: #f1f5f9;
}

.node-label.driver {
  color: #94a3b8;
}

.work-number {
  color: #64748b;
  font-size: 12px;
  margin-left: 4px;
  flex-shrink: 0;
}

/* Element UI组件覆盖 */
:deep(.el-form-item__label) {
  color: #e5e7eb !important;
}

:deep(.el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e5e7eb !important;
}

:deep(.el-input__inner) {
  color: #e5e7eb !important;
}

:deep(.el-input__inner::placeholder) {
  color: #64748b !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
}

:deep(.el-table) {
  background: transparent !important;
  color: #e5e7eb !important;
}

:deep(.el-table tr) {
  background: rgba(15, 23, 42, 0.6) !important;
}

:deep(.el-table tr:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
}

:deep(.el-table th.el-table__cell) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #f8fafc !important;
  border-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-table td.el-table__cell) {
  border-color: rgba(147, 197, 253, 0.1) !important;
  color: #e5e7eb !important;
}

:deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.8);
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  background: transparent;
  border: none;
  color: #94a3b8;
  transition: all 0.3s ease;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item:hover) {
  background: rgba(59, 130, 246, 0.1);
  color: #93c5fd;
}

/* Element Tree组件覆盖样式 */
:deep(.el-tree-node) {
  background: transparent;
}

:deep(.el-tree-node:hover > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 6px;
}

:deep(.el-tree-node__content) {
  background: transparent !important;
  border: none !important;
  color: #e5e7eb !important;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

:deep(.el-tree-node__content:hover) {
  background-color: rgba(59, 130, 246, 0.15) !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.2) !important;
  color: #93c5fd !important;
}

:deep(.el-tree-node__expand-icon) {
  color: #64748b !important;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-tree .el-tree-node__children) {
  overflow: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .driver-trips-analysis {
    padding: 10px;
  }

  .stats-section .el-col {
    margin-bottom: 16px;
  }

  .chart-card {
    height: 300px;
  }

  .chart-container {
    height: 220px;
  }
}
</style>
