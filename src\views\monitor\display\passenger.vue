<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>客流量统计</span>
          <el-button @click="$router.go(-1)">返回</el-button>
        </div>
      </template>

      <div class="passenger-content">
        <el-alert title="客流量统计" description="统计每条线路每个站点的上下车人数。" type="info" :closable="false" style="margin-bottom: 20px" />

        <el-empty description="客流量统计功能开发中" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="DisplayPassenger"></script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.passenger-content {
  padding: 20px;
}
</style>
