<template>
  <div class="security-app-container">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-left">
        <el-icon class="header-icon">
          <Lock />
        </el-icon>
        <h1 class="page-title">安全应用</h1>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="Refresh" @click="refreshAllConfigs">刷新配置</el-button>
      </div>
    </div>

    <!-- 配置标签页 -->
    <div class="config-tabs">
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="报警策略配置" name="alarm">
          <!-- 报警策略全局配置 -->
          <div class="config-section">
            <div class="section-title">
              <el-icon><Warning /></el-icon>
              <span>报警策略全局配置</span>
            </div>
            
            <el-card shadow="never" class="config-card">
              <el-form :model="alarmConfig" label-width="120px" class="config-form compact-form">
                <!-- 基础阈值配置 - 2行3列布局 -->
                <el-row :gutter="20">
                  <el-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
                    <el-form-item label="超速报警">
                      <div class="input-group">
                        <el-input-number
                          v-model="alarmConfig.speedThreshold"
                          :min="1" :max="200"
                          controls-position="right"
                          size="small"
                        />
                        <span class="unit">km/h</span>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
                    <el-form-item label="持续时间">
                      <div class="input-group">
                        <el-input-number
                          v-model="alarmConfig.speedDuration"
                          :min="1" :max="300"
                          controls-position="right"
                          size="small"
                        />
                        <span class="unit">秒</span>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
                    <el-form-item label="疲劳驾驶">
                      <div class="input-group">
                        <el-input-number
                          v-model="alarmConfig.fatigueThreshold"
                          :min="1" :max="24"
                          controls-position="right"
                          size="small"
                        />
                        <span class="unit">小时</span>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
                    <el-form-item label="偏航距离">
                      <div class="input-group">
                        <el-input-number
                          v-model="alarmConfig.deviationDistance"
                          :min="10" :max="2000"
                          controls-position="right"
                          size="small"
                        />
                        <span class="unit">米</span>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
                    <el-form-item label="长时停车">
                      <div class="input-group">
                        <el-input-number
                          v-model="alarmConfig.longStopDuration"
                          :min="1" :max="1440"
                          controls-position="right"
                          size="small"
                        />
                        <span class="unit">分钟</span>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
                    <el-form-item label="升级时间">
                      <div class="input-group">
                        <el-input-number
                          v-model="alarmConfig.escalationTime"
                          :min="1" :max="60"
                          controls-position="right"
                          size="small"
                        />
                        <span class="unit">分钟</span>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 自动处理规则 - 水平布局 -->
                <el-form-item label="自动规则">
                  <el-checkbox-group v-model="alarmConfig.autoRules" size="small">
                    <el-checkbox label="auto_resolve">自动解决</el-checkbox>
                    <el-checkbox label="auto_notify">自动通知</el-checkbox>
                    <el-checkbox label="auto_escalate">自动升级</el-checkbox>
                    <el-checkbox label="night_mode">夜间模式</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>

                <div class="form-actions">
                  <el-button type="primary" @click="saveAlarmConfig">保存配置</el-button>
                  <el-button @click="resetAlarmConfig">重置</el-button>
                </div>
              </el-form>
            </el-card>
          </div>
        </el-tab-pane>

        <el-tab-pane label="驾驶员评分" name="driver">
          <!-- 驾驶员评分规则配置 -->
          <div class="config-section">
            <div class="section-title">
              <el-icon><User /></el-icon>
              <span>驾驶员评分规则配置</span>
            </div>
            
            <el-card shadow="never" class="config-card">
              <el-form :model="driverConfig" label-width="100px" class="config-form compact-form">
                <!-- 基础配置 - 一行三列 -->
                <el-row :gutter="20" class="aligned-form-row">
                  <el-col :xl="8" :lg="8" :md="12" :sm="24" :xs="24">
                    <el-form-item label="基础分数">
                      <el-input-number
                        v-model="driverConfig.baseScore"
                        :min="60" :max="100"
                        controls-position="right"
                        size="small"
                        class="aligned-input-number"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :xl="8" :lg="8" :md="12" :sm="24" :xs="24">
                    <el-form-item label="评分周期">
                      <el-select v-model="driverConfig.scorePeriod" size="small" class="aligned-select" style="width: 100%">
                        <el-option label="每日" value="daily" />
                        <el-option label="每周" value="weekly" />
                        <el-option label="每月" value="monthly" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xl="8" :lg="8" :md="12" :sm="24" :xs="24">
                    <el-form-item label="最低分数">
                      <el-input-number
                        v-model="driverConfig.minScore"
                        :min="0" :max="60"
                        controls-position="right"
                        size="small"
                        class="aligned-input-number"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 扣分规则 - 2x2网格布局 -->
                <el-form-item label="扣分规则">
                  <el-row :gutter="12">
                    <el-col 
                      v-for="rule in driverConfig.deductionRules" 
                      :key="rule.id" 
                      :xl="12" :lg="12" :md="24" :sm="24" :xs="24"
                    >
                      <div class="rule-item-compact">
                        <div class="rule-header-compact">
                          <el-tag :type="rule.type" size="small">{{ rule.name }}</el-tag>
                          <el-switch v-model="rule.enabled" size="small" />
                        </div>
                        <div class="rule-controls">
                          <div class="control-group">
                            <span class="control-label">扣分</span>
                            <el-input-number
                              v-model="rule.points"
                              :min="1" :max="50"
                              controls-position="right"
                              size="small"
                              style="width: 70px"
                            />
                          </div>
                          <div class="control-group">
                            <span class="control-label">频率</span>
                            <el-select v-model="rule.frequency" size="small" style="width: 80px">
                              <el-option label="每次" value="each" />
                              <el-option label="每日" value="daily" />
                              <el-option label="每周" value="weekly" />
                            </el-select>
                          </div>
                          <div class="control-group">
                            <span class="control-label">日限</span>
                            <el-input-number
                              v-model="rule.maxDaily"
                              :min="1" :max="100"
                              controls-position="right"
                              size="small"
                              style="width: 70px"
                            />
                          </div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </el-form-item>

                <!-- 加分奖励 - 水平布局 -->
                <el-form-item label="加分奖励">
                  <div class="bonus-rules-compact">
                    <div v-for="bonus in driverConfig.bonusRules" :key="bonus.id" class="bonus-item-compact">
                      <el-checkbox v-model="bonus.enabled" size="small">{{ bonus.name }}</el-checkbox>
                      <el-input-number
                        v-model="bonus.points"
                        :min="1" :max="20"
                        size="small"
                        controls-position="right"
                        style="width: 60px"
                      />
                    </div>
                  </div>
                </el-form-item>

                <!-- 评级标准 - 水平布局 -->
                <el-form-item label="评级标准">
                  <div class="grade-standards-compact">
                    <div v-for="grade in driverConfig.gradeStandards" :key="grade.level" class="grade-item-compact">
                      <el-tag :type="grade.type" size="small">{{ grade.name }}</el-tag>
                      <span class="grade-range">{{ grade.minScore }}-{{ grade.maxScore }}</span>
                    </div>
                  </div>
                </el-form-item>

                <div class="form-actions">
                  <el-button type="primary" @click="saveDriverConfig">保存配置</el-button>
                  <el-button @click="resetDriverConfig">重置</el-button>
                </div>
              </el-form>
            </el-card>
          </div>
        </el-tab-pane>

        <el-tab-pane label="车队响应策略" name="fleet">
          <!-- 车队响应策略配置 -->
          <div class="config-section">
            <div class="section-title">
              <el-icon><Van /></el-icon>
              <span>车队响应策略配置</span>
            </div>
            
            <el-card shadow="never" class="config-card">
              <el-form :model="fleetConfig" label-width="120px" class="config-form compact-form">
                <!-- 响应级别设置 - 网格布局 -->
                <el-form-item label="响应级别">
                  <el-row :gutter="16">
                    <el-col 
                      v-for="level in fleetConfig.responseLevels" 
                      :key="level.id" 
                      :xl="8" :lg="12" :md="24" :sm="24" :xs="24"
                    >
                      <div class="response-item-compact">
                        <div class="response-header">
                          <div class="level-info">
                            <el-tag :type="level.type" size="small">{{ level.name }}</el-tag>
                            <el-tooltip :content="getLevelTooltip(level.name)" placement="top">
                              <el-icon class="info-icon"><InfoFilled /></el-icon>
                            </el-tooltip>
                          </div>
                          <el-switch v-model="level.enabled" size="small" />
                        </div>
                        <div class="response-config">
                          <div class="config-row">
                            <span class="config-label">响应时间</span>
                            <el-input-number
                              v-model="level.responseTime"
                              :min="1" :max="60"
                              size="small"
                              controls-position="right"
                              style="width: 80px"
                            />
                            <span class="unit">分钟</span>
                          </div>
                          <div class="config-row">
                            <span class="config-label">通知方式</span>
                            <el-checkbox-group v-model="level.notifyMethods" size="small">
                              <el-checkbox label="sms">短信</el-checkbox>
                              <el-checkbox label="call">电话</el-checkbox>
                              <el-checkbox label="app">APP</el-checkbox>
                            </el-checkbox-group>
                          </div>
                          <div class="config-row">
                            <span class="config-label">处理人员</span>
                            <el-select v-model="level.handlers" multiple size="small" style="width: 100%">
                              <el-option label="调度员" value="dispatcher" />
                              <el-option label="车队长" value="fleet_manager" />
                              <el-option label="安全员" value="safety_officer" />
                              <el-option label="维修员" value="mechanic" />
                            </el-select>
                          </div>
                        </div>
                        <!-- 响应级别说明 -->
                        <div class="level-description">
                          <div class="description-title">适用报警类型：</div>
                          <div class="alarm-types">
                            <template v-if="level.name === '一级响应'">
                              <el-tag size="small" type="danger" class="type-tag">车辆事故</el-tag>
                              <el-tag size="small" type="danger" class="type-tag">车辆起火</el-tag>
                              <el-tag size="small" type="danger" class="type-tag">乘客急病</el-tag>
                            </template>
                            <template v-if="level.name === '二级响应'">
                              <el-tag size="small" type="warning" class="type-tag">发动机故障</el-tag>
                              <el-tag size="small" type="warning" class="type-tag">严重超速</el-tag>
                              <el-tag size="small" type="warning" class="type-tag">偏离路线</el-tag>
                            </template>
                            <template v-if="level.name === '三级响应'">
                              <el-tag size="small" type="info" class="type-tag">燃油不足</el-tag>
                              <el-tag size="small" type="info" class="type-tag">空调故障</el-tag>
                              <el-tag size="small" type="info" class="type-tag">轻微超速</el-tag>
                            </template>
                          </div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </el-form-item>

                <!-- 工作时间和策略 - 一行两列 -->
                <el-row :gutter="20" class="aligned-form-row">
                  <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
                    <el-form-item label="工作时间">
                      <el-time-picker
                        v-model="fleetConfig.workingHours"
                        is-range
                        range-separator="至"
                        start-placeholder="开始"
                        end-placeholder="结束"
                        format="HH:mm"
                        value-format="HH:mm"
                        size="small"
                        class="aligned-time-picker"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
                    <el-form-item label="非工作时间策略">
                      <el-radio-group v-model="fleetConfig.afterHoursStrategy" size="small" class="aligned-radio-group">
                        <el-radio label="normal">
                          <span class="radio-label">正常模式</span>
                          <el-tooltip content="24小时按标准流程处理所有报警，需安排夜班值守" placement="top">
                            <el-icon class="info-icon"><InfoFilled /></el-icon>
                          </el-tooltip>
                        </el-radio>
                        <el-radio label="emergency_only">
                          <span class="radio-label">仅紧急模式</span>
                          <el-tooltip content="仅处理危及安全的紧急报警，其他延迟到工作时间" placement="top">
                            <el-icon class="info-icon"><InfoFilled /></el-icon>
                          </el-tooltip>
                        </el-radio>
                        <el-radio label="delayed">
                          <span class="radio-label">延迟模式</span>
                          <el-tooltip content="所有报警延迟到下一工作时间集中处理" placement="top">
                            <el-icon class="info-icon"><InfoFilled /></el-icon>
                          </el-tooltip>
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 策略详细说明 - 独立行展示 -->
                <el-form-item label="" v-if="fleetConfig.afterHoursStrategy">
                  <div class="strategy-explanation-full">
                    <div class="explanation-card" v-if="fleetConfig.afterHoursStrategy === 'normal'">
                      <div class="explanation-title">
                        <el-icon class="title-icon"><Clock /></el-icon>
                        正常模式详细说明
                      </div>
                      <ul class="explanation-list">
                        <li><strong>处理范围：</strong>所有级别报警（低、中、高、紧急）</li>
                        <li><strong>响应时间：</strong>严格按照响应级别要求执行</li>
                        <li><strong>通知方式：</strong>按配置的通知方式立即发送</li>
                        <li><strong>人员要求：</strong>需要安排24小时值班调度员</li>
                        <li><strong>适用场景：</strong>夜班公交、机场摆渡、医疗专线等</li>
                      </ul>
                    </div>
                    
                    <div class="explanation-card" v-if="fleetConfig.afterHoursStrategy === 'emergency_only'">
                      <div class="explanation-title">
                        <el-icon class="title-icon"><Warning /></el-icon>
                        仅紧急模式详细说明
                      </div>
                      <div class="emergency-categories">
                        <div class="category-item">
                          <span class="category-title">紧急报警（立即处理）：</span>
                          <el-tag size="small" type="danger" class="alarm-tag">车辆事故</el-tag>
                          <el-tag size="small" type="danger" class="alarm-tag">车辆起火</el-tag>
                          <el-tag size="small" type="danger" class="alarm-tag">乘客急病</el-tag>
                          <el-tag size="small" type="danger" class="alarm-tag">司机异常</el-tag>
                          <el-tag size="small" type="danger" class="alarm-tag">刹车失灵</el-tag>
                        </div>
                        <div class="category-item">
                          <span class="category-title">延迟报警（工作时间处理）：</span>
                          <el-tag size="small" type="warning" class="alarm-tag">轻微超速</el-tag>
                          <el-tag size="small" type="warning" class="alarm-tag">燃油不足</el-tag>
                          <el-tag size="small" type="warning" class="alarm-tag">空调故障</el-tag>
                          <el-tag size="small" type="warning" class="alarm-tag">偏离路线</el-tag>
                          <el-tag size="small" type="warning" class="alarm-tag">轮胎气压</el-tag>
                        </div>
                      </div>
                      <ul class="explanation-list">
                        <li><strong>成本优化：</strong>减少夜班成本，仅安排紧急值班</li>
                        <li><strong>安全保障：</strong>确保危及安全的情况得到及时处理</li>
                        <li><strong>适用场景：</strong>普通城市公交、校车、企业班车等</li>
                      </ul>
                    </div>
                    
                    <div class="explanation-card" v-if="fleetConfig.afterHoursStrategy === 'delayed'">
                      <div class="explanation-title">
                        <el-icon class="title-icon"><Timer /></el-icon>
                        延迟模式详细说明
                      </div>
                      <ul class="explanation-list">
                        <li><strong>延迟时长：</strong>到下一个工作时间开始（如次日08:00）</li>
                        <li><strong>处理方式：</strong>系统记录但不通知，工作时间批量处理</li>
                        <li><strong>风险提示：</strong>可能错过紧急情况的及时处理</li>
                        <li><strong>成本最低：</strong>无需安排任何夜班人员</li>
                        <li><strong>适用场景：</strong>车队维护期、节假日、临时人手不足</li>
                      </ul>
                    </div>
                  </div>
                </el-form-item>

                <!-- 区域响应设置 - 水平布局 -->
                <el-form-item label="区域响应">
                  <div class="area-config-compact">
                    <div v-for="area in fleetConfig.areaSettings" :key="area.id" class="area-item-compact">
                      <el-checkbox v-model="area.enabled">{{ area.name }}</el-checkbox>
                      <span class="priority-badge">P{{ area.priority }}</span>
                      <el-input-number
                        v-model="area.responseTime"
                        :min="1" :max="30"
                        size="small"
                        controls-position="right"
                        style="width: 70px"
                      />
                      <span class="unit">分钟</span>
                    </div>
                  </div>
                </el-form-item>

                <!-- 紧急响应流程 -->
                <el-form-item label="紧急流程">
                  <div class="emergency-flow-compact">
                    <div class="flow-steps">
                      <div v-for="(step, index) in fleetConfig.emergencyFlow" :key="index" class="step-item">
                        <div class="step-number">{{ index + 1 }}</div>
                        <div class="step-content">
                          <div class="step-title">{{ step.title }}</div>
                          <div class="step-desc">{{ step.description }}</div>
                        </div>
                      </div>
                    </div>
                    <el-button type="text" size="small" @click="editEmergencyFlow">编辑流程</el-button>
                  </div>
                </el-form-item>

                <div class="form-actions">
                  <el-button type="primary" @click="saveFleetConfig">保存配置</el-button>
                  <el-button @click="resetFleetConfig">重置</el-button>
                </div>
              </el-form>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 紧急响应流程编辑弹窗 -->
    <el-dialog
      v-model="emergencyFlowDialogVisible"
      title="编辑紧急响应流程"
      width="800px"
      append-to-body
    >
      <div class="flow-editor">
        <div v-for="(step, index) in editingFlow" :key="index" class="flow-step">
          <el-row :gutter="10">
            <el-col :span="2">
              <div class="step-number">{{ index + 1 }}</div>
            </el-col>
            <el-col :span="8">
              <el-input v-model="step.title" placeholder="步骤标题" size="small" />
            </el-col>
            <el-col :span="12">
              <el-input v-model="step.description" placeholder="步骤描述" size="small" />
            </el-col>
            <el-col :span="2">
              <el-button type="danger" icon="Delete" size="small" @click="removeFlowStep(index)" />
            </el-col>
          </el-row>
        </div>
        <el-button type="primary" icon="Plus" size="small" @click="addFlowStep">添加步骤</el-button>
      </div>
      
      <template #footer>
        <el-button @click="emergencyFlowDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveEmergencyFlow">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="SecurityApp">
import { ref, onMounted } from 'vue'
import {
  Lock,
  Warning,
  User,
  Van,
  Refresh,
  Clock,
  Plus,
  Delete,
  InfoFilled,
  Timer
} from '@element-plus/icons-vue'

// 当前标签页
const activeTab = ref('alarm')

// 报警策略配置
const alarmConfig = ref({
  speedThreshold: 80, // 超速阈值
  speedDuration: 30, // 超速持续时间
  fatigueThreshold: 4, // 疲劳驾驶阈值
  deviationDistance: 500, // 偏航距离
  longStopDuration: 30, // 长时间停车
  escalationTime: 10, // 报警升级时间
  autoRules: ['auto_resolve', 'auto_notify']
})

// 驾驶员评分配置
const driverConfig = ref({
  baseScore: 100,
  scorePeriod: 'monthly',
  minScore: 60,
  deductionRules: [
    { 
      id: 1, 
      name: '超速驾驶', 
      type: 'danger', 
      points: 5, 
      frequency: 'each', 
      maxDaily: 20, 
      enabled: true 
    },
    { 
      id: 2, 
      name: '疲劳驾驶', 
      type: 'warning', 
      points: 10, 
      frequency: 'each', 
      maxDaily: 30, 
      enabled: true 
    },
    { 
      id: 3, 
      name: '急加速', 
      type: 'info', 
      points: 2, 
      frequency: 'daily', 
      maxDaily: 10, 
      enabled: true 
    },
    { 
      id: 4, 
      name: '急刹车', 
      type: 'info', 
      points: 2, 
      frequency: 'daily', 
      maxDaily: 10, 
      enabled: true 
    }
  ],
  bonusRules: [
    { id: 1, name: '连续无事故驾驶30天', points: 10, enabled: true },
    { id: 2, name: '节能驾驶奖励', points: 5, enabled: true },
    { id: 3, name: '准点率奖励', points: 3, enabled: true }
  ],
  gradeStandards: [
    { level: 'A', name: '优秀', minScore: 90, maxScore: 100, type: 'success', color: 'success', description: '表现优异' },
    { level: 'B', name: '良好', minScore: 80, maxScore: 89, type: 'success', color: 'success', description: '表现良好' },
    { level: 'C', name: '一般', minScore: 70, maxScore: 79, type: 'warning', color: 'warning', description: '有待改进' },
    { level: 'D', name: '较差', minScore: 60, maxScore: 69, type: 'danger', color: 'danger', description: '需要培训' },
    { level: 'E', name: '不合格', minScore: 0, maxScore: 59, type: 'danger', color: 'danger', description: '禁止驾驶' }
  ]
})

// 车队响应策略配置
const fleetConfig = ref({
  responseLevels: [
    {
      id: 1,
      name: '一级响应',
      type: 'danger',
      enabled: true,
      responseTime: 5,
      notifyMethods: ['sms', 'call', 'app'],
      handlers: ['dispatcher', 'fleet_manager'],
      autoHandle: true
    },
    {
      id: 2,
      name: '二级响应',
      type: 'warning',
      enabled: true,
      responseTime: 10,
      notifyMethods: ['sms', 'app'],
      handlers: ['dispatcher'],
      autoHandle: false
    },
    {
      id: 3,
      name: '三级响应',
      type: 'info',
      enabled: true,
      responseTime: 30,
      notifyMethods: ['app'],
      handlers: ['dispatcher'],
      autoHandle: false
    }
  ],
  emergencyFlow: [
    { title: '事件检测', description: '系统自动检测到异常情况' },
    { title: '立即通知', description: '向相关人员发送紧急通知' },
    { title: '现场确认', description: '派遣人员前往现场确认情况' },
    { title: '应急处理', description: '执行相应的应急处理措施' },
    { title: '跟踪监控', description: '持续跟踪事件处理进展' }
  ],
  workingHours: ['08:00', '18:00'],
  afterHoursStrategy: 'emergency_only',
  areaSettings: [
    { id: 1, name: '市中心区域', priority: 1, responseTime: 5, enabled: true },
    { id: 2, name: '郊区区域', priority: 2, responseTime: 10, enabled: true },
    { id: 3, name: '偏远区域', priority: 3, responseTime: 20, enabled: true }
  ]
})

// 紧急响应流程编辑
const emergencyFlowDialogVisible = ref(false)
const editingFlow = ref([])

onMounted(() => {
  // 初始化配置
  loadConfigs()
})

// 加载配置
function loadConfigs() {
  // 模拟从API加载配置数据
  console.log('加载安全配置...')
}

// 刷新所有配置
function refreshAllConfigs() {
  loadConfigs()
  ElMessage.success('配置刷新成功')
}

// 保存报警配置
function saveAlarmConfig() {
  // 模拟保存到后端
  ElMessage.success('报警策略配置保存成功')
}

// 重置报警配置
function resetAlarmConfig() {
  ElMessageBox.confirm('确认重置报警策略配置吗？', '确认重置', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 重置到默认值
    ElMessage.success('配置已重置')
  })
}

// 保存驾驶员配置
function saveDriverConfig() {
  ElMessage.success('驾驶员评分规则保存成功')
}

// 重置驾驶员配置
function resetDriverConfig() {
  ElMessageBox.confirm('确认重置驾驶员评分配置吗？', '确认重置', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('配置已重置')
  })
}

// 保存车队配置
function saveFleetConfig() {
  ElMessage.success('车队响应策略保存成功')
}

// 重置车队配置
function resetFleetConfig() {
  ElMessageBox.confirm('确认重置车队响应配置吗？', '确认重置', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('配置已重置')
  })
}

// 编辑紧急响应流程
function editEmergencyFlow() {
  editingFlow.value = JSON.parse(JSON.stringify(fleetConfig.value.emergencyFlow))
  emergencyFlowDialogVisible.value = true
}

// 添加流程步骤
function addFlowStep() {
  editingFlow.value.push({
    title: '',
    description: ''
  })
}

// 移除流程步骤
function removeFlowStep(index) {
  editingFlow.value.splice(index, 1)
}

// 保存紧急响应流程
function saveEmergencyFlow() {
  fleetConfig.value.emergencyFlow = [...editingFlow.value]
  emergencyFlowDialogVisible.value = false
  ElMessage.success('紧急响应流程保存成功')
}

// 获取响应级别工具提示
function getLevelTooltip(levelName) {
  const tooltips = {
    '一级响应': '最高优先级，用于处理危及生命安全的紧急情况',
    '二级响应': '中等优先级，用于处理影响运营安全的重要事件', 
    '三级响应': '常规优先级，用于处理日常维护和轻微异常'
  }
  return tooltips[levelName] || '响应级别说明'
}
</script>

<style scoped>
.security-app-container {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 24px;
  color: #f59e0b;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #f8fafc;
}

/* 配置标签页 */
.config-tabs {
  margin-bottom: 24px;
}

.config-tabs :deep(.el-tabs--border-card) {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(41, 52, 70, 0.7) 100%);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.config-tabs :deep(.el-tabs__header) {
  background: rgba(15, 23, 42, 0.6);
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
  margin: 0;
}

.config-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 20px;
}

.config-tabs :deep(.el-tabs__item) {
  color: #cbd5e1;
  border: none;
  padding: 16px 24px;
}

.config-tabs :deep(.el-tabs__item.is-active) {
  color: #60a5fa;
  background: rgba(59, 130, 246, 0.1);
}

.config-tabs :deep(.el-tabs__content) {
  padding: 24px;
}

/* 配置区域 */
.config-section {
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: #f1f5f9;
}

.section-title .el-icon {
  font-size: 20px;
  color: #f59e0b;
}

/* 配置卡片 */
.config-card :deep(.el-card) {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(41, 52, 70, 0.6) 100%);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.config-card :deep(.el-card__body) {
  padding: 24px;
}

/* 表单样式 */
.config-form :deep(.el-form-item__label) {
  color: #cbd5e1;
  font-weight: 500;
}

.config-form :deep(.el-input__wrapper) {
  background: rgba(51, 65, 85, 0.8);
  border: 1px solid rgba(100, 116, 139, 0.4);
  box-shadow: none;
}

.config-form :deep(.el-input__wrapper:hover) {
  border-color: rgba(59, 130, 246, 0.6);
}

.config-form :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.config-form :deep(.el-input__inner) {
  color: #f1f5f9;
  background: transparent;
}

.config-form :deep(.el-textarea__inner) {
  background: rgba(51, 65, 85, 0.8);
  border: 1px solid rgba(100, 116, 139, 0.4);
  color: #f1f5f9;
}

.config-form :deep(.el-select .el-input__wrapper) {
  background: rgba(51, 65, 85, 0.8);
  border: 1px solid rgba(100, 116, 139, 0.4);
}

.config-form :deep(.el-checkbox__label) {
  color: #cbd5e1;
}

.config-form :deep(.el-radio__label) {
  color: #cbd5e1;
}

/* 单位标签 */
.unit {
  color: #94a3b8;
  font-size: 12px;
  margin-left: 8px;
}

/* 表单对齐样式 */
.aligned-form-row {
  align-items: flex-end;
}

.aligned-form-row :deep(.el-form-item) {
  margin-bottom: 18px;
  display: flex;
  align-items: center;
}

.aligned-form-row :deep(.el-form-item__label) {
  height: 32px;
  line-height: 32px;
  display: flex;
  align-items: center;
}

.aligned-form-row :deep(.el-form-item__content) {
  display: flex;
  align-items: center;
  min-height: 32px;
}

/* 统一输入控件高度 */
.aligned-input-number :deep(.el-input__wrapper),
.aligned-select :deep(.el-input__wrapper),
.aligned-time-picker :deep(.el-input__wrapper) {
  height: 32px !important;
  line-height: 32px !important;
}

.aligned-input-number :deep(.el-input__inner),
.aligned-select :deep(.el-input__inner),
.aligned-time-picker :deep(.el-input__inner) {
  height: 32px !important;
  line-height: 32px !important;
}

.aligned-input-number :deep(.el-input-number__decrease),
.aligned-input-number :deep(.el-input-number__increase) {
  height: 16px !important;
  line-height: 16px !important;
}

/* 单选按钮组对齐 */
.aligned-radio-group {
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
}

.aligned-radio-group :deep(.el-radio) {
  margin-right: 16px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
}

.aligned-radio-group :deep(.el-radio__label) {
  line-height: 32px !important;
}

/* 策略配置样式 */
.strategy-config {
  width: 100%;
}

.radio-label {
  font-weight: 500;
  margin-right: 6px;
}

.info-icon {
  font-size: 14px;
  color: #94a3b8;
  cursor: help;
  margin-left: 4px;
}

.info-icon:hover {
  color: #60a5fa;
}

.strategy-explanation {
  margin-top: 16px;
  width: 100%;
}

.strategy-explanation-full {
  width: 100%;
  margin-top: 0;
}

.explanation-card {
  background: rgba(30, 41, 59, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  padding: 16px;
  margin-top: 12px;
}

.explanation-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 12px;
}

.title-icon {
  font-size: 16px;
  color: #60a5fa;
}

.explanation-list {
  margin: 0;
  padding-left: 16px;
  color: #cbd5e1;
}

.explanation-list li {
  margin-bottom: 6px;
  line-height: 1.4;
}

.explanation-list li:last-child {
  margin-bottom: 0;
}

.emergency-categories {
  margin-bottom: 12px;
}

.category-item {
  margin-bottom: 10px;
}

.category-title {
  display: block;
  font-weight: 500;
  color: #f1f5f9;
  margin-bottom: 6px;
}

.alarm-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

/* 响应级别增强样式 */
.level-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.level-description {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(147, 197, 253, 0.1);
}

.description-title {
  font-size: 12px;
  color: #94a3b8;
  margin-bottom: 6px;
  font-weight: 500;
}

.alarm-types {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.type-tag {
  font-size: 11px;
  padding: 2px 6px;
}

/* 紧凑表单样式 */
.compact-form :deep(.el-form-item) {
  margin-bottom: 18px;
}

.compact-form :deep(.el-form-item__label) {
  font-size: 13px;
  line-height: 1.2;
  padding-bottom: 4px;
}

/* 输入组合 */
.input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 报警级别水平布局 */
.alarm-levels-horizontal {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.level-item-h {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 6px;
  min-width: 200px;
}

/* 扣分规则紧凑布局 */
.rule-item-compact {
  padding: 12px;
  background: rgba(51, 65, 85, 0.4);
  border-radius: 8px;
  border: 1px solid rgba(100, 116, 139, 0.2);
  margin-bottom: 12px;
  height: 100%;
}

.rule-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.rule-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.control-label {
  font-size: 11px;
  color: #94a3b8;
  font-weight: 500;
}

/* 加分奖励紧凑布局 */
.bonus-rules-compact {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.bonus-item-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

/* 评级标准紧凑布局 */
.grade-standards-compact {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.grade-item-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 6px;
}

.grade-range {
  color: #cbd5e1;
  font-size: 12px;
  font-weight: 500;
}

/* 响应级别紧凑布局 */
.response-item-compact {
  padding: 12px;
  background: rgba(51, 65, 85, 0.4);
  border-radius: 8px;
  border: 1px solid rgba(100, 116, 139, 0.2);
  margin-bottom: 12px;
  height: 100%;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.response-config {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.config-label {
  font-size: 11px;
  color: #94a3b8;
  font-weight: 500;
  min-width: 60px;
}

/* 区域配置紧凑布局 */
.area-config-compact {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.area-item-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 6px;
  border: 1px solid rgba(100, 116, 139, 0.2);
}

.priority-badge {
  font-size: 10px;
  color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
}

/* 紧急流程紧凑布局 */
.emergency-flow-compact {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.flow-steps {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 6px;
  min-width: 180px;
  flex: 1;
  max-width: 250px;
}

.step-number {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 12px;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 2px;
}

.step-desc {
  font-size: 11px;
  color: #94a3b8;
  line-height: 1.3;
}

/* 加分奖励 */
.bonus-rules {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.bonus-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 6px;
}

/* 评级标准 */
.grade-standards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.grade-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 16px;
  background: rgba(51, 65, 85, 0.4);
  border-radius: 8px;
}

.grade-range {
  color: #cbd5e1;
  font-weight: 500;
  min-width: 100px;
}

/* 响应级别 */
.response-levels {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.response-item {
  padding: 20px;
  background: rgba(51, 65, 85, 0.4);
  border-radius: 12px;
  border: 1px solid rgba(100, 116, 139, 0.2);
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.level-config label {
  display: block;
  color: #94a3b8;
  font-size: 12px;
  margin-bottom: 4px;
}

/* 紧急响应流程 */
.emergency-flow :deep(.el-steps) {
  margin-bottom: 16px;
}

.emergency-flow :deep(.el-step__title) {
  color: #f1f5f9;
}

.emergency-flow :deep(.el-step__description) {
  color: #cbd5e1;
}

/* 区域配置 */
.area-config {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.area-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(51, 65, 85, 0.4);
  border-radius: 8px;
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid rgba(147, 197, 253, 0.1);
}

/* 按钮样式重写 */
.header-right :deep(.el-button--primary) {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: 1px solid rgba(59, 130, 246, 0.5);
  color: white;
  backdrop-filter: blur(10px);
}

.header-right :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.form-actions :deep(.el-button--primary) {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: 1px solid rgba(59, 130, 246, 0.5);
  color: white;
  min-width: 120px;
}

.form-actions :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  border-color: #3b82f6;
}

.form-actions :deep(.el-button) {
  background: rgba(51, 65, 85, 0.8);
  border: 1px solid rgba(100, 116, 139, 0.4);
  color: #cbd5e1;
  min-width: 120px;
}

.form-actions :deep(.el-button:hover) {
  background: rgba(71, 85, 105, 0.8);
  border-color: rgba(147, 197, 253, 0.4);
  color: #f1f5f9;
}

/* 流程编辑器 */
.flow-editor {
  padding: 20px 0;
}

.flow-step {
  margin-bottom: 16px;
  padding: 16px;
  background: rgba(51, 65, 85, 0.2);
  border-radius: 8px;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

/* 下拉框和弹出层样式 */
:deep(.el-select-dropdown) {
  background: rgba(30, 41, 59, 0.95);
  border: 1px solid rgba(147, 197, 253, 0.2);
  backdrop-filter: blur(15px);
}

:deep(.el-select-dropdown .el-select-dropdown__item) {
  color: #cbd5e1;
}

:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background: rgba(59, 130, 246, 0.2);
  color: #f1f5f9;
}

:deep(.el-select-dropdown .el-select-dropdown__item.selected) {
  background: rgba(59, 130, 246, 0.3);
  color: #60a5fa;
}

:deep(.el-dialog) {
  background: rgba(30, 41, 59, 0.95);
  border: 1px solid rgba(147, 197, 253, 0.2);
  backdrop-filter: blur(15px);
}

:deep(.el-dialog__header) {
  background: rgba(15, 23, 42, 0.6);
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

:deep(.el-dialog__title) {
  color: #f1f5f9;
}

:deep(.el-dialog__body) {
  color: #cbd5e1;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .security-app-container {
    padding: 16px;
  }
  
  .level-config .el-col {
    margin-bottom: 12px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .config-tabs :deep(.el-tabs__nav-wrap) {
    padding: 0 10px;
  }
  
  .config-tabs :deep(.el-tabs__item) {
    padding: 12px 16px;
    font-size: 14px;
  }
  
  .config-tabs :deep(.el-tabs__content) {
    padding: 16px;
  }
  
  .level-item,
  .rule-item,
  .response-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>