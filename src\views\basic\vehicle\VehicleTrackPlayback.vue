<template>
  <div class="track-playback-container">
    <!-- 内容区域 -->
    <div class="content-wrapper">
      <!-- 地图容器 -->
      <div class="map-container" v-loading="mapLoading">
        <!-- 地图区域包装器 -->
        <div class="map-wrapper">
          <div id="track-playback-map" class="map"></div>

          <!-- 浮动报警按钮 -->
          <div class="floating-alert-button" @click="toggleAlertPanel">
            <el-badge :value="alertList.length" :max="99" type="danger">
              <el-icon class="alert-toggle-icon">
                <Warning />
              </el-icon>
            </el-badge>
            <span class="alert-button-text">报警</span>
          </div>

          <!-- 可展开的报警面板 -->
          <transition name="slide-left">
            <div v-show="isAlertPanelOpen" class="floating-alert-panel">
              <div class="alert-panel-header">
                <h3>报警信息</h3>
                <el-button @click="toggleAlertPanel" icon="Close" circle size="small" />
              </div>

              <div class="alert-list">
                <template v-if="alertList.length > 0">
                  <div
                    v-for="alert in alertList"
                    :key="alert.id"
                    class="alert-item"
                    :class="`alert-${alert.level}`"
                  >
                    <div class="alert-content">
                      <div class="alert-title">
                        <el-icon class="alert-icon">
                          <Warning v-if="alert.level === 'warning'" />
                          <Close v-else-if="alert.level === 'danger'" />
                          <InfoFilled v-else />
                        </el-icon>
                        <span>{{ alert.title }}</span>
                      </div>
                      <div class="alert-details">
                        <div class="alert-time">{{ alert.time }}</div>
                        <div class="alert-location">{{ alert.location }}</div>
                      </div>
                    </div>
                    <div class="alert-actions">
                      <el-button
                        type="primary"
                        size="small"
                        icon="Position"
                        @click="locateToAlert(alert)"
                        title="定位到此报警点"
                      >
                        定位
                      </el-button>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="alert-empty">
                    <el-empty description="暂无报警信息" :image-size="80">
                      <template #image>
                        <el-icon size="80" color="#94a3b8">
                          <Warning />
                        </el-icon>
                      </template>
                    </el-empty>
                  </div>
                </template>
              </div>
            </div>
          </transition>

          <!-- 视频风格播放控制条 - 悬浮在地图上 -->
          <div class="video-controls" v-if="trackData.length > 0">
            <div class="controls-container">
              <!-- 播放控制按钮 -->
              <div class="control-buttons">
                <el-button
                  :type="isPlaying ? 'danger' : 'success'"
                  :icon="isPlaying ? 'VideoPause' : 'VideoPlay'"
                  @click="togglePlayback"
                  circle
                  size="small"
                />
                <el-button icon="RefreshLeft" @click="resetPlayback" circle size="small" />
              </div>

              <!-- 时间显示 -->
              <div class="time-display">
                <span class="current-time">{{ formatTime(currentTime) }}</span>
              </div>

              <!-- 进度条 -->
              <div class="progress-container">
                <el-slider
                  v-model="currentPointIndex"
                  :max="trackData.length - 1"
                  :step="1"
                  @change="handleProgressChange"
                  :format-tooltip="formatTimeTooltip"
                  :show-tooltip="true"
                />
              </div>

              <!-- 总时间 -->
              <div class="time-display">
                <span class="total-time">{{ formatTime(totalTime) }}</span>
              </div>

              <!-- 速度控制 -->
              <div class="speed-control-mini">
                <span class="speed-label">{{ playbackSpeed }}x</span>
                <el-select v-model="playbackSpeed" @change="updatePlaybackSpeed" size="small" style="width: 60px">
                  <el-option label="0.5x" :value="0.5" />
                  <el-option label="1x" :value="1" />
                  <el-option label="2x" :value="2" />
                  <el-option label="5x" :value="5" />
                  <el-option label="10x" :value="10" />
                </el-select>
              </div>
            </div>
          </div>
        </div>

        <!-- 轨迹信息面板 -->
        <div class="info-panel" v-if="trackData.length > 0">
          <!-- 速度曲线图 -->
          <SpeedChart
            :trackData="trackData"
            :currentIndex="currentPointIndex"
            :height="180"
            @timePointClick="handleTimePointClick"
          />
          
          <div class="current-info">
            <h4>当前位置信息</h4>
            <div class="info-item">
              <span class="label">时间:</span>
              <span class="value">{{ currentPointInfo.time }}</span>
            </div>
            <div class="info-item">
              <span class="label">速度:</span>
              <span class="value">{{ currentPointInfo.speed }} km/h</span>
            </div>
            <div class="info-item">
              <span class="label">方向:</span>
              <span class="value">{{ currentPointInfo.direction }}°</span>
            </div>
            <div class="info-item">
              <span class="label">位置:</span>
              <span class="value">{{ currentPointInfo.address }}</span>
            </div>
          </div>

          <div class="statistics">
            <h4>轨迹统计</h4>
            <div class="info-item">
              <span class="label">总里程:</span>
              <span class="value">{{ trackStatistics.totalDistance }} km</span>
            </div>
            <div class="info-item">
              <span class="label">最高速度:</span>
              <span class="value">{{ trackStatistics.maxSpeed }} km/h</span>
            </div>
            <div class="info-item">
              <span class="label">平均速度:</span>
              <span class="value">{{ trackStatistics.avgSpeed }} km/h</span>
            </div>
            <div class="info-item">
              <span class="label">行驶时间:</span>
              <span class="value">{{ trackStatistics.duration }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Van } from '@element-plus/icons-vue';
import {
  createOptimizedMap,
  addOptimizedControls,
  createOptimizedPolyline,
  suppressMapWarnings
} from '@/utils/mapConfig';
import SpeedChart from '@/components/SpeedChart/index.vue';

const props = defineProps({
  vehicle: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['close']);

const { proxy } = getCurrentInstance();

// 响应式数据
const selectedDate = ref(new Date().toISOString().split('T')[0]);
const selectedTimeRange = ref(['08:00:00', '18:00:00']);
const trackData = ref([]);
const mapInstance = ref(null);
const mapLoading = ref(false);
const isPlaying = ref(false);
const playbackSpeed = ref(1);
const currentPointIndex = ref(0);
const vehicleMarker = ref(null);
const trackPolyline = ref(null);
const passedPolyline = ref(null);
const playbackInterval = ref(null);

// 当前位置信息
const currentPointInfo = ref({
  time: '',
  speed: 0,
  direction: 0,
  address: ''
});

// 轨迹统计信息
const trackStatistics = ref({
  totalDistance: 0,
  maxSpeed: 0,
  avgSpeed: 0,
  duration: ''
});

// 计算当前时间
const currentTime = computed(() => {
  if (trackData.value.length === 0) return 0;
  const point = trackData.value[currentPointIndex.value];
  if (!point || !point.time) return 0;

  // 从时间字符串解析出秒数
  const timeStr = point.time;
  const [hours, minutes, seconds] = timeStr.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds;
});

// 计算总时间
const totalTime = computed(() => {
  if (trackData.value.length === 0) return 0;
  const lastPoint = trackData.value[trackData.value.length - 1];
  if (!lastPoint || !lastPoint.time) return 0;

  const timeStr = lastPoint.time;
  const [hours, minutes, seconds] = timeStr.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds;
});

// 报警数据
const alertList = ref([]);
const alertMarkers = ref([]);
const isAlertPanelOpen = ref(false);

// 切换报警面板显示状态
function toggleAlertPanel() {
  isAlertPanelOpen.value = !isAlertPanelOpen.value;
}

// 初始化地图
onMounted(() => {
  console.log('组件挂载，开始初始化地图');
  // 等待DOM完全渲染后再初始化地图
  nextTick(() => {
    // 等待AMap API加载完成
    waitForAMapAPI().then(() => {
      console.log('AMap API加载完成，开始初始化地图');
      // 再次确保DOM已经渲染
      setTimeout(() => {
        initMap();
      }, 200);
    }).catch(error => {
      console.error('AMap API加载失败:', error);
      proxy?.$modal.msgError('地图API加载失败，请刷新页面重试');
      mapLoading.value = false;
    });
  });
});

// 清理资源
onUnmounted(() => {
  if (playbackInterval.value) {
    clearInterval(playbackInterval.value);
  }
  if (mapInstance.value) {
    mapInstance.value.destroy();
  }
});

// 等待AMap API加载
function waitForAMapAPI() {
  return new Promise((resolve, reject) => {
    console.log('开始等待AMap API加载...');

    // 如果AMap已经存在，直接resolve
    if (typeof AMap !== 'undefined' && AMap.Map) {
      console.log('AMap API已经可用');
      resolve(AMap);
      return;
    }

    let attempts = 0;
    const maxAttempts = 100; // 增加到10秒超时

    const checkAMap = setInterval(() => {
      attempts++;
      console.log(`等待AMap API... 尝试次数: ${attempts}`);

      if (typeof AMap !== 'undefined' && AMap.Map) {
        console.log('AMap API加载成功!');
        clearInterval(checkAMap);
        resolve(AMap);
      } else if (attempts >= maxAttempts) {
        console.error('AMap API加载超时');
        clearInterval(checkAMap);
        reject(new Error('AMap API加载超时'));
      }
    }, 100);
  });
}

// 初始化地图
function initMap() {
  // 防止重复初始化
  if (mapInstance.value) {
    console.log('地图已存在，跳过重复初始化');
    return;
  }

  mapLoading.value = true;
  console.log('开始初始化地图...');

  try {
    suppressMapWarnings();

    // 检查地图容器是否存在
    const container = document.getElementById('track-playback-map');
    if (!container) {
      console.error('地图容器未找到');
      proxy?.$modal.msgError('地图容器初始化失败');
      mapLoading.value = false;
      return;
    }

    console.log('地图容器找到:', container);

    const map = createOptimizedMap('track-playback-map', {
      center: [116.397428, 39.90923],
      zoom: 13
    });

    console.log('地图实例创建成功:', map);

    addOptimizedControls(map);
    mapInstance.value = map;

    map.on('complete', () => {
      console.log('地图加载完成');
      mapLoading.value = false;
    });

    // 添加错误处理
    map.on('error', (error) => {
      console.error('地图加载错误:', error);
      mapLoading.value = false;
      proxy?.$modal.msgError('地图加载失败，请刷新页面重试');
    });

  } catch (error) {
    console.error('地图初始化失败:', error);
    mapLoading.value = false;
    proxy?.$modal.msgError('地图初始化失败，请刷新页面重试');
  }
}

// 获取车辆类型文本
function getVehicleTypeText(type) {
  const typeMap = {
    'bus': '公交车',
    'electric_bus': '电动公交',
    'hybrid_bus': '混合动力'
  };
  return typeMap[type] || type;
}

// 处理日期变化
function handleDateChange() {
  trackData.value = [];
  resetPlayback();
}

// 处理时间范围变化
function handleTimeRangeChange() {
  if (trackData.value.length > 0) {
    loadTrackData();
    generateMockAlertData();
  }
}

// 聚合相近的报警点
function clusterAlerts(alerts) {
  const clustered = [];
  const processed = new Set();
  const threshold = 0.001; // 约100米的聚合距离

  alerts.forEach((alert, index) => {
    if (processed.has(index)) return;

    const cluster = {
      ...alert,
      alerts: [alert],
      count: 1
    };

    // 查找相近的报警点
    alerts.forEach((otherAlert, otherIndex) => {
      if (index === otherIndex || processed.has(otherIndex)) return;

      const distance = Math.sqrt(
        Math.pow(alert.longitude - otherAlert.longitude, 2) +
        Math.pow(alert.latitude - otherAlert.latitude, 2)
      );

      if (distance < threshold) {
        cluster.alerts.push(otherAlert);
        cluster.count++;
        processed.add(otherIndex);
      }
    });

    processed.add(index);
    clustered.push(cluster);
  });

  return clustered;
}

// 在地图上添加报警标记
function addAlertMarkers() {
  if (!mapInstance.value || !window.AMap || alertList.value.length === 0) return;

  // 清除之前的标记
  alertMarkers.value.forEach(marker => {
    mapInstance.value.remove(marker);
  });
  alertMarkers.value = [];

  // 聚合报警点
  const clusteredAlerts = clusterAlerts(alertList.value);

  // 批量创建标记，避免逐个添加
  const markers = [];
  clusteredAlerts.forEach(cluster => {
    const alert = cluster.alerts[0]; // 使用第一个报警作为主要信息
    const isCluster = cluster.count > 1;

    // 确定图标颜色（优先显示最严重的级别）
    const levels = cluster.alerts.map(a => a.level);
    const iconColor = levels.includes('danger') ? '#ff4757' :
                     levels.includes('warning') ? '#ffa726' : '#42a5f5';

    const marker = new window.AMap.Marker({
      position: [alert.longitude, alert.latitude],
      icon: new window.AMap.Icon({
        size: new window.AMap.Size(isCluster ? 40 : 32, isCluster ? 40 : 32),
        image: `data:image/svg+xml;base64,${btoa(`
          <svg xmlns="http://www.w3.org/2000/svg" width="${isCluster ? 40 : 32}" height="${isCluster ? 40 : 32}" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" fill="${iconColor}" stroke="#fff" stroke-width="2"/>
            ${isCluster ?
              `<circle cx="12" cy="12" r="6" fill="#fff" opacity="0.9"/>
               <text x="12" y="16" text-anchor="middle" fill="${iconColor}" font-size="10" font-weight="bold">${cluster.count}</text>` :
              `<text x="12" y="16" text-anchor="middle" fill="white" font-size="14" font-weight="bold">!</text>`
            }
          </svg>
        `)}`,
        imageSize: new window.AMap.Size(isCluster ? 40 : 32, isCluster ? 40 : 32)
      }),
      title: isCluster ? `${cluster.count}个报警` : alert.title,
      extData: {
        alertId: alert.id,
        isCluster: isCluster,
        alerts: cluster.alerts
      }
    });

    // 添加点击事件
    marker.on('click', () => {
      const content = isCluster ? createClusterInfoWindow(cluster) : createSingleAlertInfoWindow(alert, iconColor);

      const infoWindow = new window.AMap.InfoWindow({
        content: content,
        offset: new window.AMap.Pixel(0, -30),
        isCustom: true,
        autoMove: true,
        closeWhenClickMap: true
      });

      infoWindow.open(mapInstance.value, marker.getPosition());

      // 在信息窗口打开后添加滚轮事件处理
      setTimeout(() => {
        const infoWindowDom = infoWindow.getContentDom();
        if (infoWindowDom) {
          // 全面阻止滚轮事件传播到地图
          const preventMapZoom = (e) => {
            e.stopPropagation();
            e.preventDefault();

            // 找到可滚动的容器
            const scrollableContainer = infoWindowDom.querySelector('[data-scrollable="true"]');
            if (scrollableContainer && e.target.closest('[data-scrollable="true"]')) {
              // 手动处理滚动
              const delta = e.deltaY;
              const scrollStep = 30; // 滚动步长

              if (delta > 0) {
                // 向下滚动
                scrollableContainer.scrollTop += scrollStep;
              } else {
                // 向上滚动
                scrollableContainer.scrollTop -= scrollStep;
              }
            }
          };

          // 添加多种事件监听确保覆盖所有情况
          infoWindowDom.addEventListener('wheel', preventMapZoom, { passive: false, capture: true });
          infoWindowDom.addEventListener('mousewheel', preventMapZoom, { passive: false, capture: true });
          infoWindowDom.addEventListener('DOMMouseScroll', preventMapZoom, { passive: false, capture: true });

          // 为所有子元素也添加事件处理
          const allElements = infoWindowDom.querySelectorAll('*');
          allElements.forEach(element => {
            element.addEventListener('wheel', (e) => {
              e.stopPropagation();
            }, { passive: false });
          });
        }
      }, 100);
    });

    marker.alertId = alert.id;
    markers.push(marker);
  });

  // 批量添加到地图
  if (markers.length > 0) {
    mapInstance.value.add(markers);
    alertMarkers.value = markers;
  }

  console.log('报警标记已批量添加到地图');
}

// 创建聚合信息窗口内容
function createClusterInfoWindow(cluster) {
  const alertsHtml = cluster.alerts.map(alert => {
    const iconColor = alert.level === 'danger' ? '#ff4757' :
                     alert.level === 'warning' ? '#ffa726' : '#42a5f5';

    return `
      <div style="
        margin-bottom: 12px;
        padding: 8px;
        background: rgba(30, 41, 59, 0.6);
        border-radius: 8px;
        border-left: 4px solid ${iconColor};
      ">
        <div style="
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 6px;
        ">
          <div style="
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: ${iconColor};
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: white;
          ">!</div>
          <span style="color: #f8fafc; font-size: 13px; font-weight: 600;">${alert.title}</span>
        </div>
        <div style="font-size: 11px; color: #94a3b8; margin-bottom: 4px;">${alert.time}</div>
        <div style="font-size: 11px; color: #cbd5e1;">${alert.description}</div>
      </div>
    `;
  }).join('');

  return `
    <div style="
      padding: 16px;
      max-width: 320px;
      max-height: 400px;
      background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%);
      border: 1px solid rgba(147, 197, 253, 0.3);
      border-radius: 12px;
      backdrop-filter: blur(15px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
      color: #f8fafc;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow: hidden;
    ">
      <div style="
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid rgba(147, 197, 253, 0.2);
      ">
        <h4 style="
          margin: 0;
          color: #f8fafc;
          font-size: 16px;
          font-weight: 600;
        ">此位置有 ${cluster.count} 个报警</h4>
      </div>
      <div data-scrollable="true" style="
        max-height: 300px;
        overflow-y: auto;
        padding-right: 8px;
        scrollbar-width: thin;
        scrollbar-color: rgba(147, 197, 253, 0.5) rgba(30, 41, 59, 0.3);
      ">
        <style>
          [data-scrollable="true"]::-webkit-scrollbar {
            width: 6px;
          }
          [data-scrollable="true"]::-webkit-scrollbar-track {
            background: rgba(30, 41, 59, 0.3);
            border-radius: 3px;
          }
          [data-scrollable="true"]::-webkit-scrollbar-thumb {
            background: rgba(147, 197, 253, 0.5);
            border-radius: 3px;
          }
          [data-scrollable="true"]::-webkit-scrollbar-thumb:hover {
            background: rgba(147, 197, 253, 0.7);
          }
        </style>
        ${alertsHtml}
      </div>
    </div>
  `;
}

// 创建单个报警信息窗口内容
function createSingleAlertInfoWindow(alert, iconColor) {
  return `
    <div style="
      padding: 16px;
      max-width: 280px;
      background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%);
      border: 1px solid rgba(147, 197, 253, 0.3);
      border-radius: 12px;
      backdrop-filter: blur(15px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
      color: #f8fafc;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow: hidden;
    ">
      <div style="
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
        padding-bottom: 12px;
        border-bottom: 1px solid rgba(147, 197, 253, 0.2);
      ">
        <div style="
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: ${iconColor};
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: bold;
          color: white;
        ">!</div>
        <h4 style="
          margin: 0;
          color: ${iconColor};
          font-size: 16px;
          font-weight: 600;
          text-shadow: 0 0 10px rgba(96, 165, 250, 0.3);
        ">${alert.title}</h4>
      </div>
      <div data-scrollable="true" style="
        display: flex;
        flex-direction: column;
        gap: 8px;
        max-height: 300px;
        overflow-y: auto;
        padding-right: 8px;
        scrollbar-width: thin;
        scrollbar-color: rgba(147, 197, 253, 0.5) rgba(30, 41, 59, 0.3);
      ">
        <style>
          [data-scrollable="true"]::-webkit-scrollbar {
            width: 6px;
          }
          [data-scrollable="true"]::-webkit-scrollbar-track {
            background: rgba(30, 41, 59, 0.3);
            border-radius: 3px;
          }
          [data-scrollable="true"]::-webkit-scrollbar-thumb {
            background: rgba(147, 197, 253, 0.5);
            border-radius: 3px;
          }
          [data-scrollable="true"]::-webkit-scrollbar-thumb:hover {
            background: rgba(147, 197, 253, 0.7);
          }
        </style>
        <div style="
          display: flex;
          justify-content: space-between;
          padding: 6px 12px;
          background: rgba(30, 41, 59, 0.6);
          border-radius: 8px;
          border: 1px solid rgba(147, 197, 253, 0.1);
        ">
          <span style="color: #94a3b8; font-size: 12px; font-weight: 500;">时间:</span>
          <span style="color: #f8fafc; font-size: 12px; font-weight: 600;">${alert.time}</span>
        </div>
        <div style="
          display: flex;
          justify-content: space-between;
          padding: 6px 12px;
          background: rgba(30, 41, 59, 0.6);
          border-radius: 8px;
          border: 1px solid rgba(147, 197, 253, 0.1);
        ">
          <span style="color: #94a3b8; font-size: 12px; font-weight: 500;">位置:</span>
          <span style="color: #f8fafc; font-size: 12px; font-weight: 600;">${alert.location}</span>
        </div>
        <div style="
          padding: 8px 12px;
          background: rgba(15, 23, 42, 0.8);
          border-radius: 8px;
          border: 1px solid rgba(147, 197, 253, 0.1);
          color: #cbd5e1;
          font-size: 12px;
          line-height: 1.4;
        ">${alert.description}</div>
      </div>
    </div>
  `;
}

// 加载轨迹数据
function loadTrackData() {
  if (!selectedDate.value || !selectedTimeRange.value) {
    proxy?.$modal.msgWarning('请选择日期和时间范围');
    return;
  }

  mapLoading.value = true;

  // 模拟轨迹数据
  setTimeout(() => {
    trackData.value = generateMockTrackData();

    if (trackData.value.length > 0) {
      displayTrackOnMap();
      calculateStatistics();
      currentPointIndex.value = 0;
      updateCurrentPointInfo();
      generateMockAlertData();
      addAlertMarkers();
    } else {
      proxy?.$modal.msgInfo('选择的时间范围内没有轨迹数据');
    }

    mapLoading.value = false;
  }, 1000);
}

// 生成模拟轨迹数据
function generateMockTrackData() {
  const points = [];
  const startTime = new Date(`${selectedDate.value} ${selectedTimeRange.value[0]}`);
  const endTime = new Date(`${selectedDate.value} ${selectedTimeRange.value[1]}`);
  const duration = endTime - startTime;
  const pointCount = 120; // 2分钟一个点

  // 模拟路线：从首都机场到天安门的路线
  const route = [
    [116.597428, 40.0792], // 首都机场
    [116.587428, 40.0702],
    [116.577428, 40.0612],
    [116.567428, 40.0522],
    [116.557428, 40.0432],
    [116.547428, 40.0342],
    [116.537428, 40.0252],
    [116.527428, 40.0162],
    [116.517428, 40.0072],
    [116.507428, 39.9982],
    [116.497428, 39.9892],
    [116.487428, 39.9802],
    [116.477428, 39.9712],
    [116.467428, 39.9622],
    [116.457428, 39.9532],
    [116.447428, 39.9442],
    [116.437428, 39.9352],
    [116.427428, 39.9262],
    [116.417428, 39.9172],
    [116.407428, 39.9082],
    [116.397428, 39.90923] // 天安门
  ];

  for (let i = 0; i < pointCount; i++) {
    const progress = i / (pointCount - 1);
    const routeIndex = Math.floor(progress * (route.length - 1));
    const routeProgress = (progress * (route.length - 1)) - routeIndex;

    let lng, lat;
    if (routeIndex >= route.length - 1) {
      lng = route[route.length - 1][0];
      lat = route[route.length - 1][1];
    } else {
      const currentPoint = route[routeIndex];
      const nextPoint = route[routeIndex + 1];
      lng = currentPoint[0] + (nextPoint[0] - currentPoint[0]) * routeProgress;
      lat = currentPoint[1] + (nextPoint[1] - currentPoint[1]) * routeProgress;
    }

    // 添加随机偏移
    lng += (Math.random() - 0.5) * 0.002;
    lat += (Math.random() - 0.5) * 0.002;

    const time = new Date(startTime.getTime() + (duration * progress));
    const speed = 20 + Math.random() * 40; // 20-60 km/h
    const direction = Math.random() * 360;

    points.push({
      longitude: lng,
      latitude: lat,
      time: time.toLocaleTimeString(),
      speed: Math.round(speed),
      direction: Math.round(direction),
      address: `模拟地址 ${i + 1}`
    });
  }

  return points;
}

// 在地图上显示轨迹
function displayTrackOnMap() {
  if (!mapInstance.value || trackData.value.length === 0) return;

  // 清除旧的轨迹
  if (trackPolyline.value) {
    mapInstance.value.remove(trackPolyline.value);
  }
  if (passedPolyline.value) {
    mapInstance.value.remove(passedPolyline.value);
  }
  if (vehicleMarker.value) {
    mapInstance.value.remove(vehicleMarker.value);
  }

  // 创建轨迹线 - 验证path数据
  const path = trackData.value
    .filter(point => point && typeof point.longitude === 'number' && typeof point.latitude === 'number')
    .map(point => [point.longitude, point.latitude]);

  // 确保path至少有2个点才能创建有效的Polyline
  if (path.length < 2) {
    console.warn('轨迹数据不足，无法创建轨迹线');
    return;
  }

  trackPolyline.value = new AMap.Polyline({
    path: path,
    strokeColor: '#1890ff',
    strokeWeight: 4,
    strokeOpacity: 0.6,
    strokeStyle: 'dashed'
  });

  // 创建已经过轨迹线 - 使用第一个点初始化避免空路径
  const initialPoint = path[0];
  passedPolyline.value = new AMap.Polyline({
    path: [initialPoint],
    strokeColor: '#52c41a',
    strokeWeight: 6,
    strokeOpacity: 0.8
  });

  mapInstance.value.add([trackPolyline.value, passedPolyline.value]);

  // 创建车辆标记
  const firstPoint = trackData.value[0];
  vehicleMarker.value = new AMap.Marker({
    position: [firstPoint.longitude, firstPoint.latitude],
    icon: new AMap.Icon({
      image: 'data:image/svg+xml;base64,' + btoa(`
        <svg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <rect x='2' y='8' width='20' height='10' rx='2' fill='#409eff' stroke='white' stroke-width='1'/>
      <circle cx='7' cy='16' r='2' fill='white' stroke='#409eff' stroke-width='1'/>
      <circle cx='17' cy='16' r='2' fill='white' stroke='#409eff' stroke-width='1'/>
      <rect x='4' y='10' width='16' height='4' rx='1' fill='white' opacity='0.8'/>
    </svg>
      `),
      size: new AMap.Size(32, 32),
      imageSize: new AMap.Size(32, 32)
    }),
    anchor: 'center'
  });

  mapInstance.value.add(vehicleMarker.value);

  // 调整视野
  mapInstance.value.setFitView([trackPolyline.value], false, [50, 50, 50, 50]);
}

// 计算轨迹统计
function calculateStatistics() {
  if (trackData.value.length === 0) return;

  let totalDistance = 0;
  let maxSpeed = 0;
  let totalSpeed = 0;

  for (let i = 0; i < trackData.value.length; i++) {
    const point = trackData.value[i];
    maxSpeed = Math.max(maxSpeed, point.speed);
    totalSpeed += point.speed;

    if (i > 0) {
      const prevPoint = trackData.value[i - 1];
      const distance = AMap.GeometryUtil.distance(
        [prevPoint.longitude, prevPoint.latitude],
        [point.longitude, point.latitude]
      );
      totalDistance += distance;
    }
  }

  const avgSpeed = Math.round(totalSpeed / trackData.value.length);
  const startTime = trackData.value[0].time;
  const endTime = trackData.value[trackData.value.length - 1].time;

  trackStatistics.value = {
    totalDistance: (totalDistance / 1000).toFixed(2),
    maxSpeed,
    avgSpeed,
    duration: `${startTime} - ${endTime}`
  };
}

// 切换播放状态
function togglePlayback() {
  if (isPlaying.value) {
    pausePlayback();
  } else {
    startPlayback();
  }
}

// 开始播放
function startPlayback() {
  if (trackData.value.length === 0) return;

  // 清除现有间隔，避免重复创建
  if (playbackInterval.value) {
    clearInterval(playbackInterval.value);
  }

  isPlaying.value = true;

  // 优化：使用requestAnimationFrame替代setInterval以获得更流畅的动画
  let lastUpdate = 0;
  const updateInterval = 1000 / playbackSpeed.value;

  const animate = (currentTime) => {
    if (!isPlaying.value) return;

    if (currentTime - lastUpdate >= updateInterval) {
      if (currentPointIndex.value < trackData.value.length - 1) {
        currentPointIndex.value++;
        updateCurrentPointInfo();
        updateVehiclePosition();
        lastUpdate = currentTime;
      } else {
        pausePlayback();
        return;
      }
    }

    if (isPlaying.value) {
      requestAnimationFrame(animate);
    }
  };

  requestAnimationFrame(animate);
}

// 暂停播放
function pausePlayback() {
  isPlaying.value = false;
  if (playbackInterval.value) {
    clearInterval(playbackInterval.value);
    playbackInterval.value = null;
  }
}

// 重置播放
function resetPlayback() {
  pausePlayback();
  currentPointIndex.value = 0;
  updateCurrentPointInfo();
  updateVehiclePosition();
}

// 更新播放速度
function updatePlaybackSpeed() {
  if (isPlaying.value) {
    pausePlayback();
    startPlayback();
  }
}

// 处理进度条变化
function handleProgressChange() {
  updateCurrentPointInfo();
  updateVehiclePosition();
}

// 更新当前点信息
function updateCurrentPointInfo() {
  if (trackData.value.length === 0) return;

  const point = trackData.value[currentPointIndex.value];
  currentPointInfo.value = { ...point };
}

// 更新车辆位置
function updateVehiclePosition() {
  if (!vehicleMarker.value || trackData.value.length === 0 || !passedPolyline.value) return;

  const point = trackData.value[currentPointIndex.value];
  if (!point || typeof point.longitude !== 'number' || typeof point.latitude !== 'number') return;

  vehicleMarker.value.setPosition([point.longitude, point.latitude]);

  // 优化：批量更新轨迹，减少频繁的setPath调用
  if (currentPointIndex.value % 3 === 0 || currentPointIndex.value === trackData.value.length - 1) {
    const passedPath = trackData.value.slice(0, currentPointIndex.value + 1)
      .filter(p => p && typeof p.longitude === 'number' && typeof p.latitude === 'number')
      .map(p => [p.longitude, p.latitude]);

    // 确保至少有一个有效点才更新路径
    if (passedPath.length > 0) {
      passedPolyline.value.setPath(passedPath);
    }
  }
}

// 格式化进度条提示
function formatTimeTooltip(value) {
  if (trackData.value.length === 0) return '';
  const point = trackData.value[value];
  return point ? point.time : '';
}

// 格式化时间显示（秒转HH:MM:SS）
function formatTime(totalSeconds) {
  if (!totalSeconds && totalSeconds !== 0) return '00:00:00';

  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  return [hours, minutes, seconds]
    .map(val => String(val).padStart(2, '0'))
    .join(':');
}

// 生成模拟报警数据
function generateMockAlertData() {
  const alertTypes = [
    { title: '超速报警', level: 'danger', icon: 'Warning' },
    { title: '偏航报警', level: 'warning', icon: 'Position' },
    { title: '疲劳驾驶', level: 'danger', icon: 'Timer' },
    { title: '急刹车', level: 'warning', icon: 'Close' },
    { title: '路况拥堵', level: 'info', icon: 'InfoFilled' },
    { title: '设备故障', level: 'danger', icon: 'Tools' }
  ];

  const locations = [
    { name: '天安门广场', lat: 39.9042, lng: 116.4074 },
    { name: '王府井大街', lat: 39.9097, lng: 116.4186 },
    { name: '三里屯', lat: 39.9370, lng: 116.4458 },
    { name: '国贸中心', lat: 39.9086, lng: 116.4668 },
    { name: '机场高速', lat: 39.9797, lng: 116.4951 },
    { name: '西单商业区', lat: 39.9077, lng: 116.3752 }
  ];

  alertList.value = [];

  for (let i = 0; i < 10; i++) {
    const alertType = alertTypes[Math.floor(Math.random() * alertTypes.length)];
    let location;

    // 30% 的概率在相同位置生成报警以测试聚合功能
    if (i > 2 && Math.random() < 0.3) {
      // 使用已存在的位置
      const existingAlert = alertList.value[Math.floor(Math.random() * alertList.value.length)];
      location = {
        name: existingAlert.location,
        lat: existingAlert.latitude + (Math.random() - 0.5) * 0.0005, // 微小偏移以模拟真实情况
        lng: existingAlert.longitude + (Math.random() - 0.5) * 0.0005
      };
    } else {
      location = locations[Math.floor(Math.random() * locations.length)];
    }

    const time = new Date(Date.now() - Math.random() * 3600000).toLocaleTimeString(); // 过去1小时内随机时间

    alertList.value.push({
      id: `alert_${i + 1}`,
      title: alertType.title,
      level: alertType.level,
      time: time,
      location: location.name,
      latitude: location.lat,
      longitude: location.lng,
      description: `车辆在${location.name}附近发生${alertType.title}`
    });
  }

  console.log('生成报警数据:', alertList.value);
}

// 定位到报警点
function locateToAlert(alert) {
  if (!mapInstance.value) return;

  console.log('定位到报警点:', alert);

  // 立即定位，无动画
  mapInstance.value.setCenter([alert.longitude, alert.latitude]);
  mapInstance.value.setZoom(16);

  // 闪烁对应的报警标记 - 支持聚合标记
  const marker = alertMarkers.value.find(m => {
    const extData = m.getExtData();
    return extData.alertId === alert.id ||
           (extData.isCluster && extData.alerts.some(a => a.id === alert.id));
  });

  if (marker) {
    // 使用图标替换实现闪烁效果
    const originalIcon = marker.getIcon();
    const extData = marker.getExtData();

    // 根据是否为聚合标记确定颜色和尺寸
    const isCluster = extData.isCluster;
    const iconColor = isCluster ?
      (extData.alerts.some(a => a.level === 'danger') ? '#ff4757' :
       extData.alerts.some(a => a.level === 'warning') ? '#ffa726' : '#42a5f5') :
      (alert.level === 'danger' ? '#ff4757' :
       alert.level === 'warning' ? '#ffa726' : '#42a5f5');

    // 创建高亮图标
    const size = isCluster ? 44 : 36;
    const highlightIcon = new window.AMap.Icon({
      size: new window.AMap.Size(size, size),
      image: `data:image/svg+xml;base64,${btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="10" fill="${iconColor}" stroke="#fff" stroke-width="3"/>
          <circle cx="12" cy="12" r="6" fill="#fff" opacity="0.8"/>
          ${isCluster ?
            `<text x="12" y="16" text-anchor="middle" fill="${iconColor}" font-size="10" font-weight="bold">${extData.alerts.length}</text>` :
            `<text x="12" y="16" text-anchor="middle" fill="${iconColor}" font-size="14" font-weight="bold">!</text>`
          }
        </svg>
      `)}`,
      imageSize: new window.AMap.Size(size, size)
    });

    let flashCount = 0;
    const maxFlashes = 6;

    const flashInterval = setInterval(() => {
      if (flashCount >= maxFlashes) {
        clearInterval(flashInterval);
        // 恢复原始图标
        marker.setIcon(originalIcon);
        return;
      }

      // 切换图标实现闪烁
      if (flashCount % 2 === 0) {
        marker.setIcon(highlightIcon);
      } else {
        marker.setIcon(originalIcon);
      }
      flashCount++;
    }, 200);
  }
}

// 处理速度曲线图时间点击事件
function handleTimePointClick(timeIndex) {
  if (timeIndex >= 0 && timeIndex < trackData.value.length) {
    currentPointIndex.value = timeIndex;
    updateCurrentPointInfo();
    updateVehiclePosition();
    
    // 如果正在播放，暂停播放
    if (isPlaying.value) {
      pausePlayback();
    }
  }
}

// 暴露方法给父组件
defineExpose({
  loadTrackData
});
</script>

<style scoped>
.track-playback-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  position: relative;
  margin: 0;
  padding: 0;
}

/* 内容包装器 */
.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: calc(100vh - 160px);
}

.map-container {
  flex: 1;
  position: relative;
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  min-height: 0;
}

/* 地图区域包装器 */
.map-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
}

.map {
  width: 100% !important;
  height: 100% !important;
  position: absolute;
  top: 0;
  left: 0;
}

/* 浮动报警按钮 */
.floating-alert-button {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1001;
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid rgba(147, 197, 253, 0.3);
  border-radius: 12px;
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.floating-alert-button:hover {
  background: rgba(15, 23, 42, 0.98);
  border-color: rgba(147, 197, 253, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(96, 165, 250, 0.2);
}

.alert-toggle-icon {
  font-size: 18px;
  color: #ef4444;
}

.alert-button-text {
  color: #f8fafc;
  font-size: 14px;
  font-weight: 600;
}

/* 浮动报警面板 */
.floating-alert-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 300px;
  max-height: 85%;
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid rgba(147, 197, 253, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  z-index: 1002;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.alert-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.alert-panel-header h3 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

/* 滑入动画 */
.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.alert-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  height: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

.alert-empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-empty :deep(.el-empty) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.alert-empty :deep(.el-empty__description) {
  color: #94a3b8;
}

.alert-item {
  background: rgba(30, 41, 59, 0.8);
  border-radius: 8px;
  margin-bottom: 6px;
  padding: 8px;
  border-left: 4px solid transparent;
  transition: all 0.2s ease;
  cursor: pointer;
}

.alert-item:hover {
  background: rgba(30, 41, 59, 0.95);
  transform: translateX(2px);
}

.alert-item.alert-danger {
  border-left-color: #ef4444;
}

.alert-item.alert-warning {
  border-left-color: #f59e0b;
}

.alert-item.alert-info {
  border-left-color: #3b82f6;
}

.alert-content {
  flex: 1;
}

.alert-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.alert-icon {
  font-size: 16px;
}

.alert-danger .alert-icon {
  color: #ef4444;
}

.alert-warning .alert-icon {
  color: #f59e0b;
}

.alert-info .alert-icon {
  color: #3b82f6;
}

.alert-title span {
  color: #f8fafc;
  font-weight: 600;
  font-size: 14px;
}

.alert-details {
  margin-bottom: 8px;
}

.alert-time, .alert-location {
  font-size: 12px;
  color: #94a3b8;
  margin-bottom: 4px;
}

.alert-actions {
  display: flex;
  justify-content: flex-end;
}

.alert-actions .el-button {
  font-size: 12px;
  padding: 4px 8px;
}

.map {
  flex: 1;
  width: 100% !important;
  height: 100% !important;
  min-height: 0;
}

/* 视频风格播放控制条 - 悬浮在地图上 */
.video-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 800px;
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid rgba(147, 197, 253, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  z-index: 1001;
  padding: 10px 16px;
}

.info-panel {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 280px;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  padding: 16px;
  max-height: calc(100% - 140px);
  overflow-y: auto;
  z-index: 10;
}

.current-info,
.statistics {
  margin-bottom: 20px;
}

.current-info h4,
.statistics h4 {
  margin: 0 0 12px 0;
  color: #f8fafc;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  padding-bottom: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.info-item .label {
  color: #94a3b8;
  font-weight: 500;
}

.info-item .value {
  color: #f8fafc;
  font-weight: 600;
}

/* 视频风格播放控制条 - 固定在地图底部 */
.video-controls {
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  padding: 12px 16px;
  margin-top: 8px;
  flex-shrink: 0;
}

.controls-container {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.control-buttons {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-shrink: 0;
}

.control-buttons .el-button {
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-buttons .el-button.is-circle {
  width: 28px;
  height: 28px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-display {
  flex-shrink: 0;
  min-width: 55px;
  text-align: center;
}

.current-time,
.total-time {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  color: #e5e7eb;
  font-weight: 500;
}

.progress-container {
  flex: 1;
  margin: 0 6px;
  min-width: 150px;
}

.speed-control-mini {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-shrink: 0;
}

.speed-label {
  font-size: 11px;
  color: #94a3b8;
  min-width: 25px;
  text-align: center;
}

/* 自定义进度条样式 */
.progress-container :deep(.el-slider) {
  .el-slider__runway {
    background-color: rgba(148, 163, 184, 0.3);
    height: 6px;
    border-radius: 3px;
  }

  .el-slider__bar {
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 3px;
  }

  .el-slider__button {
    width: 14px;
    height: 14px;
    background: #f8fafc;
    border: 2px solid #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  }

  .el-slider__button:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-panel {
    flex-direction: column;
    gap: 16px;
  }

  .control-buttons {
    width: 100%;
    justify-content: center;
  }

  .time-selector {
    flex-wrap: wrap;
    justify-content: center;
  }

  .info-panel {
    position: relative;
    width: 100%;
    margin-top: 16px;
  }
}
</style>
