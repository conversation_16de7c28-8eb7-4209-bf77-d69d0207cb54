<template>
  <div class="alarm-analyse-container">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-left">
        <el-icon class="header-icon">
          <TrendCharts />
        </el-icon>
        <h1 class="page-title">报警分析</h1>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="Refresh" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <!-- 时间筛选区域 -->
    <div class="filter-section">
      <el-card shadow="never">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleDateRangeChange"
            />
          </el-form-item>
          <el-form-item label="报警类型">
            <el-select v-model="filterForm.alarmType" placeholder="请选择报警类型" clearable>
              <el-option label="全部" value="" />
              <el-option label="车辆故障" value="vehicle_fault" />
              <el-option label="超速报警" value="overspeed" />
              <el-option label="偏航报警" value="deviation" />
              <el-option label="紧急求助" value="emergency" />
              <el-option label="长时间停车" value="long_stop" />
            </el-select>
          </el-form-item>
          <el-form-item label="严重级别">
            <el-select v-model="filterForm.severity" placeholder="请选择严重级别" clearable>
              <el-option label="全部" value="" />
              <el-option label="低" value="low" />
              <el-option label="中" value="medium" />
              <el-option label="高" value="high" />
              <el-option label="紧急" value="critical" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="queryData">查询</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card total">
            <div class="stat-icon">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statisticsData.totalAlarms }}</div>
              <div class="stat-label">总报警数</div>
              <div class="stat-change" :class="{ increase: statisticsData.totalChange > 0, decrease: statisticsData.totalChange < 0 }">
                {{ statisticsData.totalChange > 0 ? '+' : '' }}{{ statisticsData.totalChange }}%
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card critical">
            <div class="stat-icon">
              <el-icon><Bell /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statisticsData.criticalAlarms }}</div>
              <div class="stat-label">紧急报警</div>
              <div class="stat-change" :class="{ increase: statisticsData.criticalChange > 0, decrease: statisticsData.criticalChange < 0 }">
                {{ statisticsData.criticalChange > 0 ? '+' : '' }}{{ statisticsData.criticalChange }}%
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card resolved">
            <div class="stat-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statisticsData.resolvedAlarms }}</div>
              <div class="stat-label">已处理</div>
              <div class="stat-change" :class="{ increase: statisticsData.resolvedChange > 0, decrease: statisticsData.resolvedChange < 0 }">
                {{ statisticsData.resolvedChange > 0 ? '+' : '' }}{{ statisticsData.resolvedChange }}%
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card rate">
            <div class="stat-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statisticsData.resolveRate }}%</div>
              <div class="stat-label">处理率</div>
              <div class="stat-change" :class="{ increase: statisticsData.rateChange > 0, decrease: statisticsData.rateChange < 0 }">
                {{ statisticsData.rateChange > 0 ? '+' : '' }}{{ statisticsData.rateChange }}%
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表分析区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <span>报警趋势分析</span>
            </template>
            <div ref="trendChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <span>报警类型分布</span>
            </template>
            <div ref="typeChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细分析区域 -->
    <div class="analysis-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <span>严重级别分布</span>
            </template>
            <div ref="severityChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="never" class="chart-card">
            <template #header>
              <span>处理时长分析</span>
            </template>
            <div ref="durationChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 热力图分析 -->
    <div class="heatmap-section">
      <el-card shadow="never" class="chart-card">
        <template #header>
          <div class="card-header">
            <span>报警时间热力图</span>
            <el-tooltip content="显示一天24小时和一周7天的报警分布情况">
              <el-icon><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <div ref="heatmapChartRef" class="heatmap-container"></div>
      </el-card>
    </div>
  </div>
</template>

<script setup name="AlarmAnalyse">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import {
  TrendCharts,
  Warning,
  Bell,
  CircleCheck,
  DataAnalysis,
  Refresh,
  InfoFilled
} from '@element-plus/icons-vue'

// 响应式数据
const filterForm = ref({
  dateRange: [],
  alarmType: '',
  severity: ''
})

const statisticsData = ref({
  totalAlarms: 0,
  totalChange: 0,
  criticalAlarms: 0,
  criticalChange: 0,
  resolvedAlarms: 0,
  resolvedChange: 0,
  resolveRate: 0,
  rateChange: 0
})

// 图表引用
const trendChartRef = ref(null)
const typeChartRef = ref(null)
const severityChartRef = ref(null)
const durationChartRef = ref(null)
const heatmapChartRef = ref(null)

// 图表实例
let trendChart = null
let typeChart = null
let severityChart = null
let durationChart = null
let heatmapChart = null

// 初始化页面
onMounted(() => {
  initializeData()
  nextTick(() => {
    initCharts()
  })
})

// 初始化数据
function initializeData() {
  // 设置默认时间范围为最近7天
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 7)

  filterForm.value.dateRange = [
    startDate.toISOString().slice(0, 19),
    endDate.toISOString().slice(0, 19)
  ]

  // 模拟统计数据
  statisticsData.value = {
    totalAlarms: 1247,
    totalChange: -12.5,
    criticalAlarms: 89,
    criticalChange: 15.2,
    resolvedAlarms: 1158,
    resolvedChange: -8.9,
    resolveRate: 92.9,
    rateChange: 3.4
  }
}

// 初始化所有图表
function initCharts() {
  initTrendChart()
  initTypeChart()
  initSeverityChart()
  initDurationChart()
  initHeatmapChart()
}

// 初始化趋势图表
function initTrendChart() {
  if (!trendChartRef.value) return

  trendChart = echarts.init(trendChartRef.value)

  // 根据筛选时间范围生成X轴数据
  const { xAxisData, chartData } = generateTrendData()

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      backgroundColor: 'rgba(30, 41, 59, 0.95)',
      borderColor: 'rgba(147, 197, 253, 0.2)',
      textStyle: {
        color: '#f1f5f9'
      }
    },
    legend: {
      data: ['总报警数', '紧急报警', '已处理'],
      textStyle: {
        color: '#f1f5f9'
      },
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLabel: {
        color: '#cbd5e1'
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(147, 197, 253, 0.2)'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#cbd5e1'
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(147, 197, 253, 0.2)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(147, 197, 253, 0.1)'
        }
      }
    },
    series: [
      {
        name: '总报警数',
        type: 'line',
        data: chartData.totalData,
        smooth: true,
        itemStyle: {
          color: '#06b6d4'
        }
      },
      {
        name: '紧急报警',
        type: 'line',
        data: chartData.criticalData,
        smooth: true,
        itemStyle: {
          color: '#ef4444'
        }
      },
      {
        name: '已处理',
        type: 'line',
        data: chartData.resolvedData,
        smooth: true,
        itemStyle: {
          color: '#10b981'
        }
      }
    ]
  }

  trendChart.setOption(option)
}

// 根据筛选条件生成趋势图数据
function generateTrendData() {
  const dateRange = filterForm.value.dateRange
  let xAxisData = []
  let totalData = []
  let criticalData = []
  let resolvedData = []

  if (dateRange && dateRange.length === 2) {
    const startDate = new Date(dateRange[0])
    const endDate = new Date(dateRange[1])
    const timeDiff = endDate.getTime() - startDate.getTime()
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24))

    // 根据时间范围生成数据
    for (let i = 0; i <= daysDiff; i++) {
      const currentDate = new Date(startDate)
      currentDate.setDate(startDate.getDate() + i)
      
      // 格式化日期显示
      const dateStr = currentDate.toLocaleDateString('zh-CN', { 
        month: 'short', 
        day: 'numeric' 
      })
      xAxisData.push(dateStr)

      // 模拟数据生成
      const baseValue = Math.floor(Math.random() * 100 + 50)
      const criticalValue = Math.floor(baseValue * 0.15 + Math.random() * 10)
      const resolvedValue = Math.floor(baseValue * 0.85 + Math.random() * 10)

      totalData.push(baseValue)
      criticalData.push(criticalValue)
      resolvedData.push(resolvedValue)
    }
  } else {
    // 默认显示最近7天
    const today = new Date()
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(today.getDate() - i)
      
      const dateStr = date.toLocaleDateString('zh-CN', { 
        month: 'short', 
        day: 'numeric' 
      })
      xAxisData.push(dateStr)

      const baseValue = Math.floor(Math.random() * 100 + 50)
      const criticalValue = Math.floor(baseValue * 0.15 + Math.random() * 10)
      const resolvedValue = Math.floor(baseValue * 0.85 + Math.random() * 10)

      totalData.push(baseValue)
      criticalData.push(criticalValue)
      resolvedData.push(resolvedValue)
    }
  }

  return {
    xAxisData,
    chartData: {
      totalData,
      criticalData,
      resolvedData
    }
  }
}

// 初始化类型分布图表
function initTypeChart() {
  if (!typeChartRef.value) return

  typeChart = echarts.init(typeChartRef.value)

  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(30, 41, 59, 0.95)',
      borderColor: 'rgba(147, 197, 253, 0.2)',
      textStyle: {
        color: '#f1f5f9'
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        color: '#f1f5f9'
      }
    },
    series: [
      {
        name: '报警类型',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: '车辆故障' },
          { value: 735, name: '超速报警' },
          { value: 580, name: '偏航报警' },
          { value: 484, name: '紧急求助' },
          { value: 300, name: '长时间停车' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  typeChart.setOption(option)
}

// 初始化严重级别图表
function initSeverityChart() {
  if (!severityChartRef.value) return

  severityChart = echarts.init(severityChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(30, 41, 59, 0.95)',
      borderColor: 'rgba(147, 197, 253, 0.2)',
      textStyle: {
        color: '#f1f5f9'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['低', '中', '高', '紧急'],
      axisLabel: {
        color: '#cbd5e1'
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(147, 197, 253, 0.2)'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#cbd5e1'
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(147, 197, 253, 0.2)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(147, 197, 253, 0.1)'
        }
      }
    },
    series: [
      {
        data: [520, 932, 701, 234],
        type: 'bar',
        itemStyle: {
          color: function(params) {
            const colors = ['#91CC75', '#FAC858', '#EE6666', '#73C0DE']
            return colors[params.dataIndex]
          }
        }
      }
    ]
  }

  severityChart.setOption(option)
}

// 初始化处理时长图表
function initDurationChart() {
  if (!durationChartRef.value) return

  durationChart = echarts.init(durationChartRef.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(30, 41, 59, 0.95)',
      borderColor: 'rgba(147, 197, 253, 0.2)',
      textStyle: {
        color: '#f1f5f9'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['<5分钟', '5-15分钟', '15-30分钟', '30-60分钟', '1-2小时', '>2小时'],
      axisLabel: {
        color: '#cbd5e1'
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(147, 197, 253, 0.2)'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#cbd5e1'
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(147, 197, 253, 0.2)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(147, 197, 253, 0.1)'
        }
      }
    },
    series: [
      {
        data: [320, 502, 301, 234, 190, 130],
        type: 'bar',
        itemStyle: {
          color: '#5470C6'
        }
      }
    ]
  }

  durationChart.setOption(option)
}

// 初始化热力图
function initHeatmapChart() {
  if (!heatmapChartRef.value) return

  heatmapChart = echarts.init(heatmapChartRef.value)

  const hours = ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11',
    '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23']
  const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

  const data = []
  for (let i = 0; i < 7; i++) {
    for (let j = 0; j < 24; j++) {
      data.push([j, i, Math.floor(Math.random() * 100)])
    }
  }

  const option = {
    tooltip: {
      position: 'top',
      backgroundColor: 'rgba(30, 41, 59, 0.95)',
      borderColor: 'rgba(147, 197, 253, 0.2)',
      textStyle: {
        color: '#f1f5f9'
      }
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: hours,
      splitArea: {
        show: true
      },
      axisLabel: {
        color: '#cbd5e1'
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(147, 197, 253, 0.2)'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: days,
      splitArea: {
        show: true
      },
      axisLabel: {
        color: '#cbd5e1'
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(147, 197, 253, 0.2)'
        }
      }
    },
    visualMap: {
      min: 0,
      max: 100,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%',
      textStyle: {
        color: '#cbd5e1'
      }
    },
    series: [
      {
        name: '报警数量',
        type: 'heatmap',
        data: data,
        label: {
          show: true
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  heatmapChart.setOption(option)
}

// 处理日期范围变化
function handleDateRangeChange(value) {
  if (value) {
    queryData()
  }
}

// 查询数据
function queryData() {
  // 这里应该调用实际的API接口获取数据
  ElMessage.success('数据查询完成')
  updateCharts()
}

// 重置筛选条件
function resetFilter() {
  filterForm.value = {
    dateRange: [],
    alarmType: '',
    severity: ''
  }
  initializeData()
  updateCharts()
}

// 刷新数据
function refreshData() {
  ElMessage.success('数据刷新完成')
  updateCharts()
}

// 更新所有图表
function updateCharts() {
  if (trendChart) initTrendChart()
  if (typeChart) initTypeChart()
  if (severityChart) initSeverityChart()
  if (durationChart) initDurationChart()
  if (heatmapChart) initHeatmapChart()
}

// 窗口大小变化时重新调整图表
window.addEventListener('resize', () => {
  if (trendChart) trendChart.resize()
  if (typeChart) typeChart.resize()
  if (severityChart) severityChart.resize()
  if (durationChart) durationChart.resize()
  if (heatmapChart) heatmapChart.resize()
})
</script>

<style scoped>
.alarm-analyse-container {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 24px;
  color: #06b6d4;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #f8fafc;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-section :deep(.el-card) {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(41, 52, 70, 0.6) 100%);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.filter-section :deep(.el-card__body) {
  padding: 20px 24px;
}

.filter-form :deep(.el-form-item__label) {
  color: #cbd5e1;
  font-weight: 500;
}

.filter-form :deep(.el-input__wrapper) {
  background: rgba(51, 65, 85, 0.8);
  border: 1px solid rgba(100, 116, 139, 0.4);
  box-shadow: none;
}

.filter-form :deep(.el-input__wrapper:hover) {
  border-color: rgba(59, 130, 246, 0.6);
}

.filter-form :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.filter-form :deep(.el-input__inner) {
  color: #f1f5f9;
  background: transparent;
}

.filter-form :deep(.el-input__inner::placeholder) {
  color: #94a3b8;
}

.filter-form :deep(.el-select .el-input__wrapper) {
  background: rgba(51, 65, 85, 0.8);
  border: 1px solid rgba(100, 116, 139, 0.4);
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(41, 52, 70, 0.7) 100%);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  cursor: pointer;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.stat-card:hover::before {
  transform: translateX(100%);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: rgba(147, 197, 253, 0.4);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  position: relative;
  z-index: 1;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.4);
}

.stat-card.critical .stat-icon {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.stat-card.resolved .stat-icon {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.stat-card.rate .stat-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
}

.stat-info {
  flex: 1;
  position: relative;
  z-index: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #f1f5f9;
  line-height: 1;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 14px;
  color: #94a3b8;
  margin-bottom: 4px;
  font-weight: 500;
}

.stat-change {
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-change.increase {
  color: #10b981;
}

.stat-change.decrease {
  color: #ef4444;
}

/* 图表区域 */
.charts-section,
.analysis-section,
.heatmap-section {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
}

.chart-card :deep(.el-card) {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(41, 52, 70, 0.7) 100%);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  height: 100%;
}

.chart-card :deep(.el-card__header) {
  background: rgba(15, 23, 42, 0.6);
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
  padding: 16px 20px;
}

.chart-card :deep(.el-card__body) {
  padding: 20px;
  height: calc(100% - 58px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #f1f5f9;
}

.card-header :deep(.el-select .el-input__wrapper) {
  background: rgba(51, 65, 85, 0.8);
  border: 1px solid rgba(100, 116, 139, 0.4);
}

.card-header :deep(.el-select .el-input__inner) {
  color: #f1f5f9;
}

.chart-container {
  height: 320px;
  width: 100%;
  background: rgba(15, 23, 42, 0.3);
  border-radius: 8px;
}

.heatmap-container {
  height: 400px;
  width: 100%;
  background: rgba(15, 23, 42, 0.3);
  border-radius: 8px;
}

.heatmap-section .chart-card {
  height: 480px;
}

/* 按钮样式重写 */
.header-right :deep(.el-button--primary) {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: 1px solid rgba(59, 130, 246, 0.5);
  color: white;
  backdrop-filter: blur(10px);
}

.header-right :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.filter-form :deep(.el-button--primary) {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: 1px solid rgba(59, 130, 246, 0.5);
  color: white;
}

.filter-form :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  border-color: #3b82f6;
}

.filter-form :deep(.el-button) {
  background: rgba(51, 65, 85, 0.8);
  border: 1px solid rgba(100, 116, 139, 0.4);
  color: #cbd5e1;
}

.filter-form :deep(.el-button:hover) {
  background: rgba(71, 85, 105, 0.8);
  border-color: rgba(147, 197, 253, 0.4);
  color: #f1f5f9;
}

/* 下拉框弹出层样式 */
:deep(.el-select-dropdown) {
  background: rgba(30, 41, 59, 0.95);
  border: 1px solid rgba(147, 197, 253, 0.2);
  backdrop-filter: blur(15px);
}

:deep(.el-select-dropdown .el-select-dropdown__item) {
  color: #cbd5e1;
}

:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background: rgba(59, 130, 246, 0.2);
  color: #f1f5f9;
}

:deep(.el-select-dropdown .el-select-dropdown__item.selected) {
  background: rgba(59, 130, 246, 0.3);
  color: #60a5fa;
}

/* 日期选择器弹出层样式 */
:deep(.el-picker-panel) {
  background: rgba(30, 41, 59, 0.95);
  border: 1px solid rgba(147, 197, 253, 0.2);
  backdrop-filter: blur(15px);
  color: #cbd5e1;
}

:deep(.el-picker-panel .el-date-picker__header) {
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

:deep(.el-picker-panel .el-picker-panel__content) {
  color: #cbd5e1;
}

/* 工具提示样式 */
:deep(.el-tooltip__content) {
  background: rgba(30, 41, 59, 0.95) !important;
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  backdrop-filter: blur(15px);
  color: #f1f5f9 !important;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .alarm-analyse-container {
    padding: 16px;
  }

  .stat-value {
    font-size: 28px;
  }

  .chart-container {
    height: 280px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .filter-form {
    flex-direction: column;
  }

  .filter-form :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .stat-card {
    padding: 16px;
    margin-bottom: 16px;
  }

  .stat-value {
    font-size: 24px;
  }

  .chart-container {
    height: 240px;
  }

  .heatmap-container {
    height: 320px;
  }
}
</style>
