<template>
  <div class="speed-chart-container">
    <div class="chart-header">
      <h4>速度曲线</h4>
    </div>
    <div
      ref="chartContainer"
      class="chart-content"
      :style="{ height: chartHeight + 'px' }"
    ></div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts';

const props = defineProps({
  trackData: {
    type: Array,
    default: () => []
  },
  currentIndex: {
    type: Number,
    default: 0
  },
  height: {
    type: Number,
    default: 200
  },
  isDragging: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['timePointClick']);

// 响应式数据
const chartContainer = ref(null);
const chartInstance = ref(null);
const chartHeight = computed(() => props.height);

// 监听数据变化
watch(
  () => [props.trackData, props.currentIndex, props.isDragging],
  () => {
    updateChart();
    // 只在非拖拽状态下更新当前时间标记
    if (!props.isDragging) {
      updateCurrentTimeMark();
    }
  },
  { deep: true }
);

// 监听容器尺寸变化
watch(chartHeight, () => {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
});

// 组件挂载时初始化图表
onMounted(() => {
  nextTick(() => {
    initChart();
  });
});

// 组件卸载时销毁图表
onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

// 初始化图表
function initChart() {
  if (!chartContainer.value) return;

  chartInstance.value = echarts.init(chartContainer.value, 'dark');
  
  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '50px',
      right: '20px',
      top: '30px',
      bottom: '45px'
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(15, 23, 42, 0.95)',
      borderColor: 'rgba(147, 197, 253, 0.3)',
      borderWidth: 1,
      textStyle: {
        color: '#f8fafc'
      },
      formatter: function(params) {
        const point = params[0];
        const dataIndex = point.dataIndex;
        const trackPoint = props.trackData[dataIndex];
        
        return `
          <div style="padding: 8px;">
            <div style="margin-bottom: 4px; font-weight: 600;">时间: ${trackPoint?.time || 'N/A'}</div>
            <div style="margin-bottom: 4px;">速度: <span style="color: #60a5fa; font-weight: 600;">${point.value} km/h</span></div>
            <div style="font-size: 12px; color: #94a3b8;">点击定位到此时间点</div>
          </div>
        `;
      }
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLine: {
        lineStyle: {
          color: 'rgba(147, 197, 253, 0.3)'
        }
      },
      axisLabel: {
        color: '#94a3b8',
        fontSize: 10,
        interval: 'auto',
        formatter: function(value, index) {
          // 只显示部分时间标签避免拥挤
          if (props.trackData.length > 20) {
            return index % Math.ceil(props.trackData.length / 6) === 0 ? value : '';
          }
          return value;
        }
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: 'km/h',
      nameTextStyle: {
        color: '#94a3b8',
        fontSize: 10
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(147, 197, 253, 0.3)'
        }
      },
      axisLabel: {
        color: '#94a3b8',
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(147, 197, 253, 0.1)',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '速度',
        type: 'line',
        data: [],
        smooth: true,
        lineStyle: {
          color: '#60a5fa',
          width: 2
        },
        itemStyle: {
          color: '#60a5fa'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(96, 165, 250, 0.3)' },
              { offset: 1, color: 'rgba(96, 165, 250, 0.05)' }
            ]
          }
        },
        symbol: 'circle',
        symbolSize: 4,
        emphasis: {
          focus: 'series',
          itemStyle: {
            color: '#3b82f6',
            borderColor: '#ffffff',
            borderWidth: 2,
            shadowColor: 'rgba(96, 165, 250, 0.5)',
            shadowBlur: 8
          }
        }
      }
    ]
  };

  chartInstance.value.setOption(option);
  
  // 添加点击事件
  chartInstance.value.on('click', function(params) {
    if (params.componentType === 'series') {
      const timeIndex = params.dataIndex;
      emit('timePointClick', timeIndex);
    }
  });

  // 添加高亮当前时间点的标记线
  updateCurrentTimeMark();
  
  // 更新图表数据
  updateChart();
}

// 更新图表数据
function updateChart() {
  if (!chartInstance.value || !props.trackData.length) return;

  const timeLabels = props.trackData.map(point => point.time || '');
  const speedData = props.trackData.map(point => point.speed || 0);

  const option = {
    xAxis: {
      data: timeLabels
    },
    series: [
      {
        data: speedData
      }
    ]
  };

  chartInstance.value.setOption(option);
}

// 更新当前时间点标记
function updateCurrentTimeMark() {
  if (!chartInstance.value || !props.trackData.length) return;

  const currentTime = props.trackData[props.currentIndex]?.time || '';
  
  const markLineOption = {
    series: [
      {
        markLine: {
          symbol: ['none', 'none'],
          symbolSize: 0,
          data: [
            {
              xAxis: props.currentIndex,
              lineStyle: {
                color: '#ef4444',
                width: 2,
                type: 'solid'
              },
              label: {
                show: false
              },
              symbol: ['none', 'none'],
              symbolSize: 0
            }
          ],
          silent: false,
          animation: false
        }
      }
    ]
  };

  chartInstance.value.setOption(markLineOption);
}

// 暴露方法给父组件
defineExpose({
  resize: () => {
    if (chartInstance.value) {
      chartInstance.value.resize();
    }
  },
  updateChart
});
</script>

<style scoped>
.speed-chart-container {
  background: rgba(30, 41, 59, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(147, 197, 253, 0.2);
  margin-bottom: 16px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(51, 65, 85, 0.6) 0%, rgba(30, 41, 59, 0.8) 100%);
}

.chart-header h4 {
  margin: 0;
  color: #f8fafc;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-header h4::before {
  content: '';
  width: 3px;
  height: 16px;
  background: linear-gradient(180deg, #60a5fa, #3b82f6);
  border-radius: 2px;
}



.chart-content {
  width: 100%;
  position: relative;
  background: rgba(15, 23, 42, 0.4);
}

/* 图表容器的深度样式调整 */
.chart-content :deep(.echarts-tooltip) {
  backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    padding: 10px 12px;
  }
}
</style>
