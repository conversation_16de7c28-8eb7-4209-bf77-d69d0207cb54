/* vis-timeline 样式覆盖 */
@import 'vis-timeline/styles/vis-timeline-graph2d.css';

/* 时间线容器样式 */
.vis-timeline {
  border: none !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif !important;
  background: #fafafa !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

/* 时间轴样式 */
.vis-time-axis {
  background: #fff !important;
  border-bottom: 1px solid #e4e7ed !important;
}

.vis-text {
  color: #606266 !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

.vis-text.vis-major {
  color: #303133 !important;
  font-weight: 600 !important;
}

/* 网格线样式 */
.vis-grid.vis-vertical {
  border-left: 1px solid #f0f0f0 !important;
}

.vis-grid.vis-vertical.vis-minor {
  border-left: 1px solid #f8f8f8 !important;
}

.vis-grid.vis-vertical.vis-major {
  border-left: 1px solid #e4e7ed !important;
}

/* 当前时间线样式 */
.vis-current-time {
  background-color: #f56c6c !important;
  width: 2px !important;
}

/* 项目样式 */
.vis-item {
  border: none !important;
  border-radius: 16px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  font-size: 13px !important;
  font-weight: 600 !important;
  color: #fff !important;
  padding: 8px 12px !important;
  cursor: move !important;
  transition: all 0.3s ease !important;
}

.vis-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  transform: translateY(-1px) !important;
}

/* 车辆项目样式 */
.vis-item.vehicle-item {
  background: linear-gradient(135deg, #409eff, #67c23a) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

.vis-item.vehicle-item:hover {
  background: linear-gradient(135deg, #337ecc, #529b2e) !important;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.4) !important;
}

.vis-item.vehicle-item.vis-selected {
  background: linear-gradient(135deg, #67c23a, #409eff) !important;
  border: 2px solid #fff !important;
  box-shadow: 0 4px 16px rgba(103, 194, 58, 0.5) !important;
}

/* 项目内容样式 */
.vis-item .vis-item-content {
  padding: 0 !important;
  margin: 0 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 拖拽时的样式 */
.vis-item.vis-drag-center {
  opacity: 0.8 !important;
  transform: rotate(2deg) !important;
  z-index: 999 !important;
}

/* 删除按钮样式 */
.vis-item .vis-delete {
  background: #f56c6c !important;
  border: none !important;
  border-radius: 50% !important;
  width: 18px !important;
  height: 18px !important;
  top: -6px !important;
  right: -6px !important;
  color: #fff !important;
  font-size: 12px !important;
  line-height: 18px !important;
  text-align: center !important;
  cursor: pointer !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.vis-item:hover .vis-delete {
  opacity: 1 !important;
}

.vis-item .vis-delete:hover {
  background: #f78989 !important;
  transform: scale(1.1) !important;
}

/* 拖拽手柄样式 */
.vis-item .vis-drag-left,
.vis-item .vis-drag-right {
  background: rgba(255, 255, 255, 0.3) !important;
  border-radius: 4px !important;
  width: 8px !important;
  cursor: ew-resize !important;
}

.vis-item:hover .vis-drag-left,
.vis-item:hover .vis-drag-right {
  background: rgba(255, 255, 255, 0.6) !important;
}

/* 时间线面板样式 */
.vis-panel {
  background: #fff !important;
}

.vis-panel.vis-center {
  background: #fafafa !important;
}

/* 标签轴样式 */
.vis-labelset {
  background: #fff !important;
  border-right: 1px solid #e4e7ed !important;
}

.vis-label {
  color: #606266 !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  padding: 8px 12px !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.vis-label:hover {
  background: #f5f7fa !important;
}

/* 自定义时间条样式 */
.vis-custom-time {
  background-color: #e6a23c !important;
  width: 2px !important;
  cursor: ew-resize !important;
}

.vis-custom-time > .vis-custom-time-marker {
  background-color: #e6a23c !important;
  color: #fff !important;
  border-radius: 4px !important;
  padding: 4px 8px !important;
  font-size: 11px !important;
  font-weight: 600 !important;
  white-space: nowrap !important;
}

/* 滚动条样式 */
.vis-timeline::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}

.vis-timeline::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
  border-radius: 4px !important;
}

.vis-timeline::-webkit-scrollbar-thumb {
  background: #c0c4cc !important;
  border-radius: 4px !important;
}

.vis-timeline::-webkit-scrollbar-thumb:hover {
  background: #909399 !important;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .vis-item {
    font-size: 11px !important;
    padding: 6px 8px !important;
  }

  .vis-text {
    font-size: 10px !important;
  }

  .vis-label {
    font-size: 11px !important;
    padding: 6px 8px !important;
  }
}

/* 动画效果 */
@keyframes vis-item-appear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.vis-item {
  animation: vis-item-appear 0.3s ease-out !important;
}

/* 冲突提示样式 */
.vis-item.conflict {
  background: linear-gradient(135deg, #f56c6c, #e6a23c) !important;
  animation: pulse 1s infinite !important;
}

@keyframes pulse {
  0% {
    box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
  }
  50% {
    box-shadow: 0 4px 16px rgba(245, 108, 108, 0.6);
  }
  100% {
    box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
  }
}

/* 拖拽区域高亮 */
.vis-timeline.drag-over {
  background: rgba(64, 158, 255, 0.05) !important;
  border: 2px dashed #409eff !important;
}

/* 空状态样式 */
.vis-timeline.empty-state {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 300px !important;
  color: #909399 !important;
  font-size: 14px !important;
}

.vis-timeline.empty-state::before {
  content: '将车辆拖拽到此处进行排班' !important;
  background: #f0f0f0 !important;
  padding: 20px 40px !important;
  border-radius: 20px !important;
  border: 2px dashed #d3d4d6 !important;
}
