<template>
  <div class='vehicle-speeding-analysis'>
    <el-row :gutter='20'>
      <!-- 左侧车辆和线路树 -->
      <el-col :lg='4' :xs='24'>
        <div class='left-panel'>
          <div class='panel-header'>
            <div class='header-left'>
              <el-icon>
                <Van />
              </el-icon>
              <span>车辆线路</span>
            </div>
          </div>
          <div class='panel-content'>
            <div class='search-box'>
              <el-input v-model='deptName' placeholder='请输入车辆或线路' prefix-icon='Search' clearable />
            </div>

            <el-tree
              ref='deptTreeRef'
              class='dept-tree'
              node-key='id'
              :data='deptOptions'
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node='false'
              :filter-node-method='filterNode'
              highlight-current
              default-expand-all
              @node-click='handleNodeClick'
            >
              <template #default="{ node, data }">
                <span class="tree-node">
                  <el-icon v-if="data.type === 'route'" class="route-icon">
                    <Connection />
                  </el-icon>
                  <el-icon v-else-if="data.type === 'vehicle'" class="vehicle-icon">
                    <Van />
                  </el-icon>
                  <span class="node-label" :class="data.type">{{ data.label }}</span>
                  <el-tag
                    v-if="data.type === 'vehicle' && data.speedingLevel"
                    :type="getSpeedingLevelType(data.speedingLevel)"
                    size="small"
                    style="margin-left: 8px;"
                  >
                    {{ getSpeedingLevelText(data.speedingLevel) }}
                  </el-tag>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
      </el-col>

      <el-col :lg='20' :xs='24'>
        <div class='right-content'>
          <!-- 筛选条件区域 -->
          <div class='filter-section'>
            <el-card class='filter-card'>
              <!-- 时间分析选择器 -->
              <TimeAnalysisSelector
                :initialAnalysisType="queryParams.analysisType"
                @params-change="handleTimeAnalysisChange"
                ref="timeAnalysisSelectorRef"
              >
                <template #actions>
                  <el-button type="primary" @click="handleQuery" icon="Search">查询</el-button>
                  <el-button @click="resetQuery" icon="Refresh">重置</el-button>
                  <el-button type="success" plain @click="handleExport" icon="Download">导出</el-button>
                  <el-button type="primary" plain @click="handlePrint" icon="Printer">打印</el-button>
                </template>
              </TimeAnalysisSelector>

              <!-- 其他筛选条件 -->
              <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px" style="margin-top: 16px;">
                <el-form-item label="车牌号" prop="plateNumber">
                  <el-input
                    v-model="queryParams.plateNumber"
                    placeholder="请输入车牌号"
                    clearable
                    style="width: 160px"
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="线路" prop="routeId">
                  <el-select v-model="queryParams.routeId" placeholder="请选择线路" clearable style="width: 120px">
                    <el-option
                      v-for="route in routeOptions"
                      :key="route.value"
                      :label="route.label"
                      :value="route.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="超速等级" prop="speedingLevel">
                  <el-select v-model="queryParams.speedingLevel" placeholder="请选择等级" clearable style="width: 120px">
                    <el-option label="全部" value="" />
                    <el-option label="轻微" value="light" />
                    <el-option label="中度" value="moderate" />
                    <el-option label="严重" value="severe" />
                  </el-select>
                </el-form-item>
              </el-form>
            </el-card>
          </div>

          <!-- Tab切换区域 -->
          <div class='tabs-section'>
            <!-- 统一的时间范围显示 -->
            <div class="time-range-info" v-if="queryParams.startTime && queryParams.endTime">
              <el-alert
                :title="`统计时间范围：${getTimeRangeText()} (${getAnalysisTypeText(queryParams.analysisType)})`"
                type="info"
                :closable="false"
                show-icon
                style="margin-bottom: 16px;"
              />
            </div>
            <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
              <!-- 超速统计图表 -->
              <el-tab-pane label="超速统计图表" name="speedingCharts">
                <div class="tab-content">
                  <!-- 统计卡片区域 -->
                  <div class='stats-section'>
                    <el-row :gutter='16'>
                      <el-col :span='6'>
                        <div class='stat-card total'>
                          <div class='stat-icon'>
                            <el-icon><Van /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.totalVehicles }}</div>
                            <div class='stat-label'>监控车辆</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card speeding'>
                          <div class='stat-icon'>
                            <el-icon><Warning /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.speedingVehicles }}</div>
                            <div class='stat-label'>超速车辆</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card rate'>
                          <div class='stat-icon'>
                            <el-icon><TrendCharts /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.speedingRate }}%</div>
                            <div class='stat-label'>超速率</div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span='6'>
                        <div class='stat-card speed'>
                          <div class='stat-icon'>
                            <el-icon><Odometer /></el-icon>
                          </div>
                          <div class='stat-info'>
                            <div class='stat-value'>{{ summaryStats.avgExcessSpeed }}</div>
                            <div class='stat-label'>平均超速(km/h)</div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 图表区域 -->
                  <div class='charts-section'>
                    <el-row :gutter='16'>
                      <el-col :span='8'>
                        <div class='chart-card'>
                          <div class='chart-header'>
                            <h3>超速等级分布</h3>
                          </div>
                          <div ref="speedingLevelChartRef" class='chart-container'></div>
                        </div>
                      </el-col>
                      <el-col :span='16'>
                        <div class='chart-card'>
                          <div class='chart-header'>
                            <h3>超速趋势图</h3>
                            <el-select v-model="trendType" size="small" style="width: 120px;">
                              <el-option label="7天" value="7d" />
                              <el-option label="30天" value="30d" />
                            </el-select>
                          </div>
                          <div ref="speedingTrendChartRef" class='chart-container'></div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                </div>
              </el-tab-pane>

              <!-- 超速明细报表 -->
              <el-tab-pane label="超速明细报表" name="speedingDetails">
                <div class="tab-content">
                  <div class='table-container' v-loading='loading'>
                    <div class="table-header">
                      <h4>超速明细报表</h4>
                    </div>
                    <el-table :data="speedingDetailsList" @selection-change="handleSelectionChange" style="width: 100%" height="520" max-height="720">
                      <el-table-column type="selection" width="55" align="center" />
                      <el-table-column label="序号" type="index" width="80" align="center" />
                      <el-table-column label="超速时间" align="center" prop="speedingTime" width="160" />
                      <el-table-column label="车牌号" align="center" prop="plateNumber" width="120" />
                      <el-table-column label="线路" align="center" prop="routeName" min-width="100" />
                      <el-table-column label="驾驶员" align="center" prop="driverName" min-width="100" />
                      <el-table-column label="超速地点" align="center" prop="speedingLocation" min-width="150" show-overflow-tooltip />
                      <el-table-column label="限速" align="center" prop="speedLimit" width="80">
                        <template #default="scope">
                          <span>{{ scope.row.speedLimit }}km/h</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="实际时速" align="center" prop="actualSpeed" width="100">
                        <template #default="scope">
                          <span :class="getSpeedClass(scope.row.actualSpeed, scope.row.speedLimit)">
                            {{ scope.row.actualSpeed }}km/h
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column label="超速值" align="center" prop="excessSpeed" width="100">
                        <template #default="scope">
                          <span :class="getExcessSpeedClass(scope.row.excessSpeed)">
                            {{ scope.row.excessSpeed }}km/h
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column label="持续时间" align="center" prop="duration" width="100">
                        <template #default="scope">
                          <span>{{ scope.row.duration }}秒</span>
                        </template>
                      </el-table-column>
                      <el-table-column label="超速等级" align="center" prop="speedingLevel" width="100">
                        <template #default="scope">
                          <el-tag :type="getSpeedingLevelType(scope.row.speedingLevel)" size="small">
                            {{ getSpeedingLevelText(scope.row.speedingLevel) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="处理状态" align="center" prop="handleStatus" width="100">
                        <template #default="scope">
                          <el-tag :type="getHandleStatusType(scope.row.handleStatus)" size="small">
                            {{ getHandleStatusText(scope.row.handleStatus) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="备注" align="center" prop="remark" min-width="120" show-overflow-tooltip />
                      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width" fixed="right">
                        <template #default="scope">
                          <el-button link type="primary" icon="View" @click="handleSpeedingDetail(scope.row)" size="small">详情</el-button>
                          <el-button
                            v-if="scope.row.handleStatus !== 'completed'"
                            link
                            type="success"
                            icon="Check"
                            @click="handleProcess(scope.row)"
                            size="small"
                          >
                            处理
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <pagination
                      v-show="speedingDetailsTotal > 0"
                      :total="speedingDetailsTotal"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      @pagination="getSpeedingDetailsList"
                    />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 详情弹窗 -->
    <el-dialog :title="dialogTitle" v-model="showDetailDialog" width="800px" append-to-body>
      <div v-if="detailData" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item v-if="detailData.plateNumber" label="车牌号">{{ detailData.plateNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.vehicleNumber" label="车辆编号">{{ detailData.vehicleNumber }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.routeName" label="所属线路">{{ detailData.routeName }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.driverName" label="驾驶员">{{ detailData.driverName }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.monitorDays" label="监控天数">{{ detailData.monitorDays }}天</el-descriptions-item>
          <el-descriptions-item v-if="detailData.speedingCount" label="超速次数">{{ detailData.speedingCount }}次</el-descriptions-item>
          <el-descriptions-item v-if="detailData.speedingDuration" label="超速时长">{{ detailData.speedingDuration }}分钟</el-descriptions-item>
          <el-descriptions-item v-if="detailData.maxSpeed" label="最高时速">{{ detailData.maxSpeed }}km/h</el-descriptions-item>
          <el-descriptions-item v-if="detailData.avgExcessSpeed" label="平均超速">{{ detailData.avgExcessSpeed }}km/h</el-descriptions-item>
          <el-descriptions-item v-if="detailData.speedingLevel" label="超速等级">
            <el-tag :type="getSpeedingLevelType(detailData.speedingLevel)" size="small">
              {{ getSpeedingLevelText(detailData.speedingLevel) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item v-if="detailData.speedingTime" label="超速时间">{{ detailData.speedingTime }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.speedingLocation" label="超速地点">{{ detailData.speedingLocation }}</el-descriptions-item>
          <el-descriptions-item v-if="detailData.speedLimit" label="限速">{{ detailData.speedLimit }}km/h</el-descriptions-item>
          <el-descriptions-item v-if="detailData.actualSpeed" label="实际时速">{{ detailData.actualSpeed }}km/h</el-descriptions-item>
          <el-descriptions-item v-if="detailData.excessSpeed" label="超速值">{{ detailData.excessSpeed }}km/h</el-descriptions-item>
          <el-descriptions-item v-if="detailData.duration" label="持续时间">{{ detailData.duration }}秒</el-descriptions-item>
          <el-descriptions-item v-if="detailData.handleStatus" label="处理状态">
            <el-tag :type="getHandleStatusType(detailData.handleStatus)" size="small">
              {{ getHandleStatusText(detailData.handleStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="备注" span="2">{{ detailData.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 处理弹窗 -->
    <el-dialog title="超速记录处理" v-model="showProcessDialog" width="600px" append-to-body>
      <div v-if="processData" class="process-content">
        <el-descriptions :column="2" border style="margin-bottom: 20px;">
          <el-descriptions-item label="车牌号">{{ processData.plateNumber }}</el-descriptions-item>
          <el-descriptions-item label="驾驶员">{{ processData.driverName }}</el-descriptions-item>
          <el-descriptions-item label="超速时间">{{ processData.speedingTime }}</el-descriptions-item>
          <el-descriptions-item label="超速地点">{{ processData.speedingLocation }}</el-descriptions-item>
          <el-descriptions-item label="超速值">{{ processData.excessSpeed }}km/h</el-descriptions-item>
          <el-descriptions-item label="超速等级">
            <el-tag :type="getSpeedingLevelType(processData.speedingLevel)" size="small">
              {{ getSpeedingLevelText(processData.speedingLevel) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <el-form :model="processForm" ref="processFormRef" label-width="100px">
          <el-form-item label="处理方式" prop="processType" :rules="[{ required: true, message: '请选择处理方式', trigger: 'change' }]">
            <el-select v-model="processForm.processType" placeholder="请选择处理方式" style="width: 100%">
              <el-option label="口头警告" value="warning" />
              <el-option label="书面警告" value="written_warning" />
              <el-option label="安全培训" value="training" />
              <el-option label="停班整改" value="suspension" />
              <el-option label="罚款处理" value="fine" />
            </el-select>
          </el-form-item>
          <el-form-item label="处理人员" prop="handler" :rules="[{ required: true, message: '请输入处理人员', trigger: 'blur' }]">
            <el-input v-model="processForm.handler" placeholder="请输入处理人员姓名" />
          </el-form-item>
          <el-form-item label="处理说明" prop="processNote" :rules="[{ required: true, message: '请输入处理说明', trigger: 'blur' }]">
            <el-input
              v-model="processForm.processNote"
              type="textarea"
              :rows="4"
              placeholder="请详细说明处理过程和结果"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showProcessDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmProcess" :loading="processLoading">确认处理</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="VehicleSpeedingAnalysis">
import { ref, reactive, onMounted, nextTick, watchEffect, computed } from 'vue';
import { Van, Connection, Warning, TrendCharts, Odometer } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import TimeAnalysisSelector from '@/components/TimeAnalysisSelector/index.vue';

const loading = ref(true);
const showDetailDialog = ref(false);
const showProcessDialog = ref(false);
const processLoading = ref(false);
const detailData = ref(null);
const processData = ref(null);
const activeTab = ref('speedingCharts');
const trendType = ref('7d');

// 树形相关
const deptName = ref('');
const deptTreeRef = ref();

// 图表引用
const speedingLevelChartRef = ref(null);
const speedingTrendChartRef = ref(null);
let speedingLevelChart = null;
let speedingTrendChart = null;

// 表单引用
const queryRef = ref();
const processFormRef = ref();
const timeAnalysisSelectorRef = ref();

// 处理表单数据
const processForm = reactive({
  processType: '',
  handler: '',
  processNote: ''
});

// 分页数据
const speedingDetailsTotal = ref(0);

// 各个Tab的数据列表
const speedingDetailsList = ref([]);

// 统计数据
const summaryStats = ref({
  totalVehicles: '256',
  speedingVehicles: '48',
  speedingRate: '18.8',
  avgExcessSpeed: '12.4'
});

// 线路选项
const routeOptions = ref([
  { label: '115路', value: '115' },
  { label: '135路', value: '135' },
  { label: '201路', value: '201' },
  { label: '202路', value: '202' },
  { label: '301路', value: '301' }
]);

// 车辆线路树数据
const deptOptions = ref([]);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  analysisType: '3', // 默认日分析
  startTime: undefined,
  endTime: undefined,
  dateRange: null,
  plateNumber: null,
  routeId: null,
  speedingLevel: null,
  vehicleId: null
});

// 计算弹窗标题
const dialogTitle = computed(() => {
  if (!detailData.value) return '详情';
  if (detailData.value.plateNumber) {
    return `${detailData.value.plateNumber} - 超速详情`;
  }
  return '车辆超速详情';
});

onMounted(() => {
  getTreeSelect();
  handleTabChange(activeTab.value);
});

/** 组织机构树筛选 */
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选组织机构树 */
watchEffect(() => {
  deptTreeRef.value?.filter(deptName.value);
}, {
  flush: 'post'
});

/** 查询车辆线路下拉树结构 */
async function getTreeSelect() {
  try {
    deptOptions.value = [
      {
        id: 'route_115',
        label: '115路',
        type: 'route',
        routeNumber: '115',
        children: [
          { id: 'vehicle_1', label: '京A12345 (V001)', type: 'vehicle', plateNumber: '京A12345', vehicleNumber: 'V001', routeId: 'route_115', speedingLevel: 'light' },
          { id: 'vehicle_2', label: '京A12346 (V002)', type: 'vehicle', plateNumber: '京A12346', vehicleNumber: 'V002', routeId: 'route_115', speedingLevel: 'moderate' },
          { id: 'vehicle_3', label: '京A12347 (V003)', type: 'vehicle', plateNumber: '京A12347', vehicleNumber: 'V003', routeId: 'route_115', speedingLevel: 'severe' }
        ]
      },
      {
        id: 'route_135',
        label: '135路',
        type: 'route',
        routeNumber: '135',
        children: [
          { id: 'vehicle_4', label: '京A12348 (V004)', type: 'vehicle', plateNumber: '京A12348', vehicleNumber: 'V004', routeId: 'route_135', speedingLevel: 'light' },
          { id: 'vehicle_5', label: '京A12349 (V005)', type: 'vehicle', plateNumber: '京A12349', vehicleNumber: 'V005', routeId: 'route_135', speedingLevel: 'moderate' }
        ]
      },
      {
        id: 'route_201',
        label: '201路',
        type: 'route',
        routeNumber: '201',
        children: [
          { id: 'vehicle_6', label: '京A12350 (V006)', type: 'vehicle', plateNumber: '京A12350', vehicleNumber: 'V006', routeId: 'route_201', speedingLevel: 'severe' },
          { id: 'vehicle_7', label: '京A12351 (V007)', type: 'vehicle', plateNumber: '京A12351', vehicleNumber: 'V007', routeId: 'route_201', speedingLevel: 'light' }
        ]
      },
      {
        id: 'route_301',
        label: '301路',
        type: 'route',
        routeNumber: '301',
        children: [
          { id: 'vehicle_8', label: '京A12352 (V008)', type: 'vehicle', plateNumber: '京A12352', vehicleNumber: 'V008', routeId: 'route_301', speedingLevel: 'moderate' },
          { id: 'vehicle_9', label: '京A12353 (V009)', type: 'vehicle', plateNumber: '京A12353', vehicleNumber: 'V009', routeId: 'route_301', speedingLevel: 'light' }
        ]
      }
    ];
  } catch (error) {
    console.error('获取车辆线路树失败:', error);
  }
}

/** 时间分析参数变化处理 */
function handleTimeAnalysisChange(params) {
  queryParams.analysisType = params.analysisType;
  queryParams.startTime = params.startTime;
  queryParams.endTime = params.endTime;

  // 更新统计数据和图表
  updateStatsData();
  if (activeTab.value === 'speedingCharts') {
    nextTick(() => {
      initCharts();
    });
  }
}

/** 更新统计数据 */
function updateStatsData() {
  // 根据选中的时间范围和类型更新统计数据
  const analysisText = getAnalysisTypeText(queryParams.analysisType);
  const timeRange = getTimeRangeText();

  // 更新统计卡片数据
  summaryStats.value = {
    totalVehicles: '256',
    speedingVehicles: '48',
    speedingRate: '18.8',
    avgExcessSpeed: '12.4'
  };
}

/** 获取分析类型文本 */
function getAnalysisTypeText(type) {
  const typeMap = {
    '0': '年度统计',
    '1': '周统计',
    '2': '月度统计',
    '3': '日统计',
    '4': '小时统计'
  };
  return typeMap[type] || '日统计';
}

/** 获取时间范围文本 */
function getTimeRangeText() {
  if (!queryParams.startTime || !queryParams.endTime) {
    return '未选择时间范围';
  }

  const startTime = new Date(queryParams.startTime);
  const endTime = new Date(queryParams.endTime);

  switch(queryParams.analysisType) {
    case '0': // 年度
      return `${startTime.getFullYear()}年 - ${endTime.getFullYear()}年`;
    case '1': // 周
      return `${startTime.toISOString().split('T')[0]} 至 ${endTime.toISOString().split('T')[0]} (周统计)`;
    case '2': // 月度
      return `${startTime.getFullYear()}-${(startTime.getMonth() + 1).toString().padStart(2, '0')} 至 ${endTime.getFullYear()}-${(endTime.getMonth() + 1).toString().padStart(2, '0')}`;
    case '3': // 日
      return `${startTime.toISOString().split('T')[0]} 至 ${endTime.toISOString().split('T')[0]}`;
    case '4': // 小时
      return `${startTime.toISOString().split('T')[0]} ${startTime.toTimeString().split(' ')[0]} 至 ${endTime.toISOString().split('T')[0]} ${endTime.toTimeString().split(' ')[0]}`;
    default:
      return '未知时间范围';
  }
}

/** 节点单击事件 */
function handleNodeClick(data) {
  if (data.type === 'route') {
    queryParams.routeId = data.routeNumber;
    queryParams.vehicleId = null;
    queryParams.plateNumber = null;
  } else if (data.type === 'vehicle') {
    queryParams.vehicleId = data.id;
    queryParams.plateNumber = data.plateNumber;
    queryParams.routeId = null;
  }
  handleQuery();
}

/** Tab切换事件 */
function handleTabChange(tabName) {
  queryParams.pageNum = 1;
  activeTab.value = tabName;
  loading.value = true;

  switch (tabName) {
    case 'speedingCharts':
      // 只更新图表，不加载表格数据
      loading.value = false;
      nextTick(() => {
        initCharts();
      });
      break;
    case 'speedingDetails':
      getSpeedingDetailsList();
      break;
  }
}

// 获取超速明细数据
function getSpeedingDetailsList() {
  loading.value = true;

  setTimeout(() => {
    const mockData = generateSpeedingDetailsData();
    speedingDetailsList.value = mockData.slice((queryParams.pageNum - 1) * queryParams.pageSize, queryParams.pageNum * queryParams.pageSize);
    speedingDetailsTotal.value = mockData.length;
    loading.value = false;
  }, 500);
}

// 生成超速明细模拟数据
function generateSpeedingDetailsData() {
  const data = [];
  const vehicles = [
    { plateNumber: '京A12345', routeName: '115路', driverName: '张志明' },
    { plateNumber: '京A12346', routeName: '135路', driverName: '李华强' },
    { plateNumber: '京A12347', routeName: '201路', driverName: '王建国' },
    { plateNumber: '京A12348', routeName: '301路', driverName: '陈美丽' }
  ];
  const locations = ['市中心路段', '开发区大道', '商业街路段', '工业园路段', '环城快速路'];

  for (let i = 0; i < 80; i++) {
    const vehicleIndex = Math.floor(Math.random() * vehicles.length);
    const locationIndex = Math.floor(Math.random() * locations.length);

    const baseDate = new Date();
    baseDate.setDate(baseDate.getDate() - Math.floor(i / 15));
    const hours = Math.floor(Math.random() * 18) + 6;
    const minutes = Math.floor(Math.random() * 60);
    baseDate.setHours(hours, minutes, 0);

    const speedLimit = [40, 50, 60, 80][Math.floor(Math.random() * 4)];
    const excessSpeed = Math.floor(Math.random() * 30) + 5;
    const actualSpeed = speedLimit + excessSpeed;
    const duration = Math.floor(Math.random() * 180) + 10;

    let speedingLevel, handleStatus;
    if (excessSpeed < 10) {
      speedingLevel = 'light';
      handleStatus = 'completed';
    } else if (excessSpeed < 20) {
      speedingLevel = 'moderate';
      handleStatus = Math.random() > 0.4 ? 'completed' : 'processing';
    } else {
      speedingLevel = 'severe';
      handleStatus = Math.random() > 0.6 ? 'processing' : 'pending';
    }

    data.push({
      id: i + 1,
      speedingTime: baseDate.toISOString().replace('T', ' ').substring(0, 19),
      ...vehicles[vehicleIndex],
      speedingLocation: locations[locationIndex],
      speedLimit,
      actualSpeed,
      excessSpeed,
      duration,
      speedingLevel,
      handleStatus,
      remark: Math.random() > 0.7 ? (speedingLevel === 'severe' ? '严重超速，已处罚' : '轻微超速') : null
    });
  }

  return data.sort((a, b) => new Date(b.speedingTime) - new Date(a.speedingTime));
}

// 初始化图表
function initCharts() {
  initSpeedingLevelChart();
  initSpeedingTrendChart();
}

// 初始化超速等级分布图表
function initSpeedingLevelChart() {
  if (!speedingLevelChartRef.value) return;

  speedingLevelChart = echarts.init(speedingLevelChartRef.value);
  updateSpeedingLevelChart();
}

// 更新超速等级分布图表
function updateSpeedingLevelChart() {
  if (!speedingLevelChart) return;

  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' }
    },
    legend: {
      orient: 'vertical',
      left: 'right',
      textStyle: { color: '#e5e7eb' },
      top: 'center'
    },
    series: [
      {
        name: '超速等级',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#0f172a',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
            color: '#e5e7eb'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 26, name: '轻微超速', itemStyle: { color: '#67C23A' } },
          { value: 15, name: '中度超速', itemStyle: { color: '#E6A23C' } },
          { value: 7, name: '严重超速', itemStyle: { color: '#F56C6C' } }
        ]
      }
    ]
  };

  speedingLevelChart.setOption(option);
}

// 初始化超速趋势图表
function initSpeedingTrendChart() {
  if (!speedingTrendChartRef.value) return;

  speedingTrendChart = echarts.init(speedingTrendChartRef.value);
  updateSpeedingTrendChart();
}

// 更新超速趋势图表
function updateSpeedingTrendChart() {
  if (!speedingTrendChart) return;

  const dates = [];
  const counts = [];

  // 根据分析类型生成不同的时间数据
  switch(queryParams.analysisType) {
    case '0': // 年度分析
      for (let i = 4; i >= 0; i--) {
        const year = new Date().getFullYear() - i;
        dates.push(`${year}年`);
        counts.push(Math.floor(Math.random() * 200) + 100);
      }
      break;
    case '1': // 周分析
      for (let i = 11; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i * 7);
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        dates.push(`${weekStart.getMonth() + 1}/${weekStart.getDate()}`);
        counts.push(Math.floor(Math.random() * 80) + 30);
      }
      break;
    case '2': // 月度分析
      for (let i = 11; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        dates.push(`${date.getMonth() + 1}月`);
        counts.push(Math.floor(Math.random() * 150) + 50);
      }
      break;
    case '3': // 日分析
      for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        dates.push(date.toISOString().substring(5, 10));
        counts.push(Math.floor(Math.random() * 15) + 5);
      }
      break;
    case '4': // 小时分析
      for (let i = 23; i >= 0; i--) {
        const hour = (new Date().getHours() - i + 24) % 24;
        dates.push(`${hour.toString().padStart(2, '0')}:00`);
        counts.push(Math.floor(Math.random() * 8) + 1);
      }
      break;
    default: // 默认使用日分析
      for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        dates.push(date.toISOString().substring(5, 10));
        counts.push(Math.floor(Math.random() * 15) + 5);
      }
  }

  // 根据分析类型设置Y轴名称
  const getYAxisName = () => {
    switch(queryParams.analysisType) {
      case '0': return '年度超速次数';
      case '1': return '周超速次数';
      case '2': return '月度超速次数';
      case '3': return '日超速次数';
      case '4': return '小时超速次数';
      default: return '超速次数';
    }
  };

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' },
      formatter: function(params) {
        const param = params[0];
        let timeLabel = '';
        switch(queryParams.analysisType) {
          case '0': timeLabel = '年份'; break;
          case '1': timeLabel = '周'; break;
          case '2': timeLabel = '月份'; break;
          case '3': timeLabel = '日期'; break;
          case '4': timeLabel = '时间'; break;
          default: timeLabel = '时间';
        }
        return `${timeLabel}: ${param.name}<br/>超速次数: ${param.value}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        color: '#94a3b8',
        rotate: queryParams.analysisType === '4' ? 45 : 0 // 小时分析时旋转标签
      },
      axisLine: { lineStyle: { color: '#374151' } },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      name: getYAxisName(),
      nameTextStyle: { color: '#94a3b8' },
      axisLabel: { color: '#94a3b8' },
      axisLine: { lineStyle: { color: '#374151' } },
      splitLine: { lineStyle: { color: '#374151', type: 'dashed' } }
    },
    series: [
      {
        name: '超速次数',
        type: 'line',
        data: counts,
        smooth: true,
        itemStyle: { color: '#F56C6C' },
        lineStyle: { color: '#F56C6C', width: 3 },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(245, 108, 108, 0.3)' },
            { offset: 1, color: 'rgba(245, 108, 108, 0.05)' }
          ])
        },
        symbol: 'circle',
        symbolSize: 6
      }
    ]
  };

  speedingTrendChart.setOption(option);
}

// 查询
function handleQuery() {
  queryParams.pageNum = 1;
  // 更新统计数据
  updateStatsData();
  handleTabChange(activeTab.value);
}

// 重置
function resetQuery() {
  queryRef.value?.resetFields();
  queryParams.routeId = null;
  queryParams.vehicleId = null;
  timeAnalysisSelectorRef.value?.reset();
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

// 表格选择
function handleSelectionChange(selection) {
  // 处理表格选择
}

// 查看详情
function handleDetail(row) {
  detailData.value = row;
  showDetailDialog.value = true;
}

// 查看超速详情
function handleSpeedingDetail(row) {
  detailData.value = {
    ...row,
    plateNumber: row.plateNumber,
    routeName: row.routeName
  };
  showDetailDialog.value = true;
}

// 处理超速记录
function handleProcess(row) {
  processData.value = row;
  showProcessDialog.value = true;
  // 重置表单
  Object.keys(processForm).forEach(key => {
    processForm[key] = '';
  });
}

// 确认处理
function confirmProcess() {
  processFormRef.value?.validate((valid) => {
    if (valid) {
      processLoading.value = true;

      // 模拟处理请求
      setTimeout(() => {
        // 更新处理状态
        const index = speedingDetailsList.value.findIndex(item => item.id === processData.value.id);
        if (index !== -1) {
          speedingDetailsList.value[index].handleStatus = 'completed';
          speedingDetailsList.value[index].remark = `${getProcessTypeText(processForm.processType)} - ${processForm.handler}`;
        }

        processLoading.value = false;
        showProcessDialog.value = false;
        ElMessage.success('处理完成');
      }, 1000);
    }
  });
}

// 获取处理方式文本
function getProcessTypeText(type) {
  const textMap = {
    'warning': '口头警告',
    'written_warning': '书面警告',
    'training': '安全培训',
    'suspension': '停班整改',
    'fine': '罚款处理'
  };
  return textMap[type] || '其他处理';
}

// 导出数据
function handleExport() {
  ElMessage.success('导出成功');
}

// 打印报表
function handlePrint() {
  window.print();
}

// 获取超速等级类型
function getSpeedingLevelType(level) {
  const typeMap = {
    'light': 'success',
    'moderate': 'warning',
    'severe': 'danger'
  };
  return typeMap[level] || 'info';
}

// 获取超速等级文本
function getSpeedingLevelText(level) {
  const textMap = {
    'light': '轻微',
    'moderate': '中度',
    'severe': '严重'
  };
  return textMap[level] || '未知';
}

// 获取处理状态类型
function getHandleStatusType(status) {
  const typeMap = {
    'completed': 'success',
    'processing': 'warning',
    'pending': 'danger'
  };
  return typeMap[status] || 'info';
}

// 获取处理状态文本
function getHandleStatusText(status) {
  const textMap = {
    'completed': '已处理',
    'processing': '处理中',
    'pending': '待处理'
  };
  return textMap[status] || '未知';
}

// 获取速度样式类
function getSpeedClass(actualSpeed, speedLimit) {
  const excess = actualSpeed - speedLimit;
  if (excess < 10) return 'speed-light';
  if (excess < 20) return 'speed-moderate';
  return 'speed-severe';
}

// 获取超速值样式类
function getExcessSpeedClass(excessSpeed) {
  if (excessSpeed < 10) return 'excess-light';
  if (excessSpeed < 20) return 'excess-moderate';
  return 'excess-severe';
}
</script>

<style scoped>
.vehicle-speeding-analysis {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 左侧面板样式 */
.left-panel {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
}

.panel-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.panel-content {
  padding: 24px;
}

.search-box {
  margin-bottom: 16px;
}

/* 右侧内容区域 */
.right-content {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(147, 197, 253, 0.1);
  backdrop-filter: blur(10px);
}

/* 筛选条件区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  background: rgba(15, 23, 42, 0.98) !important;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

/* 时间范围信息 */
.time-range-info {
  margin-bottom: 16px;
}

/* Tab区域 */
.tabs-section {
  margin-bottom: 20px;
}

.tab-content {
  padding-top: 20px;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(41, 52, 70, 0.6) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.2);
  border-color: rgba(96, 165, 250, 0.4);
}

.stat-card.total {
  border-left: 4px solid #409EFF;
}

.stat-card.speeding {
  border-left: 4px solid #F56C6C;
}

.stat-card.rate {
  border-left: 4px solid #E6A23C;
}

.stat-card.speed {
  border-left: 4px solid #67C23A;
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #60a5fa;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #f8fafc;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #94a3b8;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  background: rgba(10, 22, 48, 0.6);
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 12px;
  padding: 20px;
  height: 400px;
}

.chart-card .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.chart-card .chart-header h3 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

.chart-container {
  height: 320px;
  width: 100%;
}

/* 表格样式 */
.table-container {
  background: rgba(10, 22, 48, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.1);
  border-radius: 12px;
  padding: 20px;
  min-height: 620px;
  height: calc(100vh - 480px);
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.1);
}

.table-header h4 {
  margin: 0;
  color: #f8fafc;
  font-size: 16px;
  font-weight: 600;
}

/* 数据样式 */
.speeding-count {
  color: #F56C6C;
  font-weight: 600;
}

.speeding-duration {
  color: #E6A23C;
  font-weight: 600;
}

.max-speed {
  color: #F56C6C;
  font-weight: 600;
}

.avg-speed {
  color: #E6A23C;
  font-weight: 600;
}

.speed-light {
  color: #67C23A;
  font-weight: 600;
}

.speed-moderate {
  color: #E6A23C;
  font-weight: 600;
}

.speed-severe {
  color: #F56C6C;
  font-weight: 600;
}

.excess-light {
  color: #67C23A;
  font-weight: 600;
}

.excess-moderate {
  color: #E6A23C;
  font-weight: 600;
}

.excess-severe {
  color: #F56C6C;
  font-weight: 600;
}

/* 详情弹窗 */
.detail-content {
  max-height: 500px;
  overflow-y: auto;
}

/* 处理弹窗 */
.process-content {
  margin-top: 10px;
}

/* 树形组件样式 */
.dept-tree {
  background: transparent;
  color: #e5e7eb;
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
}

.tree-node {
  display: flex;
  align-items: center;
  font-size: 14px;
  flex: 1;
}

.route-icon {
  margin-right: 8px;
  color: #60a5fa;
  font-size: 16px;
  flex-shrink: 0;
}

.vehicle-icon {
  margin-right: 8px;
  color: #E6A23C;
  font-size: 16px;
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  min-width: 0;
  white-space: nowrap;
}

.node-label.route {
  font-weight: 500;
  color: #f1f5f9;
}

.node-label.vehicle {
  color: #94a3b8;
}

/* Element UI组件覆盖 */
:deep(.el-form-item__label) {
  color: #e5e7eb !important;
}

:deep(.el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e5e7eb !important;
}

:deep(.el-input__inner) {
  color: #e5e7eb !important;
}

:deep(.el-input__inner::placeholder) {
  color: #64748b !important;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(10, 22, 48, 0.8) !important;
}

:deep(.el-table) {
  background: transparent !important;
  color: #e5e7eb !important;
  height: 100% !important;
  flex: 1 !important;
}

:deep(.el-table tr) {
  background: rgba(15, 23, 42, 0.6) !important;
}

:deep(.el-table tr:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
}

:deep(.el-table th.el-table__cell) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #f8fafc !important;
  border-color: rgba(147, 197, 253, 0.2) !important;
}

:deep(.el-table td.el-table__cell) {
  border-color: rgba(147, 197, 253, 0.1) !important;
  color: #e5e7eb !important;
}

:deep(.el-tabs__header) {
  margin: 0 0 20px 0;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: 1px solid rgba(147, 197, 253, 0.2);
  border-radius: 8px;
  background: rgba(15, 23, 42, 0.8);
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  background: transparent;
  border: none;
  color: #94a3b8;
  transition: all 0.3s ease;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item:hover) {
  background: rgba(59, 130, 246, 0.1);
  color: #93c5fd;
}

/* Element Tree组件覆盖样式 */
:deep(.el-tree-node) {
  background: transparent;
}

:deep(.el-tree-node:hover > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 6px;
}

:deep(.el-tree-node__content) {
  background: transparent !important;
  border: none !important;
  color: #e5e7eb !important;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

:deep(.el-tree-node__content:hover) {
  background-color: rgba(59, 130, 246, 0.15) !important;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: rgba(59, 130, 246, 0.2) !important;
  color: #93c5fd !important;
}

:deep(.el-tree-node__expand-icon) {
  color: #64748b !important;
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.el-tree .el-tree-node__children) {
  overflow: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vehicle-speeding-analysis {
    padding: 10px;
  }

  .stats-section .el-col {
    margin-bottom: 16px;
  }

  .chart-card {
    height: 300px;
  }

  .chart-container {
    height: 220px;
  }
}
</style>
