<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 组织机构树 -->
      <el-col :lg="4" :xs="24">
        <el-card class="dept-tree-card" shadow="hover">
          <template #header>
            <div class="card-header-modern">
              <el-icon class="header-icon"><OfficeBuilding /></el-icon>
              <span class="header-title">组织架构</span>
            </div>
          </template>
          <el-input v-model="deptName" placeholder="搜索组织机构" prefix-icon="Search" clearable class="dept-search" />
          <el-tree
            ref="deptTreeRef"
            class="dept-tree"
            node-key="id"
            :data="deptOptions"
            :props="{ label: 'label', children: 'children' }"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>

      <el-col :lg="20" :xs="24">
        <!-- 搜索面板 -->
        <el-card class="search-panel" shadow="never" v-show="showSearch">
          <template #header>
            <div class="panel-header">
              <el-icon class="header-icon"><Search /></el-icon>
              <span class="header-title">搜索筛选</span>
            </div>
          </template>
          <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="80px">
            <el-form-item label="司机姓名" prop="driverName">
              <el-input v-model="queryParams.driverName" placeholder="请输入司机姓名" clearable prefix-icon="User" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工号" prop="employeeId">
              <el-input v-model="queryParams.employeeId" placeholder="请输入工号" clearable prefix-icon="Postcard" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="卡号" prop="cardNumber">
              <el-input v-model="queryParams.cardNumber" placeholder="请输入卡号" clearable prefix-icon="CreditCard" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="在职状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                <el-option label="在职" value="1">
                  <span style="float: left">在职</span>
                  <el-tag type="primary" size="small" style="float: right; background: #4ade80; border-color: #4ade80; color: white">●</el-tag>
                </el-option>
                <el-option label="离职" value="0">
                  <span style="float: left">离职</span>
                  <el-tag type="danger" size="small" style="float: right; background: #f87171; border-color: #f87171; color: white">●</el-tag>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 操作工具栏 -->
        <el-card class="toolbar-panel" shadow="never">
          <template #header>
            <div class="panel-header">
              <el-icon class="header-icon"><Operation /></el-icon>
              <span class="header-title">操作面板</span>
              <div class="header-extra">
                <span class="result-count">共 {{ total }} 名司机</span>
              </div>
            </div>
          </template>

          <div class="toolbar-content">
            <div class="action-buttons">
              <el-button type="primary" icon="Plus" @click="handleAdd"> 新增司机 </el-button>
              <el-tooltip content="请先选择一条数据进行编辑" placement="top" :disabled="!single">
                <el-button type="success" icon="Edit" :disabled="single" @click="handleUpdate"> 编辑信息 </el-button>
              </el-tooltip>
              <el-tooltip content="请先选择要删除的数据" placement="top" :disabled="!multiple">
                <el-button type="danger" icon="Delete" :disabled="multiple" @click="handleDelete"> 删除司机 </el-button>
              </el-tooltip>
              <el-dropdown @command="handleBatchCommand" :disabled="multiple" class="batch-dropdown">
                <el-button type="info" icon="Operation" :class="{ 'is-disabled-visual': multiple }">
                  批量操作
                  <el-tooltip content="请先选择要操作的数据" placement="top" :disabled="!multiple">
                    <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-tooltip>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="batchStatus" icon="Switch">
                      批量状态变更
                    </el-dropdown-item>
                    <el-dropdown-item command="batchDept" icon="OfficeBuilding">
                      批量调整组织机构
                    </el-dropdown-item>
                    <el-dropdown-item command="batchExport" icon="Download">
                      导出选中数据
                    </el-dropdown-item>
                    <el-dropdown-item command="batchPrint" icon="Printer" divided>
                      批量打印信息
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-button type="warning" icon="Download" @click="handleExport"> 导出数据 </el-button>
              <el-button type="primary" icon="Upload" @click="handleImport" plain> 导入数据 </el-button>
            </div>

            <div class="view-controls">
              <!-- 选择状态提示 -->
              <div class="selection-status" v-if="selectedDrivers.length > 0">
                <el-tag type="info" size="default" effect="dark">
                  <el-icon><Check /></el-icon>
                  已选择 {{ selectedDrivers.length }} 项
                </el-tag>
              </div>
              <div class="selection-status" v-else>
                <el-tag type="info" size="default" effect="plain">
                  <el-icon><Operation /></el-icon>
                  未选择数据
                </el-tag>
              </div>
              <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </div>
          </div>
        </el-card>

        <!-- 数据展示区域 -->
        <el-card class="data-panel" shadow="never">
          <template #header>
            <div class="panel-header">
              <el-icon class="header-icon"><User /></el-icon>
              <span class="header-title">司机管理</span>
            </div>
          </template>

          <!-- 卡片视图 -->
          <div v-loading="loading" class="driver-cards-container">
            <div class="cards-grid">
              <div
                v-for="driver in driverList"
                :key="driver.driverId"
                class="driver-card-compact"
                :class="{ 'selected': selectedDrivers.includes(driver.driverId) }"
                @click="toggleSelection(driver)"
              >
                <!-- 卡片内容 -->
                <div class="card-body-compact">
                  <!-- 头部信息 -->
                  <div class="card-header-compact">
                    <div class="driver-avatar-compact">
                      <el-avatar :size="45" :src="driver.avatar" class="driver-avatar">
                        <el-icon size="18"><User /></el-icon>
                      </el-avatar>
                    </div>
                    <div class="driver-info-compact">
                      <h4 class="driver-name-compact">{{ driver.driverName }}</h4>
                      <p class="employee-id-compact">{{ driver.employeeId }}</p>
                    </div>
                    <div class="selection-checkbox">
                      <el-checkbox
                        :model-value="selectedDrivers.includes(driver.driverId)"
                        @change="toggleSelection(driver)"
                        @click.stop
                        size="default"
                      />
                    </div>
                  </div>

                  <!-- 详细信息 -->
                  <div class="card-details-compact">
                    <div class="detail-item-compact">
                      <el-icon class="detail-icon"><OfficeBuilding /></el-icon>
                      <span class="detail-text">{{ driver.deptName }}</span>
                    </div>
                    <div class="detail-item-compact">
                      <el-icon class="detail-icon"><Phone /></el-icon>
                      <span class="detail-text">{{ driver.phone }}</span>
                    </div>
                    <div class="detail-item-compact">
                      <el-icon class="detail-icon"><Van /></el-icon>
                      <span class="detail-text">{{ driver.licenseType }}</span>
                    </div>
                  </div>

                  <!-- 操作按钮 -->
                  <div class="card-actions-compact">
                    <el-button type="info" size="small" icon="View" @click.stop="handleDetail(driver)" plain> 详情 </el-button>
                    <el-button type="primary" size="small" icon="Edit" @click.stop="handleUpdate(driver)"> 编辑 </el-button>
                    <el-button type="danger" size="small" icon="Delete" @click.stop="handleDelete(driver)" plain> 删除 </el-button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <el-empty v-if="!loading && driverList.length === 0" description="暂无司机数据" :image-size="120" class="empty-state">
              <el-button type="primary" @click="handleAdd">新增司机</el-button>
            </el-empty>
          </div>
        </el-card>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
            background
            layout="total, sizes, prev, pager, next, jumper"
          />
        </div>

        <!-- 添加或修改司机对话框 -->
        <el-dialog :title="title" v-model="open" width="1400px" append-to-body class="modern-dialog driver-edit-dialog-compact">

          <el-form ref="driverFormRef" :model="form" :rules="rules" label-width="110px" class="modern-form driver-form-layout-enhanced">

            <div class="form-section-enhanced basic-info-section">
              <div class="section-header">
                <div class="section-icon">
                  <el-icon><UserFilled /></el-icon>
                </div>
                <div class="section-info">
                  <h4 class="section-title">基本信息</h4>
                  <p class="section-desc">司机个人基础资料</p>
                </div>
              </div>
              <!-- 头像和基本信息布局 -->
              <div class="form-content-layout-enhanced">
                <!-- 左侧头像区域 -->
                <div class="avatar-section-enhanced">
                  <div class="avatar-upload-container">
                    <el-form-item label="头像">
                      <ImageUpload
                        v-model="form.avatar"
                        :limit="1"
                        :file-size="2"
                        :file-type="['png', 'jpg', 'jpeg']"
                        :is-show-tip="true"
                      />
                    </el-form-item>
                  </div>
                </div>

                <!-- 右侧信息区域 -->
                <div class="info-fields-enhanced">
                  <div class="form-grid-layout">
                    <el-form-item label="工号" prop="employeeId">
                      <el-input v-model="form.employeeId" placeholder="请输入工号" prefix-icon="Postcard" />
                    </el-form-item>
                    <el-form-item label="卡号" prop="cardNumber">
                      <el-input v-model="form.cardNumber" placeholder="请输入卡号" prefix-icon="CreditCard" />
                    </el-form-item>
                    <el-form-item label="司机姓名" prop="driverName">
                      <el-input v-model="form.driverName" placeholder="请输入司机姓名" prefix-icon="User" />
                    </el-form-item>
                    <el-form-item label="性别" prop="gender">
                      <el-select v-model="form.gender" placeholder="请选择性别" style="width: 100%">
                        <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value">
                          <span style="float: left">{{ dict.label }}</span>
                          <el-icon style="float: right; margin-top: 2px">
                            <Male v-if="dict.value === '1'" />
                            <Female v-else />
                          </el-icon>
                        </el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="年龄" prop="age">
                      <el-input-number v-model="form.age" :min="18" :max="65" style="width: 100%" />
                    </el-form-item>
                    <el-form-item label="联系电话" prop="phone">
                      <el-input v-model="form.phone" placeholder="请输入联系电话" prefix-icon="Phone" />
                    </el-form-item>
                    <el-form-item label="身份证号" prop="idCard" class="full-width-field">
                      <el-input v-model="form.idCard" placeholder="请输入身份证号" prefix-icon="Postcard" />
                    </el-form-item>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-section-enhanced driving-info-section">
              <div class="section-header">
                <div class="section-icon">
                  <el-icon><Van /></el-icon>
                </div>
                <div class="section-info">
                  <h4 class="section-title">驾驶信息</h4>
                  <p class="section-desc">驾驶证件和从业资质</p>
                </div>
              </div>
              <div class="form-grid-layout">
                <el-form-item label="驾驶证号" prop="licenseNumber">
                  <el-input v-model="form.licenseNumber" placeholder="请输入驾驶证号" prefix-icon="CreditCard" />
                </el-form-item>
                <el-form-item label="准驾车型" prop="licenseType">
                  <el-select v-model="form.licenseType" placeholder="请选择准驾车型" style="width: 100%">
                    <el-option label="A1 - 大型客车" value="A1" />
                    <el-option label="A2 - 牵引车" value="A2" />
                    <el-option label="A3 - 城市公交车" value="A3" />
                    <el-option label="B1 - 中型客车" value="B1" />
                    <el-option label="B2 - 大型货车" value="B2" />
                  </el-select>
                </el-form-item>
                <el-form-item label="所属机构" prop="deptId">
                  <el-tree-select
                    v-model="form.deptId"
                    :data="deptOptions"
                    :props="{ value: 'id', label: 'label', children: 'children' }"
                    value-key="id"
                    placeholder="请选择机构"
                    check-strictly
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item label="入职时间" prop="hireDate">
                  <el-date-picker v-model="form.hireDate" type="date" placeholder="选择入职时间" value-format="YYYY-MM-DD" style="width: 100%" />
                </el-form-item>
                <el-form-item label="在职状态" prop="status" class="full-width-field">
                  <el-radio-group v-model="form.status">
                    <el-radio label="1" class="status-radio">
                      <el-tag type="primary" size="small" style="background: #4ade80; border-color: #4ade80; color: white;">
                        在职
                      </el-tag>
                    </el-radio>
                    <el-radio label="0" class="status-radio">
                      <el-tag type="danger" size="small" style="background: #f87171; border-color: #f87171; color: white;">
                        离职
                      </el-tag>
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
            </div>

            <div class="form-section-enhanced remark-section">
              <div class="section-header">
                <div class="section-icon">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="section-info">
                  <h4 class="section-title">备注信息</h4>
                  <p class="section-desc">其他补充说明</p>
                </div>
              </div>
              <el-form-item label="备注内容" prop="remark">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  placeholder="请输入备注信息，如特殊技能、工作经历等..."
                  :rows="3"
                  maxlength="200"
                  show-word-limit
                  class="remark-textarea"
                />
              </el-form-item>
            </div>
          </el-form>

          <template #footer>
            <div class="dialog-footer">
              <el-button @click="cancel" size="default">
                <el-icon><Close /></el-icon>
                取消
              </el-button>
              <el-button type="primary" @click="submitForm" size="default">
                <el-icon><Check /></el-icon>
                确定保存
              </el-button>
            </div>
          </template>
        </el-dialog>

        <!-- 司机详情对话框 -->
        <el-dialog :title="detailTitle" v-model="detailOpen" width="750px" append-to-body class="driver-detail-dialog-enhanced">

          <div class="detail-content-enhanced" v-if="detailData">
            <!-- 基本信息区域 -->
            <div class="form-section-enhanced">
              <div class="section-header">
                <div class="section-icon">
                  <el-icon><UserFilled /></el-icon>
                </div>
                <div class="section-title">基本信息</div>
              </div>

              <!-- 头像和基本信息布局 -->
              <div class="form-content-layout-enhanced">
                <!-- 左侧头像区域 -->
                <div class="avatar-section-enhanced detail-avatar">
                  <div class="detail-avatar-container">
                    <ImagePreview
                      v-if="detailData.avatar"
                      :src="detailData.avatar"
                      width="80"
                      height="80"
                      class="detail-avatar-preview"
                    />
                    <div v-else class="detail-avatar-placeholder">
                      <el-icon size="32"><User /></el-icon>
                    </div>
                  </div>
                  <div class="avatar-info-detail">
                    <div class="driver-name">{{ detailData.driverName }}</div>
                    <div class="driver-id">工号: {{ detailData.employeeId }}</div>
                  </div>
                </div>

                <!-- 右侧信息区域 -->
                <div class="detail-info-grid-enhanced">
                  <div class="detail-info-item">
                    <span class="detail-label">性别:</span>
                    <dict-tag :options="sys_user_sex" :value="detailData.gender" size="small" />
                  </div>
                  <div class="detail-info-item">
                    <span class="detail-label">年龄:</span>
                    <span class="detail-value">{{ detailData.age }}岁</span>
                  </div>
                  <div class="detail-info-item">
                    <span class="detail-label">联系电话:</span>
                    <span class="detail-value phone">{{ detailData.phone }}</span>
                  </div>
                  <div class="detail-info-item full-width">
                    <span class="detail-label">身份证号:</span>
                    <span class="detail-value">{{ detailData.idCard }}</span>
                  </div>
                  <div class="detail-info-item">
                    <span class="detail-label">所属机构:</span>
                    <span class="detail-value">{{ detailData.deptName }}</span>
                  </div>
                  <div class="detail-info-item">
                    <span class="detail-label">入职时间:</span>
                    <span class="detail-value">{{ parseTime(detailData.hireDate, '{y}-{m}-{d}') }}</span>
                  </div>
                  <div class="detail-info-item">
                    <span class="detail-label">在职状态:</span>
                    <el-tag :type="getStatusInfo(detailData.status).type" size="small" :style="{ background: getStatusInfo(detailData.status).color, borderColor: getStatusInfo(detailData.status).color, color: 'white' }">
                      {{ getStatusInfo(detailData.status).text }}
                    </el-tag>
                  </div>
                  <div class="detail-info-item">
                    <span class="detail-label">驾驶证号:</span>
                    <span class="detail-value">{{ detailData.licenseNumber }}</span>
                  </div>
                  <div class="detail-info-item">
                    <span class="detail-label">准驾车型:</span>
                    <el-tag type="success" size="small">{{ detailData.licenseType }}</el-tag>
                  </div>
                </div>
              </div>

              <!-- 备注信息 -->
              <div v-if="detailData.remark" class="detail-remark-enhanced">
                <div class="remark-label-enhanced">备注信息:</div>
                <div class="remark-content-enhanced">{{ detailData.remark }}</div>
              </div>
            </div>
          </div>

          <template #footer>
            <div class="dialog-footer">
              <el-button @click="closeDetail" size="default">
                <el-icon><Close /></el-icon>
                关闭
              </el-button>
              <el-button type="primary" @click="handleUpdate(detailData)" size="default">
                <el-icon><Edit /></el-icon>
                编辑资料
              </el-button>
            </div>
          </template>
        </el-dialog>

        <!-- 批量状态变更对话框 -->
        <el-dialog title="批量状态变更" v-model="batchStatusOpen" width="500px" append-to-body class="modern-dialog">
          <template #header>
            <div class="dialog-header">
              <el-icon class="dialog-icon"><Switch /></el-icon>
              <span class="dialog-title">批量状态变更</span>
            </div>
          </template>

          <div class="batch-content">
            <el-alert :title="`已选择 ${selectedDrivers.length} 名司机`" type="info" show-icon :closable="false" class="batch-alert" />

            <el-form :model="batchForm" label-width="100px" class="batch-form">
              <el-form-item label="变更状态" required>
                <el-radio-group v-model="batchForm.status">
                  <el-radio label="1" class="status-radio">
                    <el-tag type="primary" size="small" style="background: #4ade80; border-color: #4ade80; color: white;">
                      在职
                    </el-tag>
                  </el-radio>
                  <el-radio label="0" class="status-radio">
                    <el-tag type="danger" size="small" style="background: #f87171; border-color: #f87171; color: white;">
                      离职
                    </el-tag>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="变更原因">
                <el-input v-model="batchForm.reason" type="textarea" :rows="3" placeholder="请输入变更原因（选填）" maxlength="200" show-word-limit />
              </el-form-item>
            </el-form>
          </div>

          <template #footer>
            <div class="dialog-footer">
              <el-button @click="batchStatusOpen = false">取消</el-button>
              <el-button type="primary" @click="handleBatchStatusConfirm">确认变更</el-button>
            </div>
          </template>
        </el-dialog>

        <!-- 批量组织机构调整对话框 -->
        <el-dialog title="批量组织机构调整" v-model="batchDeptOpen" width="500px" append-to-body class="modern-dialog">
          <template #header>
            <div class="dialog-header">
              <el-icon class="dialog-icon"><OfficeBuilding /></el-icon>
              <span class="dialog-title">批量组织机构调整</span>
            </div>
          </template>

          <div class="batch-content">
            <el-alert :title="`已选择 ${selectedDrivers.length} 名司机`" type="info" show-icon :closable="false" class="batch-alert" />

            <el-form :model="batchForm" label-width="100px" class="batch-form">
              <el-form-item label="目标组织机构" required>
                <el-tree-select
                  v-model="batchForm.deptId"
                  :data="deptOptions"
                  :props="{ label: 'label', children: 'children', value: 'id' }"
                  placeholder="请选择目标组织机构"
                  check-strictly
                  :render-after-expand="false"
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item label="调整原因">
                <el-input v-model="batchForm.reason" type="textarea" :rows="3" placeholder="请输入调整原因（选填）" maxlength="200" show-word-limit />
              </el-form-item>
            </el-form>
          </div>

          <template #footer>
            <div class="dialog-footer">
              <el-button @click="batchDeptOpen = false">取消</el-button>
              <el-button type="primary" @click="handleBatchDeptConfirm">确认调整</el-button>
            </div>
          </template>
        </el-dialog>

        <!-- 数据导入对话框 -->
        <el-dialog title="数据导入" v-model="importOpen" width="600px" append-to-body class="modern-dialog">
          <template #header>
            <div class="dialog-header">
              <el-icon class="dialog-icon"><Upload /></el-icon>
              <span class="dialog-title">数据导入</span>
            </div>
          </template>

          <div class="import-content">
            <el-steps :active="importStep" align-center class="import-steps">
              <el-step title="下载模板" icon="Download"></el-step>
              <el-step title="上传文件" icon="Upload"></el-step>
              <el-step title="数据预览" icon="View"></el-step>
              <el-step title="导入完成" icon="Check"></el-step>
            </el-steps>

            <div class="step-content">
              <!-- 步骤1: 下载模板 -->
              <div v-if="importStep === 0" class="step-panel">
                <el-alert title="请先下载导入模板，按照模板格式填写数据" type="info" show-icon :closable="false" />
                <div class="template-download">
                  <el-button type="primary" icon="Download" @click="downloadTemplate"> 下载导入模板 </el-button>
                  <div class="template-info">
                    <p>模板包含以下字段：</p>
                    <el-tag v-for="field in templateFields" :key="field" size="small" class="field-tag">
                      {{ field }}
                    </el-tag>
                  </div>
                </div>
              </div>

              <!-- 步骤2: 上传文件 -->
              <div v-if="importStep === 1" class="step-panel">
                <el-upload
                  ref="importUploadRef"
                  class="import-upload"
                  drag
                  action="#"
                  :limit="1"
                  accept=".xlsx,.xls"
                  :before-upload="beforeImportUpload"
                  :http-request="handleImportUpload"
                  :on-exceed="handleExceed"
                >
                  <el-icon class="el-icon--upload"><Upload /></el-icon>
                  <div class="el-upload__text">将文件拖拽到此处，或<em>点击上传</em></div>
                  <template #tip>
                    <div class="el-upload__tip">只能上传 xlsx/xls 文件，且不超过 10MB</div>
                  </template>
                </el-upload>
              </div>

              <!-- 步骤3: 数据预览 -->
              <div v-if="importStep === 2" class="step-panel">
                <div class="preview-info">
                  <el-alert
                    :title="`共解析到 ${importData.length} 条数据，其中 ${validData.length} 条有效，${invalidData.length} 条无效`"
                    :type="invalidData.length > 0 ? 'warning' : 'success'"
                    show-icon
                  />
                </div>

                <el-table :data="importData" max-height="300" size="small">
                  <el-table-column type="index" label="#" width="50" align="center" ></el-table-column>
                  <el-table-column prop="employeeId" label="工号" width="100" align="center" ></el-table-column>
                  <el-table-column prop="driverName" label="姓名" width="100" align="center" ></el-table-column>
                  <el-table-column prop="phone" label="电话" width="120" align="center" ></el-table-column>
                  <el-table-column prop="deptName" label="组织机构" width="100" align="center" ></el-table-column>
                  <el-table-column label="状态" width="80" align="center">
                    <template #default="scope">
                      <el-tag :type="scope.row.valid ? 'success' : 'danger'" size="small">
                        {{ scope.row.valid ? '有效' : '无效' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="error" label="错误信息" show-overflow-tooltip align="center"></el-table-column>
                </el-table>
              </div>

              <!-- 步骤4: 导入完成 -->
              <div v-if="importStep === 3" class="step-panel">
                <el-result :icon="importResult.success ? 'success' : 'error'" :title="importResult.title" :sub-title="importResult.message">
                  <template #extra>
                    <el-button type="primary" @click="resetImport">重新导入</el-button>
                  </template>
                </el-result>
              </div>
            </div>
          </div>

          <template #footer>
            <div class="dialog-footer">
              <el-button @click="importOpen = false">取消</el-button>
              <el-button v-if="importStep === 0" type="primary" @click="importStep = 1">下一步</el-button>
              <el-button v-if="importStep === 2 && validData.length > 0" type="primary" @click="confirmImport">
                导入数据 ({{ validData.length }} 条)
              </el-button>
              <el-button v-if="importStep === 1" @click="importStep = 0">上一步</el-button>
              <el-button v-if="importStep === 2" @click="importStep = 1">上一步</el-button>
            </div>
          </template>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Driver">
import { listDriver, getDriver, delDriver, addDriver, updateDriver } from '@/api/basic/driver';
import { deptTreeSelect } from '@/api/system/user';
import ImageUpload from '@/components/ImageUpload/index.vue';
import ImagePreview from '@/components/ImagePreview/index.vue';
import {
  User,
  OfficeBuilding,
  Avatar,
  Calendar,
  Phone,
  Document,
  Van,
  Clock,
  Grid,
  List,
  Search,
  Operation,
  UserFilled,
  Male,
  Female,
  CreditCard,
  Check,
  Close,
  Postcard,
  Plus,
  Camera,
  View,
  Edit,
  Switch,
  Download,
  Upload,
  Printer,
  ArrowDown
} from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const { sys_user_sex } = proxy.useDict('sys_user_sex');

const driverList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');

// 详情对话框相关
const detailOpen = ref(false);
const detailTitle = ref('');
const detailData = ref(null);

// 批量操作相关
const batchStatusOpen = ref(false);
const batchDeptOpen = ref(false);
const batchForm = ref({
  status: '1',
  deptId: null,
  reason: ''
});

// 数据导入相关
const importOpen = ref(false);
const importStep = ref(0);
const importUploadRef = ref();
const importData = ref([]);
const importResult = ref({
  success: false,
  title: '',
  message: ''
});

const templateFields = ref(['工号', '姓名', '性别', '年龄', '联系电话', '身份证号', '驾驶证号', '准驾车型', '所属组织机构', '入职时间']);

// 计算属性
const validData = computed(() => importData.value.filter((item) => item.valid));
const invalidData = computed(() => importData.value.filter((item) => !item.valid));

// 卡片选择相关
const selectedDrivers = ref([]);

// 组织机构树相关数据
const deptName = ref('');
const deptOptions = ref([]);
const deptTreeRef = ref();

// 表单引用
const queryFormRef = ref();
const driverFormRef = ref();

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    driverName: null,
    employeeId: null,
    cardNumber: null,
    status: null,
    deptId: null
  },
  rules: {
    employeeId: [
      { required: true, message: '工号不能为空', trigger: 'blur' },
      { min: 3, max: 20, message: '工号长度在 3 到 20 个字符', trigger: 'blur' },
      { pattern: /^[A-Z0-9]+$/, message: '工号只能包含大写字母和数字', trigger: 'blur' }
    ],
    driverName: [
      { required: true, message: '司机姓名不能为空', trigger: 'blur' },
      { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' },
      { pattern: /^[\u4e00-\u9fa5a-zA-Z]+$/, message: '姓名只能包含中文和英文字母', trigger: 'blur' }
    ],
    gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
    age: [
      { required: true, message: '请输入年龄', trigger: 'blur' },
      { type: 'number', min: 18, max: 65, message: '年龄必须在18-65岁之间', trigger: 'blur' }
    ],
    phone: [
      { required: true, message: '联系电话不能为空', trigger: 'blur' },
      { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    idCard: [
      { required: true, message: '身份证号不能为空', trigger: 'blur' },
      { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号码', trigger: 'blur' }
    ],
    licenseNumber: [
      { required: true, message: '驾驶证号不能为空', trigger: 'blur' },
      { min: 12, max: 18, message: '驾驶证号长度在 12 到 18 个字符', trigger: 'blur' }
    ],
    licenseType: [{ required: true, message: '请选择准驾车型', trigger: 'change' }],
    deptId: [{ required: true, message: '请选择所属组织机构', trigger: 'change' }],
    hireDate: [{ required: true, message: '请选择入职时间', trigger: 'change' }],
    status: [{ required: true, message: '请选择在职状态', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 自定义验证函数 */
// 验证工号唯一性
const validateEmployeeId = (rule, value, callback) => {
  if (value) {
    // 模拟检查工号唯一性
    const existingIds = driverList.value.filter((item) => item.employeeId === value && item.driverId !== form.value.driverId);
    if (existingIds.length > 0) {
      callback(new Error('工号已存在，请更换'));
    } else {
      callback();
    }
  } else {
    callback();
  }
};

// 验证身份证号和驾驶证号一致性
const validateLicenseConsistency = (rule, value, callback) => {
  if (value && form.value.idCard) {
    if (value !== form.value.idCard) {
      callback(new Error('驾驶证号应与身份证号一致'));
    } else {
      callback();
    }
  } else {
    callback();
  }
};

// 验证入职时间
const validateHireDate = (rule, value, callback) => {
  if (value) {
    const hireDate = new Date(value);
    const today = new Date();
    const minDate = new Date('1980-01-01');

    if (hireDate > today) {
      callback(new Error('入职时间不能晚于今天'));
    } else if (hireDate < minDate) {
      callback(new Error('入职时间不能早于1980年'));
    } else {
      callback();
    }
  } else {
    callback();
  }
};

// 更新验证规则，添加自定义验证
watch(
  () => form.value,
  () => {
    if (driverFormRef.value) {
      // 动态添加自定义验证规则
      const customRules = {
        ...rules.value,
        employeeId: [...rules.value.employeeId, { validator: validateEmployeeId, trigger: 'blur' }],
        licenseNumber: [...rules.value.licenseNumber, { validator: validateLicenseConsistency, trigger: 'blur' }],
        hireDate: [...rules.value.hireDate, { validator: validateHireDate, trigger: 'change' }]
      };

      nextTick(() => {
        if (driverFormRef.value) {
          driverFormRef.value.clearValidate();
        }
      });
    }
  },
  { deep: true }
);

/** 组织机构树筛选 */
function filterNode(value, data) {
  if (!value) return true;
  return data.label.includes(value);
}

/** 根据名称筛选组织机构树 */
watchEffect(
  () => {
    deptTreeRef.value?.filter(deptName.value);
  },
  {
    flush: 'post'
  }
);

/** 查询组织机构下拉树结构 */
async function getTreeSelect() {
  try {
    const res = await deptTreeSelect();
    deptOptions.value = res.data;
  } catch (error) {
    console.error('获取组织机构树失败:', error);
    // 模拟组织机构数据
    deptOptions.value = [
      {
        id: 1,
        label: '总公司',
        children: [
          {
            id: 2,
            label: '运营部',
            children: [
              { id: 3, label: '第一车队' },
              { id: 4, label: '第二车队' }
            ]
          },
          {
            id: 5,
            label: '维修部',
            children: [
              { id: 6, label: '机修组' },
              { id: 7, label: '电修组' }
            ]
          }
        ]
      }
    ];
  }
}

/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = data.id;
  handleQuery();
}










/** 查询司机列表 */
function getList() {
  loading.value = true;
  // 模拟数据
  const mockData = [
    {
      driverId: 1,
      employeeId: 'D001',
      driverName: '张三',
      deptId: 3,
      deptName: '第一车队',
      gender: '1',
      age: 35,
      phone: '13800138001',
      idCard: '110101198801011234',
      licenseNumber: '110101198801011234',
      licenseType: 'A1',
      hireDate: '2020-01-15',
      status: '1',
      remark: '经验丰富的老司机'
    },
    {
      driverId: 2,
      employeeId: 'D002',
      driverName: '李四',
      deptId: 4,
      deptName: '第二车队',
      gender: '1',
      age: 28,
      phone: '13800138002',
      idCard: '110101199201011234',
      licenseNumber: '110101199201011234',
      licenseType: 'A3',
      hireDate: '2021-03-20',
      status: '1',
      remark: '新入职司机'
    },
    {
      driverId: 3,
      employeeId: 'D003',
      driverName: '王五',
      deptId: 6,
      deptName: '机修组',
      gender: '1',
      age: 42,
      phone: '13800138003',
      idCard: '110101198001011234',
      licenseNumber: '110101198001011234',
      licenseType: 'A1',
      hireDate: '2018-06-10',
      status: '0',
      remark: '已离职'
    }
  ];

  setTimeout(() => {
    let filteredData = mockData;

    // 根据组织机构筛选
    if (queryParams.value.deptId) {
      filteredData = filteredData.filter((item) => item.deptId === queryParams.value.deptId);
    }

    // 根据司机姓名筛选
    if (queryParams.value.driverName) {
      filteredData = filteredData.filter((item) => item.driverName.includes(queryParams.value.driverName));
    }

    // 根据工号筛选
    if (queryParams.value.employeeId) {
      filteredData = filteredData.filter((item) => item.employeeId.includes(queryParams.value.employeeId));
    }

    // 根据卡号筛选
    if (queryParams.value.cardNumber) {
      filteredData = filteredData.filter((item) => item.cardNumber && item.cardNumber.includes(queryParams.value.cardNumber));
    }

    // 根据状态筛选
    if (queryParams.value.status) {
      filteredData = filteredData.filter((item) => item.status === queryParams.value.status);
    }

    driverList.value = filteredData;
    total.value = filteredData.length;
    loading.value = false;
  }, 500);
}

// 获取状态文本和样式
function getStatusInfo(status) {
  const statusMap = {
    '1': { text: '在职', type: 'primary', color: '#4ade80' },
    '0': { text: '离职', type: 'danger', color: '#f87171' }
  };
  return statusMap[status] || { text: '未知', type: 'info', color: '#9ca3af' };
}

// 其他方法实现...
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  queryFormRef.value?.resetFields();
  queryParams.value.pageNum = 1;
  queryParams.value.deptId = null;
  deptTreeRef.value?.setCurrentKey(null);
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.driverId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 卡片选择切换 */
function toggleSelection(driver) {
  const index = selectedDrivers.value.indexOf(driver.driverId);
  if (index > -1) {
    selectedDrivers.value.splice(index, 1);
  } else {
    selectedDrivers.value.push(driver.driverId);
  }

  // 更新传统选择状态以保持兼容性
  ids.value = selectedDrivers.value;
  single.value = selectedDrivers.value.length !== 1;
  multiple.value = selectedDrivers.value.length === 0;
}

function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加司机';
}

/** 查看司机详情 */
function handleDetail(row) {
  detailData.value = { ...row };
  detailTitle.value = `司机详情 - ${row.driverName}`;
  detailOpen.value = true;
}

/** 关闭详情对话框 */
function closeDetail() {
  detailOpen.value = false;
  detailData.value = null;
}

function handleUpdate(row) {
  reset();
  const driverId = row.driverId || ids.value;
  form.value = { ...row };
  open.value = true;
  title.value = '修改司机';
  // 如果是从详情页面跳转过来的，关闭详情对话框
  if (detailOpen.value) {
    closeDetail();
  }
}

function handleDelete(row) {
  const driverIds = row.driverId || ids.value;
  proxy.$modal
    .confirm('是否确认删除司机编号为"' + driverIds + '"的数据项？')
    .then(function () {
      // 模拟删除
      proxy.$modal.msgSuccess('删除成功');
      getList();
    })
    .catch(() => {});
}

function handleExport() {
  proxy.$modal.confirm('是否确认导出所有司机数据项？').then(() => {
    proxy.$modal.msgSuccess('导出成功');
  });
}

/** 批量操作命令处理 */
function handleBatchCommand(command) {
  if (selectedDrivers.value.length === 0) {
    proxy.$modal.msgWarning('请先选择要操作的数据');
    return;
  }

  switch (command) {
    case 'batchStatus':
      batchForm.value = { status: '1', deptId: null, reason: '' };
      batchStatusOpen.value = true;
      break;
    case 'batchDept':
      batchForm.value = { status: '1', deptId: null, reason: '' };
      batchDeptOpen.value = true;
      break;
    case 'batchExport':
      handleBatchExport();
      break;
    case 'batchPrint':
      handleBatchPrint();
      break;
  }
}

/** 批量状态变更确认 */
function handleBatchStatusConfirm() {
  if (!batchForm.value.status) {
    proxy.$modal.msgError('请选择要变更的状态');
    return;
  }

  const statusMap = {
    '1': '在职',
    '0': '离职'
  };
  const statusText = statusMap[batchForm.value.status] || '未知状态';
  proxy.$modal.confirm(`确认将选中的 ${selectedDrivers.value.length} 名司机状态变更为"${statusText}"？`).then(() => {
    // 模拟批量状态变更
    const updatedCount = selectedDrivers.value.length;
    driverList.value.forEach((driver) => {
      if (selectedDrivers.value.includes(driver.driverId)) {
        driver.status = batchForm.value.status;
      }
    });

    proxy.$modal.msgSuccess(`成功变更 ${updatedCount} 名司机的状态`);
    batchStatusOpen.value = false;
    selectedDrivers.value = [];
    single.value = true;
    multiple.value = true;
  });
}

/** 批量组织机构调整确认 */
function handleBatchDeptConfirm() {
  if (!batchForm.value.deptId) {
    proxy.$modal.msgError('请选择目标组织机构');
    return;
  }

  const targetDept = findDeptById(batchForm.value.deptId);
  proxy.$modal.confirm(`确认将选中的 ${selectedDrivers.value.length} 名司机调整到"${targetDept?.label}"？`).then(() => {
    // 模拟批量组织机构调整
    const updatedCount = selectedDrivers.value.length;
    driverList.value.forEach((driver) => {
      if (selectedDrivers.value.includes(driver.driverId)) {
        driver.deptId = batchForm.value.deptId;
        driver.deptName = targetDept?.label;
      }
    });

    proxy.$modal.msgSuccess(`成功调整 ${updatedCount} 名司机的组织机构`);
    batchDeptOpen.value = false;
    selectedDrivers.value = [];
    single.value = true;
    multiple.value = true;
  });
}

/** 查找组织机构信息 */
function findDeptById(deptId) {
  function findInTree(nodes) {
    for (const node of nodes) {
      if (node.id === deptId) return node;
      if (node.children) {
        const result = findInTree(node.children);
        if (result) return result;
      }
    }
    return null;
  }
  return findInTree(deptOptions.value);
}

/** 批量导出 */
function handleBatchExport() {
  proxy.$modal.msgSuccess(`成功导出 ${selectedDrivers.value.length} 名司机的数据`);
}

/** 批量打印 */
function handleBatchPrint() {
  proxy.$modal.msgSuccess(`正在准备打印 ${selectedDrivers.value.length} 名司机的信息`);
}

/** 数据导入 */
function handleImport() {
  importStep.value = 0;
  importOpen.value = true;
}

/** 下载导入模板 */
function downloadTemplate() {
  // 模拟下载模板
  proxy.$modal.msgSuccess('模板下载成功');
}

/** 导入文件上传前验证 */
function beforeImportUpload(file) {
  const isExcel = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'].includes(file.type);
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isExcel) {
    proxy.$modal.msgError('上传文件只能是 Excel 格式!');
    return false;
  }
  if (!isLt10M) {
    proxy.$modal.msgError('上传文件大小不能超过 10MB!');
    return false;
  }
  return true;
}

/** 处理导入文件上传 */
function handleImportUpload(options) {
  // 模拟文件解析
  setTimeout(() => {
    importData.value = [
      {
        employeeId: 'D004',
        driverName: '赵六',
        phone: '13800138004',
        deptName: '第三车队',
        valid: true,
        error: ''
      },
      {
        employeeId: 'D005',
        driverName: '钱七',
        phone: '138001380', // 错误的手机号
        deptName: '第四车队',
        valid: false,
        error: '手机号格式不正确'
      }
    ];
    importStep.value = 2;
  }, 1000);
}

/** 文件数量超限处理 */
function handleExceed() {
  proxy.$modal.msgWarning('只能上传一个文件');
}

/** 确认导入数据 */
function confirmImport() {
  proxy.$modal.confirm(`确认导入 ${validData.value.length} 条有效数据？`).then(() => {
    // 模拟导入过程
    importResult.value = {
      success: true,
      title: '导入成功',
      message: `成功导入 ${validData.value.length} 条司机数据`
    };
    importStep.value = 3;

    // 刷新列表数据
    setTimeout(() => {
      getList();
    }, 1000);
  });
}

/** 重置导入流程 */
function resetImport() {
  importStep.value = 0;
  importData.value = [];
  importUploadRef.value?.clearFiles();
}

function submitForm() {
  driverFormRef.value?.validate((valid) => {
    if (valid) {
      if (form.value.driverId != null) {
        proxy.$modal.msgSuccess('修改成功');
      } else {
        proxy.$modal.msgSuccess('新增成功');
      }
      open.value = false;
      getList();
    }
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    driverId: null,
    employeeId: null,
    cardNumber: null,
    driverName: null,
    gender: null,
    age: null,
    phone: null,
    idCard: null,
    licenseNumber: null,
    licenseType: null,
    deptId: null,
    hireDate: null,
    status: '1',
    remark: null,
    avatar: null
  };
  driverFormRef.value?.resetFields();
  driverFormRef.value?.clearValidate();
}



getList();
getTreeSelect();
</script>

<style scoped>
/* 个人记录弹窗样式 */
.personal-records-content {
  .records-card {
    background: rgba(15, 23, 42, 0.6);
    border: 1px solid rgba(147, 197, 253, 0.2) !important;
  }
}

/* 个人记录表格Element UI样式覆盖 */
.personal-records-content :deep(.el-table) {
  background: rgba(15, 23, 42, 0.6) !important;
  color: #e5e7eb !important;
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 8px !important;
}

.personal-records-content :deep(.el-table tr) {
  background: rgba(15, 23, 42, 0.4) !important;
}

.personal-records-content :deep(.el-table tr:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
}

.personal-records-content :deep(.el-table th.el-table__cell) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #f8fafc !important;
  border-color: rgba(147, 197, 253, 0.2) !important;
  font-weight: 600 !important;
}

.personal-records-content :deep(.el-table td.el-table__cell) {
  border-color: rgba(147, 197, 253, 0.1) !important;
  color: #e5e7eb !important;
  background: transparent !important;
}

.personal-records-content :deep(.el-descriptions) {
  --el-descriptions-table-border: 1px solid rgba(147, 197, 253, 0.2);
  --el-descriptions-item-bordered-label-background: rgba(30, 41, 59, 0.8);
}

.personal-records-content :deep(.el-descriptions__body .el-descriptions__table) {
  border-color: rgba(147, 197, 253, 0.2) !important;
}

.personal-records-content :deep(.el-descriptions__label) {
  background: rgba(30, 41, 59, 0.8) !important;
  color: #f8fafc !important;
  font-weight: 600 !important;
}

.personal-records-content :deep(.el-descriptions__content) {
  background: rgba(15, 23, 42, 0.4) !important;
  color: #e5e7eb !important;
}


/* 全屏布局基础样式 - 科技感深色主题 */
.app-container {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  min-height: 100vh;
  padding: 20px;
}

/* 左侧面板样式 */
.dept-tree-card {
  background: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.dept-tree-card:hover {
  box-shadow: 0 12px 40px rgba(96, 165, 250, 0.2);
  transform: translateY(-1px);
  border-color: rgba(96, 165, 250, 0.4) !important;
}

/* 卡片头部样式 - 深色主题 */
.card-header-modern,
.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
  gap: 12px;
  font-weight: 600;
  color: #f8fafc;
  font-size: 16px;
}

.header-icon {
  color: #60a5fa;
  font-size: 18px;
}

.header-title {
  font-size: 16px;
  flex: 1;
  color: #f8fafc;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.header-extra {
  font-size: 14px;
  color: #94a3b8;
  font-weight: normal;
}

.result-count {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
}

/* 搜索区域样式优化 - 参考车辆管理页面 */
.search-panel {
  background: rgba(10, 22, 48, 0.6) !important;
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(10px) !important;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.search-panel:hover {
  box-shadow: 0 8px 32px rgba(96, 165, 250, 0.2);
  transform: translateY(-1px);
  border-color: rgba(96, 165, 250, 0.4) !important;
}

/* 搜索表单输入框优化 - 增强可见性 */
.search-panel :deep(.el-input__wrapper) {
  background: rgba(30, 41, 59, 0.95) !important;
  border: 2px solid rgba(147, 197, 253, 0.4) !important;
  box-shadow:
    0 0 10px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.search-panel :deep(.el-input__wrapper:hover) {
  border-color: #60a5fa !important;
  background: rgba(30, 41, 59, 1) !important;
  box-shadow:
    0 0 15px rgba(96, 165, 250, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.search-panel :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6 !important;
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.3),
    0 0 0 1px rgba(59, 130, 246, 0.2) inset !important;
  background: rgba(30, 41, 59, 1) !important;
}

.search-panel :deep(.el-input__inner) {
  color: #f8fafc !important;
  background: transparent !important;
  font-weight: 500;
}

.search-panel :deep(.el-input__inner::placeholder) {
  color: #94a3b8 !important;
  font-weight: 400;
}

/* 搜索表单选择框优化 */
.search-panel :deep(.el-select .el-input .el-input__wrapper) {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.15) 0%, rgba(59, 130, 246, 0.1) 100%) !important;
  border: 2px solid rgba(99, 102, 241, 0.6) !important;
  box-shadow:
    0 0 15px rgba(99, 102, 241, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.search-panel :deep(.el-select .el-input.is-focus .el-input__wrapper) {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.25) 0%, rgba(59, 130, 246, 0.2) 100%) !important;
  border-color: rgba(99, 102, 241, 0.8) !important;
  box-shadow:
    0 0 25px rgba(99, 102, 241, 0.4),
    0 0 0 1px rgba(99, 102, 241, 0.3) inset !important;
}

.search-panel :deep(.el-select .el-input:hover .el-input__wrapper) {
  border-color: rgba(99, 102, 241, 0.7) !important;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.2) 0%, rgba(59, 130, 246, 0.15) 100%) !important;
  box-shadow:
    0 0 20px rgba(99, 102, 241, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

/* 搜索表单选择框内部文字颜色 */
.search-panel :deep(.el-select .el-input__inner) {
  color: #f8fafc !important;
  background: transparent !important;
  font-weight: 500;
}

.search-panel :deep(.el-select .el-input__inner::placeholder) {
  color: #94a3b8 !important;
  font-weight: 400;
}

/* 选择框箭头图标颜色 */
.search-panel :deep(.el-select .el-select__caret) {
  color: #94a3b8 !important;
  transition: color 0.3s ease;
}

.search-panel :deep(.el-select .el-select__caret:hover) {
  color: #60a5fa !important;
}

.search-panel :deep(.el-select.is-focused .el-select__caret) {
  color: #3b82f6 !important;
}

/* 搜索区域标签样式 */
.search-panel :deep(.el-form-item__label) {
  color: #e2e8f0 !important;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 搜索按钮特殊样式 */
.search-panel :deep(.el-button--primary) {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  border: 2px solid rgba(59, 130, 246, 0.6) !important;
  color: #ffffff !important;
  font-weight: 600;
  box-shadow:
    0 0 15px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.search-panel :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;
  border-color: rgba(59, 130, 246, 0.8) !important;
  transform: translateY(-2px) !important;
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.4),
    0 0 25px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.search-panel :deep(.el-button:not(.el-button--primary)) {
  background: linear-gradient(135deg, rgba(71, 85, 105, 0.9) 0%, rgba(51, 65, 85, 0.9) 100%) !important;
  border: 2px solid rgba(148, 163, 184, 0.4) !important;
  color: #f1f5f9 !important;
  font-weight: 600;
  box-shadow:
    0 0 10px rgba(71, 85, 105, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

.search-panel :deep(.el-button:not(.el-button--primary):hover) {
  background: linear-gradient(135deg, rgba(71, 85, 105, 1) 0%, rgba(51, 65, 85, 1) 100%) !important;
  border-color: rgba(148, 163, 184, 0.6) !important;
  transform: translateY(-1px) !important;
  box-shadow:
    0 4px 15px rgba(71, 85, 105, 0.3),
    0 0 20px rgba(71, 85, 105, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

/* 组织机构搜索输入框 */
.dept-search {
  margin-bottom: 16px;
  padding: 0 24px;
}

.dept-search :deep(.el-input__wrapper) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  box-shadow: none !important;
}

.dept-search :deep(.el-input__wrapper:hover) {
  border-color: #60a5fa !important;
}

.dept-search :deep(.el-input__wrapper.is-focus) {
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 1px rgba(96, 165, 250, 0.4) inset !important;
}

.dept-search :deep(.el-input__inner) {
  color: #e5e7eb !important;
}

.dept-search :deep(.el-input__inner::placeholder) {
  color: #64748b !important;
}

/* 组织机构树样式 */
.dept-tree {
  max-height: 600px;
  overflow-y: auto;
  background: transparent;
  color: #cbd5e1;
  padding: 0 24px 24px 24px;
}

/* 组织机构树节点样式 - 深色主题 */
.dept-tree :deep(.el-tree) {
  background: transparent !important;
  color: #cbd5e1 !important;
}

.dept-tree :deep(.el-tree-node__content) {
  height: 40px !important;
  background: transparent !important;
  color: #e5e7eb !important;
  border-radius: 8px !important;
  margin-bottom: 2px !important;
  transition: all 0.2s ease !important;
  padding: 0 8px !important;
  border: 1px solid transparent;
}

.dept-tree :deep(.el-tree-node__content:hover) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(96, 165, 250, 0.1) 100%) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
  color: #60a5fa !important;
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.dept-tree :deep(.el-tree-node__expand-icon) {
  color: #93c5fd !important;
  font-size: 14px;
  transition: all 0.2s ease;
}

.dept-tree :deep(.el-tree-node__expand-icon:hover) {
  color: #60a5fa !important;
  transform: scale(1.1);
}

.dept-tree :deep(.el-tree-node__expand-icon.is-leaf) {
  color: transparent;
}

.dept-tree :deep(.el-tree-node__label) {
  font-weight: 500;
  color: #e5e7eb;
  transition: color 0.2s ease;
}

.dept-tree :deep(.el-tree-node__content:hover .el-tree-node__label) {
  color: #60a5fa;
  font-weight: 600;
}

/* 当前选中节点样式 */
.dept-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%) !important;
  border-color: rgba(34, 197, 94, 0.4) !important;
  color: #22c55e !important;
  font-weight: 600;
  box-shadow:
    0 0 15px rgba(34, 197, 94, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dept-tree :deep(.el-tree-node.is-current > .el-tree-node__content .el-tree-node__label) {
  color: #22c55e !important;
  font-weight: 600;
  text-shadow: 0 0 8px rgba(34, 197, 94, 0.3);
}

.dept-tree :deep(.el-tree-node.is-current > .el-tree-node__content .el-tree-node__expand-icon) {
  color: #22c55e !important;
}

/* 工具栏样式 */
.toolbar-panel {
  background: rgba(10, 22, 48, 0.6) !important;
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(10px) !important;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.toolbar-panel:hover {
  box-shadow: 0 8px 32px rgba(96, 165, 250, 0.2);
  transform: translateY(-1px);
  border-color: rgba(96, 165, 250, 0.4) !important;
}

.toolbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px 20px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selection-status {
  display: flex;
  align-items: center;
}

.selection-status .el-tag {
  transition: all 0.3s ease;
}

.selection-status .el-icon {
  margin-right: 4px;
}

/* 数据展示区域 */
.data-panel {
  background: rgba(10, 22, 48, 0.4) !important;
  border: 1px solid rgba(147, 197, 253, 0.1) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.data-panel:hover {
  box-shadow: 0 8px 32px rgba(96, 165, 250, 0.2);
  transform: translateY(-1px);
  border-color: rgba(96, 165, 250, 0.4) !important;
}

/* 卡片视图容器 - 5列紧凑布局 */
.driver-cards-container {
  min-height: 400px;
  background: transparent;
  padding: 20px;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
}

/* 紧凑型司机卡片样式 - 深色科技风格 */
.driver-card-compact {
  background: linear-gradient(145deg, rgba(30, 41, 59, 0.9) 0%, rgba(51, 65, 85, 0.8) 100%);
  border: 2px solid rgba(147, 197, 253, 0.2);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.driver-card-compact:hover {
  transform: translateY(-4px);
  border-color: rgba(96, 165, 250, 0.4);
  box-shadow:
    0 12px 40px rgba(96, 165, 250, 0.2),
    0 0 30px rgba(96, 165, 250, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.driver-card-compact.selected {
  border-color: #22c55e;
  background: linear-gradient(145deg, rgba(34, 197, 94, 0.15) 0%, rgba(30, 41, 59, 0.9) 50%, rgba(51, 65, 85, 0.8) 100%);
  box-shadow:
    0 0 30px rgba(34, 197, 94, 0.3),
    0 8px 25px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 科技感装饰线 */
.driver-card-compact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, rgba(96, 165, 250, 0.6) 50%, transparent 100%);
}

.driver-card-compact.selected::before {
  background: linear-gradient(90deg, transparent 0%, rgba(34, 197, 94, 0.8) 50%, transparent 100%);
}

/* 卡片主体 */
.card-body-compact {
  padding: 16px;
  position: relative;
  z-index: 1;
  color: #e5e7eb;
}

/* 头部区域 - 紧凑布局 */
.card-header-compact {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
  justify-content: space-between;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
}

.driver-avatar-compact {
  position: relative;
  flex-shrink: 0;
}

.driver-avatar {
  border: 2px solid rgba(147, 197, 253, 0.3);
  transition: all 0.3s ease;
}

.driver-card-compact:hover .driver-avatar {
  border-color: #60a5fa;
  box-shadow: 0 0 12px rgba(96, 165, 250, 0.4);
}

.driver-info-compact {
  flex: 1;
  min-width: 0;
}

.driver-name-compact {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #f8fafc;
  line-height: 1.2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.employee-id-compact {
  margin: 0;
  font-size: 11px;
  color: #94a3b8;
  font-weight: 400;
}

.selection-checkbox {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

/* 详细信息区域 - 紧凑布局 */
.card-details-compact {
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.detail-item-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.6) 0%, rgba(30, 41, 59, 0.4) 100%);
  border-radius: 6px;
  transition: all 0.3s ease;
  border: 1px solid rgba(147, 197, 253, 0.1);
}

.detail-item-compact:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(30, 41, 59, 0.6) 100%);
  border-color: rgba(147, 197, 253, 0.3);
}

.detail-icon {
  color: #60a5fa;
  font-size: 14px;
  flex-shrink: 0;
}

.detail-text {
  flex: 1;
  font-size: 11px;
  color: #cbd5e1;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 操作按钮区域 - 紧凑布局 */
.card-actions-compact {
  display: flex;
  gap: 6px;
  padding-top: 10px;
  border-top: 1px solid rgba(147, 197, 253, 0.2);
}

.card-actions-compact .el-button {
  flex: 1;
  font-size: 11px;
  padding: 4px 8px;
  height: 28px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
}

/* 评分记录按钮特殊样式 */
.card-actions-compact .el-button:nth-child(2) {
  flex: 1.2;
  min-width: 80px;
}

/* 卡片按钮发光效果 */
.card-actions-compact .el-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  transition: left 0.4s ease;
}

.card-actions-compact .el-button:hover::before {
  left: 100%;
}

.card-actions-compact .el-button:hover {
  transform: translateY(-1px);
}

/* 空状态样式 */
.empty-state {
  padding: 60px 0;
  text-align: center;
  color: #94a3b8;
}

/* 分页样式 - 深色主题 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(147, 197, 253, 0.2);
}

/* 对话框样式 - 深色科技风格 */
.modern-dialog {
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(145deg, rgba(30, 41, 59, 0.95) 0%, rgba(15, 23, 42, 0.98) 100%) !important;
  border: 2px solid rgba(96, 165, 250, 0.3) !important;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(96, 165, 250, 0.2) !important;
  backdrop-filter: blur(12px) !important;
}

/* 司机编辑对话框优化 - 简化版本 */
.driver-edit-dialog-compact {
  max-height: 90vh !important;
  margin-top: 5vh !important;
  margin-bottom: 5vh !important;
}

.driver-edit-dialog-compact .el-dialog__body {
  height: calc(90vh - 120px) !important;
  overflow: hidden !important;
  padding: 12px 20px !important;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
}



.driver-edit-dialog .modern-form {
  max-width: 100% !important;
}

/* 对话框标题样式 */
.driver-edit-dialog-compact .el-dialog__header {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  padding: 16px 20px;
}

.driver-edit-dialog-compact .el-dialog__title {
  color: #f8fafc !important;
  font-weight: 600;
  font-size: 18px;
}

/* 增强的对话框头部样式 - 暗色主题 */
.dialog-header-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  border-radius: 8px;
  margin: -10px;
  margin-bottom: 0;
  color: #f8fafc;
  border: 1px solid rgba(147, 197, 253, 0.2);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(96, 165, 250, 0.4);
  border: 1px solid rgba(147, 197, 253, 0.3);
}

.dialog-icon {
  color: white;
  font-size: 24px;
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.dialog-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #f8fafc;
}

.dialog-subtitle {
  margin: 0;
  font-size: 13px;
  color: #cbd5e1;
  opacity: 0.8;
}

.header-right .el-tag {
  background: rgba(96, 165, 250, 0.15);
  border-color: rgba(147, 197, 253, 0.4);
  color: #93c5fd;
  backdrop-filter: blur(10px);
}

/* 增强的表单布局 */
.driver-form-layout-enhanced {
  padding: 0;
}

.form-section-enhanced {
  margin-bottom: 8px;
  padding: 12px 16px;
  background: rgba(15, 23, 42, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(147, 197, 253, 0.2);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
}

.form-section-enhanced:last-child {
  margin-bottom: 0;
}

/* 增强的分区头部 */
.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
}

.section-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: rgba(30, 41, 59, 0.8);
  border-radius: 6px;
  border: 1px solid rgba(147, 197, 253, 0.3);
  backdrop-filter: blur(10px);
}

.section-icon .el-icon {
  font-size: 14px;
  color: #93c5fd;
}

.section-info {
  flex: 1;
}

.section-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #f8fafc;
}

.section-desc {
  margin: 0;
  font-size: 13px;
  color: #cbd5e1;
}

/* 特殊分区样式 - 暗色主题 */
.basic-info-section .section-icon {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(37, 99, 235, 0.3) 100%);
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

.basic-info-section .section-icon .el-icon {
  color: #60a5fa;
}

.driving-info-section .section-icon {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(21, 128, 61, 0.3) 100%);
  border-color: rgba(34, 197, 94, 0.5);
  box-shadow: 0 0 10px rgba(34, 197, 94, 0.3);
}

.driving-info-section .section-icon .el-icon {
  color: #4ade80;
}

.remark-section .section-icon {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(217, 119, 6, 0.3) 100%);
  border-color: rgba(245, 158, 11, 0.5);
  box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
}

.remark-section .section-icon .el-icon {
  color: #fbbf24;
}

/* 增强的表单内容布局 */
.form-content-layout-enhanced {
  display: flex;
  gap: 16px;
  align-items: center;
}

.avatar-section-enhanced {
  flex-shrink: 0;
  width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.info-fields-enhanced {
  flex: 1;
  min-width: 0;
}

/* 网格布局样式 */
.form-grid-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 12px;
  align-items: start;
}

.form-grid-layout .full-width-field {
  grid-column: 1 / -1;
}

/* 头像上传容器 */
.avatar-upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

/* 司机详情对话框样式 - 暗色主题 */
.driver-detail-dialog-enhanced .el-dialog__header {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  border-bottom: 1px solid rgba(147, 197, 253, 0.2);
  padding: 16px 20px;
}

.driver-detail-dialog-enhanced .el-dialog__title {
  color: #f8fafc !important;
  font-weight: 600;
  font-size: 18px;
}

.driver-detail-dialog-enhanced .el-dialog__body {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e5e7eb;
  padding: 20px;
}

/* 详情内容布局 */
.detail-content-enhanced {
  max-height: calc(80vh - 120px);
  overflow-y: auto;
}

/* 详情头像区域 */
.avatar-section-enhanced.detail-avatar {
  width: 180px;
  text-align: center;
}

/* 详情头像容器 */
.detail-avatar-container {
  margin-bottom: 12px;
  display: flex;
  justify-content: center;
}

/* 详情头像预览样式 */
.detail-avatar-preview {
  border-radius: 50% !important;
  overflow: hidden;
  border: 3px solid rgba(147, 197, 253, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.detail-avatar-preview :deep(.el-image) {
  border-radius: 50% !important;
  background-color: rgba(30, 41, 59, 0.8) !important;
  box-shadow: none !important;
}

.detail-avatar-preview :deep(.el-image__inner) {
  border-radius: 50% !important;
}

.detail-avatar-preview :deep(.el-image__inner:hover) {
  transform: scale(1.05) !important;
}

/* 头像占位符样式 */
.detail-avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(30, 41, 59, 0.8);
  border: 3px solid rgba(147, 197, 253, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
}

.avatar-info-detail {
  text-align: center;
}

.avatar-info-detail .driver-name {
  font-size: 16px;
  font-weight: 600;
  color: #f8fafc;
  margin-bottom: 6px;
}

.avatar-info-detail .driver-id {
  font-size: 14px;
  color: #94a3b8;
  margin-bottom: 8px;
}


/* 详情信息网格布局 */
.detail-info-grid-enhanced {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px 16px;
  align-items: start;
}

.detail-info-grid-enhanced .full-width {
  grid-column: 1 / -1;
}

/* 详情信息项 */
.detail-info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: #94a3b8;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #e5e7eb;
  font-weight: 400;
}

.detail-value.phone {
  color: #60a5fa;
  cursor: pointer;
}

.detail-value.phone:hover {
  color: #93c5fd;
}

/* 详情备注区域 */
.detail-remark-enhanced {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(147, 197, 253, 0.2);
}

.remark-label-enhanced {
  font-size: 12px;
  color: #94a3b8;
  font-weight: 500;
  margin-bottom: 8px;
}

.remark-content-enhanced {
  font-size: 14px;
  color: #e5e7eb;
  line-height: 1.6;
  background: rgba(30, 41, 59, 0.6);
  padding: 12px;
  border-radius: 8px;
  border: 1px solid rgba(147, 197, 253, 0.2);
}


/* 备注文本域样式 - 暗色主题 */
.remark-textarea {
  border-radius: 8px;
}

.remark-textarea .el-textarea__inner {
  border-radius: 8px !important;
  border: 2px solid rgba(147, 197, 253, 0.3) !important;
  background: rgba(30, 41, 59, 0.8) !important;
  color: #e5e7eb !important;
}

.remark-textarea .el-textarea__inner:focus {
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2) !important;
  background: rgba(30, 41, 59, 1) !important;
}

.remark-textarea .el-textarea__inner::placeholder {
  color: #94a3b8 !important;
}

/* 表单输入框暗色主题样式 */
.driver-form-layout-enhanced :deep(.el-input__wrapper) {
  background: rgba(30, 41, 59, 0.8) !important;
  border: 2px solid rgba(147, 197, 253, 0.3) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.driver-form-layout-enhanced :deep(.el-input__wrapper.is-focus) {
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2) !important;
  background: rgba(30, 41, 59, 1) !important;
}

.driver-form-layout-enhanced :deep(.el-input__inner) {
  color: #e5e7eb !important;
  background: transparent !important;
}

.driver-form-layout-enhanced :deep(.el-input__inner::placeholder) {
  color: #94a3b8 !important;
}

/* 选择框暗色主题样式 */
.driver-form-layout-enhanced :deep(.el-select .el-input .el-input__wrapper) {
  background: rgba(30, 41, 59, 0.8) !important;
  border: 2px solid rgba(147, 197, 253, 0.3) !important;
}

.driver-form-layout-enhanced :deep(.el-select .el-input.is-focus .el-input__wrapper) {
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2) !important;
  background: rgba(30, 41, 59, 1) !important;
}

/* 数字输入框暗色主题样式 */
.driver-form-layout-enhanced :deep(.el-input-number .el-input__wrapper) {
  background: rgba(30, 41, 59, 0.8) !important;
  border: 2px solid rgba(147, 197, 253, 0.3) !important;
}

.driver-form-layout-enhanced :deep(.el-input-number .el-input__wrapper.is-focus) {
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2) !important;
}

/* 日期选择器暗色主题样式 */
.driver-form-layout-enhanced :deep(.el-date-editor .el-input__wrapper) {
  background: rgba(30, 41, 59, 0.8) !important;
  border: 2px solid rgba(147, 197, 253, 0.3) !important;
}

.driver-form-layout-enhanced :deep(.el-date-editor .el-input__wrapper.is-focus) {
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2) !important;
}

/* 树形选择器暗色主题样式 */
.driver-form-layout-enhanced :deep(.el-tree-select .el-input__wrapper) {
  background: rgba(30, 41, 59, 0.8) !important;
  border: 2px solid rgba(147, 197, 253, 0.3) !important;
}

.driver-form-layout-enhanced :deep(.el-tree-select .el-input__wrapper.is-focus) {
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2) !important;
}

/* 单选框暗色主题样式 */
.driver-form-layout-enhanced :deep(.el-radio) {
  --el-radio-text-color: #e5e7eb;
  --el-radio-input-border-color: rgba(147, 197, 253, 0.3);
  --el-radio-input-bg-color: rgba(30, 41, 59, 0.8);
}

.driver-form-layout-enhanced :deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: #60a5fa !important;
  border-color: #60a5fa !important;
}

/* 表单标签暗色主题样式 */
.driver-form-layout-enhanced :deep(.el-form-item__label) {
  color: #f8fafc !important;
  font-weight: 500;
}

.driver-form-layout-enhanced :deep(.el-form-item) {
  margin-bottom: 10px !important;
}





/* 详情对话框样式 */
.detail-dialog {
  --el-dialog-border-radius: 16px;
}

.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid rgba(147, 197, 253, 0.2);
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.6) 0%, rgba(30, 41, 59, 0.4) 100%);
}

.detail-card:last-child {
  margin-bottom: 0;
}

.detail-section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #f8fafc;
}

.detail-section-header .el-icon {
  color: #60a5fa;
  font-size: 18px;
}

.detail-info-grid {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.detail-avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.detail-avatar {
  border: 3px solid rgba(96, 165, 250, 0.3);
  box-shadow: 0 4px 12px rgba(96, 165, 250, 0.15);
}

.detail-status {
  display: flex;
  justify-content: center;
}

.detail-basic-info {
  flex: 1;
}



.detail-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%);
  border-radius: 8px;
  border: 1px solid rgba(147, 197, 253, 0.2);
  margin-bottom: 16px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-label {
  font-size: 12px;
  font-weight: 500;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 14px;
  font-weight: 600;
  color: #f8fafc;
  display: flex;
  align-items: center;
  gap: 6px;
}

.detail-remark {
  background: rgba(15, 23, 42, 0.8);
  padding: 12px;
  border-radius: 6px;
  border: 1px solid rgba(147, 197, 253, 0.2);
  font-size: 14px;
  line-height: 1.5;
  color: #e5e7eb;
  min-height: 60px;
}

/* 批量操作样式 */
.batch-dropdown {
  position: relative;
}

.batch-dropdown .el-button.is-disabled-visual {
  opacity: 0.6;
  cursor: not-allowed;
  position: relative;
}

.batch-dropdown .el-button.is-disabled-visual::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  border-radius: inherit;
  pointer-events: none;
}

.batch-content {
  padding: 20px 0;
}

.batch-alert {
  margin-bottom: 20px;
}

.batch-form {
  margin-top: 20px;
}

.batch-form .el-form-item {
  margin-bottom: 20px;
}

/* 导入对话框样式 */
.import-steps {
  margin-bottom: 30px;
}

.step-content {
  min-height: 300px;
}

.step-panel {
  padding: 20px;
  text-align: center;
}

.template-download {
  margin-top: 20px;
}

.template-info {
  margin-top: 20px;
  text-align: left;
  background: rgba(15, 23, 42, 0.6);
  padding: 15px;
  border-radius: 8px;
  border: 1px solid rgba(147, 197, 253, 0.2);
}

.template-info p {
  margin: 0 0 10px 0;
  font-weight: 500;
  color: #e5e7eb;
}

.field-tag {
  margin-right: 8px;
  margin-bottom: 6px;
}

.import-upload {
  margin: 20px 0;
}

.preview-info {
  margin-bottom: 20px;
}

/* 表单样式 - 深色主题 */
.modern-form {
  padding: 20px 0;
}



.status-radio {
  margin-right: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%);
  border-top: 1px solid rgba(147, 197, 253, 0.2);
}

/* 响应式设计 - 5列布局的适配性 */
@media (max-width: 1400px) {
  .cards-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 1200px) {
  .cards-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 12px;
  }

  .cards-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .card-body-compact {
    padding: 12px;
  }

  .driver-name-compact {
    font-size: 13px;
  }

  .employee-id-compact {
    font-size: 10px;
  }

  .detail-text {
    font-size: 10px;
  }

  .card-actions-compact .el-button {
    font-size: 10px;
    padding: 3px 6px;
    height: 24px;
  }

  .card-actions-compact .el-button:nth-child(2) {
    flex: 1.3;
    min-width: 75px;
  }

  .toolbar-content {
    flex-direction: column;
    align-items: stretch;
  }

  .action-buttons {
    justify-content: center;
    order: 2;
  }

  .view-controls {
    justify-content: center;
    order: 1;
  }

  .form-section {
    padding: 16px;
  }

  .detail-info-grid {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 20px;
  }

  .detail-basic-info {
    width: 100%;
  }


}

@media (max-width: 480px) {
  .cards-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .card-body-compact {
    padding: 10px;
  }

  .card-header-compact {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
  }

  .selection-checkbox {
    align-self: flex-end;
    position: absolute;
    top: 10px;
    right: 10px;
  }

  .driver-info-compact {
    width: 100%;
    text-align: center;
  }

  .card-actions-compact {
    flex-direction: column;
    gap: 4px;
  }

  .card-actions-compact .el-button {
    height: 32px;
  }

  .form-section {
    padding: 12px;
  }

  .dialog-footer {
    flex-direction: column;
  }
}

/* 全局下拉框样式 - 覆盖 Element UI，解决下拉框白色问题 */
:global(.el-select__popper) {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 100%) !important;
  backdrop-filter: blur(15px) !important;
  border: 2px solid rgba(147, 197, 253, 0.3) !important;
  border-radius: 12px !important;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(59, 130, 246, 0.2) !important;
}

:global(.el-select-dropdown) {
  background: transparent !important;
  border: none !important;
}

:global(.el-select-dropdown__item) {
  color: #e5e7eb !important;
  background: transparent !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  margin: 2px 8px !important;
}

:global(.el-select-dropdown__item:hover) {
  background: rgba(59, 130, 246, 0.2) !important;
  color: #60a5fa !important;
}

:global(.el-select-dropdown__item.is-selected) {
  background: rgba(34, 197, 94, 0.2) !important;
  color: #22c55e !important;
  font-weight: 600 !important;
}

:global(.el-select-dropdown__item.is-selected:hover) {
  background: rgba(34, 197, 94, 0.3) !important;
}

/* 在职状态下拉选项内的标签样式 */
:global(.el-select-dropdown__item .el-tag) {
  background: transparent !important;
  border: 1px solid currentColor !important;
  color: inherit !important;
  font-size: 12px !important;
}

:global(.el-select-dropdown__item .el-tag.el-tag--primary) {
  color: #4ade80 !important;
  border-color: #4ade80 !important;
  background: rgba(74, 222, 128, 0.1) !important;
}

:global(.el-select-dropdown__item .el-tag.el-tag--danger) {
  color: #f87171 !important;
  border-color: #f87171 !important;
  background: rgba(248, 113, 113, 0.1) !important;
}

:global(.el-select-dropdown__item span) {
  color: inherit !important;
}

:global(.el-select-dropdown__item.is-selected .el-tag.el-tag--primary) {
  color: #4ade80 !important;
  border-color: #4ade80 !important;
  background: rgba(74, 222, 128, 0.2) !important;

}

:global(.el-select-dropdown__item.is-selected .el-tag.el-tag--danger) {
  color: #f87171 !important;
  border-color: #f87171 !important;
  background: rgba(248, 113, 113, 0.2) !important;

}

:global(.el-select-dropdown__item:hover .el-tag.el-tag--primary) {
  color: #4ade80 !important;
  border-color: #4ade80 !important;
  background: rgba(74, 222, 128, 0.2) !important;

}

:global(.el-select-dropdown__item:hover .el-tag.el-tag--danger) {
  color: #f87171 !important;
  border-color: #f87171 !important;
  background: rgba(248, 113, 113, 0.2) !important;

}

/* 操作面板按钮样式 - 透明风格与车辆管理界面一致 */
.toolbar-panel :deep(.el-button) {
  border-color: rgba(147, 197, 253, 0.3) !important;
  background: rgba(30, 41, 59, 0.6) !important;
  color: #94a3b8 !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
}

.toolbar-panel :deep(.el-button:hover) {
  border-color: #60a5fa !important;
  background: rgba(59, 130, 246, 0.2) !important;
  color: #e5e7eb !important;
}

.toolbar-panel :deep(.el-button--primary) {
  background: rgba(59, 130, 246, 0.1) !important;
  border-color: rgba(59, 130, 246, 0.4) !important;
  color: #60a5fa !important;
}

.toolbar-panel :deep(.el-button--primary:hover) {
  background: rgba(59, 130, 246, 0.2) !important;
  border-color: rgba(59, 130, 246, 0.6) !important;
  color: #93c5fd !important;
}

.toolbar-panel :deep(.el-button--success) {
  background: rgba(16, 185, 129, 0.1) !important;
  border-color: rgba(16, 185, 129, 0.4) !important;
  color: #22c55e !important;
}

.toolbar-panel :deep(.el-button--success:hover) {
  background: rgba(16, 185, 129, 0.2) !important;
  border-color: rgba(16, 185, 129, 0.6) !important;
  color: #4ade80 !important;

}

.toolbar-panel :deep(.el-button--danger) {
  background: rgba(239, 68, 68, 0.1) !important;
  border-color: rgba(239, 68, 68, 0.4) !important;
  color: #f87171 !important;
}

.toolbar-panel :deep(.el-button--danger:hover) {
  background: rgba(239, 68, 68, 0.2) !important;
  border-color: rgba(239, 68, 68, 0.6) !important;
  color: #fca5a5 !important;

}

.toolbar-panel :deep(.el-button--info) {
  background: rgba(6, 182, 212, 0.1) !important;
  border-color: rgba(6, 182, 212, 0.4) !important;
  color: #22d3ee !important;
}

.toolbar-panel :deep(.el-button--info:hover) {
  background: rgba(6, 182, 212, 0.2) !important;
  border-color: rgba(6, 182, 212, 0.6) !important;
  color: #67e8f9 !important;

}

.toolbar-panel :deep(.el-button--warning) {
  background: rgba(245, 158, 11, 0.1) !important;
  border-color: rgba(245, 158, 11, 0.4) !important;
  color: #fbbf24 !important;
}

.toolbar-panel :deep(.el-button--warning:hover) {
  background: rgba(245, 158, 11, 0.2) !important;
  border-color: rgba(245, 158, 11, 0.6) !important;
  color: #fcd34d !important;

}

/* 通用按钮样式 */
:deep(.el-button) {
  border-color: rgba(147, 197, 253, 0.3) !important;
  background: rgba(30, 41, 59, 0.6) !important;
  color: #e5e7eb !important;
  transition: all 0.3s ease !important;
}

:deep(.el-button:hover) {
  border-color: #60a5fa !important;
  background: rgba(59, 130, 246, 0.2) !important;
  box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2) !important;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4) !important;
  transform: translateY(-1px) !important;
}

:deep(.el-button--success) {
  background: linear-gradient(135deg, #10b981 0%, #047857 100%) !important;
  border-color: #10b981 !important;
}

:deep(.el-button--success:hover) {
  background: linear-gradient(135deg, #059669 0%, #065f46 100%) !important;
  box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4) !important;
}

:deep(.el-button--danger) {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  border-color: #ef4444 !important;
}

:deep(.el-button--danger:hover) {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
  box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4) !important;
}

:deep(.el-button--warning) {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  border-color: #f59e0b !important;
}

:deep(.el-button--warning:hover) {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%) !important;
  box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4) !important;
}

:deep(.el-button--info) {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%) !important;
  border-color: #6b7280 !important;
}

:deep(.el-button--info:hover) {
  background: linear-gradient(135deg, #4b5563 0%, #374151 100%) !important;
  box-shadow: 0 6px 16px rgba(107, 114, 128, 0.4) !important;
}

/* 分页组件样式 */
:deep(.el-pagination) {
  color: #e5e7eb !important;
}

:deep(.el-pagination .el-pager li) {
  background: rgba(30, 41, 59, 0.6) !important;
  color: #e5e7eb !important;
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 6px !important;
  margin: 0 2px !important;
  transition: all 0.3s ease !important;
}

:deep(.el-pagination .el-pager li:hover) {
  background: rgba(59, 130, 246, 0.2) !important;
  border-color: #60a5fa !important;
  color: #f8fafc !important;
}

:deep(.el-pagination .el-pager li.is-active) {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  border-color: #3b82f6 !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  background: rgba(30, 41, 59, 0.6) !important;
  color: #e5e7eb !important;
  border: 1px solid rgba(147, 197, 253, 0.2) !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

:deep(.el-pagination .btn-prev:hover),
:deep(.el-pagination .btn-next:hover) {
  background: rgba(59, 130, 246, 0.2) !important;
  border-color: #60a5fa !important;
  color: #f8fafc !important;
}

:deep(.el-pagination .btn-prev:disabled),
:deep(.el-pagination .btn-next:disabled) {
  background: rgba(30, 41, 59, 0.3) !important;
  color: #64748b !important;
  border-color: rgba(107, 114, 128, 0.2) !important;
  cursor: not-allowed !important;
}

/* 其他必要的组件样式 */
:deep(.el-radio) {
  color: #e5e7eb !important;
}

:deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: #60a5fa !important;
  border-color: #60a5fa !important;
}

:deep(.el-checkbox) {
  color: #e5e7eb !important;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #60a5fa !important;
  border-color: #60a5fa !important;
}

:deep(.el-textarea__inner) {
  background: rgba(15, 23, 42, 0.8) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e5e7eb !important;
}

:deep(.el-textarea__inner:hover) {
  border-color: #60a5fa !important;
}

:deep(.el-textarea__inner:focus) {
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 1px rgba(96, 165, 250, 0.4) inset !important;
}

:deep(.el-tag) {
  background: rgba(30, 41, 59, 0.6) !important;
  border-color: rgba(147, 197, 253, 0.3) !important;
  color: #e5e7eb !important;
}

:deep(.el-tag.el-tag--primary) {
  background: rgba(74, 222, 128, 0.2) !important;
  border-color: rgba(74, 222, 128, 0.4) !important;
  color: #4ade80 !important;
}

:deep(.el-tag.el-tag--danger) {
  background: rgba(248, 113, 113, 0.2) !important;
  border-color: rgba(248, 113, 113, 0.4) !important;
  color: #f87171 !important;
}

/* 全局输入框基础样式 */
:deep(.el-input__wrapper) {
  background: rgba(30, 41, 59, 0.8) !important;
  border: 1px solid rgba(147, 197, 253, 0.3) !important;
  box-shadow:
    0 0 8px rgba(59, 130, 246, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.03) !important;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
}

:deep(.el-input__wrapper:hover) {
  border-color: #60a5fa !important;
  background: rgba(30, 41, 59, 0.9) !important;
  box-shadow:
    0 0 12px rgba(96, 165, 250, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6 !important;
  box-shadow:
    0 0 16px rgba(59, 130, 246, 0.2),
    0 0 0 1px rgba(59, 130, 246, 0.1) inset !important;
  background: rgba(30, 41, 59, 0.95) !important;
}

:deep(.el-input__inner) {
  color: #e5e7eb !important;
  background: transparent !important;
  font-weight: 400;
}

:deep(.el-input__inner::placeholder) {
  color: #64748b !important;
  font-weight: 400;
}

/* 全局选择框样式 */
:deep(.el-select .el-input .el-input__wrapper) {
  background: rgba(30, 41, 59, 0.8) !important;
  border: 1px solid rgba(147, 197, 253, 0.3) !important;
}

:deep(.el-select .el-input.is-focus .el-input__wrapper) {
  background: rgba(30, 41, 59, 0.95) !important;
  border-color: #3b82f6 !important;
}

:deep(.el-select .el-input:hover .el-input__wrapper) {
  border-color: #60a5fa !important;
  background: rgba(30, 41, 59, 0.9) !important;
}

/* 表单标签样式 */
:deep(.el-form-item__label) {
  color: #94a3b8 !important;
  font-weight: 500;
}

/* 对话框样式 */
:deep(.el-dialog) {
  background: linear-gradient(145deg, #1e293b 0%, #334155 50%, #1e293b 100%);
  border: 2px solid rgba(59, 130, 246, 0.4);
  border-radius: 16px;
  color: #e2e8f0;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.5),
    0 0 40px rgba(59, 130, 246, 0.2);
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 100%);
  border-bottom: 2px solid rgba(59, 130, 246, 0.3);
  color: #e2e8f0;
  border-radius: 14px 14px 0 0;
}

:deep(.el-dialog__title) {
  color: #f8fafc;
  font-weight: 600;
  font-size: 18px;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: #94a3b8;
  font-size: 20px;
  transition: all 0.3s ease;
}

:deep(.el-dialog__headerbtn .el-dialog__close:hover) {
  color: #60a5fa;
  text-shadow: 0 0 8px rgba(96, 165, 250, 0.5);
}

:deep(.el-dialog__body) {
  background: transparent;
  color: #e2e8f0;
  padding: 24px;
}

:deep(.el-dialog__footer) {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 100%);
  border-top: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 0 0 14px 14px;
  padding: 20px 24px;
}

/* 紧凑弹窗样式 */
.compact-dialog :deep(.el-dialog__body) {
  padding: 16px 20px !important;
}

.compact-dialog :deep(.el-dialog__header) {
  padding: 16px 20px 12px !important;
}

.compact-dialog :deep(.el-dialog__footer) {
  padding: 12px 20px !important;
}


.compact-dialog .records-card {
  margin: 0 !important;
}

.compact-dialog .records-card :deep(.el-card__body) {
  padding: 12px !important;
}



.detail-info-compact {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  width: 100%;
  overflow: visible;
}

.detail-avatar-section.compact {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
  min-width: 160px;
  max-width: 160px;
}

.detail-avatar-section.compact .detail-avatar {
  border: 2px solid rgba(96, 165, 250, 0.3);
}

.avatar-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.driver-name {
  font-size: 18px;
  font-weight: 600;
  color: #f8fafc;
}

.driver-id {
  font-size: 13px;
  color: #94a3b8;
  margin: 2px 0;
}


.detail-info-grid.compact {
  display: flex;
  gap: 16px;
  flex: 1;
  min-width: 0;
  overflow: visible;
}

.info-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-width: 0;
  overflow: visible;
}

.column-title {
  font-size: 13px;
  font-weight: 600;
  color: #60a5fa;
  margin-bottom: 6px;
  padding-bottom: 4px;
  border-bottom: 1px solid rgba(96, 165, 250, 0.2);
  white-space: nowrap;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  min-height: 24px;
  line-height: 1.4;
}

.info-label {
  font-size: 12px;
  color: #94a3b8;
  min-width: 60px;
  flex-shrink: 0;
  white-space: nowrap;
}

.info-value {
  font-size: 12px;
  color: #e2e8f0;
  flex: 1;
  word-break: break-all;
  line-height: 1.4;
}

.detail-remark-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(96, 165, 250, 0.2);
}

.remark-label {
  font-size: 13px;
  color: #94a3b8;
  margin-bottom: 8px;
}

.remark-content {
  font-size: 13px;
  color: #e2e8f0;
  background: rgba(30, 41, 59, 0.5);
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid rgba(96, 165, 250, 0.5);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .detail-info-compact {
    flex-direction: column;
    gap: 16px;
  }

  /* 增强对话框响应式样式 */
  .driver-edit-dialog-compact {
    margin: 4vh 16px !important;
    width: calc(100vw - 32px) !important;
    max-width: none !important;
    max-height: 92vh !important;
  }

  .driver-edit-dialog-compact .el-dialog__body {
    max-height: calc(92vh - 120px) !important;
  }

  .form-section-enhanced {
    padding: 14px 16px;
    margin-bottom: 12px;
  }

  .section-header {
    gap: 10px;
    margin-bottom: 12px;
    padding-bottom: 10px;
  }

  .section-icon {
    width: 32px;
    height: 32px;
  }

  .section-title {
    font-size: 15px;
  }

  .section-desc {
    font-size: 12px;
  }

  .form-content-layout-enhanced {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }

  .avatar-section-enhanced {
    width: 100%;
    max-width: 160px;
  }

  .info-fields-enhanced {
    width: 100%;
  }

  .form-grid-layout {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .form-grid-layout .full-width-field {
    grid-column: 1;
  }

  /* 详情界面响应式 */
  .detail-info-grid-enhanced {
    grid-template-columns: 1fr 1fr;
    gap: 10px 12px;
  }

  .avatar-section-enhanced.detail-avatar {
    width: 100%;
    max-width: 160px;
  }





  .detail-avatar-section.compact {
    justify-content: center;
    min-width: auto;
  }

  .detail-info-grid.compact {
    flex-direction: column;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .driver-edit-dialog-compact {
    margin: 1vh 4px !important;
    width: calc(100vw - 8px) !important;
    max-width: none !important;
    max-height: 98vh !important;
  }

  .driver-edit-dialog-compact .el-dialog__body {
    max-height: calc(98vh - 100px) !important;
    padding: 12px !important;
  }

  .header-left {
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }

  .header-icon-wrapper {
    width: 40px;
    height: 40px;
  }

  .dialog-icon {
    font-size: 18px;
  }

  .dialog-title {
    font-size: 16px;
  }

  .dialog-subtitle {
    font-size: 12px;
  }

  .form-section-enhanced {
    padding: 10px 12px;
    margin-bottom: 8px;
  }

  .section-header {
    gap: 6px;
    margin-bottom: 10px;
    padding-bottom: 6px;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .section-icon {
    width: 32px;
    height: 32px;
  }

  .section-icon .el-icon {
    font-size: 16px;
  }

  .section-title {
    font-size: 14px;
  }





  .dialog-footer {
    flex-direction: column;
    gap: 8px;
  }

  /* 详情界面小屏幕响应式 */
  .detail-info-grid-enhanced {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .form-content-layout-enhanced {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }

  .avatar-section-enhanced.detail-avatar {
    width: 100%;
    max-width: 140px;
  }

  .dialog-footer .el-button {
    width: 100%;
  }

  .driver-form-layout-enhanced .el-form-item {
    margin-bottom: 16px;
  }

  .el-row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .el-col {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-bottom: 12px;
  }



  .form-grid-layout {
    gap: 6px;
  }

  .driver-form-layout-enhanced :deep(.el-form-item) {
    margin-bottom: 10px !important;
  }
}
</style>
