<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>临时调整处理</span>
          <el-button @click="$router.go(-1)">返回</el-button>
        </div>
      </template>

      <div class="adjust-content">
        <el-alert
          title="临时调整处理页面"
          description="此页面用于处理突发情况的临时调整，如司机请假、车辆故障等。"
          type="warning"
          :closable="false"
          style="margin-bottom: 20px"
        />

        <el-empty description="临时调整处理功能开发中" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="PlanAdjust"></script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.adjust-content {
  padding: 20px;
}
</style>
