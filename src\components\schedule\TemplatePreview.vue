<!-- 模板预览组件 -->
<template>
  <div class="template-preview">
    <div class="preview-header">
      <div class="template-summary">
        <h3>{{ config.templateName || '新建模板' }}</h3>
        <div class="summary-tags">
          <el-tag :type="getModeTag().type">{{ getModeTag().label }}</el-tag>
          <el-tag v-if="config.routeId" type="info">{{ getRouteName() }}</el-tag>
        </div>
      </div>
      <div class="preview-stats">
        <div class="stat-item">
          <span class="stat-value">{{ scheduleData.length }}</span>
          <span class="stat-label">发车班次</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{{ operatingTime }}</span>
          <span class="stat-label">运营时长</span>
        </div>
        <div class="stat-item">
          <span class="stat-value">{{ averageInterval }}</span>
          <span class="stat-label">平均间隔</span>
        </div>
      </div>
    </div>

    <div class="preview-content">
      <div class="schedule-view">
        <h4>
          <el-icon><Clock /></el-icon>
          发车时刻表
          <el-button-group size="small" style="margin-left: auto">
            <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
              <el-icon><Grid /></el-icon>
              表格视图
            </el-button>
            <el-button :type="viewMode === 'timeline' ? 'primary' : ''" @click="viewMode = 'timeline'">
              <el-icon><Timer /></el-icon>
              时间轴视图
            </el-button>
          </el-button-group>
        </h4>

        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'" class="table-view">
          <el-table :data="paginatedSchedule" height="400" :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
            <el-table-column label="班次" width="80" align="center">
              <template #default="scope">
                {{ scope.$index + 1 + (currentPage - 1) * pageSize }}
              </template>
            </el-table-column>
            <el-table-column label="发车时间" prop="departureTime" width="120" align="center" />
            <el-table-column label="时段" prop="period" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getPeriodColor(scope.row.period)" size="small">
                  {{ scope.row.period }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="间隔(分钟)" prop="interval" width="100" align="center" />
            <el-table-column label="备注" prop="note" />
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[20, 50, 100]"
              :total="scheduleData.length"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>

        <!-- 时间轴视图 -->
        <div v-if="viewMode === 'timeline'" class="timeline-view">
          <div class="timeline-container">
            <div class="timeline-hour-marks">
              <div v-for="hour in 24" :key="hour" class="hour-mark" :style="{ left: getHourPosition(hour - 1) + '%' }">
                {{ String(hour - 1).padStart(2, '0') }}:00
              </div>
            </div>
            <div class="timeline-schedule">
              <div
                v-for="(item, index) in scheduleData"
                :key="index"
                class="schedule-point"
                :style="{ left: getTimePosition(item.departureTime) + '%' }"
                :title="`${item.departureTime} - ${item.period}`"
              >
                <div class="point-dot" :class="getPeriodClass(item.period)"></div>
                <div class="point-time">{{ item.departureTime }}</div>
              </div>
            </div>
          </div>

          <div class="timeline-legend">
            <h5>时段说明：</h5>
            <div class="legend-items">
              <div v-for="period in uniquePeriods" :key="period" class="legend-item">
                <span class="legend-dot" :class="getPeriodClass(period)"></span>
                <span>{{ period }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="config-summary">
        <h4>
          <el-icon><Setting /></el-icon>
          配置摘要
        </h4>
        <div class="summary-content">
          <div class="summary-section" v-if="config.templateName"><strong>模板名称：</strong>{{ config.templateName }}</div>

          <!-- 固定间隔模式摘要 -->
          <div v-if="selectedMode === 'fixed'" class="summary-section">
            <strong>模式：</strong>固定间隔模式<br />
            <strong>运营时间：</strong>{{ config.firstBusTime }} - {{ config.lastBusTime }}<br />
            <strong>发车间隔：</strong>{{ config.departureInterval }}分钟<br />
            <strong>单程时间：</strong>{{ config.singleTripTime }}分钟
          </div>

          <!-- 分时段模式摘要 -->
          <div v-if="selectedMode === 'period'" class="summary-section">
            <strong>模式：</strong>分时段模式<br />
            <div v-for="(period, index) in config.periods" :key="index" class="period-summary">
              {{ period.startTime }}-{{ period.endTime }}: {{ period.interval }}分钟 ({{ period.description }})
            </div>
          </div>

          <!-- 智能密度模式摘要 -->
          <div v-if="selectedMode === 'density'" class="summary-section">
            <strong>模式：</strong>智能密度模式<br />
            <strong>密度等级：</strong>{{ getDensityLevelName() }}<br />
            <strong>运营时间：</strong>{{ config.firstBusTime }} - {{ config.lastBusTime }}<br />
            <strong>优化选项：</strong>{{ getOptimizationNames() }}
          </div>

          <div v-if="config.description" class="summary-section"><strong>描述：</strong>{{ config.description }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Clock, Timer, Setting, Grid } from '@element-plus/icons-vue';

const props = defineProps({
  config: {
    type: Object,
    default: () => ({})
  },
  selectedMode: {
    type: String,
    default: ''
  }
});

// 视图模式
const viewMode = ref('table');
const currentPage = ref(1);
const pageSize = ref(20);

// 路线选项
const routeOptions = ref([
  { routeId: 1, routeName: '1路' },
  { routeId: 2, routeName: '2路' },
  { routeId: 3, routeName: '3路' },
  { routeId: 4, routeName: '4路' }
]);

// 生成时刻表数据
const scheduleData = computed(() => {
  if (!props.config || !props.selectedMode) return [];

  switch (props.selectedMode) {
    case 'fixed':
      return generateFixedSchedule();
    case 'period':
      return generatePeriodSchedule();
    case 'density':
      return generateDensitySchedule();
    default:
      return [];
  }
});

// 分页数据
const paginatedSchedule = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return scheduleData.value.slice(start, end);
});

// 生成固定间隔时刻表
function generateFixedSchedule() {
  const { firstBusTime, lastBusTime, departureInterval } = props.config;
  if (!firstBusTime || !lastBusTime || !departureInterval) return [];

  const schedule = [];
  const [startHour, startMin] = firstBusTime.split(':').map(Number);
  const [endHour, endMin] = lastBusTime.split(':').map(Number);

  let currentMinutes = startHour * 60 + startMin;
  const endMinutes = endHour * 60 + endMin;
  let tripNumber = 1;

  while (currentMinutes <= endMinutes) {
    const hours = Math.floor(currentMinutes / 60);
    const minutes = currentMinutes % 60;
    const timeStr = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

    let period = '平峰';
    if (hours >= 7 && hours < 9) period = '早高峰';
    else if (hours >= 17 && hours < 19) period = '晚高峰';
    else if (hours >= 22 || hours < 6) period = '夜间';

    schedule.push({
      tripNumber,
      departureTime: timeStr,
      period,
      interval: departureInterval,
      note: tripNumber === 1 ? '首班车' : currentMinutes + departureInterval > endMinutes ? '末班车' : ''
    });

    currentMinutes += departureInterval;
    tripNumber++;
  }

  return schedule;
}

// 生成分时段时刻表
function generatePeriodSchedule() {
  const { periods } = props.config;
  if (!periods || !periods.length) return [];

  const schedule = [];
  let tripNumber = 1;

  periods.forEach((period) => {
    if (!period.startTime || !period.endTime || !period.interval) return;

    const [startHour, startMin] = period.startTime.split(':').map(Number);
    const [endHour, endMin] = period.endTime.split(':').map(Number);

    let currentMinutes = startHour * 60 + startMin;
    const endMinutes = endHour * 60 + endMin;

    while (currentMinutes < endMinutes) {
      const hours = Math.floor(currentMinutes / 60);
      const minutes = currentMinutes % 60;
      const timeStr = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

      schedule.push({
        tripNumber,
        departureTime: timeStr,
        period: period.description || `时段${periods.indexOf(period) + 1}`,
        interval: period.interval,
        note: ''
      });

      currentMinutes += period.interval;
      tripNumber++;
    }
  });

  return schedule.sort((a, b) => a.departureTime.localeCompare(b.departureTime));
}

// 生成智能密度时刻表
function generateDensitySchedule() {
  const { firstBusTime, lastBusTime, densityLevel } = props.config;
  if (!firstBusTime || !lastBusTime || !densityLevel) return [];

  // 根据密度等级确定基础间隔
  const baseInterval = densityLevel === 'high' ? 6 : densityLevel === 'medium' ? 12 : 20;

  const schedule = [];
  const [startHour, startMin] = firstBusTime.split(':').map(Number);
  const [endHour, endMin] = lastBusTime.split(':').map(Number);

  let currentMinutes = startHour * 60 + startMin;
  const endMinutes = endHour * 60 + endMin;
  let tripNumber = 1;

  while (currentMinutes <= endMinutes) {
    const hours = Math.floor(currentMinutes / 60);
    const minutes = currentMinutes % 60;
    const timeStr = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

    // 智能调整间隔
    let interval = baseInterval;
    let period = '常规';

    if (hours >= 7 && hours < 9) {
      interval = Math.max(baseInterval * 0.7, 3);
      period = '早高峰';
    } else if (hours >= 17 && hours < 19) {
      interval = Math.max(baseInterval * 0.7, 3);
      period = '晚高峰';
    } else if (hours >= 22 || hours < 6) {
      interval = baseInterval * 1.5;
      period = '夜间';
    }

    schedule.push({
      tripNumber,
      departureTime: timeStr,
      period,
      interval: Math.round(interval),
      note: ''
    });

    currentMinutes += Math.round(interval);
    tripNumber++;
  }

  return schedule;
}

// 计算运营时长
const operatingTime = computed(() => {
  if (scheduleData.value.length < 2) return '--';

  const firstTime = scheduleData.value[0].departureTime;
  const lastTime = scheduleData.value[scheduleData.value.length - 1].departureTime;

  const [firstHour, firstMin] = firstTime.split(':').map(Number);
  const [lastHour, lastMin] = lastTime.split(':').map(Number);

  const totalMinutes = lastHour * 60 + lastMin - (firstHour * 60 + firstMin);
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  return `${hours}小时${minutes}分钟`;
});

// 计算平均间隔
const averageInterval = computed(() => {
  if (scheduleData.value.length < 2) return '--';

  const intervals = [];
  for (let i = 1; i < scheduleData.value.length; i++) {
    const prevTime = scheduleData.value[i - 1].departureTime;
    const currTime = scheduleData.value[i].departureTime;

    const [prevHour, prevMin] = prevTime.split(':').map(Number);
    const [currHour, currMin] = currTime.split(':').map(Number);

    const interval = currHour * 60 + currMin - (prevHour * 60 + prevMin);
    if (interval > 0) intervals.push(interval);
  }

  if (intervals.length === 0) return '--';
  const avg = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
  return Math.round(avg) + '分钟';
});

// 获取独特的时段
const uniquePeriods = computed(() => {
  return [...new Set(scheduleData.value.map((item) => item.period))];
});

// 获取模式标签
function getModeTag() {
  const modeMap = {
    'fixed': { label: '固定间隔', type: 'info' },
    'period': { label: '分时段', type: 'warning' },
    'density': { label: '智能密度', type: 'success' },
    'holiday': { label: '节假日', type: 'primary' },
    'copy': { label: '复制模式', type: 'info' }
  };
  return modeMap[props.selectedMode] || { label: '未知', type: 'info' };
}

// 获取线路名称
function getRouteName() {
  const route = routeOptions.value.find((r) => r.routeId === props.config.routeId);
  return route ? route.routeName : '';
}

// 时间轴相关方法
function getHourPosition(hour) {
  return (hour / 24) * 100;
}

function getTimePosition(timeStr) {
  const [hour, minute] = timeStr.split(':').map(Number);
  const totalMinutes = hour * 60 + minute;
  return (totalMinutes / (24 * 60)) * 100;
}

function getPeriodColor(period) {
  const colorMap = {
    '早高峰': 'danger',
    '晚高峰': 'danger',
    '平峰': 'success',
    '夜间': 'info'
  };
  return colorMap[period] || 'primary';
}

function getPeriodClass(period) {
  const classMap = {
    '早高峰': 'rush-hour',
    '晚高峰': 'rush-hour',
    '平峰': 'normal',
    '夜间': 'night'
  };
  return classMap[period] || 'normal';
}

// 其他辅助方法
function getDensityLevelName() {
  const levelMap = {
    'high': '高密度',
    'medium': '中密度',
    'low': '低密度'
  };
  return levelMap[props.config.densityLevel] || '未知';
}

function getOptimizationNames() {
  if (!props.config.optimizations || !props.config.optimizations.length) return '无';

  const nameMap = {
    'peakOptimization': '高峰加密',
    'weatherAdjustment': '天气调整',
    'eventOptimization': '节假日调整',
    'realTimeAdjustment': '实时调整'
  };

  return props.config.optimizations.map((opt) => nameMap[opt] || opt).join('、');
}

function handleSizeChange(val) {
  pageSize.value = val;
  currentPage.value = 1;
}

function handleCurrentChange(val) {
  currentPage.value = val;
}
</script>

<style scoped>
.template-preview {
  padding: 20px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.template-summary h3 {
  margin: 0 0 12px 0;
  font-size: 20px;
}

.summary-tags {
  display: flex;
  gap: 8px;
}

.preview-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
}

.preview-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.schedule-view {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.schedule-view h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px 0;
  color: #1f2937;
}

.table-view {
  height: 500px;
  display: flex;
  flex-direction: column;
}

.pagination-wrapper {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.timeline-view {
  height: 500px;
  overflow-y: auto;
}

.timeline-container {
  position: relative;
  height: 300px;
  background: #f9fafb;
  border-radius: 8px;
  margin-bottom: 20px;
  overflow-x: auto;
}

.timeline-hour-marks {
  position: relative;
  height: 40px;
  border-bottom: 1px solid #e5e7eb;
}

.hour-mark {
  position: absolute;
  top: 0;
  height: 100%;
  font-size: 12px;
  color: #6b7280;
  padding: 8px 4px;
  border-left: 1px solid #e5e7eb;
}

.timeline-schedule {
  position: relative;
  height: 260px;
  padding: 10px 0;
}

.schedule-point {
  position: absolute;
  top: 20px;
  transform: translateX(-50%);
}

.point-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin: 0 auto 4px;
}

.point-dot.rush-hour {
  background: #ef4444;
}

.point-dot.normal {
  background: #10b981;
}

.point-dot.night {
  background: #6b7280;
}

.point-time {
  font-size: 10px;
  color: #374151;
  text-align: center;
  white-space: nowrap;
}

.timeline-legend {
  background: white;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.timeline-legend h5 {
  margin: 0 0 12px 0;
  color: #374151;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.config-summary {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  height: fit-content;
}

.config-summary h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  color: #1f2937;
}

.summary-content {
  font-size: 14px;
  line-height: 1.6;
}

.summary-section {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f4f6;
}

.summary-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.period-summary {
  margin: 4px 0;
  padding-left: 12px;
  color: #6b7280;
}
</style>
